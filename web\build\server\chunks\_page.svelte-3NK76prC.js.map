{"version": 3, "file": "_page.svelte-3NK76prC.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/releases/_page.svelte.js"], "sourcesContent": ["import { U as ensure_array_like, V as escape_html, R as attr, N as bind_props, y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { P as PortableText } from \"../../../../chunks/PortableText.js\";\nimport { C as Calendar } from \"../../../../chunks/calendar.js\";\nimport { M as Map_pin } from \"../../../../chunks/map-pin.js\";\nimport { E as External_link } from \"../../../../chunks/external-link.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  const {\n    pressReleasesPage,\n    pressReleasesByYear,\n    years\n  } = data;\n  const fallbackPressReleases = {\n    \"2023\": [\n      {\n        title: \"<PERSON>rl<PERSON> Raises $5M Series A to Revolutionize Job Applications\",\n        publishedAt: \"2023-06-15\",\n        excerpt: \"Funding will accelerate product development and market expansion for the AI-powered job application platform.\",\n        slug: { current: \"series-a-funding\" },\n        location: \"San Francisco, CA\"\n      },\n      {\n        title: \"Hirli Launches AI Co-Pilot for Personalized Job Search Assistance\",\n        publishedAt: \"2023-03-08\",\n        excerpt: \"New AI-powered feature provides tailored guidance and support throughout the job search process.\",\n        slug: { current: \"ai-copilot-launch\" }\n      }\n    ],\n    \"2022\": [\n      {\n        title: \"Hirli Reaches Milestone of 100,000 Users\",\n        publishedAt: \"2022-11-22\",\n        excerpt: \"Platform celebrates helping 100,000 job seekers streamline their application process and find employment opportunities.\",\n        slug: { current: \"100k-users\" }\n      }\n    ]\n  };\n  const fallbackYears = [\"2023\", \"2022\"];\n  Object.keys(pressReleasesByYear).length > 0 ? pressReleasesByYear : fallbackPressReleases;\n  years.length > 0 ? years : fallbackYears;\n  function formatDate(dateStr) {\n    const date = new Date(dateStr);\n    return date.toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"long\",\n      day: \"numeric\"\n    });\n  }\n  SEO($$payload, {\n    title: pressReleasesPage?.seo?.metaTitle || \"Press Releases | Hirli\",\n    description: pressReleasesPage?.seo?.metaDescription || \"Official press releases from Hirli, the AI-powered job application platform.\",\n    keywords: pressReleasesPage?.seo?.keywords?.join(\", \") || \"Hirli press releases, company news, job application platform, AI technology\"\n  });\n  $$payload.out += `<!----> <div><h2 class=\"mb-8 text-3xl font-semibold\">Releases</h2> `;\n  if (pressReleasesPage?.content) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"mb-8\">`;\n    PortableText($$payload, { value: pressReleasesPage.content });\n    $$payload.out += `<!----></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <div class=\"space-y-16\">`;\n  if (Object.keys(pressReleasesByYear).length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array = ensure_array_like(years);\n    $$payload.out += `<!--[-->`;\n    for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {\n      let year = each_array[$$index_1];\n      const each_array_1 = ensure_array_like(pressReleasesByYear[year]);\n      $$payload.out += `<div><h2 class=\"mb-8 text-2xl font-semibold\">${escape_html(year)}</h2> <div class=\"space-y-6\"><!--[-->`;\n      for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {\n        let release = each_array_1[$$index];\n        $$payload.out += `<div class=\"border-b pb-6\"><div class=\"mb-2 flex items-center gap-2\">`;\n        Calendar($$payload, { class: \"h-4 w-4 text-gray-500\" });\n        $$payload.out += `<!----> <p class=\"text-muted-foreground text-sm\">${escape_html(formatDate(release.publishedAt))}</p> `;\n        if (release.location) {\n          $$payload.out += \"<!--[-->\";\n          $$payload.out += `<span class=\"mx-1 text-gray-400\">•</span> `;\n          Map_pin($$payload, { class: \"h-4 w-4 text-gray-500\" });\n          $$payload.out += `<!----> <p class=\"text-muted-foreground text-sm\">${escape_html(release.location)}</p>`;\n        } else {\n          $$payload.out += \"<!--[!-->\";\n        }\n        $$payload.out += `<!--]--></div> <h3 class=\"mb-2 text-xl font-medium\">${escape_html(release.title)}</h3> `;\n        if (release.subtitle) {\n          $$payload.out += \"<!--[-->\";\n          $$payload.out += `<p class=\"mb-2 text-lg text-gray-700\">${escape_html(release.subtitle)}</p>`;\n        } else {\n          $$payload.out += \"<!--[!-->\";\n        }\n        $$payload.out += `<!--]--> `;\n        if (release.excerpt) {\n          $$payload.out += \"<!--[-->\";\n          $$payload.out += `<p class=\"text-muted-foreground mb-4\">${escape_html(release.excerpt)}</p>`;\n        } else {\n          $$payload.out += \"<!--[!-->\";\n        }\n        $$payload.out += `<!--]--> <div class=\"flex\"><a${attr(\"href\", `/press/releases/${release.slug.current}`)} class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">Read More `;\n        External_link($$payload, { class: \"ml-1 h-3 w-3\" });\n        $$payload.out += `<!----></a></div></div>`;\n      }\n      $$payload.out += `<!--]--></div></div>`;\n    }\n    $$payload.out += `<!--]-->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    const each_array_2 = ensure_array_like(fallbackYears);\n    $$payload.out += `<!--[-->`;\n    for (let $$index_3 = 0, $$length = each_array_2.length; $$index_3 < $$length; $$index_3++) {\n      let year = each_array_2[$$index_3];\n      const each_array_3 = ensure_array_like(fallbackPressReleases[year]);\n      $$payload.out += `<div><h2 class=\"mb-8 text-2xl font-semibold\">${escape_html(year)}</h2> <div class=\"space-y-6\"><!--[-->`;\n      for (let $$index_2 = 0, $$length2 = each_array_3.length; $$index_2 < $$length2; $$index_2++) {\n        let release = each_array_3[$$index_2];\n        $$payload.out += `<div class=\"border-b pb-6\"><div class=\"mb-2 flex items-center gap-2\">`;\n        Calendar($$payload, { class: \"h-4 w-4 text-gray-500\" });\n        $$payload.out += `<!----> <p class=\"text-muted-foreground text-sm\">${escape_html(formatDate(release.publishedAt))}</p> `;\n        if (release.location) {\n          $$payload.out += \"<!--[-->\";\n          $$payload.out += `<span class=\"mx-1 text-gray-400\">•</span> `;\n          Map_pin($$payload, { class: \"h-4 w-4 text-gray-500\" });\n          $$payload.out += `<!----> <p class=\"text-muted-foreground text-sm\">${escape_html(release.location)}</p>`;\n        } else {\n          $$payload.out += \"<!--[!-->\";\n        }\n        $$payload.out += `<!--]--></div> <h3 class=\"mb-2 text-xl font-medium\">${escape_html(release.title)}</h3> `;\n        if (release.excerpt) {\n          $$payload.out += \"<!--[-->\";\n          $$payload.out += `<p class=\"text-muted-foreground mb-4\">${escape_html(release.excerpt)}</p>`;\n        } else {\n          $$payload.out += \"<!--[!-->\";\n        }\n        $$payload.out += `<!--]--> <div class=\"flex\"><a${attr(\"href\", `/press/releases/${release.slug.current}`)} class=\"text-primary inline-flex items-center text-sm font-medium hover:underline\">Read More `;\n        External_link($$payload, { class: \"ml-1 h-3 w-3\" });\n        $$payload.out += `<!----></a></div></div>`;\n      }\n      $$payload.out += `<!--]--></div></div>`;\n    }\n    $$payload.out += `<!--]-->`;\n  }\n  $$payload.out += `<!--]--></div></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAMA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM;AACR,IAAI,iBAAiB;AACrB,IAAI,mBAAmB;AACvB,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,qBAAqB,GAAG;AAChC,IAAI,MAAM,EAAE;AACZ,MAAM;AACN,QAAQ,KAAK,EAAE,6DAA6D;AAC5E,QAAQ,WAAW,EAAE,YAAY;AACjC,QAAQ,OAAO,EAAE,+GAA+G;AAChI,QAAQ,IAAI,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE;AAC7C,QAAQ,QAAQ,EAAE;AAClB,OAAO;AACP,MAAM;AACN,QAAQ,KAAK,EAAE,mEAAmE;AAClF,QAAQ,WAAW,EAAE,YAAY;AACjC,QAAQ,OAAO,EAAE,kGAAkG;AACnH,QAAQ,IAAI,EAAE,EAAE,OAAO,EAAE,mBAAmB;AAC5C;AACA,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM;AACN,QAAQ,KAAK,EAAE,0CAA0C;AACzD,QAAQ,WAAW,EAAE,YAAY;AACjC,QAAQ,OAAO,EAAE,yHAAyH;AAC1I,QAAQ,IAAI,EAAE,EAAE,OAAO,EAAE,YAAY;AACrC;AACA;AACA,GAAG;AACH,EAAE,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;AACxC,EAAE,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,mBAAmB,GAAG,qBAAqB;AAC3F,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,aAAa;AAC1C,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE;AAC5C,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,GAAG,EAAE;AACX,KAAK,CAAC;AACN;AACA,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,iBAAiB,EAAE,GAAG,EAAE,SAAS,IAAI,wBAAwB;AACxE,IAAI,WAAW,EAAE,iBAAiB,EAAE,GAAG,EAAE,eAAe,IAAI,8EAA8E;AAC1I,IAAI,QAAQ,EAAE,iBAAiB,EAAE,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;AAC9D,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mEAAmE,CAAC;AACxF,EAAE,IAAI,iBAAiB,EAAE,OAAO,EAAE;AAClC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AACzC,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,iBAAiB,CAAC,OAAO,EAAE,CAAC;AACjE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iCAAiC,CAAC;AACtD,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AACnD,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,KAAK,CAAC;AAC/C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,MAAM,IAAI,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC;AACtC,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;AACvE,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,6CAA6C,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,qCAAqC,CAAC;AAC/H,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,EAAE;AAC7F,QAAQ,IAAI,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;AAC3C,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,qEAAqE,CAAC;AAChG,QAAQ,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AAC/D,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;AAChI,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC9B,UAAU,SAAS,CAAC,GAAG,IAAI,UAAU;AACrC,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,0CAA0C,CAAC;AACvE,UAAU,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AAChE,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;AAClH,SAAS,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,IAAI,WAAW;AACtC;AACA,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,oDAAoD,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAClH,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC9B,UAAU,SAAS,CAAC,GAAG,IAAI,UAAU;AACrC,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,sCAAsC,EAAE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;AACvG,SAAS,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,IAAI,WAAW;AACtC;AACA,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,QAAQ,IAAI,OAAO,CAAC,OAAO,EAAE;AAC7B,UAAU,SAAS,CAAC,GAAG,IAAI,UAAU;AACrC,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,sCAAsC,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AACtG,SAAS,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,IAAI,WAAW;AACtC;AACA,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,6FAA6F,CAAC;AAC/M,QAAQ,aAAa,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC3D,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AAClD;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC7C;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,aAAa,CAAC;AACzD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AACxC,MAAM,MAAM,YAAY,GAAG,iBAAiB,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;AACzE,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,6CAA6C,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,qCAAqC,CAAC;AAC/H,MAAM,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACnG,QAAQ,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,qEAAqE,CAAC;AAChG,QAAQ,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AAC/D,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC;AAChI,QAAQ,IAAI,OAAO,CAAC,QAAQ,EAAE;AAC9B,UAAU,SAAS,CAAC,GAAG,IAAI,UAAU;AACrC,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,0CAA0C,CAAC;AACvE,UAAU,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;AAChE,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,iDAAiD,EAAE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;AAClH,SAAS,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,IAAI,WAAW;AACtC;AACA,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,oDAAoD,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AAClH,QAAQ,IAAI,OAAO,CAAC,OAAO,EAAE;AAC7B,UAAU,SAAS,CAAC,GAAG,IAAI,UAAU;AACrC,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,sCAAsC,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC;AACtG,SAAS,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,IAAI,WAAW;AACtC;AACA,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,6BAA6B,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,6FAA6F,CAAC;AAC/M,QAAQ,aAAa,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC3D,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AAClD;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC7C;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}