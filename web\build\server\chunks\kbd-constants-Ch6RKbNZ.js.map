{"version": 3, "file": "kbd-constants-Ch6RKbNZ.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/kbd-constants.js"], "sourcesContent": ["function getDataOpenClosed(condition) {\n  return condition ? \"open\" : \"closed\";\n}\nfunction getDataChecked(condition) {\n  return condition ? \"checked\" : \"unchecked\";\n}\nfunction getAriaDisabled(condition) {\n  return condition ? \"true\" : \"false\";\n}\nfunction getAriaReadonly(condition) {\n  return condition ? \"true\" : \"false\";\n}\nfunction getAriaExpanded(condition) {\n  return condition ? \"true\" : \"false\";\n}\nfunction getDataDisabled(condition) {\n  return condition ? \"\" : void 0;\n}\nfunction getAriaRequired(condition) {\n  return condition ? \"true\" : \"false\";\n}\nfunction getAriaSelected(condition) {\n  return condition ? \"true\" : \"false\";\n}\nfunction getAriaChecked(checked, indeterminate) {\n  if (indeterminate) {\n    return \"mixed\";\n  }\n  return checked ? \"true\" : \"false\";\n}\nfunction getAriaOrientation(orientation) {\n  return orientation;\n}\nfunction getAriaHidden(condition) {\n  return condition ? \"true\" : void 0;\n}\nfunction getDataOrientation(orientation) {\n  return orientation;\n}\nfunction getDataInvalid(condition) {\n  return condition ? \"\" : void 0;\n}\nfunction getDataRequired(condition) {\n  return condition ? \"\" : void 0;\n}\nfunction getDataReadonly(condition) {\n  return condition ? \"\" : void 0;\n}\nfunction getDataSelected(condition) {\n  return condition ? \"\" : void 0;\n}\nfunction getDataUnavailable(condition) {\n  return condition ? \"\" : void 0;\n}\nfunction getHidden(condition) {\n  return condition ? true : void 0;\n}\nfunction getDisabled(condition) {\n  return condition ? true : void 0;\n}\nfunction getRequired(condition) {\n  return condition ? true : void 0;\n}\nconst ARROW_DOWN = \"ArrowDown\";\nconst ARROW_LEFT = \"ArrowLeft\";\nconst ARROW_RIGHT = \"ArrowRight\";\nconst ARROW_UP = \"ArrowUp\";\nconst END = \"End\";\nconst ENTER = \"Enter\";\nconst ESCAPE = \"Escape\";\nconst HOME = \"Home\";\nconst PAGE_DOWN = \"PageDown\";\nconst PAGE_UP = \"PageUp\";\nconst SPACE = \" \";\nconst TAB = \"Tab\";\nconst p = \"p\";\nconst n = \"n\";\nconst j = \"j\";\nconst k = \"k\";\nexport {\n  ARROW_LEFT as A,\n  getDataReadonly as B,\n  getDataInvalid as C,\n  getDataUnavailable as D,\n  END as E,\n  getAriaReadonly as F,\n  HOME as H,\n  PAGE_UP as P,\n  SPACE as S,\n  TAB as T,\n  ARROW_RIGHT as a,\n  ARROW_DOWN as b,\n  getAriaExpanded as c,\n  getDataOpenClosed as d,\n  getDataDisabled as e,\n  ARROW_UP as f,\n  getDataOrientation as g,\n  PAGE_DOWN as h,\n  ENTER as i,\n  getAriaOrientation as j,\n  getAriaDisabled as k,\n  ESCAPE as l,\n  k as m,\n  j as n,\n  n as o,\n  p,\n  getDataSelected as q,\n  getAriaSelected as r,\n  getDisabled as s,\n  getHidden as t,\n  getDataRequired as u,\n  getDataChecked as v,\n  getAriaRequired as w,\n  getAriaChecked as x,\n  getAriaHidden as y,\n  getRequired as z\n};\n"], "names": [], "mappings": "AAAA,SAAS,iBAAiB,CAAC,SAAS,EAAE;AACtC,EAAE,OAAO,SAAS,GAAG,MAAM,GAAG,QAAQ;AACtC;AACA,SAAS,cAAc,CAAC,SAAS,EAAE;AACnC,EAAE,OAAO,SAAS,GAAG,SAAS,GAAG,WAAW;AAC5C;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,MAAM,GAAG,OAAO;AACrC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,MAAM,GAAG,OAAO;AACrC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,MAAM,GAAG,OAAO;AACrC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,EAAE,GAAG,MAAM;AAChC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,MAAM,GAAG,OAAO;AACrC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,MAAM,GAAG,OAAO;AACrC;AACA,SAAS,cAAc,CAAC,OAAO,EAAE,aAAa,EAAE;AAChD,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,OAAO,OAAO;AAClB;AACA,EAAE,OAAO,OAAO,GAAG,MAAM,GAAG,OAAO;AACnC;AACA,SAAS,kBAAkB,CAAC,WAAW,EAAE;AACzC,EAAE,OAAO,WAAW;AACpB;AACA,SAAS,aAAa,CAAC,SAAS,EAAE;AAClC,EAAE,OAAO,SAAS,GAAG,MAAM,GAAG,MAAM;AACpC;AACA,SAAS,kBAAkB,CAAC,WAAW,EAAE;AACzC,EAAE,OAAO,WAAW;AACpB;AACA,SAAS,cAAc,CAAC,SAAS,EAAE;AACnC,EAAE,OAAO,SAAS,GAAG,EAAE,GAAG,MAAM;AAChC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,EAAE,GAAG,MAAM;AAChC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,EAAE,GAAG,MAAM;AAChC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,EAAE,GAAG,MAAM;AAChC;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE;AACvC,EAAE,OAAO,SAAS,GAAG,EAAE,GAAG,MAAM;AAChC;AACA,SAAS,SAAS,CAAC,SAAS,EAAE;AAC9B,EAAE,OAAO,SAAS,GAAG,IAAI,GAAG,MAAM;AAClC;AACA,SAAS,WAAW,CAAC,SAAS,EAAE;AAChC,EAAE,OAAO,SAAS,GAAG,IAAI,GAAG,MAAM;AAClC;AACA,SAAS,WAAW,CAAC,SAAS,EAAE;AAChC,EAAE,OAAO,SAAS,GAAG,IAAI,GAAG,MAAM;AAClC;AACK,MAAC,UAAU,GAAG;AACd,MAAC,UAAU,GAAG;AACd,MAAC,WAAW,GAAG;AACf,MAAC,QAAQ,GAAG;AACZ,MAAC,GAAG,GAAG;AACP,MAAC,KAAK,GAAG;AACT,MAAC,MAAM,GAAG;AACV,MAAC,IAAI,GAAG;AACR,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG;AACX,MAAC,KAAK,GAAG;AACT,MAAC,GAAG,GAAG;AACP,MAAC,CAAC,GAAG;AACL,MAAC,CAAC,GAAG;AACL,MAAC,CAAC,GAAG;AACL,MAAC,CAAC,GAAG;;;;"}