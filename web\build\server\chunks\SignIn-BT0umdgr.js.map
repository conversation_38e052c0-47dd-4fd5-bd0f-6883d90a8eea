{"version": 3, "file": "SignIn-BT0umdgr.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/SignIn.js"], "sourcesContent": ["import { Y as fallback, O as copy_payload, P as assign_payload, N as bind_props, y as pop, w as push, R as attr, V as escape_html } from \"./index3.js\";\nimport { C as Checkbox } from \"./checkbox.js\";\nimport \"@simplewebauthn/browser\";\nimport \"./client.js\";\nimport \"./Toaster.svelte_svelte_type_style_lang.js\";\nfunction SignIn($$payload, $$props) {\n  push();\n  let onEmailPasswordLogin = $$props[\"onEmailPasswordLogin\"];\n  let isLoading = fallback($$props[\"isLoading\"], false);\n  let callbackUrl = fallback($$props[\"callbackUrl\"], \"/dashboard\");\n  let email = \"\";\n  let password = \"\";\n  let rememberMe = false;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<form class=\"grid gap-6\"><div class=\"flex flex-col items-center justify-center gap-4\">`;\n    {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]--> <button type=\"button\" class=\"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\"><img alt=\"Google\" src=\"/assets/svg/google.svg\" class=\"mr-2 h-4 w-4\"/> Continue with Google</button> <button type=\"button\" class=\"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex w-full items-center justify-center whitespace-nowrap rounded-md border p-4 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\"><img alt=\"LinkedIn\" src=\"/assets/svg/linkedin.svg\" class=\"mr-2 h-4 w-4\"/> Continue with LinkedIn</button></div> <div class=\"relative\"><div class=\"absolute inset-0 flex items-center\"><span class=\"border-border w-full border-2 border-t\"></span></div> <div class=\"relative z-10 flex justify-center text-xs uppercase\"><span class=\"text-muted-foreground bg-background px-2\">Or</span></div></div> <div class=\"grid gap-2\"><input id=\"email\" type=\"email\"${attr(\"value\", email)}${attr(\"disabled\", isLoading, true)} placeholder=\"<EMAIL>\" class=\"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm\"/></div> <div><input id=\"password\" type=\"password\"${attr(\"value\", password)} placeholder=\"Password\"${attr(\"disabled\", isLoading, true)} class=\"border-input placeholder:text-muted-foreground focus-visible:ring-ring flex h-9 w-full rounded-md border bg-transparent px-3 py-1 text-sm shadow-sm\"/></div> <div class=\"flex items-center justify-between\"><div class=\"flex items-center\">`;\n    Checkbox($$payload2, {\n      id: \"remember-me\",\n      name: \"remember-me\",\n      get checked() {\n        return rememberMe;\n      },\n      set checked($$value) {\n        rememberMe = $$value;\n        $$settled = false;\n      }\n    });\n    $$payload2.out += `<!----> <label for=\"remember-me\" class=\"text-foreground ml-2 block text-sm\">Remember me</label></div> <div class=\"text-sm\"><a href=\"/auth/forgot-password\" class=\"text-primary hover:text-primary/80 font-medium\">Forgot your password?</a></div></div> <button class=\"focus-visible:ring-ring border-input bg-background hover:bg-accent hover:text-accent-foreground inline-flex h-9 cursor-pointer items-center justify-center whitespace-nowrap rounded-md border px-4 py-2 text-sm font-medium shadow-sm transition-colors disabled:pointer-events-none disabled:opacity-50\" type=\"submit\"${attr(\"disabled\", isLoading, true)}>${escape_html(isLoading ? \"Signing in...\" : \"Sign in\")}</button></form>`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { onEmailPasswordLogin, isLoading, callbackUrl });\n  pop();\n}\nexport {\n  SignIn as S\n};\n"], "names": [], "mappings": ";;;;AAKA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,oBAAoB,GAAG,OAAO,CAAC,sBAAsB,CAAC;AAC5D,EAAE,IAAI,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC;AACvD,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,YAAY,CAAC;AAClE,EAAE,IAAI,KAAK,GAAG,EAAE;AAChB,EAAE,IAAI,QAAQ,GAAG,EAAE;AACnB,EAAE,IAAI,UAAU,GAAG,KAAK;AACxB,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,sFAAsF,CAAC;AAC9G,IAAI;AACJ,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,spCAAspC,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,6OAA6O,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,uBAAuB,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,mPAAmP,CAAC;AAC9xD,IAAI,QAAQ,CAAC,UAAU,EAAE;AACzB,MAAM,EAAE,EAAE,aAAa;AACvB,MAAM,IAAI,EAAE,aAAa;AACzB,MAAM,IAAI,OAAO,GAAG;AACpB,QAAQ,OAAO,UAAU;AACzB,OAAO;AACP,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,UAAU,GAAG,OAAO;AAC5B,QAAQ,SAAS,GAAG,KAAK;AACzB;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,+jBAA+jB,EAAE,IAAI,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,SAAS,GAAG,eAAe,GAAG,SAAS,CAAC,CAAC,gBAAgB,CAAC;AACnsB;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC;AACvE,EAAE,GAAG,EAAE;AACP;;;;"}