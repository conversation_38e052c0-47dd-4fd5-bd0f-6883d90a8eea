{"version": 3, "file": "hidden-input-1eDzjGOB.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/hidden-input.js"], "sourcesContent": ["import { w as push, M as spread_attributes, N as bind_props, y as pop } from \"./index3.js\";\nimport { e as srOnlyStylesString } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nfunction Hidden_input($$payload, $$props) {\n  push();\n  let {\n    value = void 0,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const mergedProps = mergeProps(restProps, {\n    \"aria-hidden\": \"true\",\n    tabindex: -1,\n    style: srOnlyStylesString\n  });\n  if (mergedProps.type === \"checkbox\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<input${spread_attributes({ ...mergedProps, value }, null)}/>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<input${spread_attributes({ value, ...mergedProps }, null)}/>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { value });\n  pop();\n}\nexport {\n  Hidden_input as H\n};\n"], "names": [], "mappings": ";;;;AAIA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE;AAC5C,IAAI,aAAa,EAAE,MAAM;AACzB,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE;AACvC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,KAAK,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;AACpF,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAAE,KAAK,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC;AACpF;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;;;;"}