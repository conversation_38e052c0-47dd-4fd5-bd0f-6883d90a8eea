// Test uploading via <PERSON>flare Worker instead of direct R2 SDK
import fs from 'fs';
import path from 'path';

const WORKER_UPLOAD_URL = 'https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev/upload';

async function testWorkerUpload() {
  console.log('🧪 Testing upload via Cloudflare Worker...');
  
  try {
    // Create a test image buffer
    const testImageData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==', 'base64');
    
    // Create form data
    const formData = new FormData();
    const blob = new Blob([testImageData], { type: 'image/png' });
    formData.append('file', blob, 'test-logo.png');
    formData.append('bucket', 'hirli-company-logos');
    formData.append('key', 'test-upload.png');
    
    console.log('📤 Uploading test image via worker...');
    
    const response = await fetch(WORKER_UPLOAD_URL, {
      method: 'POST',
      body: formData,
      headers: {
        'Authorization': 'Bearer test-token', // We'll need to implement auth
      }
    });
    
    console.log(`📊 Response status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ Upload successful!');
      console.log('📋 Result:', result);
      
      // Test if we can access the uploaded file
      const testUrl = 'https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev/logos/test-upload.png';
      console.log(`🔗 Testing access: ${testUrl}`);
      
      const accessResponse = await fetch(testUrl);
      console.log(`📊 Access response: ${accessResponse.status}`);
      
      if (accessResponse.ok) {
        console.log('🎉 File upload and access working via Worker!');
        return true;
      } else {
        console.log('❌ File uploaded but access failed');
        return false;
      }
    } else {
      const errorText = await response.text();
      console.log('❌ Upload failed');
      console.log('📋 Error:', errorText);
      return false;
    }
    
  } catch (error) {
    console.log('💥 Worker upload test failed:', error.message);
    
    // If worker upload endpoint doesn't exist, that's expected
    if (error.message.includes('fetch failed') || error.message.includes('404')) {
      console.log('💡 Worker upload endpoint not implemented yet - this is expected');
      console.log('🔧 We can implement this as an alternative to direct R2 SDK');
      return 'not_implemented';
    }
    
    return false;
  }
}

// Also test if we can at least read from the worker
async function testWorkerRead() {
  console.log('\n🔍 Testing worker read access...');
  
  try {
    const testUrl = 'https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev/logos/test-logo.png';
    const response = await fetch(testUrl);
    
    console.log(`📊 Worker read response: ${response.status}`);
    console.log(`📋 Content-Type: ${response.headers.get('content-type')}`);
    console.log(`🔒 CORS headers: ${response.headers.get('access-control-allow-origin')}`);
    
    if (response.ok) {
      console.log('✅ Worker read access working!');
      return true;
    } else {
      console.log('❌ Worker read access failed');
      return false;
    }
  } catch (error) {
    console.log('💥 Worker read test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Testing Cloudflare Worker as R2 alternative...\n');
  
  const readResult = await testWorkerRead();
  const uploadResult = await testWorkerUpload();
  
  console.log('\n📊 Test Results:');
  console.log(`   📖 Worker Read: ${readResult ? '✅ Working' : '❌ Failed'}`);
  console.log(`   📤 Worker Upload: ${uploadResult === true ? '✅ Working' : uploadResult === 'not_implemented' ? '🔧 Not Implemented' : '❌ Failed'}`);
  
  if (readResult && uploadResult === 'not_implemented') {
    console.log('\n💡 Recommendation: Implement worker upload endpoint as R2 SDK alternative');
    console.log('   This would bypass the SSL handshake issues entirely');
  }
}

runTests();
