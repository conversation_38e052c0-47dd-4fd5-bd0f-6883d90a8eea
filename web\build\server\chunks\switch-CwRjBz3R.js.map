{"version": 3, "file": "switch-CwRjBz3R.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/switch.js"], "sourcesContent": ["import { J as derived, w as push, Q as spread_props, y as pop, M as spread_attributes, N as bind_props, O as copy_payload, P as assign_payload } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport \"clsx\";\nimport { C as Context } from \"./context.js\";\nimport { i as ENTER, S as SPACE, u as getDataRequired, v as getDataChecked, e as getDataDisabled, w as getAriaRequired, x as getAriaChecked, s as getDisabled } from \"./kbd-constants.js\";\nimport { H as Hidden_input } from \"./hidden-input.js\";\nimport { u as useId } from \"./use-id.js\";\nimport { n as noop } from \"./noop.js\";\nconst SWITCH_ROOT_ATTR = \"data-switch-root\";\nconst SWITCH_THUMB_ATTR = \"data-switch-thumb\";\nclass SwitchRootState {\n  opts;\n  constructor(opts) {\n    this.opts = opts;\n    useRefById(opts);\n    this.onkeydown = this.onkeydown.bind(this);\n    this.onclick = this.onclick.bind(this);\n  }\n  #toggle() {\n    this.opts.checked.current = !this.opts.checked.current;\n  }\n  onkeydown(e) {\n    if (!(e.key === ENTER || e.key === SPACE) || this.opts.disabled.current) return;\n    e.preventDefault();\n    this.#toggle();\n  }\n  onclick(_) {\n    if (this.opts.disabled.current) return;\n    this.#toggle();\n  }\n  #sharedProps = derived(() => ({\n    \"data-disabled\": getDataDisabled(this.opts.disabled.current),\n    \"data-state\": getDataChecked(this.opts.checked.current),\n    \"data-required\": getDataRequired(this.opts.required.current)\n  }));\n  get sharedProps() {\n    return this.#sharedProps();\n  }\n  set sharedProps($$value) {\n    return this.#sharedProps($$value);\n  }\n  #snippetProps = derived(() => ({ checked: this.opts.checked.current }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n  #props = derived(() => ({\n    ...this.sharedProps,\n    id: this.opts.id.current,\n    role: \"switch\",\n    disabled: getDisabled(this.opts.disabled.current),\n    \"aria-checked\": getAriaChecked(this.opts.checked.current, false),\n    \"aria-required\": getAriaRequired(this.opts.required.current),\n    [SWITCH_ROOT_ATTR]: \"\",\n    //\n    onclick: this.onclick,\n    onkeydown: this.onkeydown\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass SwitchInputState {\n  root;\n  #shouldRender = derived(() => this.root.opts.name.current !== void 0);\n  get shouldRender() {\n    return this.#shouldRender();\n  }\n  set shouldRender($$value) {\n    return this.#shouldRender($$value);\n  }\n  constructor(root) {\n    this.root = root;\n  }\n  #props = derived(() => ({\n    type: \"checkbox\",\n    name: this.root.opts.name.current,\n    value: this.root.opts.value.current,\n    checked: this.root.opts.checked.current,\n    disabled: this.root.opts.disabled.current,\n    required: this.root.opts.required.current\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass SwitchThumbState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n  }\n  #snippetProps = derived(() => ({ checked: this.root.opts.checked.current }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n  #props = derived(() => ({\n    ...this.root.sharedProps,\n    id: this.opts.id.current,\n    [SWITCH_THUMB_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst SwitchRootContext = new Context(\"Switch.Root\");\nfunction useSwitchRoot(props) {\n  return SwitchRootContext.set(new SwitchRootState(props));\n}\nfunction useSwitchInput() {\n  return new SwitchInputState(SwitchRootContext.get());\n}\nfunction useSwitchThumb(props) {\n  return new SwitchThumbState(props, SwitchRootContext.get());\n}\nfunction Switch_input($$payload, $$props) {\n  push();\n  const inputState = useSwitchInput();\n  if (inputState.shouldRender) {\n    $$payload.out += \"<!--[-->\";\n    Hidden_input($$payload, spread_props([inputState.props]));\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]-->`;\n  pop();\n}\nfunction Switch$1($$payload, $$props) {\n  push();\n  let {\n    child,\n    children,\n    ref = null,\n    id = useId(),\n    disabled = false,\n    required = false,\n    checked = false,\n    value = \"on\",\n    name = void 0,\n    type = \"button\",\n    onCheckedChange = noop,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const rootState = useSwitchRoot({\n    checked: box.with(() => checked, (v) => {\n      checked = v;\n      onCheckedChange?.(v);\n    }),\n    disabled: box.with(() => disabled ?? false),\n    required: box.with(() => required),\n    value: box.with(() => value),\n    name: box.with(() => name),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, rootState.props, { type });\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps, ...rootState.snippetProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<button${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload, rootState.snippetProps);\n    $$payload.out += `<!----></button>`;\n  }\n  $$payload.out += `<!--]--> `;\n  Switch_input($$payload);\n  $$payload.out += `<!---->`;\n  bind_props($$props, { ref, checked });\n  pop();\n}\nfunction Switch_thumb($$payload, $$props) {\n  push();\n  let {\n    child,\n    children,\n    ref = null,\n    id = useId(),\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const thumbState = useSwitchThumb({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, thumbState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, {\n      props: mergedProps,\n      ...thumbState.snippetProps\n    });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload, thumbState.snippetProps);\n    $$payload.out += `<!----></span>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Switch($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    checked = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Switch$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"switch\",\n        class: cn(\"data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 shadow-xs peer inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent outline-none transition-all focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        },\n        get checked() {\n          return checked;\n        },\n        set checked($$value) {\n          checked = $$value;\n          $$settled = false;\n        },\n        children: ($$payload3) => {\n          $$payload3.out += `<!---->`;\n          Switch_thumb($$payload3, {\n            \"data-slot\": \"switch-thumb\",\n            class: cn(\"bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0\")\n          });\n          $$payload3.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref, checked });\n  pop();\n}\nexport {\n  Switch as S\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAWA,MAAM,gBAAgB,GAAG,kBAAkB;AAC3C,MAAM,iBAAiB,GAAG,mBAAmB;AAC7C,MAAM,eAAe,CAAC;AACtB,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;AAC1D;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,EAAE,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AAC7E,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB;AACA,EAAE,YAAY,GAAG,OAAO,CAAC,OAAO;AAChC,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAChE,IAAI,YAAY,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AAC3D,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AAC/D,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;AAC9B;AACA,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACrC;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;AACzE,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,GAAG,IAAI,CAAC,WAAW;AACvB,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACrD,IAAI,cAAc,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC;AACpE,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAChE,IAAI,CAAC,gBAAgB,GAAG,EAAE;AAC1B;AACA,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,SAAS,EAAE,IAAI,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,MAAM,CAAC;AACvE,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACrC,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACvC,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;AAC3C,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AAC7C,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACtC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;AAC9E,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW;AAC5B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,CAAC,iBAAiB,GAAG;AACzB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,iBAAiB,GAAG,IAAI,OAAO,CAAC,aAAa,CAAC;AACpD,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,EAAE,OAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC1D;AACA,SAAS,cAAc,GAAG;AAC1B,EAAE,OAAO,IAAI,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;AACtD;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC7D;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,UAAU,GAAG,cAAc,EAAE;AACrC,EAAE,IAAI,UAAU,CAAC,YAAY,EAAE;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7D,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,OAAO,GAAG,KAAK;AACnB,IAAI,KAAK,GAAG,IAAI;AAChB,IAAI,IAAI,GAAG,MAAM;AACjB,IAAI,IAAI,GAAG,QAAQ;AACnB,IAAI,eAAe,GAAG,IAAI;AAC1B,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,aAAa,CAAC;AAClC,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC,KAAK;AAC5C,MAAM,OAAO,GAAG,CAAC;AACjB,MAAM,eAAe,GAAG,CAAC,CAAC;AAC1B,KAAK,CAAC;AACN,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,CAAC;AAC/C,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9B,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC;AACtE,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;AACvE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7E,IAAI,QAAQ,GAAG,SAAS,EAAE,SAAS,CAAC,YAAY,CAAC;AACjD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACvC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,YAAY,CAAC,SAAS,CAAC;AACzB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;AACvC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC;AACpC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC;AAC7D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE;AACrB,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,GAAG,UAAU,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,QAAQ,GAAG,SAAS,EAAE,UAAU,CAAC,YAAY,CAAC;AAClD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO,GAAG,KAAK;AACnB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC;AACtC,MAAM;AACN,QAAQ,WAAW,EAAE,QAAQ;AAC7B,QAAQ,KAAK,EAAE,EAAE,CAAC,2WAA2W,EAAE,SAAS;AACxY,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B,SAAS;AACT,QAAQ,IAAI,OAAO,GAAG;AACtB,UAAU,OAAO,OAAO;AACxB,SAAS;AACT,QAAQ,IAAI,OAAO,CAAC,OAAO,EAAE;AAC7B,UAAU,OAAO,GAAG,OAAO;AAC3B,UAAU,SAAS,GAAG,KAAK;AAC3B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,WAAW,EAAE,cAAc;AACvC,YAAY,KAAK,EAAE,EAAE,CAAC,0QAA0Q;AAChS,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;AACvC,EAAE,GAAG,EAAE;AACP;;;;"}