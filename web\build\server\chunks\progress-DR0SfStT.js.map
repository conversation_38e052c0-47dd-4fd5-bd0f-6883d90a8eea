{"version": 3, "file": "progress-DR0SfStT.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/progress.js"], "sourcesContent": ["import { J as derived, w as push, M as spread_attributes, N as bind_props, y as pop, O as copy_payload, P as assign_payload, Q as spread_props, $ as attr_style, W as stringify } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport \"clsx\";\nimport { u as useId } from \"./use-id.js\";\nconst ROOT_ATTR = \"data-progress-root\";\nclass ProgressRootState {\n  opts;\n  constructor(opts) {\n    this.opts = opts;\n    useRefById(opts);\n  }\n  #props = derived(() => ({\n    role: \"progressbar\",\n    value: this.opts.value.current,\n    \"aria-valuemin\": this.opts.min.current,\n    \"aria-valuemax\": this.opts.max.current,\n    \"aria-valuenow\": this.opts.value.current === null ? void 0 : this.opts.value.current,\n    \"data-value\": this.opts.value.current === null ? void 0 : this.opts.value.current,\n    \"data-state\": getProgressDataState(this.opts.value.current, this.opts.max.current),\n    \"data-max\": this.opts.max.current,\n    \"data-min\": this.opts.min.current,\n    \"data-indeterminate\": this.opts.value.current === null ? \"\" : void 0,\n    [ROOT_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nfunction getProgressDataState(value, max) {\n  if (value === null) return \"indeterminate\";\n  return value === max ? \"loaded\" : \"loading\";\n}\nfunction useProgressRootState(props) {\n  return new ProgressRootState(props);\n}\nfunction Progress$1($$payload, $$props) {\n  push();\n  let {\n    child,\n    children,\n    value = 0,\n    max = 100,\n    min = 0,\n    id = useId(),\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const rootState = useProgressRootState({\n    value: box.with(() => value),\n    max: box.with(() => max),\n    min: box.with(() => min),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, rootState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Progress($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    max = 100,\n    value,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Progress$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"progress\",\n        class: cn(\"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\", className),\n        value,\n        max\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        },\n        children: ($$payload3) => {\n          $$payload3.out += `<div data-slot=\"progress-indicator\" class=\"bg-primary h-full w-full flex-1 transition-all\"${attr_style(`transform: translateX(-${stringify(100 - 100 * (value ?? 0) / (max ?? 1))}%)`)}></div>`;\n        },\n        $$slots: { default: true }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Progress as P\n};\n"], "names": [], "mappings": ";;;;;;;AAOA,MAAM,SAAS,GAAG,oBAAoB;AACtC,MAAM,iBAAiB,CAAC;AACxB,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAClC,IAAI,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC1C,IAAI,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC1C,IAAI,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACxF,IAAI,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACrF,IAAI,YAAY,EAAE,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;AACtF,IAAI,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,MAAM;AACxE,IAAI,CAAC,SAAS,GAAG;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,SAAS,oBAAoB,CAAC,KAAK,EAAE,GAAG,EAAE;AAC1C,EAAE,IAAI,KAAK,KAAK,IAAI,EAAE,OAAO,eAAe;AAC5C,EAAE,OAAO,KAAK,KAAK,GAAG,GAAG,QAAQ,GAAG,SAAS;AAC7C;AACA,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACrC,EAAE,OAAO,IAAI,iBAAiB,CAAC,KAAK,CAAC;AACrC;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,GAAG,GAAG,GAAG;AACb,IAAI,GAAG,GAAG,CAAC;AACX,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,oBAAoB,CAAC;AACzC,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,GAAG,GAAG,GAAG;AACb,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC;AACxC,MAAM;AACN,QAAQ,WAAW,EAAE,UAAU;AAC/B,QAAQ,KAAK,EAAE,EAAE,CAAC,gEAAgE,EAAE,SAAS,CAAC;AAC9F,QAAQ,KAAK;AACb,QAAQ;AACR,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,0FAA0F,EAAE,UAAU,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,GAAG,GAAG,GAAG,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;AAC5N,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}