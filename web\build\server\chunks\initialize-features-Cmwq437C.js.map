{"version": 3, "file": "initialize-features-Cmwq437C.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/initialize-features.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nasync function initializeFeatureData() {\n  try {\n    const featureCount = await prisma.feature.count();\n    const limitCount = await prisma.featureLimit.count();\n    if (featureCount === 0) {\n      return {\n        success: true,\n        features: 0,\n        limits: 0,\n        message: \"No features found in the database. Features should be created via the API.\"\n      };\n    }\n    return {\n      success: true,\n      features: featureCount,\n      limits: limitCount,\n      message: `Database contains ${featureCount} features and ${limitCount} limits.`\n    };\n  } catch (error) {\n    return {\n      success: false,\n      features: 0,\n      limits: 0,\n      error: error.message ?? \"Unknown error\"\n    };\n  }\n}\nexport {\n  initializeFeatureData as i\n};\n"], "names": [], "mappings": ";;AACA,eAAe,qBAAqB,GAAG;AACvC,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;AACrD,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,KAAK,EAAE;AACxD,IAAI,IAAI,YAAY,KAAK,CAAC,EAAE;AAC5B,MAAM,OAAO;AACb,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,CAAC;AACnB,QAAQ,MAAM,EAAE,CAAC;AACjB,QAAQ,OAAO,EAAE;AACjB,OAAO;AACP;AACA,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,CAAC,kBAAkB,EAAE,YAAY,CAAC,cAAc,EAAE,UAAU,CAAC,QAAQ;AACpF,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,QAAQ,EAAE,CAAC;AACjB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,KAAK,EAAE,KAAK,CAAC,OAAO,IAAI;AAC9B,KAAK;AACL;AACA;;;;"}