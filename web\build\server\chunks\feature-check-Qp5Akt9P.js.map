{"version": 3, "file": "feature-check-Qp5Akt9P.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/feature-check.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nimport { F as FeatureAccessLevel } from \"./features.js\";\nimport { g as getFeatureById } from \"./registry.js\";\nasync function hasFeatureAccess(userId, featureId) {\n  try {\n    console.log(`Checking feature access for user ${userId} and feature ${featureId}`);\n    const feature = getFeatureById(featureId);\n    if (!feature) {\n      console.warn(`Feature not found: ${featureId}`);\n      return false;\n    }\n    console.log(`Feature found: ${feature.name}`);\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      include: {\n        subscriptions: {\n          orderBy: { createdAt: \"desc\" },\n          take: 1,\n          include: {\n            plan: {\n              include: {\n                features: true\n              }\n            }\n          }\n        }\n      }\n    });\n    if (!user) {\n      console.warn(`User not found: ${userId}`);\n      return false;\n    }\n    console.log(`User found: ${user.email}, role: ${user.role}`);\n    const currentPlan = user.subscriptions[0]?.plan;\n    if (!currentPlan) {\n      const hasAccess2 = user.role === \"free\" && isFeatureInFreePlan(featureId);\n      console.log(`User has no plan. Free role check: ${hasAccess2}`);\n      return hasAccess2;\n    }\n    console.log(`User has plan: ${currentPlan.name}`);\n    const planFeature = currentPlan.features.find((pf) => pf.featureId === featureId);\n    if (!planFeature) {\n      console.log(`Feature not found in plan ${currentPlan.name}`);\n      return false;\n    }\n    console.log(`Feature found in plan with access level: ${planFeature.accessLevel}`);\n    const hasAccess = planFeature.accessLevel !== FeatureAccessLevel.NotIncluded;\n    console.log(`Feature access result: ${hasAccess}`);\n    return hasAccess;\n  } catch (error) {\n    console.error(\"Error checking feature access:\", error);\n    return false;\n  }\n}\nfunction isFeatureInFreePlan(featureId) {\n  const freeFeatures = [\n    \"dashboard\",\n    \"profile\",\n    \"job_search_profiles\",\n    \"job_search\"\n    // Add any other core features that should be available to all users\n  ];\n  console.log(`Checking if feature ${featureId} is in free plan`);\n  return freeFeatures.includes(featureId);\n}\nasync function getFeatureAccessDetails(userId, featureId) {\n  try {\n    const feature = getFeatureById(featureId);\n    if (!feature) {\n      console.warn(`Feature not found: ${featureId}`);\n      return { hasAccess: false };\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      include: {\n        subscriptions: {\n          orderBy: { createdAt: \"desc\" },\n          take: 1,\n          include: {\n            plan: {\n              include: {\n                features: {\n                  include: {\n                    limits: true\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    });\n    if (!user) {\n      console.warn(`User not found: ${userId}`);\n      return { hasAccess: false };\n    }\n    const currentPlan = user.subscriptions[0]?.plan;\n    if (!currentPlan) {\n      const hasAccess2 = user.role === \"free\" && isFeatureInFreePlan(featureId);\n      return {\n        hasAccess: hasAccess2,\n        accessLevel: hasAccess2 ? FeatureAccessLevel.Included : FeatureAccessLevel.NotIncluded,\n        feature\n      };\n    }\n    const planFeature = currentPlan.features.find((pf) => pf.featureId === featureId);\n    if (!planFeature) {\n      return { hasAccess: false, feature, plan: currentPlan };\n    }\n    const hasAccess = planFeature.accessLevel !== FeatureAccessLevel.NotIncluded;\n    return {\n      hasAccess,\n      accessLevel: planFeature.accessLevel,\n      feature,\n      planFeature,\n      plan: currentPlan,\n      limits: planFeature.limits\n    };\n  } catch (error) {\n    console.error(\"Error getting feature access details:\", error);\n    return { hasAccess: false };\n  }\n}\nasync function hasReachedLimit(userId, featureId, limitId) {\n  try {\n    const accessDetails = await getFeatureAccessDetails(userId, featureId);\n    if (!accessDetails.hasAccess) {\n      return true;\n    }\n    if (accessDetails.accessLevel === FeatureAccessLevel.Unlimited) {\n      return false;\n    }\n    if (accessDetails.accessLevel !== FeatureAccessLevel.Limited) {\n      return false;\n    }\n    const limitValue = accessDetails.limits?.find((l) => l.limitId === limitId);\n    if (!limitValue) {\n      return true;\n    }\n    if (limitValue.value === \"unlimited\") {\n      return false;\n    }\n    const usage = await prisma.featureUsage.findUnique({\n      where: {\n        userId_featureId_limitId_period: {\n          userId,\n          featureId,\n          limitId,\n          period: getCurrentPeriod()\n        }\n      }\n    });\n    if (!usage) {\n      return false;\n    }\n    return usage.used >= parseInt(limitValue.value, 10);\n  } catch (error) {\n    console.error(\"Error checking if user has reached limit:\", error);\n    return true;\n  }\n}\nfunction getCurrentPeriod() {\n  const now = /* @__PURE__ */ new Date();\n  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, \"0\")}`;\n}\nexport {\n  hasFeatureAccess as a,\n  getFeatureAccessDetails as g,\n  hasReachedLimit as h\n};\n"], "names": [], "mappings": ";;;;AAGA,eAAe,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE;AACnD,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,MAAM,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;AACtF,IAAI,MAAM,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC;AAC7C,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC,CAAC;AACrD,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC;AAC/C,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAChE,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;AACnD,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC;AAC/E,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,mCAAmC,EAAE,UAAU,CAAC,CAAC,CAAC;AACrE,MAAM,OAAO,UAAU;AACvB;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AACrD,IAAI,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,KAAK,SAAS,CAAC;AACrF,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC;AAClE,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,yCAAyC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,CAAC;AACtF,IAAI,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,WAAW;AAChF,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC,CAAC;AACtD,IAAI,OAAO,SAAS;AACpB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,KAAK;AAChB;AACA;AACA,SAAS,mBAAmB,CAAC,SAAS,EAAE;AACxC,EAAE,MAAM,YAAY,GAAG;AACvB,IAAI,WAAW;AACf,IAAI,SAAS;AACb,IAAI,qBAAqB;AACzB,IAAI;AACJ;AACA,GAAG;AACH,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,CAAC,gBAAgB,CAAC,CAAC;AACjE,EAAE,OAAO,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC;AACzC;AACA,eAAe,uBAAuB,CAAC,MAAM,EAAE,SAAS,EAAE;AAC1D,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC;AAC7C,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC,CAAC;AACrD,MAAM,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;AACjC;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,OAAO,EAAE;AAC3B,oBAAoB,MAAM,EAAE;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC;AAC/C,MAAM,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;AACjC;AACA,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;AACnD,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,mBAAmB,CAAC,SAAS,CAAC;AAC/E,MAAM,OAAO;AACb,QAAQ,SAAS,EAAE,UAAU;AAC7B,QAAQ,WAAW,EAAE,UAAU,GAAG,kBAAkB,CAAC,QAAQ,GAAG,kBAAkB,CAAC,WAAW;AAC9F,QAAQ;AACR,OAAO;AACP;AACA,IAAI,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,KAAK,SAAS,CAAC;AACrF,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE;AAC7D;AACA,IAAI,MAAM,SAAS,GAAG,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,WAAW;AAChF,IAAI,OAAO;AACX,MAAM,SAAS;AACf,MAAM,WAAW,EAAE,WAAW,CAAC,WAAW;AAC1C,MAAM,OAAO;AACb,MAAM,WAAW;AACjB,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,MAAM,EAAE,WAAW,CAAC;AAC1B,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE;AAC/B;AACA;AACA,eAAe,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE;AAC3D,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC;AAC1E,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;AAClC,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,IAAI,aAAa,CAAC,WAAW,KAAK,kBAAkB,CAAC,SAAS,EAAE;AACpE,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,IAAI,aAAa,CAAC,WAAW,KAAK,kBAAkB,CAAC,OAAO,EAAE;AAClE,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC;AAC/E,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,IAAI,UAAU,CAAC,KAAK,KAAK,WAAW,EAAE;AAC1C,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AACvD,MAAM,KAAK,EAAE;AACb,QAAQ,+BAA+B,EAAE;AACzC,UAAU,MAAM;AAChB,UAAU,SAAS;AACnB,UAAU,OAAO;AACjB,UAAU,MAAM,EAAE,gBAAgB;AAClC;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,OAAO,KAAK,CAAC,IAAI,IAAI,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,CAAC;AACvD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC;AACrE,IAAI,OAAO,IAAI;AACf;AACA;AACA,SAAS,gBAAgB,GAAG;AAC5B,EAAE,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AACxC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9E;;;;"}