import { r as redirect } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { v as verifySessionToken } from './auth-BPad-IlN.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { d as designFormSchema, a as designDefaultValues, r as resumeFormSchema } from './buildResume-ByCXwpI3.js';
import '@prisma/client';
import 'jsonwebtoken';
import 'ua-parser-js';
import './index4-HpJcNJHQ.js';
import './false-CRHihH2U.js';
import './constants-BaiUsPxc.js';
import './types-D78SXuvm.js';
import './_commonjsHelpers-BFTU3MAI.js';

const load = async ({ params, cookies, locals }) => {
  const token = cookies.get("auth_token");
  const user = token && verifySessionToken(token);
  if (!user) throw redirect(302, "/auth/sign-in");
  const id = params.id;
  console.log("Looking up with ID:", id);
  let resume = await prisma.resume.findUnique({
    where: { id },
    include: {
      document: {
        include: {
          profile: true
        }
      }
    }
  });
  if (!resume) {
    console.log("Resume not found by ID, trying to find by document ID");
    const document = await prisma.document.findUnique({
      where: { id },
      include: {
        profile: true
      }
    });
    if (document) {
      console.log("Document found, looking for associated resume");
      resume = await prisma.resume.findUnique({
        where: { documentId: document.id },
        include: {
          document: {
            include: {
              profile: true
            }
          }
        }
      });
    }
  }
  console.log("Resume found:", resume ? "Yes" : "No");
  if (!resume) {
    console.log("No resume found for ID:", id);
    throw redirect(302, "/dashboard/documents");
  }
  console.log("Resume document userId:", resume.document.userId);
  console.log("Current user id:", user.id);
  const design = await superValidate(designDefaultValues, zod(designFormSchema));
  let initialResumeData = resume.parsedData || {};
  console.log("Initial resume data:", JSON.stringify(initialResumeData));
  const defaultData = {
    header: {
      name: "",
      email: "",
      phone: ""
    },
    summary: {
      content: ""
    },
    experience: [],
    education: [],
    skills: [],
    projects: [],
    certifications: []
  };
  let needsUpdate = false;
  if (!initialResumeData.header) {
    console.log("Resume data missing header, using default");
    initialResumeData.header = defaultData.header;
    needsUpdate = true;
  }
  if (!initialResumeData.summary) {
    console.log("Resume data missing summary, using default");
    initialResumeData.summary = defaultData.summary;
    needsUpdate = true;
  }
  if (!initialResumeData.experience) {
    console.log("Resume data missing experience, using default");
    initialResumeData.experience = defaultData.experience;
    needsUpdate = true;
  }
  if (!initialResumeData.education) {
    console.log("Resume data missing education, using default");
    initialResumeData.education = defaultData.education;
    needsUpdate = true;
  }
  if (!initialResumeData.skills) {
    console.log("Resume data missing skills, using default");
    initialResumeData.skills = defaultData.skills;
    needsUpdate = true;
  }
  if (!initialResumeData.projects) {
    console.log("Resume data missing projects, using default");
    initialResumeData.projects = defaultData.projects;
    needsUpdate = true;
  }
  if (!initialResumeData.certifications) {
    console.log("Resume data missing certifications, using default");
    initialResumeData.certifications = defaultData.certifications;
    needsUpdate = true;
  }
  if (needsUpdate) {
    console.log("Updating resume with default structure");
    await prisma.resume.update({
      where: { id: resume.id },
      data: {
        parsedData: initialResumeData
      }
    });
  }
  const resumeForm = await superValidate(initialResumeData, zod(resumeFormSchema));
  return {
    form: {
      resume: resumeForm,
      design
    },
    resumeData: resume,
    user
  };
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 31;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CrPBXgQS.js')).default;
const server_id = "src/routes/dashboard/builder/[id]/+page.server.ts";
const imports = ["_app/immutable/nodes/31.CrBnkojk.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/B3MJtjra.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/De0hHRMm.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/CPe_16wQ.js","_app/immutable/chunks/FAbXdqfL.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/qwsZpUIl.js","_app/immutable/chunks/BuYRPDDz.js","_app/immutable/chunks/BNEH2jqx.js","_app/immutable/chunks/C3y1xd2Y.js","_app/immutable/chunks/BoNCRmBc.js","_app/immutable/chunks/DRGimm5x.js","_app/immutable/chunks/CrpvsheG.js","_app/immutable/chunks/lZwfPN85.js","_app/immutable/chunks/VNuMAkuB.js","_app/immutable/chunks/BG1dFdGG.js","_app/immutable/chunks/Z9Zpt0fH.js","_app/immutable/chunks/CrHU05dq.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/7AwcL9ec.js","_app/immutable/chunks/6UJoWgvL.js","_app/immutable/chunks/DYwWIJ9y.js","_app/immutable/chunks/Dc4vaUpe.js","_app/immutable/chunks/D9My4UUX.js","_app/immutable/chunks/CWmzcjye.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/0ykhD7u6.js","_app/immutable/chunks/FeejBSkx.js","_app/immutable/chunks/C8B1VUaq.js","_app/immutable/chunks/WD4kvFhR.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/hrXlVaSN.js","_app/immutable/chunks/BHzYYMdu.js","_app/immutable/chunks/8b74MdfD.js","_app/immutable/chunks/Z6UAQTuv.js","_app/immutable/chunks/Dz4exfp3.js","_app/immutable/chunks/DumgozFE.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/scroll-area.bHHIbcsu.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=31-CtmL687L.js.map
