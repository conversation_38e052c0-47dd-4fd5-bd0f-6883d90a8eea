{"version": 3, "file": "42-Djz0DhXY.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/resumes/_id_/optimize/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/42.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../../chunks/prisma.js\";\nimport { v as verifySessionToken } from \"../../../../../../chunks/auth.js\";\nimport { r as redirect } from \"../../../../../../chunks/index.js\";\nconst load = async ({ params, cookies, locals }) => {\n  const token = cookies.get(\"auth_token\");\n  const user = token && verifySessionToken(token);\n  if (user) locals.user = user;\n  if (!locals.user) throw redirect(302, \"/auth/sign-in\");\n  const resume = await prisma.resume.findFirst({\n    where: {\n      id: params.id,\n      profile: {\n        OR: [{ userId: user.id }, { team: { members: { some: { userId: user.id } } } }]\n      }\n    },\n    include: { profile: true }\n  });\n  if (!resume) throw redirect(302, \"/dashboard/resume\");\n  const optimization = await prisma.resumeOptimizationResult.findUnique({\n    where: { resumeId: resume.id }\n  });\n  return { resume, optimization };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/resumes/_id_/optimize/_page.server.ts.js';\n\nexport const index = 42;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/resumes/_id_/optimize/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/resumes/[id]/optimize/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/42.DH2y0Lvi.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;;;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;AACpD,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AACzC,EAAE,MAAM,IAAI,GAAG,KAAK,IAAI,kBAAkB,CAAC,KAAK,CAAC;AACjD,EAAE,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG,IAAI;AAC9B,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxD,EAAE,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;AAC/C,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE;AACnB,MAAM,OAAO,EAAE;AACf,QAAQ,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;AACtF;AACA,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,QAAQ,CAAC,GAAG,EAAE,mBAAmB,CAAC;AACvD,EAAE,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,wBAAwB,CAAC,UAAU,CAAC;AACxE,IAAI,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE;AAChC,GAAG,CAAC;AACJ,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE;AACjC,CAAC;;;;;;;ACpBW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAkE,CAAC,EAAE;AAEhI,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACrpB,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}