{"version": 3, "file": "HelpArticleCard-CzuyIqVY.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/HelpArticleCard.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot, W as stringify, V as escape_html, R as attr, U as ensure_array_like, y as pop, w as push } from \"./index3.js\";\nimport { C as Card } from \"./card.js\";\nimport { C as Card_description } from \"./card-description.js\";\nimport { C as Card_footer } from \"./card-footer.js\";\nimport { C as Card_header } from \"./card-header.js\";\nimport { C as Card_title } from \"./card-title.js\";\nimport { B as Badge } from \"./badge.js\";\nimport { formatDistanceToNow } from \"date-fns\";\nimport { I as Icon } from \"./Icon.js\";\nimport { F as File_text } from \"./file-text.js\";\nimport { C as Credit_card } from \"./credit-card.js\";\nimport { S as Shield } from \"./shield.js\";\nfunction Book_open($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\"path\", { \"d\": \"M12 7v14\" }],\n    [\n      \"path\",\n      {\n        \"d\": \"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z\"\n      }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"book-open\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nfunction HelpArticleCard($$payload, $$props) {\n  push();\n  let { article, className = \"\" } = $$props;\n  let updatedDate = formatDistanceToNow(new Date(article.updatedAt), { addSuffix: true });\n  $$payload.out += `<!---->`;\n  Card($$payload, {\n    class: `h-full overflow-hidden ${stringify(className)}`,\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->`;\n      Card_header($$payload2, {\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"flex items-center gap-2\">`;\n          Badge($$payload3, {\n            variant: \"outline\",\n            class: \"flex items-center gap-1\",\n            children: ($$payload4) => {\n              if (article.category.icon === \"BookOpen\") {\n                $$payload4.out += \"<!--[-->\";\n                $$payload4.out += `<!---->`;\n                Book_open($$payload4, { class: \"h-3 w-3\" });\n                $$payload4.out += `<!---->`;\n              } else if (article.category.icon === \"FileText\") {\n                $$payload4.out += \"<!--[1-->\";\n                $$payload4.out += `<!---->`;\n                File_text($$payload4, { class: \"h-3 w-3\" });\n                $$payload4.out += `<!---->`;\n              } else if (article.category.icon === \"CreditCard\") {\n                $$payload4.out += \"<!--[2-->\";\n                $$payload4.out += `<!---->`;\n                Credit_card($$payload4, { class: \"h-3 w-3\" });\n                $$payload4.out += `<!---->`;\n              } else if (article.category.icon === \"Shield\") {\n                $$payload4.out += \"<!--[3-->\";\n                $$payload4.out += `<!---->`;\n                Shield($$payload4, { class: \"h-3 w-3\" });\n                $$payload4.out += `<!---->`;\n              } else {\n                $$payload4.out += \"<!--[!-->\";\n                $$payload4.out += `<!---->`;\n                File_text($$payload4, { class: \"h-3 w-3\" });\n                $$payload4.out += `<!---->`;\n              }\n              $$payload4.out += `<!--]--> <span>${escape_html(article.category.name)}</span>`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> <span class=\"text-muted-foreground text-xs\">Updated ${escape_html(updatedDate)}</span></div> <!---->`;\n          Card_title($$payload3, {\n            class: \"mt-2\",\n            children: ($$payload4) => {\n              $$payload4.out += `<a${attr(\"href\", `/help/${stringify(article.slug)}`)} class=\"hover:text-primary hover:underline\">${escape_html(article.title)}</a>`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> `;\n          if (article.excerpt) {\n            $$payload3.out += \"<!--[-->\";\n            $$payload3.out += `<!---->`;\n            Card_description($$payload3, {\n              class: \"line-clamp-2\",\n              children: ($$payload4) => {\n                $$payload4.out += `<!---->${escape_html(article.excerpt)}`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload3.out += `<!---->`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]-->`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> <!---->`;\n      Card_footer($$payload2, {\n        children: ($$payload3) => {\n          const each_array = ensure_array_like((article.tags || []).slice(0, 3));\n          $$payload3.out += `<div class=\"flex flex-wrap gap-2\"><!--[-->`;\n          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n            let tag = each_array[$$index];\n            Badge($$payload3, {\n              variant: \"secondary\",\n              class: \"text-xs\",\n              children: ($$payload4) => {\n                $$payload4.out += `<!---->${escape_html(tag.name)}`;\n              },\n              $$slots: { default: true }\n            });\n          }\n          $$payload3.out += `<!--]--> `;\n          if ((article.tags || []).length > 3) {\n            $$payload3.out += \"<!--[-->\";\n            Badge($$payload3, {\n              variant: \"secondary\",\n              class: \"text-xs\",\n              children: ($$payload4) => {\n                $$payload4.out += `<!---->+${escape_html((article.tags || []).length - 3)} more`;\n              },\n              $$slots: { default: true }\n            });\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--></div>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!---->`;\n  pop();\n}\nexport {\n  Book_open as B,\n  HelpArticleCard as H\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAYA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;AACjC,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,GAAG,EAAE;AACb;AACA;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE;AACzB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;AACA,SAAS,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,OAAO;AAC3C,EAAE,IAAI,WAAW,GAAG,mBAAmB,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;AACzF,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,KAAK,EAAE,CAAC,uBAAuB,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3D,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,MAAM,WAAW,CAAC,UAAU,EAAE;AAC9B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,qCAAqC,CAAC;AACnE,UAAU,KAAK,CAAC,UAAU,EAAE;AAC5B,YAAY,OAAO,EAAE,SAAS;AAC9B,YAAY,KAAK,EAAE,yBAAyB;AAC5C,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE;AACxD,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,gBAAgB,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC3D,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE;AAC/D,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,gBAAgB,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC3D,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,YAAY,EAAE;AACjE,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,gBAAgB,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC7D,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC7D,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,gBAAgB,MAAM,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACxD,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,gBAAgB,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC3D,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;AAC7F,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,4DAA4D,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,qBAAqB,CAAC;AAC1I,UAAU,UAAU,CAAC,UAAU,EAAE;AACjC,YAAY,KAAK,EAAE,MAAM;AACzB,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACpK,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,IAAI,OAAO,CAAC,OAAO,EAAE;AAC/B,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,YAAY,gBAAgB,CAAC,UAAU,EAAE;AACzC,cAAc,KAAK,EAAE,cAAc;AACnC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;AAC1E,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACzC,MAAM,WAAW,CAAC,UAAU,EAAE;AAC9B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,MAAM,UAAU,GAAG,iBAAiB,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAChF,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,0CAA0C,CAAC;AACxE,UAAU,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC7F,YAAY,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACzC,YAAY,KAAK,CAAC,UAAU,EAAE;AAC9B,cAAc,OAAO,EAAE,WAAW;AAClC,cAAc,KAAK,EAAE,SAAS;AAC9B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACnE,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,UAAU,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,MAAM,GAAG,CAAC,EAAE;AAC/C,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,KAAK,CAAC,UAAU,EAAE;AAC9B,cAAc,OAAO,EAAE,WAAW;AAClC,cAAc,KAAK,EAAE,SAAS;AAC9B,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;AAChG,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC5C,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;;;;"}