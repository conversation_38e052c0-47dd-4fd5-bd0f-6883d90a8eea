{"version": 3, "file": "circle-check-big-DBrIQZ7A.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/circle-check-big.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Circle_check_big($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\n      \"path\",\n      { \"d\": \"M21.801 10A10 10 0 1 1 17 3.335\" }\n    ],\n    [\"path\", { \"d\": \"m9 11 3 3L22 4\" }]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"circle-check-big\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Circle_check_big as C\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM,EAAE,GAAG,EAAE,iCAAiC;AAC9C,KAAK;AACL,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,gBAAgB,EAAE;AACtC,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE;AAChC,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}