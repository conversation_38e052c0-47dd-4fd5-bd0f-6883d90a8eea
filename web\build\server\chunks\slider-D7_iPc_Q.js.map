{"version": 3, "file": "slider-D7_iPc_Q.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/slider.js"], "sourcesContent": ["import { K as run, J as derived, w as push, M as spread_attributes, N as bind_props, y as pop, O as copy_payload, P as assign_payload, Q as spread_props, U as ensure_array_like, R as attr, S as attr_class, T as clsx } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { w as watch, b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport \"clsx\";\nimport { C as Context } from \"./context.js\";\nimport { A as ARROW_LEFT, a as ARROW_RIGHT, f as ARROW_UP, b as ARROW_DOWN, H as HOME, E as END, e as getDataDisabled, g as getDataOrientation, j as getAriaOrientation, k as getAriaDisabled } from \"./kbd-constants.js\";\nimport { b as isElementOrSVGElement } from \"./is.js\";\nimport { i as isValidIndex } from \"./mounted.js\";\nimport { u as useId } from \"./use-id.js\";\nimport { n as noop } from \"./noop.js\";\nfunction getRangeStyles(direction, min, max) {\n  const styles = {\n    position: \"absolute\"\n  };\n  if (direction === \"lr\") {\n    styles.left = `${min}%`;\n    styles.right = `${max}%`;\n  } else if (direction === \"rl\") {\n    styles.right = `${min}%`;\n    styles.left = `${max}%`;\n  } else if (direction === \"bt\") {\n    styles.bottom = `${min}%`;\n    styles.top = `${max}%`;\n  } else {\n    styles.top = `${min}%`;\n    styles.bottom = `${max}%`;\n  }\n  return styles;\n}\nfunction getThumbStyles(direction, thumbPos) {\n  const styles = {\n    position: \"absolute\"\n  };\n  if (direction === \"lr\") {\n    styles.left = `${thumbPos}%`;\n    styles.translate = \"-50% 0\";\n  } else if (direction === \"rl\") {\n    styles.right = `${thumbPos}%`;\n    styles.translate = \"50% 0\";\n  } else if (direction === \"bt\") {\n    styles.bottom = `${thumbPos}%`;\n    styles.translate = \"0 50%\";\n  } else {\n    styles.top = `${thumbPos}%`;\n    styles.translate = \"0 -50%\";\n  }\n  return styles;\n}\nfunction getTickStyles(direction, tickPosition, offsetPercentage) {\n  const style = {\n    position: \"absolute\"\n  };\n  if (direction === \"lr\") {\n    style.left = `${tickPosition}%`;\n    style.translate = `${offsetPercentage}% 0`;\n  } else if (direction === \"rl\") {\n    style.right = `${tickPosition}%`;\n    style.translate = `${-offsetPercentage}% 0`;\n  } else if (direction === \"bt\") {\n    style.bottom = `${tickPosition}%`;\n    style.translate = `0 ${-offsetPercentage}%`;\n  } else {\n    style.top = `${tickPosition}%`;\n    style.translate = `0 ${offsetPercentage}%`;\n  }\n  return style;\n}\nfunction snapValueToStep(value, min, max, step) {\n  const remainder = (value - (Number.isNaN(min) ? 0 : min)) % step;\n  let snappedValue = Math.abs(remainder) * 2 >= step ? value + Math.sign(remainder) * (step - Math.abs(remainder)) : value - remainder;\n  if (!Number.isNaN(min)) {\n    if (snappedValue < min) {\n      snappedValue = min;\n    } else if (!Number.isNaN(max) && snappedValue > max) {\n      snappedValue = min + Math.floor((max - min) / step) * step;\n    }\n  } else if (!Number.isNaN(max) && snappedValue > max) {\n    snappedValue = Math.floor(max / step) * step;\n  }\n  const string = step.toString();\n  const index = string.indexOf(\".\");\n  const precision = index >= 0 ? string.length - index : 0;\n  if (precision > 0) {\n    const pow = 10 ** precision;\n    snappedValue = Math.round(snappedValue * pow) / pow;\n  }\n  return snappedValue;\n}\nfunction linearScale(domain, range, clamp = true) {\n  const [d0, d1] = domain;\n  const [r0, r1] = range;\n  const slope = (r1 - r0) / (d1 - d0);\n  return (x) => {\n    const result = r0 + slope * (x - d0);\n    if (!clamp)\n      return result;\n    if (result > Math.max(r0, r1))\n      return Math.max(r0, r1);\n    if (result < Math.min(r0, r1))\n      return Math.min(r0, r1);\n    return result;\n  };\n}\nconst SLIDER_ROOT_ATTR = \"data-slider-root\";\nconst SLIDER_THUMB_ATTR = \"data-slider-thumb\";\nconst SLIDER_RANGE_ATTR = \"data-slider-range\";\nconst SLIDER_TICK_ATTR = \"data-slider-tick\";\nclass SliderBaseRootState {\n  opts;\n  isActive = false;\n  #direction = derived(() => {\n    if (this.opts.orientation.current === \"horizontal\") {\n      return this.opts.dir.current === \"rtl\" ? \"rl\" : \"lr\";\n    } else {\n      return this.opts.dir.current === \"rtl\" ? \"tb\" : \"bt\";\n    }\n  });\n  get direction() {\n    return this.#direction();\n  }\n  set direction($$value) {\n    return this.#direction($$value);\n  }\n  constructor(opts) {\n    this.opts = opts;\n    useRefById(opts);\n  }\n  isThumbActive(_index) {\n    return this.isActive;\n  }\n  #touchAction = derived(() => {\n    if (this.opts.disabled.current) return void 0;\n    return this.opts.orientation.current === \"horizontal\" ? \"pan-y\" : \"pan-x\";\n  });\n  getAllThumbs = () => {\n    const node = this.opts.ref.current;\n    if (!node) return [];\n    return Array.from(node.querySelectorAll(`[${SLIDER_THUMB_ATTR}]`));\n  };\n  getThumbScale = () => {\n    if (this.opts.thumbPositioning.current === \"exact\") {\n      return [0, 100];\n    }\n    const isVertical = this.opts.orientation.current === \"vertical\";\n    const activeThumb = this.getAllThumbs()[0];\n    const thumbSize = isVertical ? activeThumb?.offsetHeight : activeThumb?.offsetWidth;\n    if (thumbSize === void 0 || Number.isNaN(thumbSize) || thumbSize === 0) return [0, 100];\n    const trackSize = isVertical ? this.opts.ref.current?.offsetHeight : this.opts.ref.current?.offsetWidth;\n    if (trackSize === void 0 || Number.isNaN(trackSize) || trackSize === 0) return [0, 100];\n    const percentPadding = thumbSize / 2 / trackSize * 100;\n    const min = percentPadding;\n    const max = 100 - percentPadding;\n    return [min, max];\n  };\n  getPositionFromValue = (thumbValue) => {\n    const thumbScale = this.getThumbScale();\n    const scale = linearScale([this.opts.min.current, this.opts.max.current], thumbScale);\n    return scale(thumbValue);\n  };\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"data-orientation\": getDataOrientation(this.opts.orientation.current),\n    \"data-disabled\": getDataDisabled(this.opts.disabled.current),\n    style: { touchAction: this.#touchAction() },\n    [SLIDER_ROOT_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass SliderSingleRootState extends SliderBaseRootState {\n  opts;\n  isMulti = false;\n  constructor(opts) {\n    super(opts);\n    this.opts = opts;\n    watch(\n      [\n        () => this.opts.step.current,\n        () => this.opts.min.current,\n        () => this.opts.max.current,\n        () => this.opts.value.current\n      ],\n      ([step, min, max, value]) => {\n        const isValidValue = (v) => {\n          const snappedValue = snapValueToStep(v, min, max, step);\n          return snappedValue === v;\n        };\n        const gcv = (v) => {\n          return snapValueToStep(v, min, max, step);\n        };\n        if (!isValidValue(value)) {\n          this.opts.value.current = gcv(value);\n        }\n      }\n    );\n  }\n  applyPosition({ clientXY, start, end }) {\n    const min = this.opts.min.current;\n    const max = this.opts.max.current;\n    const percent = (clientXY - start) / (end - start);\n    const val = percent * (max - min) + min;\n    if (val < min) {\n      this.updateValue(min);\n    } else if (val > max) {\n      this.updateValue(max);\n    } else {\n      const step = this.opts.step.current;\n      const currStep = Math.floor((val - min) / step);\n      const midpointOfCurrStep = min + currStep * step + step / 2;\n      const midpointOfNextStep = min + (currStep + 1) * step + step / 2;\n      const newValue = val >= midpointOfCurrStep && val < midpointOfNextStep ? (currStep + 1) * step + min : currStep * step + min;\n      if (newValue <= max) {\n        this.updateValue(newValue);\n      }\n    }\n  }\n  updateValue = (newValue) => {\n    this.opts.value.current = snapValueToStep(newValue, this.opts.min.current, this.opts.max.current, this.opts.step.current);\n  };\n  handlePointerMove = (e) => {\n    if (!this.isActive || this.opts.disabled.current) return;\n    e.preventDefault();\n    e.stopPropagation();\n    const sliderNode = this.opts.ref.current;\n    const activeThumb = this.getAllThumbs()[0];\n    if (!sliderNode || !activeThumb) return;\n    activeThumb.focus();\n    const { left, right, top, bottom } = sliderNode.getBoundingClientRect();\n    if (this.direction === \"lr\") {\n      this.applyPosition({ clientXY: e.clientX, start: left, end: right });\n    } else if (this.direction === \"rl\") {\n      this.applyPosition({ clientXY: e.clientX, start: right, end: left });\n    } else if (this.direction === \"bt\") {\n      this.applyPosition({ clientXY: e.clientY, start: bottom, end: top });\n    } else if (this.direction === \"tb\") {\n      this.applyPosition({ clientXY: e.clientY, start: top, end: bottom });\n    }\n  };\n  handlePointerDown = (e) => {\n    if (e.button !== 0 || this.opts.disabled.current) return;\n    const sliderNode = this.opts.ref.current;\n    const closestThumb = this.getAllThumbs()[0];\n    if (!closestThumb || !sliderNode) return;\n    const target = e.target;\n    if (!isElementOrSVGElement(target) || !sliderNode.contains(target)) return;\n    e.preventDefault();\n    closestThumb.focus();\n    this.isActive = true;\n    this.handlePointerMove(e);\n  };\n  handlePointerUp = () => {\n    if (this.opts.disabled.current) return;\n    if (this.isActive) {\n      this.opts.onValueCommit.current(run(() => this.opts.value.current));\n    }\n    this.isActive = false;\n  };\n  #thumbsPropsArr = derived(() => {\n    const currValue = this.opts.value.current;\n    return Array.from({ length: 1 }, () => {\n      const thumbValue = currValue;\n      const thumbPosition = this.getPositionFromValue(thumbValue);\n      const style = getThumbStyles(this.direction, thumbPosition);\n      return {\n        role: \"slider\",\n        \"aria-valuemin\": this.opts.min.current,\n        \"aria-valuemax\": this.opts.max.current,\n        \"aria-valuenow\": thumbValue,\n        \"aria-disabled\": getAriaDisabled(this.opts.disabled.current),\n        \"aria-orientation\": getAriaOrientation(this.opts.orientation.current),\n        \"data-value\": thumbValue,\n        tabindex: this.opts.disabled.current ? -1 : 0,\n        style,\n        [SLIDER_THUMB_ATTR]: \"\"\n      };\n    });\n  });\n  get thumbsPropsArr() {\n    return this.#thumbsPropsArr();\n  }\n  set thumbsPropsArr($$value) {\n    return this.#thumbsPropsArr($$value);\n  }\n  #thumbsRenderArr = derived(() => {\n    return this.thumbsPropsArr.map((_, i) => i);\n  });\n  get thumbsRenderArr() {\n    return this.#thumbsRenderArr();\n  }\n  set thumbsRenderArr($$value) {\n    return this.#thumbsRenderArr($$value);\n  }\n  #ticksPropsArr = derived(() => {\n    const max = this.opts.max.current;\n    const min = this.opts.min.current;\n    const step = this.opts.step.current;\n    const difference = max - min;\n    let count = Math.ceil(difference / step);\n    if (difference % step == 0) {\n      count++;\n    }\n    const currValue = this.opts.value.current;\n    return Array.from({ length: count }, (_, i) => {\n      const tickPosition = i * step;\n      const scale = linearScale([0, (count - 1) * step], this.getThumbScale());\n      const isFirst = i === 0;\n      const isLast = i === count - 1;\n      const offsetPercentage = isFirst ? 0 : isLast ? -100 : -50;\n      const style = getTickStyles(this.direction, scale(tickPosition), offsetPercentage);\n      const tickValue = min + i * step;\n      const bounded = tickValue <= currValue;\n      return {\n        \"data-disabled\": getDataDisabled(this.opts.disabled.current),\n        \"data-orientation\": getDataOrientation(this.opts.orientation.current),\n        \"data-bounded\": bounded ? \"\" : void 0,\n        \"data-value\": tickValue,\n        style,\n        [SLIDER_TICK_ATTR]: \"\"\n      };\n    });\n  });\n  get ticksPropsArr() {\n    return this.#ticksPropsArr();\n  }\n  set ticksPropsArr($$value) {\n    return this.#ticksPropsArr($$value);\n  }\n  #ticksRenderArr = derived(() => {\n    return this.ticksPropsArr.map((_, i) => i);\n  });\n  get ticksRenderArr() {\n    return this.#ticksRenderArr();\n  }\n  set ticksRenderArr($$value) {\n    return this.#ticksRenderArr($$value);\n  }\n  #snippetProps = derived(() => ({\n    ticks: this.ticksRenderArr,\n    thumbs: this.thumbsRenderArr\n  }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n}\nclass SliderMultiRootState extends SliderBaseRootState {\n  opts;\n  isMulti = true;\n  activeThumb = null;\n  currentThumbIdx = 0;\n  constructor(opts) {\n    super(opts);\n    this.opts = opts;\n    watch(\n      [\n        () => this.opts.step.current,\n        () => this.opts.min.current,\n        () => this.opts.max.current,\n        () => this.opts.value.current\n      ],\n      ([step, min, max, value]) => {\n        const isValidValue = (v) => {\n          const snappedValue = snapValueToStep(v, min, max, step);\n          return snappedValue === v;\n        };\n        const gcv = (v) => {\n          return snapValueToStep(v, min, max, step);\n        };\n        if (value.some((v) => !isValidValue(v))) {\n          this.opts.value.current = value.map(gcv);\n        }\n      }\n    );\n  }\n  isThumbActive(index) {\n    return this.isActive && this.activeThumb?.idx === index;\n  }\n  applyPosition({ clientXY, activeThumbIdx, start, end }) {\n    const min = this.opts.min.current;\n    const max = this.opts.max.current;\n    const percent = (clientXY - start) / (end - start);\n    const val = percent * (max - min) + min;\n    if (val < min) {\n      this.updateValue(min, activeThumbIdx);\n    } else if (val > max) {\n      this.updateValue(max, activeThumbIdx);\n    } else {\n      const step = this.opts.step.current;\n      const currStep = Math.floor((val - min) / step);\n      const midpointOfCurrStep = min + currStep * step + step / 2;\n      const midpointOfNextStep = min + (currStep + 1) * step + step / 2;\n      const newValue = val >= midpointOfCurrStep && val < midpointOfNextStep ? (currStep + 1) * step + min : currStep * step + min;\n      if (newValue <= max) {\n        this.updateValue(newValue, activeThumbIdx);\n      }\n    }\n  }\n  #getClosestThumb = (e) => {\n    const thumbs = this.getAllThumbs();\n    if (!thumbs.length) return;\n    for (const thumb of thumbs) {\n      thumb.blur();\n    }\n    const distances = thumbs.map((thumb) => {\n      if (this.opts.orientation.current === \"horizontal\") {\n        const { left, right } = thumb.getBoundingClientRect();\n        return Math.abs(e.clientX - (left + right) / 2);\n      } else {\n        const { top, bottom } = thumb.getBoundingClientRect();\n        return Math.abs(e.clientY - (top + bottom) / 2);\n      }\n    });\n    const node = thumbs[distances.indexOf(Math.min(...distances))];\n    const idx = thumbs.indexOf(node);\n    return { node, idx };\n  };\n  handlePointerMove = (e) => {\n    if (!this.isActive || this.opts.disabled.current) return;\n    e.preventDefault();\n    e.stopPropagation();\n    const sliderNode = this.opts.ref.current;\n    const activeThumb = this.activeThumb;\n    if (!sliderNode || !activeThumb) return;\n    activeThumb.node.focus();\n    const { left, right, top, bottom } = sliderNode.getBoundingClientRect();\n    const direction = this.direction;\n    if (direction === \"lr\") {\n      this.applyPosition({\n        clientXY: e.clientX,\n        activeThumbIdx: activeThumb.idx,\n        start: left,\n        end: right\n      });\n    } else if (direction === \"rl\") {\n      this.applyPosition({\n        clientXY: e.clientX,\n        activeThumbIdx: activeThumb.idx,\n        start: right,\n        end: left\n      });\n    } else if (direction === \"bt\") {\n      this.applyPosition({\n        clientXY: e.clientY,\n        activeThumbIdx: activeThumb.idx,\n        start: bottom,\n        end: top\n      });\n    } else if (direction === \"tb\") {\n      this.applyPosition({\n        clientXY: e.clientY,\n        activeThumbIdx: activeThumb.idx,\n        start: top,\n        end: bottom\n      });\n    }\n  };\n  handlePointerDown = (e) => {\n    if (e.button !== 0 || this.opts.disabled.current) return;\n    const sliderNode = this.opts.ref.current;\n    const closestThumb = this.#getClosestThumb(e);\n    if (!closestThumb || !sliderNode) return;\n    const target = e.target;\n    if (!isElementOrSVGElement(target) || !sliderNode.contains(target)) return;\n    e.preventDefault();\n    this.activeThumb = closestThumb;\n    closestThumb.node.focus();\n    this.isActive = true;\n    this.handlePointerMove(e);\n  };\n  handlePointerUp = () => {\n    if (this.opts.disabled.current) return;\n    if (this.isActive) {\n      this.opts.onValueCommit.current(run(() => this.opts.value.current));\n    }\n    this.isActive = false;\n  };\n  getAllThumbs = () => {\n    const node = this.opts.ref.current;\n    if (!node) return [];\n    const thumbs = Array.from(node.querySelectorAll(`[${SLIDER_THUMB_ATTR}]`));\n    return thumbs;\n  };\n  updateValue = (thumbValue, idx) => {\n    const currValue = this.opts.value.current;\n    if (!currValue.length) {\n      this.opts.value.current.push(thumbValue);\n      return;\n    }\n    const valueAtIndex = currValue[idx];\n    if (valueAtIndex === thumbValue) return;\n    const newValue = [...currValue];\n    if (!isValidIndex(idx, newValue)) return;\n    const direction = newValue[idx] > thumbValue ? -1 : 1;\n    const swap = () => {\n      const diffIndex = idx + direction;\n      newValue[idx] = newValue[diffIndex];\n      newValue[diffIndex] = thumbValue;\n      const thumbs = this.getAllThumbs();\n      if (!thumbs.length) return;\n      thumbs[diffIndex]?.focus();\n      this.activeThumb = { node: thumbs[diffIndex], idx: diffIndex };\n    };\n    if (this.opts.autoSort.current && (direction === -1 && thumbValue < newValue[idx - 1] || direction === 1 && thumbValue > newValue[idx + 1])) {\n      swap();\n      this.opts.value.current = newValue;\n      return;\n    }\n    const min = this.opts.min.current;\n    const max = this.opts.max.current;\n    const step = this.opts.step.current;\n    newValue[idx] = snapValueToStep(thumbValue, min, max, step);\n    this.opts.value.current = newValue;\n  };\n  #thumbsPropsArr = derived(() => {\n    const currValue = this.opts.value.current;\n    return Array.from({ length: currValue.length || 1 }, (_, i) => {\n      const currThumb = run(() => this.currentThumbIdx);\n      if (currThumb < currValue.length) {\n        run(() => {\n          this.currentThumbIdx = currThumb + 1;\n        });\n      }\n      const thumbValue = currValue[i];\n      const thumbPosition = this.getPositionFromValue(thumbValue ?? 0);\n      const style = getThumbStyles(this.direction, thumbPosition);\n      return {\n        role: \"slider\",\n        \"aria-valuemin\": this.opts.min.current,\n        \"aria-valuemax\": this.opts.max.current,\n        \"aria-valuenow\": thumbValue,\n        \"aria-disabled\": getAriaDisabled(this.opts.disabled.current),\n        \"aria-orientation\": getAriaOrientation(this.opts.orientation.current),\n        \"data-value\": thumbValue,\n        tabindex: this.opts.disabled.current ? -1 : 0,\n        style,\n        [SLIDER_THUMB_ATTR]: \"\"\n      };\n    });\n  });\n  get thumbsPropsArr() {\n    return this.#thumbsPropsArr();\n  }\n  set thumbsPropsArr($$value) {\n    return this.#thumbsPropsArr($$value);\n  }\n  #thumbsRenderArr = derived(() => {\n    return this.thumbsPropsArr.map((_, i) => i);\n  });\n  get thumbsRenderArr() {\n    return this.#thumbsRenderArr();\n  }\n  set thumbsRenderArr($$value) {\n    return this.#thumbsRenderArr($$value);\n  }\n  #ticksPropsArr = derived(() => {\n    const max = this.opts.max.current;\n    const min = this.opts.min.current;\n    const step = this.opts.step.current;\n    const difference = max - min;\n    let count = Math.ceil(difference / step);\n    if (difference % step == 0) {\n      count++;\n    }\n    const currValue = this.opts.value.current;\n    return Array.from({ length: count }, (_, i) => {\n      const tickPosition = i * step;\n      const scale = linearScale([0, (count - 1) * step], this.getThumbScale());\n      const isFirst = i === 0;\n      const isLast = i === count - 1;\n      const offsetPercentage = isFirst ? 0 : isLast ? -100 : -50;\n      const style = getTickStyles(this.direction, scale(tickPosition), offsetPercentage);\n      const tickValue = min + i * step;\n      const bounded = currValue.length === 1 ? tickValue <= currValue[0] : currValue[0] <= tickValue && tickValue <= currValue[currValue.length - 1];\n      return {\n        \"data-disabled\": getDataDisabled(this.opts.disabled.current),\n        \"data-orientation\": getDataOrientation(this.opts.orientation.current),\n        \"data-bounded\": bounded ? \"\" : void 0,\n        \"data-value\": tickValue,\n        style,\n        [SLIDER_TICK_ATTR]: \"\"\n      };\n    });\n  });\n  get ticksPropsArr() {\n    return this.#ticksPropsArr();\n  }\n  set ticksPropsArr($$value) {\n    return this.#ticksPropsArr($$value);\n  }\n  #ticksRenderArr = derived(() => {\n    return this.ticksPropsArr.map((_, i) => i);\n  });\n  get ticksRenderArr() {\n    return this.#ticksRenderArr();\n  }\n  set ticksRenderArr($$value) {\n    return this.#ticksRenderArr($$value);\n  }\n  #snippetProps = derived(() => ({\n    ticks: this.ticksRenderArr,\n    thumbs: this.thumbsRenderArr\n  }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n}\nconst VALID_SLIDER_KEYS = [\n  ARROW_LEFT,\n  ARROW_RIGHT,\n  ARROW_UP,\n  ARROW_DOWN,\n  HOME,\n  END\n];\nclass SliderRangeState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n  }\n  #rangeStyles = derived(() => {\n    const min = Array.isArray(this.root.opts.value.current) ? this.root.opts.value.current.length > 1 ? this.root.getPositionFromValue(Math.min(...this.root.opts.value.current) ?? 0) : 0 : 0;\n    const max = Array.isArray(this.root.opts.value.current) ? 100 - this.root.getPositionFromValue(Math.max(...this.root.opts.value.current) ?? 0) : 100 - this.root.getPositionFromValue(this.root.opts.value.current);\n    return {\n      position: \"absolute\",\n      ...getRangeStyles(this.root.direction, min, max)\n    };\n  });\n  get rangeStyles() {\n    return this.#rangeStyles();\n  }\n  set rangeStyles($$value) {\n    return this.#rangeStyles($$value);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"data-orientation\": getDataOrientation(this.root.opts.orientation.current),\n    \"data-disabled\": getDataDisabled(this.root.opts.disabled.current),\n    style: this.rangeStyles,\n    [SLIDER_RANGE_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass SliderThumbState {\n  opts;\n  root;\n  #isDisabled = derived(() => this.root.opts.disabled.current || this.opts.disabled.current);\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n    this.onkeydown = this.onkeydown.bind(this);\n  }\n  #updateValue(newValue) {\n    if (this.root.isMulti) {\n      this.root.updateValue(newValue, this.opts.index.current);\n    } else {\n      this.root.updateValue(newValue);\n    }\n  }\n  onkeydown(e) {\n    if (this.#isDisabled()) return;\n    const currNode = this.opts.ref.current;\n    if (!currNode) return;\n    const thumbs = this.root.getAllThumbs();\n    if (!thumbs.length) return;\n    const idx = thumbs.indexOf(currNode);\n    if (this.root.isMulti) {\n      this.root.currentThumbIdx = idx;\n    }\n    if (!VALID_SLIDER_KEYS.includes(e.key)) return;\n    e.preventDefault();\n    const min = this.root.opts.min.current;\n    const max = this.root.opts.max.current;\n    const value = this.root.opts.value.current;\n    const thumbValue = Array.isArray(value) ? value[idx] : value;\n    const orientation = this.root.opts.orientation.current;\n    const direction = this.root.direction;\n    const step = this.root.opts.step.current;\n    switch (e.key) {\n      case HOME:\n        this.#updateValue(min);\n        break;\n      case END:\n        this.#updateValue(max);\n        break;\n      case ARROW_LEFT:\n        if (orientation !== \"horizontal\") break;\n        if (e.metaKey) {\n          const newValue = direction === \"rl\" ? max : min;\n          this.#updateValue(newValue);\n        } else if (direction === \"rl\" && thumbValue < max) {\n          this.#updateValue(thumbValue + step);\n        } else if (direction === \"lr\" && thumbValue > min) {\n          this.#updateValue(thumbValue - step);\n        }\n        break;\n      case ARROW_RIGHT:\n        if (orientation !== \"horizontal\") break;\n        if (e.metaKey) {\n          const newValue = direction === \"rl\" ? min : max;\n          this.#updateValue(newValue);\n        } else if (direction === \"rl\" && thumbValue > min) {\n          this.#updateValue(thumbValue - step);\n        } else if (direction === \"lr\" && thumbValue < max) {\n          this.#updateValue(thumbValue + step);\n        }\n        break;\n      case ARROW_UP:\n        if (e.metaKey) {\n          const newValue = direction === \"tb\" ? min : max;\n          this.#updateValue(newValue);\n        } else if (direction === \"tb\" && thumbValue > min) {\n          this.#updateValue(thumbValue - step);\n        } else if (direction !== \"tb\" && thumbValue < max) {\n          this.#updateValue(thumbValue + step);\n        }\n        break;\n      case ARROW_DOWN:\n        if (e.metaKey) {\n          const newValue = direction === \"tb\" ? max : min;\n          this.#updateValue(newValue);\n        } else if (direction === \"tb\" && thumbValue < max) {\n          this.#updateValue(thumbValue + step);\n        } else if (direction !== \"tb\" && thumbValue > min) {\n          this.#updateValue(thumbValue - step);\n        }\n        break;\n    }\n    this.root.opts.onValueCommit.current(this.root.opts.value.current);\n  }\n  #props = derived(() => ({\n    ...this.root.thumbsPropsArr[this.opts.index.current],\n    id: this.opts.id.current,\n    onkeydown: this.onkeydown,\n    \"data-active\": this.root.isThumbActive(this.opts.index.current) ? \"\" : void 0\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst SliderRootContext = new Context(\"Slider.Root\");\nfunction useSliderRoot(props) {\n  const { type, ...rest } = props;\n  const rootState = type === \"single\" ? new SliderSingleRootState(rest) : new SliderMultiRootState(rest);\n  return SliderRootContext.set(rootState);\n}\nfunction useSliderRange(props) {\n  return new SliderRangeState(props, SliderRootContext.get());\n}\nfunction useSliderThumb(props) {\n  return new SliderThumbState(props, SliderRootContext.get());\n}\nfunction Slider$1($$payload, $$props) {\n  push();\n  let {\n    children,\n    child,\n    id = useId(),\n    ref = null,\n    value = void 0,\n    type,\n    onValueChange = noop,\n    onValueCommit = noop,\n    disabled = false,\n    min = 0,\n    max = 100,\n    step = 1,\n    dir = \"ltr\",\n    autoSort = true,\n    orientation = \"horizontal\",\n    thumbPositioning = \"contain\",\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  function handleDefaultValue() {\n    if (value !== void 0) return;\n    value = type === \"single\" ? 0 : [];\n  }\n  handleDefaultValue();\n  watch.pre(() => value, () => {\n    handleDefaultValue();\n  });\n  const rootState = useSliderRoot({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v),\n    value: box.with(() => value, (v) => {\n      value = v;\n      onValueChange(v);\n    }),\n    // @ts-expect-error - we know\n    onValueCommit: box.with(() => onValueCommit),\n    disabled: box.with(() => disabled),\n    min: box.with(() => min),\n    max: box.with(() => max),\n    step: box.with(() => step),\n    dir: box.with(() => dir),\n    autoSort: box.with(() => autoSort),\n    orientation: box.with(() => orientation),\n    thumbPositioning: box.with(() => thumbPositioning),\n    type\n  });\n  const mergedProps = mergeProps(restProps, rootState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps, ...rootState.snippetProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload, rootState.snippetProps);\n    $$payload.out += `<!----></span>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref, value });\n  pop();\n}\nfunction Slider_range($$payload, $$props) {\n  push();\n  let {\n    children,\n    child,\n    ref = null,\n    id = useId(),\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const rangeState = useSliderRange({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, rangeState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></span>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Slider_thumb($$payload, $$props) {\n  push();\n  let {\n    children,\n    child,\n    ref = null,\n    id = useId(),\n    index,\n    disabled = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const thumbState = useSliderThumb({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v),\n    index: box.with(() => index),\n    disabled: box.with(() => disabled)\n  });\n  const mergedProps = mergeProps(restProps, thumbState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, {\n      active: thumbState.root.isThumbActive(thumbState.opts.index.current),\n      props: mergedProps\n    });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload, {\n      active: thumbState.root.isThumbActive(thumbState.opts.index.current)\n    });\n    $$payload.out += `<!----></span>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Slider($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    value = void 0,\n    orientation = \"horizontal\",\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    {\n      let children = function($$payload3, { thumbs }) {\n        const each_array = ensure_array_like(thumbs);\n        $$payload3.out += `<span${attr(\"data-orientation\", orientation)} data-slot=\"slider-track\"${attr_class(clsx(cn(\"bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=vertical]:h-full data-[orientation=horizontal]:w-full data-[orientation=vertical]:w-1.5\")))}><!---->`;\n        Slider_range($$payload3, {\n          \"data-slot\": \"slider-range\",\n          style: \"width: -webkit-fill-available;\",\n          class: cn(\"bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full\")\n        });\n        $$payload3.out += `<!----></span> <!--[-->`;\n        for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n          let thumb = each_array[$$index];\n          $$payload3.out += `<!---->`;\n          Slider_thumb($$payload3, {\n            \"data-slot\": \"slider-thumb\",\n            index: thumb,\n            class: \"border-primary bg-background ring-ring/50 focus-visible:outline-hidden block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 disabled:pointer-events-none disabled:opacity-50\"\n          });\n          $$payload3.out += `<!---->`;\n        }\n        $$payload3.out += `<!--]-->`;\n      };\n      Slider$1($$payload2, spread_props([\n        {\n          \"data-slot\": \"slider\",\n          orientation,\n          class: cn(\"relative flex w-full touch-none select-none items-center data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col data-[disabled]:opacity-50\", className)\n        },\n        restProps,\n        {\n          get ref() {\n            return ref;\n          },\n          set ref($$value) {\n            ref = $$value;\n            $$settled = false;\n          },\n          get value() {\n            return value;\n          },\n          set value($$value) {\n            value = $$value;\n            $$settled = false;\n          },\n          children,\n          $$slots: { default: true }\n        }\n      ]));\n    }\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref, value });\n  pop();\n}\nexport {\n  Slider as S\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAYA,SAAS,cAAc,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;AAC7C,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,IAAI,SAAS,KAAK,IAAI,EAAE;AAC1B,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3B,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC5B,GAAG,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACjC,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC5B,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3B,GAAG,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACjC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7B,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1B,GAAG,MAAM;AACT,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1B,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC7B;AACA,EAAE,OAAO,MAAM;AACf;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,QAAQ,EAAE;AAC7C,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,IAAI,SAAS,KAAK,IAAI,EAAE;AAC1B,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AAChC,IAAI,MAAM,CAAC,SAAS,GAAG,QAAQ;AAC/B,GAAG,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACjC,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AACjC,IAAI,MAAM,CAAC,SAAS,GAAG,OAAO;AAC9B,GAAG,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACjC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AAClC,IAAI,MAAM,CAAC,SAAS,GAAG,OAAO;AAC9B,GAAG,MAAM;AACT,IAAI,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC/B,IAAI,MAAM,CAAC,SAAS,GAAG,QAAQ;AAC/B;AACA,EAAE,OAAO,MAAM;AACf;AACA,SAAS,aAAa,CAAC,SAAS,EAAE,YAAY,EAAE,gBAAgB,EAAE;AAClE,EAAE,MAAM,KAAK,GAAG;AAChB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,IAAI,SAAS,KAAK,IAAI,EAAE;AAC1B,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AACnC,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,gBAAgB,CAAC,GAAG,CAAC;AAC9C,GAAG,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACjC,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AACpC,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC;AAC/C,GAAG,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACjC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AACrC,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC/C,GAAG,MAAM;AACT,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AAClC,IAAI,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAC,CAAC;AAC9C;AACA,EAAE,OAAO,KAAK;AACd;AACA,SAAS,eAAe,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;AAChD,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI;AAClE,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,KAAK,GAAG,SAAS;AACtI,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC1B,IAAI,IAAI,YAAY,GAAG,GAAG,EAAE;AAC5B,MAAM,YAAY,GAAG,GAAG;AACxB,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,YAAY,GAAG,GAAG,EAAE;AACzD,MAAM,YAAY,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI;AAChE;AACA,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,YAAY,GAAG,GAAG,EAAE;AACvD,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI;AAChD;AACA,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;AAChC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;AACnC,EAAE,MAAM,SAAS,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC;AAC1D,EAAE,IAAI,SAAS,GAAG,CAAC,EAAE;AACrB,IAAI,MAAM,GAAG,GAAG,EAAE,IAAI,SAAS;AAC/B,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG;AACvD;AACA,EAAE,OAAO,YAAY;AACrB;AACA,SAAS,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE;AAClD,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,MAAM;AACzB,EAAE,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK;AACxB,EAAE,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AACrC,EAAE,OAAO,CAAC,CAAC,KAAK;AAChB,IAAI,MAAM,MAAM,GAAG,EAAE,GAAG,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC;AACxC,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,OAAO,MAAM;AACnB,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACjC,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AAC7B,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACjC,MAAM,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AAC7B,IAAI,OAAO,MAAM;AACjB,GAAG;AACH;AACA,MAAM,gBAAgB,GAAG,kBAAkB;AAC3C,MAAM,iBAAiB,GAAG,mBAAmB;AAC7C,MAAM,iBAAiB,GAAG,mBAAmB;AAC7C,MAAM,gBAAgB,GAAG,kBAAkB;AAC3C,MAAM,mBAAmB,CAAC;AAC1B,EAAE,IAAI;AACN,EAAE,QAAQ,GAAG,KAAK;AAClB,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM;AAC7B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,YAAY,EAAE;AACxD,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI;AAC1D,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI;AAC1D;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;AAC5B;AACA,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACnC;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,aAAa,CAAC,MAAM,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,QAAQ;AACxB;AACA,EAAE,YAAY,GAAG,OAAO,CAAC,MAAM;AAC/B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,MAAM;AACjD,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,YAAY,GAAG,OAAO,GAAG,OAAO;AAC7E,GAAG,CAAC;AACJ,EAAE,YAAY,GAAG,MAAM;AACvB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACtC,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACxB,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,GAAG;AACH,EAAE,aAAa,GAAG,MAAM;AACxB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,KAAK,OAAO,EAAE;AACxD,MAAM,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;AACrB;AACA,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,UAAU;AACnE,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AAC9C,IAAI,MAAM,SAAS,GAAG,UAAU,GAAG,WAAW,EAAE,YAAY,GAAG,WAAW,EAAE,WAAW;AACvF,IAAI,IAAI,SAAS,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;AAC3F,IAAI,MAAM,SAAS,GAAG,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,WAAW;AAC3G,IAAI,IAAI,SAAS,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC;AAC3F,IAAI,MAAM,cAAc,GAAG,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG,GAAG;AAC1D,IAAI,MAAM,GAAG,GAAG,cAAc;AAC9B,IAAI,MAAM,GAAG,GAAG,GAAG,GAAG,cAAc;AACpC,IAAI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AACrB,GAAG;AACH,EAAE,oBAAoB,GAAG,CAAC,UAAU,KAAK;AACzC,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;AAC3C,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,UAAU,CAAC;AACzF,IAAI,OAAO,KAAK,CAAC,UAAU,CAAC;AAC5B,GAAG;AACH,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACzE,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAChE,IAAI,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,EAAE,EAAE;AAC/C,IAAI,CAAC,gBAAgB,GAAG;AACxB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,qBAAqB,SAAS,mBAAmB,CAAC;AACxD,EAAE,IAAI;AACN,EAAE,OAAO,GAAG,KAAK;AACjB,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,KAAK,CAAC,IAAI,CAAC;AACf,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,KAAK;AACT,MAAM;AACN,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACpC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACnC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACnC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,OAAO;AACP,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK;AACnC,QAAQ,MAAM,YAAY,GAAG,CAAC,CAAC,KAAK;AACpC,UAAU,MAAM,YAAY,GAAG,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;AACjE,UAAU,OAAO,YAAY,KAAK,CAAC;AACnC,SAAS;AACT,QAAQ,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK;AAC3B,UAAU,OAAO,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;AAClC,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC;AAC9C;AACA;AACA,KAAK;AACL;AACA,EAAE,aAAa,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;AAC1C,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,OAAO,GAAG,CAAC,QAAQ,GAAG,KAAK,KAAK,GAAG,GAAG,KAAK,CAAC;AACtD,IAAI,MAAM,GAAG,GAAG,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;AAC3C,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE;AACnB,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;AAC3B,KAAK,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE;AAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;AAC3B,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACzC,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC;AACrD,MAAM,MAAM,kBAAkB,GAAG,GAAG,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACjE,MAAM,MAAM,kBAAkB,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC;AACvE,MAAM,MAAM,QAAQ,GAAG,GAAG,IAAI,kBAAkB,IAAI,GAAG,GAAG,kBAAkB,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG;AAClI,MAAM,IAAI,QAAQ,IAAI,GAAG,EAAE;AAC3B,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;AAClC;AACA;AACA;AACA,EAAE,WAAW,GAAG,CAAC,QAAQ,KAAK;AAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7H,GAAG;AACH,EAAE,iBAAiB,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACtD,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,CAAC,CAAC,eAAe,EAAE;AACvB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC5C,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,WAAW,EAAE;AACrC,IAAI,WAAW,CAAC,KAAK,EAAE;AACvB,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,qBAAqB,EAAE;AAC3E,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AACjC,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AAC1E,KAAK,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AACxC,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AAC1E,KAAK,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AACxC,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAC1E,KAAK,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AACxC,MAAM,IAAI,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC;AAC1E;AACA,GAAG;AACH,EAAE,iBAAiB,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACtD,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC5C,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,EAAE;AACtC,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;AAC3B,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACxE,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,YAAY,CAAC,KAAK,EAAE;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI;AACxB,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,eAAe,GAAG,MAAM;AAC1B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACzE;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK;AACzB,GAAG;AACH,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM;AAClC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAC7C,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM;AAC3C,MAAM,MAAM,UAAU,GAAG,SAAS;AAClC,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;AACjE,MAAM,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC;AACjE,MAAM,OAAO;AACb,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC9C,QAAQ,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC9C,QAAQ,eAAe,EAAE,UAAU;AACnC,QAAQ,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACpE,QAAQ,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC7E,QAAQ,YAAY,EAAE,UAAU;AAChC,QAAQ,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,EAAE,GAAG,CAAC;AACrD,QAAQ,KAAK;AACb,QAAQ,CAAC,iBAAiB,GAAG;AAC7B,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,IAAI,cAAc,GAAG;AACvB,IAAI,OAAO,IAAI,CAAC,eAAe,EAAE;AACjC;AACA,EAAE,IAAI,cAAc,CAAC,OAAO,EAAE;AAC9B,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;AACxC;AACA,EAAE,gBAAgB,GAAG,OAAO,CAAC,MAAM;AACnC,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC/C,GAAG,CAAC;AACJ,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,OAAO,IAAI,CAAC,gBAAgB,EAAE;AAClC;AACA,EAAE,IAAI,eAAe,CAAC,OAAO,EAAE;AAC/B,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;AACzC;AACA,EAAE,cAAc,GAAG,OAAO,CAAC,MAAM;AACjC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACvC,IAAI,MAAM,UAAU,GAAG,GAAG,GAAG,GAAG;AAChC,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC5C,IAAI,IAAI,UAAU,GAAG,IAAI,IAAI,CAAC,EAAE;AAChC,MAAM,KAAK,EAAE;AACb;AACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAC7C,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AACnD,MAAM,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI;AACnC,MAAM,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;AAC9E,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC;AAC7B,MAAM,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,MAAM,MAAM,gBAAgB,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG;AAChE,MAAM,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC;AACxF,MAAM,MAAM,SAAS,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI;AACtC,MAAM,MAAM,OAAO,GAAG,SAAS,IAAI,SAAS;AAC5C,MAAM,OAAO;AACb,QAAQ,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACpE,QAAQ,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC7E,QAAQ,cAAc,EAAE,OAAO,GAAG,EAAE,GAAG,MAAM;AAC7C,QAAQ,YAAY,EAAE,SAAS;AAC/B,QAAQ,KAAK;AACb,QAAQ,CAAC,gBAAgB,GAAG;AAC5B,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,cAAc,EAAE;AAChC;AACA,EAAE,IAAI,aAAa,CAAC,OAAO,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;AACvC;AACA,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM;AAClC,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC9C,GAAG,CAAC;AACJ,EAAE,IAAI,cAAc,GAAG;AACvB,IAAI,OAAO,IAAI,CAAC,eAAe,EAAE;AACjC;AACA,EAAE,IAAI,cAAc,CAAC,OAAO,EAAE;AAC9B,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;AACxC;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO;AACjC,IAAI,KAAK,EAAE,IAAI,CAAC,cAAc;AAC9B,IAAI,MAAM,EAAE,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA;AACA,MAAM,oBAAoB,SAAS,mBAAmB,CAAC;AACvD,EAAE,IAAI;AACN,EAAE,OAAO,GAAG,IAAI;AAChB,EAAE,WAAW,GAAG,IAAI;AACpB,EAAE,eAAe,GAAG,CAAC;AACrB,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,KAAK,CAAC,IAAI,CAAC;AACf,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,KAAK;AACT,MAAM;AACN,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACpC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACnC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACnC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,OAAO;AACP,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,KAAK;AACnC,QAAQ,MAAM,YAAY,GAAG,CAAC,CAAC,KAAK;AACpC,UAAU,MAAM,YAAY,GAAG,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;AACjE,UAAU,OAAO,YAAY,KAAK,CAAC;AACnC,SAAS;AACT,QAAQ,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK;AAC3B,UAAU,OAAO,eAAe,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;AACnD,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;AACjD,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;AAClD;AACA;AACA,KAAK;AACL;AACA,EAAE,aAAa,CAAC,KAAK,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE,GAAG,KAAK,KAAK;AAC3D;AACA,EAAE,aAAa,CAAC,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;AAC1D,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,OAAO,GAAG,CAAC,QAAQ,GAAG,KAAK,KAAK,GAAG,GAAG,KAAK,CAAC;AACtD,IAAI,MAAM,GAAG,GAAG,OAAO,IAAI,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;AAC3C,IAAI,IAAI,GAAG,GAAG,GAAG,EAAE;AACnB,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC;AAC3C,KAAK,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE;AAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC;AAC3C,KAAK,MAAM;AACX,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACzC,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC;AACrD,MAAM,MAAM,kBAAkB,GAAG,GAAG,GAAG,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAG,CAAC;AACjE,MAAM,MAAM,kBAAkB,GAAG,GAAG,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC;AACvE,MAAM,MAAM,QAAQ,GAAG,GAAG,IAAI,kBAAkB,IAAI,GAAG,GAAG,kBAAkB,GAAG,CAAC,QAAQ,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,GAAG,QAAQ,GAAG,IAAI,GAAG,GAAG;AAClI,MAAM,IAAI,QAAQ,IAAI,GAAG,EAAE;AAC3B,QAAQ,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC;AAClD;AACA;AACA;AACA,EAAE,gBAAgB,GAAG,CAAC,CAAC,KAAK;AAC5B,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE;AACtC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACxB,IAAI,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AAChC,MAAM,KAAK,CAAC,IAAI,EAAE;AAClB;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AAC5C,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,YAAY,EAAE;AAC1D,QAAQ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC,qBAAqB,EAAE;AAC7D,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC;AACvD,OAAO,MAAM;AACb,QAAQ,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,qBAAqB,EAAE;AAC7D,QAAQ,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,CAAC;AACvD;AACA,KAAK,CAAC;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AAClE,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;AACpC,IAAI,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE;AACxB,GAAG;AACH,EAAE,iBAAiB,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACtD,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,CAAC,CAAC,eAAe,EAAE;AACvB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC5C,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW;AACxC,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,WAAW,EAAE;AACrC,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE;AAC5B,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,UAAU,CAAC,qBAAqB,EAAE;AAC3E,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;AACpC,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;AAC5B,MAAM,IAAI,CAAC,aAAa,CAAC;AACzB,QAAQ,QAAQ,EAAE,CAAC,CAAC,OAAO;AAC3B,QAAQ,cAAc,EAAE,WAAW,CAAC,GAAG;AACvC,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,GAAG,EAAE;AACb,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACnC,MAAM,IAAI,CAAC,aAAa,CAAC;AACzB,QAAQ,QAAQ,EAAE,CAAC,CAAC,OAAO;AAC3B,QAAQ,cAAc,EAAE,WAAW,CAAC,GAAG;AACvC,QAAQ,KAAK,EAAE,KAAK;AACpB,QAAQ,GAAG,EAAE;AACb,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACnC,MAAM,IAAI,CAAC,aAAa,CAAC;AACzB,QAAQ,QAAQ,EAAE,CAAC,CAAC,OAAO;AAC3B,QAAQ,cAAc,EAAE,WAAW,CAAC,GAAG;AACvC,QAAQ,KAAK,EAAE,MAAM;AACrB,QAAQ,GAAG,EAAE;AACb,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,SAAS,KAAK,IAAI,EAAE;AACnC,MAAM,IAAI,CAAC,aAAa,CAAC;AACzB,QAAQ,QAAQ,EAAE,CAAC,CAAC,OAAO;AAC3B,QAAQ,cAAc,EAAE,WAAW,CAAC,GAAG;AACvC,QAAQ,KAAK,EAAE,GAAG;AAClB,QAAQ,GAAG,EAAE;AACb,OAAO,CAAC;AACR;AACA,GAAG;AACH,EAAE,iBAAiB,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACtD,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC5C,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,EAAE;AACtC,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;AAC3B,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACxE,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,IAAI,CAAC,WAAW,GAAG,YAAY;AACnC,IAAI,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE;AAC7B,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI;AACxB,IAAI,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,eAAe,GAAG,MAAM;AAC1B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AACvB,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACzE;AACA,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK;AACzB,GAAG;AACH,EAAE,YAAY,GAAG,MAAM;AACvB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACtC,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACxB,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9E,IAAI,OAAO,MAAM;AACjB,GAAG;AACH,EAAE,WAAW,GAAG,CAAC,UAAU,EAAE,GAAG,KAAK;AACrC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAC7C,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AAC3B,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM;AACN;AACA,IAAI,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC;AACvC,IAAI,IAAI,YAAY,KAAK,UAAU,EAAE;AACrC,IAAI,MAAM,QAAQ,GAAG,CAAC,GAAG,SAAS,CAAC;AACnC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE;AACtC,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,EAAE,GAAG,CAAC;AACzD,IAAI,MAAM,IAAI,GAAG,MAAM;AACvB,MAAM,MAAM,SAAS,GAAG,GAAG,GAAG,SAAS;AACvC,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;AACzC,MAAM,QAAQ,CAAC,SAAS,CAAC,GAAG,UAAU;AACtC,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE;AACxC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AAC1B,MAAM,MAAM,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE;AAChC,MAAM,IAAI,CAAC,WAAW,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,SAAS,EAAE;AACpE,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,KAAK,SAAS,KAAK,EAAE,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,SAAS,KAAK,CAAC,IAAI,UAAU,GAAG,QAAQ,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE;AACjJ,MAAM,IAAI,EAAE;AACZ,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,QAAQ;AACxC,MAAM;AACN;AACA,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACvC,IAAI,QAAQ,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC;AAC/D,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,QAAQ;AACtC,GAAG;AACH,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM;AAClC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAC7C,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AACnE,MAAM,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC;AACvD,MAAM,IAAI,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE;AACxC,QAAQ,GAAG,CAAC,MAAM;AAClB,UAAU,IAAI,CAAC,eAAe,GAAG,SAAS,GAAG,CAAC;AAC9C,SAAS,CAAC;AACV;AACA,MAAM,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC;AACrC,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,IAAI,CAAC,CAAC;AACtE,MAAM,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,aAAa,CAAC;AACjE,MAAM,OAAO;AACb,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC9C,QAAQ,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC9C,QAAQ,eAAe,EAAE,UAAU;AACnC,QAAQ,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACpE,QAAQ,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC7E,QAAQ,YAAY,EAAE,UAAU;AAChC,QAAQ,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,EAAE,GAAG,CAAC;AACrD,QAAQ,KAAK;AACb,QAAQ,CAAC,iBAAiB,GAAG;AAC7B,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,IAAI,cAAc,GAAG;AACvB,IAAI,OAAO,IAAI,CAAC,eAAe,EAAE;AACjC;AACA,EAAE,IAAI,cAAc,CAAC,OAAO,EAAE;AAC9B,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;AACxC;AACA,EAAE,gBAAgB,GAAG,OAAO,CAAC,MAAM;AACnC,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC/C,GAAG,CAAC;AACJ,EAAE,IAAI,eAAe,GAAG;AACxB,IAAI,OAAO,IAAI,CAAC,gBAAgB,EAAE;AAClC;AACA,EAAE,IAAI,eAAe,CAAC,OAAO,EAAE;AAC/B,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;AACzC;AACA,EAAE,cAAc,GAAG,OAAO,CAAC,MAAM;AACjC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACrC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACvC,IAAI,MAAM,UAAU,GAAG,GAAG,GAAG,GAAG;AAChC,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC5C,IAAI,IAAI,UAAU,GAAG,IAAI,IAAI,CAAC,EAAE;AAChC,MAAM,KAAK,EAAE;AACb;AACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAC7C,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;AACnD,MAAM,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI;AACnC,MAAM,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,aAAa,EAAE,CAAC;AAC9E,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC;AAC7B,MAAM,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC;AACpC,MAAM,MAAM,gBAAgB,GAAG,OAAO,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,GAAG,GAAG;AAChE,MAAM,MAAM,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,gBAAgB,CAAC;AACxF,MAAM,MAAM,SAAS,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI;AACtC,MAAM,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,SAAS,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,IAAI,SAAS,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AACpJ,MAAM,OAAO;AACb,QAAQ,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACpE,QAAQ,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC7E,QAAQ,cAAc,EAAE,OAAO,GAAG,EAAE,GAAG,MAAM;AAC7C,QAAQ,YAAY,EAAE,SAAS;AAC/B,QAAQ,KAAK;AACb,QAAQ,CAAC,gBAAgB,GAAG;AAC5B,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,cAAc,EAAE;AAChC;AACA,EAAE,IAAI,aAAa,CAAC,OAAO,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;AACvC;AACA,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM;AAClC,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;AAC9C,GAAG,CAAC;AACJ,EAAE,IAAI,cAAc,GAAG;AACvB,IAAI,OAAO,IAAI,CAAC,eAAe,EAAE;AACjC;AACA,EAAE,IAAI,cAAc,CAAC,OAAO,EAAE;AAC9B,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC;AACxC;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO;AACjC,IAAI,KAAK,EAAE,IAAI,CAAC,cAAc;AAC9B,IAAI,MAAM,EAAE,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA;AACA,MAAM,iBAAiB,GAAG;AAC1B,EAAE,UAAU;AACZ,EAAE,WAAW;AACb,EAAE,QAAQ;AACV,EAAE,UAAU;AACZ,EAAE,IAAI;AACN,EAAE;AACF,CAAC;AACD,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,YAAY,GAAG,OAAO,CAAC,MAAM;AAC/B,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;AAC9L,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACvN,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,GAAG,EAAE,GAAG;AACrD,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;AAC9B;AACA,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACrC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC9E,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACrE,IAAI,KAAK,EAAE,IAAI,CAAC,WAAW;AAC3B,IAAI,CAAC,iBAAiB,GAAG;AACzB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC5F,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C;AACA,EAAE,YAAY,CAAC,QAAQ,EAAE;AACzB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC3B,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC9D,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;AACrC;AACA;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AAC5B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC1C,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AAC3C,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACxB,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACxC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC3B,MAAM,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,GAAG;AACrC;AACA,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC5C,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC1C,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC1C,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAC9C,IAAI,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK;AAChE,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;AAC1D,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS;AACzC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAC5C,IAAI,QAAQ,CAAC,CAAC,GAAG;AACjB,MAAM,KAAK,IAAI;AACf,QAAQ,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;AAC9B,QAAQ;AACR,MAAM,KAAK,GAAG;AACd,QAAQ,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;AAC9B,QAAQ;AACR,MAAM,KAAK,UAAU;AACrB,QAAQ,IAAI,WAAW,KAAK,YAAY,EAAE;AAC1C,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE;AACvB,UAAU,MAAM,QAAQ,GAAG,SAAS,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AACzD,UAAU,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AACrC,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI,IAAI,UAAU,GAAG,GAAG,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9C,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI,IAAI,UAAU,GAAG,GAAG,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9C;AACA,QAAQ;AACR,MAAM,KAAK,WAAW;AACtB,QAAQ,IAAI,WAAW,KAAK,YAAY,EAAE;AAC1C,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE;AACvB,UAAU,MAAM,QAAQ,GAAG,SAAS,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AACzD,UAAU,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AACrC,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI,IAAI,UAAU,GAAG,GAAG,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9C,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI,IAAI,UAAU,GAAG,GAAG,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9C;AACA,QAAQ;AACR,MAAM,KAAK,QAAQ;AACnB,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE;AACvB,UAAU,MAAM,QAAQ,GAAG,SAAS,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AACzD,UAAU,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AACrC,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI,IAAI,UAAU,GAAG,GAAG,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9C,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI,IAAI,UAAU,GAAG,GAAG,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9C;AACA,QAAQ;AACR,MAAM,KAAK,UAAU;AACrB,QAAQ,IAAI,CAAC,CAAC,OAAO,EAAE;AACvB,UAAU,MAAM,QAAQ,GAAG,SAAS,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AACzD,UAAU,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AACrC,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI,IAAI,UAAU,GAAG,GAAG,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9C,SAAS,MAAM,IAAI,SAAS,KAAK,IAAI,IAAI,UAAU,GAAG,GAAG,EAAE;AAC3D,UAAU,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9C;AACA,QAAQ;AACR;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACtE;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACxD,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AAC7B,IAAI,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG;AAC3E,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,iBAAiB,GAAG,IAAI,OAAO,CAAC,aAAa,CAAC;AACpD,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK;AACjC,EAAE,MAAM,SAAS,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC;AACxG,EAAE,OAAO,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC;AACzC;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC7D;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC7D;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,IAAI;AACR,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,GAAG,GAAG,CAAC;AACX,IAAI,GAAG,GAAG,GAAG;AACb,IAAI,IAAI,GAAG,CAAC;AACZ,IAAI,GAAG,GAAG,KAAK;AACf,IAAI,QAAQ,GAAG,IAAI;AACnB,IAAI,WAAW,GAAG,YAAY;AAC9B,IAAI,gBAAgB,GAAG,SAAS;AAChC,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,IAAI,KAAK,GAAG,IAAI,KAAK,QAAQ,GAAG,CAAC,GAAG,EAAE;AACtC;AACA,EAAE,kBAAkB,EAAE;AACtB,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,EAAE,MAAM;AAC/B,IAAI,kBAAkB,EAAE;AACxB,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,aAAa,CAAC;AAClC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,KAAK;AACxC,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,aAAa,CAAC,CAAC,CAAC;AACtB,KAAK,CAAC;AACN;AACA,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,aAAa,CAAC;AAChD,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,gBAAgB,CAAC;AACtD,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;AACvE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,QAAQ,GAAG,SAAS,EAAE,SAAS,CAAC,YAAY,CAAC;AACjD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC;AACpC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC;AAC7D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,KAAK;AACT,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC;AACpC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ;AACrC,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC;AAC7D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE;AACrB,MAAM,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC1E,MAAM,KAAK,EAAE;AACb,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,QAAQ,GAAG,SAAS,EAAE;AAC1B,MAAM,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACzE,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,WAAW,GAAG,YAAY;AAC9B,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI;AACJ,MAAM,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE,EAAE,MAAM,EAAE,EAAE;AACtD,QAAQ,MAAM,UAAU,GAAG,iBAAiB,CAAC,MAAM,CAAC;AACpD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC,yBAAyB,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,mMAAmM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AACtU,QAAQ,YAAY,CAAC,UAAU,EAAE;AACjC,UAAU,WAAW,EAAE,cAAc;AACrC,UAAU,KAAK,EAAE,gCAAgC;AACjD,UAAU,KAAK,EAAE,EAAE,CAAC,6FAA6F;AACjH,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACnD,QAAQ,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC3F,UAAU,IAAI,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC;AACzC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,WAAW,EAAE,cAAc;AACvC,YAAY,KAAK,EAAE,KAAK;AACxB,YAAY,KAAK,EAAE;AACnB,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,OAAO;AACP,MAAM,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC;AACxC,QAAQ;AACR,UAAU,WAAW,EAAE,QAAQ;AAC/B,UAAU,WAAW;AACrB,UAAU,KAAK,EAAE,EAAE,CAAC,qOAAqO,EAAE,SAAS;AACpQ,SAAS;AACT,QAAQ,SAAS;AACjB,QAAQ;AACR,UAAU,IAAI,GAAG,GAAG;AACpB,YAAY,OAAO,GAAG;AACtB,WAAW;AACX,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE;AAC3B,YAAY,GAAG,GAAG,OAAO;AACzB,YAAY,SAAS,GAAG,KAAK;AAC7B,WAAW;AACX,UAAU,IAAI,KAAK,GAAG;AACtB,YAAY,OAAO,KAAK;AACxB,WAAW;AACX,UAAU,IAAI,KAAK,CAAC,OAAO,EAAE;AAC7B,YAAY,KAAK,GAAG,OAAO;AAC3B,YAAY,SAAS,GAAG,KAAK;AAC7B,WAAW;AACX,UAAU,QAAQ;AAClB,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC;AACA,OAAO,CAAC,CAAC;AACT;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;;;;"}