{"version": 3, "file": "alert-title-gIeEAof-.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/alert-title.js"], "sourcesContent": ["import { M as spread_attributes, T as clsx, N as bind_props, y as pop, w as push } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { tv } from \"tailwind-variants\";\nconst alertVariants = tv({\n  base: \"relative grid w-full grid-cols-[0_1fr] items-start gap-y-0.5 rounded-lg border px-4 py-3 text-sm has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] has-[>svg]:gap-x-3 [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current\",\n  variants: {\n    variant: {\n      default: \"bg-card text-card-foreground\",\n      destructive: \"text-destructive bg-card *:data-[slot=alert-description]:text-destructive/90 [&>svg]:text-current\"\n    }\n  },\n  defaultVariants: { variant: \"default\" }\n});\nfunction Alert($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    variant = \"default\",\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  $$payload.out += `<div${spread_attributes(\n    {\n      \"data-slot\": \"alert\",\n      class: clsx(cn(alertVariants({ variant }), className)),\n      ...restProps,\n      role: \"alert\"\n    },\n    null\n  )}>`;\n  children?.($$payload);\n  $$payload.out += `<!----></div>`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Alert_description($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  $$payload.out += `<div${spread_attributes(\n    {\n      \"data-slot\": \"alert-description\",\n      class: clsx(cn(\"text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed\", className)),\n      ...restProps\n    },\n    null\n  )}>`;\n  children?.($$payload);\n  $$payload.out += `<!----></div>`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Alert_title($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  $$payload.out += `<div${spread_attributes(\n    {\n      \"data-slot\": \"alert-title\",\n      class: clsx(cn(\"col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight\", className)),\n      ...restProps\n    },\n    null\n  )}>`;\n  children?.($$payload);\n  $$payload.out += `<!----></div>`;\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Alert as A,\n  Alert_title as a,\n  Alert_description as b\n};\n"], "names": ["tv"], "mappings": ";;;;AAGA,MAAM,aAAa,GAAGA,EAAE,CAAC;AACzB,EAAE,IAAI,EAAE,mOAAmO;AAC3O,EAAE,QAAQ,EAAE;AACZ,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,8BAA8B;AAC7C,MAAM,WAAW,EAAE;AACnB;AACA,GAAG;AACH,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,SAAS;AACvC,CAAC,CAAC;AACF,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO,GAAG,SAAS;AACvB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AAC5D,MAAM,GAAG,SAAS;AAClB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,WAAW,EAAE,mBAAmB;AACtC,MAAM,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,gGAAgG,EAAE,SAAS,CAAC,CAAC;AAClI,MAAM,GAAG;AACT,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,WAAW,EAAE,aAAa;AAChC,MAAM,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,6DAA6D,EAAE,SAAS,CAAC,CAAC;AAC/F,MAAM,GAAG;AACT,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}