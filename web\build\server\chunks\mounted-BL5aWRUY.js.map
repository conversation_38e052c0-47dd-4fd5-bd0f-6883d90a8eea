{"version": 3, "file": "mounted-BL5aWRUY.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/mounted.js"], "sourcesContent": ["import \"clsx\";\nimport { b as boxAutoReset } from \"./box-auto-reset.svelte.js\";\nimport { w as push, N as bind_props, y as pop } from \"./index3.js\";\nimport \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { n as noop } from \"./noop.js\";\nfunction chunk(arr, size) {\n  const result = [];\n  for (let i = 0; i < arr.length; i += size) {\n    result.push(arr.slice(i, i + size));\n  }\n  return result;\n}\nfunction isValidIndex(index, arr) {\n  return index >= 0 && index < arr.length;\n}\nfunction next(array, index, loop = true) {\n  if (array.length === 0 || index < 0 || index >= array.length) {\n    return void 0;\n  }\n  if (array.length === 1 && index === 0) {\n    return array[0];\n  }\n  if (index === array.length - 1) {\n    return loop ? array[0] : void 0;\n  }\n  return array[index + 1];\n}\nfunction prev(array, index, loop = true) {\n  if (array.length === 0 || index < 0 || index >= array.length) {\n    return void 0;\n  }\n  if (array.length === 1 && index === 0) {\n    return array[0];\n  }\n  if (index === 0) {\n    return loop ? array[array.length - 1] : void 0;\n  }\n  return array[index - 1];\n}\nfunction forward(array, index, increment, loop = true) {\n  if (array.length === 0 || index < 0 || index >= array.length) {\n    return void 0;\n  }\n  let targetIndex = index + increment;\n  if (loop) {\n    targetIndex = (targetIndex % array.length + array.length) % array.length;\n  } else {\n    targetIndex = Math.max(0, Math.min(targetIndex, array.length - 1));\n  }\n  return array[targetIndex];\n}\nfunction backward(array, index, decrement, loop = true) {\n  if (array.length === 0 || index < 0 || index >= array.length) {\n    return void 0;\n  }\n  let targetIndex = index - decrement;\n  if (loop) {\n    targetIndex = (targetIndex % array.length + array.length) % array.length;\n  } else {\n    targetIndex = Math.max(0, Math.min(targetIndex, array.length - 1));\n  }\n  return array[targetIndex];\n}\nfunction getNextMatch(values, search, currentMatch) {\n  const lowerSearch = search.toLowerCase();\n  if (lowerSearch.endsWith(\" \")) {\n    const searchWithoutSpace = lowerSearch.slice(0, -1);\n    const matchesWithoutSpace = values.filter((value) => value.toLowerCase().startsWith(searchWithoutSpace));\n    if (matchesWithoutSpace.length <= 1) {\n      return getNextMatch(values, searchWithoutSpace, currentMatch);\n    }\n    const currentMatchLowercase = currentMatch?.toLowerCase();\n    if (currentMatchLowercase && currentMatchLowercase.startsWith(searchWithoutSpace) && currentMatchLowercase.charAt(searchWithoutSpace.length) === \" \" && search.trim() === searchWithoutSpace) {\n      return currentMatch;\n    }\n    const spacedMatches = values.filter((value) => value.toLowerCase().startsWith(lowerSearch));\n    if (spacedMatches.length > 0) {\n      const currentMatchIndex2 = currentMatch ? values.indexOf(currentMatch) : -1;\n      let wrappedMatches = wrapArray(spacedMatches, Math.max(currentMatchIndex2, 0));\n      const nextMatch2 = wrappedMatches.find((match) => match !== currentMatch);\n      return nextMatch2 || currentMatch;\n    }\n  }\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const normalizedLowerSearch = normalizedSearch.toLowerCase();\n  const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n  let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n  const excludeCurrentMatch = normalizedSearch.length === 1;\n  if (excludeCurrentMatch)\n    wrappedValues = wrappedValues.filter((v) => v !== currentMatch);\n  const nextMatch = wrappedValues.find((value) => value?.toLowerCase().startsWith(normalizedLowerSearch));\n  return nextMatch !== currentMatch ? nextMatch : void 0;\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nfunction useDOMTypeahead(opts) {\n  const search = boxAutoReset(\"\", 1e3);\n  const onMatch = opts?.onMatch ?? ((node) => node.focus());\n  const getCurrentItem = opts?.getCurrentItem ?? (() => document.activeElement);\n  function handleTypeaheadSearch(key, candidates) {\n    if (!candidates.length) return;\n    search.current = search.current + key;\n    const currentItem = getCurrentItem();\n    const currentMatch = candidates.find((item) => item === currentItem)?.textContent?.trim() ?? \"\";\n    const values = candidates.map((item) => item.textContent?.trim() ?? \"\");\n    const nextMatch = getNextMatch(values, search.current, currentMatch);\n    const newItem = candidates.find((item) => item.textContent?.trim() === nextMatch);\n    if (newItem) onMatch(newItem);\n    return newItem;\n  }\n  function resetTypeahead() {\n    search.current = \"\";\n  }\n  return {\n    search,\n    handleTypeaheadSearch,\n    resetTypeahead\n  };\n}\nfunction Mounted($$payload, $$props) {\n  push();\n  let { mounted = false, onMountedChange = noop } = $$props;\n  bind_props($$props, { mounted });\n  pop();\n}\nexport {\n  Mounted as M,\n  backward as b,\n  chunk as c,\n  forward as f,\n  getNextMatch as g,\n  isValidIndex as i,\n  next as n,\n  prev as p,\n  useDOMTypeahead as u\n};\n"], "names": [], "mappings": ";;;;;;AAMA,SAAS,KAAK,CAAC,GAAG,EAAE,IAAI,EAAE;AAC1B,EAAE,MAAM,MAAM,GAAG,EAAE;AACnB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,IAAI,EAAE;AAC7C,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AACvC;AACA,EAAE,OAAO,MAAM;AACf;AACA,SAAS,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE;AAClC,EAAE,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,GAAG,CAAC,MAAM;AACzC;AACA,SAAS,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,GAAG,IAAI,EAAE;AACzC,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AAChE,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC;AACnB;AACA,EAAE,IAAI,KAAK,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,IAAI,OAAO,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM;AACnC;AACA,EAAE,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AACzB;AACA,SAAS,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,GAAG,IAAI,EAAE;AACzC,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AAChE,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE;AACzC,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC;AACnB;AACA,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE;AACnB,IAAI,OAAO,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM;AAClD;AACA,EAAE,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC;AACzB;AACA,SAAS,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE;AACvD,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AAChE,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,IAAI,WAAW,GAAG,KAAK,GAAG,SAAS;AACrC,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,WAAW,GAAG,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM;AAC5E,GAAG,MAAM;AACT,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACtE;AACA,EAAE,OAAO,KAAK,CAAC,WAAW,CAAC;AAC3B;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE;AACxD,EAAE,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;AAChE,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,IAAI,WAAW,GAAG,KAAK,GAAG,SAAS;AACrC,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,WAAW,GAAG,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM;AAC5E,GAAG,MAAM;AACT,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACtE;AACA,EAAE,OAAO,KAAK,CAAC,WAAW,CAAC;AAC3B;AACA,SAAS,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE;AACpD,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAE;AAC1C,EAAE,IAAI,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACjC,IAAI,MAAM,kBAAkB,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;AACvD,IAAI,MAAM,mBAAmB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC;AAC5G,IAAI,IAAI,mBAAmB,CAAC,MAAM,IAAI,CAAC,EAAE;AACzC,MAAM,OAAO,YAAY,CAAC,MAAM,EAAE,kBAAkB,EAAE,YAAY,CAAC;AACnE;AACA,IAAI,MAAM,qBAAqB,GAAG,YAAY,EAAE,WAAW,EAAE;AAC7D,IAAI,IAAI,qBAAqB,IAAI,qBAAqB,CAAC,UAAU,CAAC,kBAAkB,CAAC,IAAI,qBAAqB,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,kBAAkB,EAAE;AAClM,MAAM,OAAO,YAAY;AACzB;AACA,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;AAC/F,IAAI,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,MAAM,MAAM,kBAAkB,GAAG,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE;AACjF,MAAM,IAAI,cAAc,GAAG,SAAS,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AACpF,MAAM,MAAM,UAAU,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,YAAY,CAAC;AAC/E,MAAM,OAAO,UAAU,IAAI,YAAY;AACvC;AACA;AACA,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;AAChG,EAAE,MAAM,gBAAgB,GAAG,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM;AAC1D,EAAE,MAAM,qBAAqB,GAAG,gBAAgB,CAAC,WAAW,EAAE;AAC9D,EAAE,MAAM,iBAAiB,GAAG,YAAY,GAAG,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,EAAE;AAC5E,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;AACvE,EAAE,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,MAAM,KAAK,CAAC;AAC3D,EAAE,IAAI,mBAAmB;AACzB,IAAI,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,YAAY,CAAC;AACnE,EAAE,MAAM,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE,WAAW,EAAE,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;AACzG,EAAE,OAAO,SAAS,KAAK,YAAY,GAAG,SAAS,GAAG,MAAM;AACxD;AACA,SAAS,SAAS,CAAC,KAAK,EAAE,UAAU,EAAE;AACtC,EAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,KAAK,KAAK,CAAC,CAAC,UAAU,GAAG,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;AAC5E;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,MAAM,GAAG,YAAY,CAAC,EAAE,EAAE,GAAG,CAAC;AACtC,EAAE,MAAM,OAAO,GAAG,IAAI,EAAE,OAAO,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;AAC3D,EAAE,MAAM,cAAc,GAAG,IAAI,EAAE,cAAc,KAAK,MAAM,QAAQ,CAAC,aAAa,CAAC;AAC/E,EAAE,SAAS,qBAAqB,CAAC,GAAG,EAAE,UAAU,EAAE;AAClD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAC5B,IAAI,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG;AACzC,IAAI,MAAM,WAAW,GAAG,cAAc,EAAE;AACxC,IAAI,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,KAAK,WAAW,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE;AACnG,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AAC3E,IAAI,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC;AACxE,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,SAAS,CAAC;AACrF,IAAI,IAAI,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,OAAO;AAClB;AACA,EAAE,SAAS,cAAc,GAAG;AAC5B,IAAI,MAAM,CAAC,OAAO,GAAG,EAAE;AACvB;AACA,EAAE,OAAO;AACT,IAAI,MAAM;AACV,IAAI,qBAAqB;AACzB,IAAI;AACJ,GAAG;AACH;AACA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,OAAO;AAC3D,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,CAAC;AAClC,EAAE,GAAG,EAAE;AACP;;;;"}