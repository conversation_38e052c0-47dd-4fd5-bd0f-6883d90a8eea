{"version": 3, "file": "tabs-trigger-Zq-B9CEL.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/tabs-trigger.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, N as bind_props, y as pop, w as push, Q as spread_props } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { b as Tabs_trigger$1 } from \"./index9.js\";\nfunction Tabs_trigger($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Tabs_trigger$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"tabs-trigger\",\n        class: cn(\"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/50 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 whitespace-nowrap rounded-md border border-transparent px-2 py-1 text-sm font-medium transition-[color,box-shadow] focus-visible:outline-1 focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Tabs_trigger as T\n};\n"], "names": [], "mappings": ";;;;AAGA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC;AAC5C,MAAM;AACN,QAAQ,WAAW,EAAE,cAAc;AACnC,QAAQ,KAAK,EAAE,EAAE,CAAC,moBAAmoB,EAAE,SAAS;AAChqB,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}