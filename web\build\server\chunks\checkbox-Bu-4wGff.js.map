{"version": 3, "file": "checkbox-Bu-4wGff.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/checkbox.js"], "sourcesContent": ["import { J as derived, w as push, Q as spread_props, y as pop, M as spread_attributes, N as bind_props, O as copy_payload, P as assign_payload } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { w as watch, b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport \"clsx\";\nimport { s as snapshot } from \"./clone.js\";\nimport { C as Context } from \"./context.js\";\nimport { i as ENTER, S as SPACE, e as getDataDisabled, w as getAriaRequired, x as getAriaChecked } from \"./kbd-constants.js\";\nimport { H as Hidden_input } from \"./hidden-input.js\";\nimport { u as useId } from \"./use-id.js\";\nimport { C as Check } from \"./check2.js\";\nimport { I as Icon } from \"./Icon2.js\";\nconst CHECKBOX_ROOT_ATTR = \"data-checkbox-root\";\nclass CheckboxRootState {\n  opts;\n  group;\n  #trueName = derived(() => {\n    if (this.group && this.group.opts.name.current) {\n      return this.group.opts.name.current;\n    } else {\n      return this.opts.name.current;\n    }\n  });\n  get trueName() {\n    return this.#trueName();\n  }\n  set trueName($$value) {\n    return this.#trueName($$value);\n  }\n  #trueRequired = derived(() => {\n    if (this.group && this.group.opts.required.current) {\n      return true;\n    }\n    return this.opts.required.current;\n  });\n  get trueRequired() {\n    return this.#trueRequired();\n  }\n  set trueRequired($$value) {\n    return this.#trueRequired($$value);\n  }\n  #trueDisabled = derived(() => {\n    if (this.group && this.group.opts.disabled.current) {\n      return true;\n    }\n    return this.opts.disabled.current;\n  });\n  get trueDisabled() {\n    return this.#trueDisabled();\n  }\n  set trueDisabled($$value) {\n    return this.#trueDisabled($$value);\n  }\n  constructor(opts, group = null) {\n    this.opts = opts;\n    this.group = group;\n    this.onkeydown = this.onkeydown.bind(this);\n    this.onclick = this.onclick.bind(this);\n    useRefById(opts);\n    watch.pre(\n      [\n        () => snapshot(this.group?.opts.value.current),\n        () => this.opts.value.current\n      ],\n      ([groupValue, value]) => {\n        if (!groupValue || !value) return;\n        this.opts.checked.current = groupValue.includes(value);\n      }\n    );\n    watch.pre(() => this.opts.checked.current, (checked) => {\n      if (!this.group) return;\n      if (checked) {\n        this.group?.addValue(this.opts.value.current);\n      } else {\n        this.group?.removeValue(this.opts.value.current);\n      }\n    });\n  }\n  onkeydown(e) {\n    if (this.opts.disabled.current) return;\n    if (e.key === ENTER) e.preventDefault();\n    if (e.key === SPACE) {\n      e.preventDefault();\n      this.#toggle();\n    }\n  }\n  #toggle() {\n    if (this.opts.indeterminate.current) {\n      this.opts.indeterminate.current = false;\n      this.opts.checked.current = true;\n    } else {\n      this.opts.checked.current = !this.opts.checked.current;\n    }\n  }\n  onclick(_) {\n    if (this.opts.disabled.current) return;\n    this.#toggle();\n  }\n  #snippetProps = derived(() => ({\n    checked: this.opts.checked.current,\n    indeterminate: this.opts.indeterminate.current\n  }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"checkbox\",\n    type: this.opts.type.current,\n    disabled: this.trueDisabled,\n    \"aria-checked\": getAriaChecked(this.opts.checked.current, this.opts.indeterminate.current),\n    \"aria-required\": getAriaRequired(this.trueRequired),\n    \"data-disabled\": getDataDisabled(this.trueDisabled),\n    \"data-state\": getCheckboxDataState(this.opts.checked.current, this.opts.indeterminate.current),\n    [CHECKBOX_ROOT_ATTR]: \"\",\n    //\n    onclick: this.onclick,\n    onkeydown: this.onkeydown\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass CheckboxInputState {\n  root;\n  #trueChecked = derived(() => {\n    if (this.root.group) {\n      if (this.root.opts.value.current !== void 0 && this.root.group.opts.value.current.includes(this.root.opts.value.current)) {\n        return true;\n      }\n      return false;\n    }\n    return this.root.opts.checked.current;\n  });\n  get trueChecked() {\n    return this.#trueChecked();\n  }\n  set trueChecked($$value) {\n    return this.#trueChecked($$value);\n  }\n  #shouldRender = derived(() => Boolean(this.root.trueName));\n  get shouldRender() {\n    return this.#shouldRender();\n  }\n  set shouldRender($$value) {\n    return this.#shouldRender($$value);\n  }\n  constructor(root) {\n    this.root = root;\n  }\n  #props = derived(() => ({\n    type: \"checkbox\",\n    checked: this.root.opts.checked.current === true,\n    disabled: this.root.trueDisabled,\n    required: this.root.trueRequired,\n    name: this.root.trueName,\n    value: this.root.opts.value.current\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nfunction getCheckboxDataState(checked, indeterminate) {\n  if (indeterminate) return \"indeterminate\";\n  return checked ? \"checked\" : \"unchecked\";\n}\nconst CheckboxGroupContext = new Context(\"Checkbox.Group\");\nconst CheckboxRootContext = new Context(\"Checkbox.Root\");\nfunction useCheckboxRoot(props, group) {\n  return CheckboxRootContext.set(new CheckboxRootState(props, group));\n}\nfunction useCheckboxInput() {\n  return new CheckboxInputState(CheckboxRootContext.get());\n}\nfunction Checkbox_input($$payload, $$props) {\n  push();\n  const inputState = useCheckboxInput();\n  if (inputState.shouldRender) {\n    $$payload.out += \"<!--[-->\";\n    Hidden_input($$payload, spread_props([inputState.props]));\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]-->`;\n  pop();\n}\nfunction Checkbox$1($$payload, $$props) {\n  push();\n  let {\n    checked = false,\n    ref = null,\n    onCheckedChange,\n    children,\n    disabled = false,\n    required = false,\n    name = void 0,\n    value = \"on\",\n    id = useId(),\n    indeterminate = false,\n    onIndeterminateChange,\n    child,\n    type = \"button\",\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const group = CheckboxGroupContext.getOr(null);\n  if (group && value) {\n    if (group.opts.value.current.includes(value)) {\n      checked = true;\n    } else {\n      checked = false;\n    }\n  }\n  watch.pre(() => value, () => {\n    if (group && value) {\n      if (group.opts.value.current.includes(value)) {\n        checked = true;\n      } else {\n        checked = false;\n      }\n    }\n  });\n  const rootState = useCheckboxRoot(\n    {\n      checked: box.with(() => checked, (v) => {\n        checked = v;\n        onCheckedChange?.(v);\n      }),\n      disabled: box.with(() => disabled ?? false),\n      required: box.with(() => required),\n      name: box.with(() => name),\n      value: box.with(() => value),\n      id: box.with(() => id),\n      ref: box.with(() => ref, (v) => ref = v),\n      indeterminate: box.with(() => indeterminate, (v) => {\n        indeterminate = v;\n        onIndeterminateChange?.(v);\n      }),\n      type: box.with(() => type)\n    },\n    group\n  );\n  const mergedProps = mergeProps({ ...restProps }, rootState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps, ...rootState.snippetProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<button${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload, rootState.snippetProps);\n    $$payload.out += `<!----></button>`;\n  }\n  $$payload.out += `<!--]--> `;\n  Checkbox_input($$payload);\n  $$payload.out += `<!---->`;\n  bind_props($$props, { checked, ref, indeterminate });\n  pop();\n}\nfunction Minus($$payload, $$props) {\n  push();\n  let { $$slots, $$events, ...props } = $$props;\n  const iconNode = [[\"path\", { \"d\": \"M5 12h14\" }]];\n  Icon($$payload, spread_props([\n    { name: \"minus\" },\n    props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        props.children?.($$payload2);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n  pop();\n}\nfunction Checkbox($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    checked = false,\n    indeterminate = false,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    {\n      let children = function($$payload3, { checked: checked2, indeterminate: indeterminate2 }) {\n        $$payload3.out += `<div data-slot=\"checkbox-indicator\" class=\"text-current transition-none\">`;\n        if (checked2) {\n          $$payload3.out += \"<!--[-->\";\n          Check($$payload3, { class: \"size-3.5\" });\n        } else if (indeterminate2) {\n          $$payload3.out += \"<!--[1-->\";\n          Minus($$payload3, { class: \"size-3.5\" });\n        } else {\n          $$payload3.out += \"<!--[!-->\";\n        }\n        $$payload3.out += `<!--]--></div>`;\n      };\n      Checkbox$1($$payload2, spread_props([\n        {\n          \"data-slot\": \"checkbox\",\n          class: cn(\"border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive shadow-xs peer flex size-4 shrink-0 items-center justify-center rounded-[4px] border outline-none transition-shadow focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\", className)\n        },\n        restProps,\n        {\n          get ref() {\n            return ref;\n          },\n          set ref($$value) {\n            ref = $$value;\n            $$settled = false;\n          },\n          get checked() {\n            return checked;\n          },\n          set checked($$value) {\n            checked = $$value;\n            $$settled = false;\n          },\n          get indeterminate() {\n            return indeterminate;\n          },\n          set indeterminate($$value) {\n            indeterminate = $$value;\n            $$settled = false;\n          },\n          children,\n          $$slots: { default: true }\n        }\n      ]));\n    }\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref, checked, indeterminate });\n  pop();\n}\nexport {\n  Checkbox as C\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA,MAAM,kBAAkB,GAAG,oBAAoB;AAC/C,MAAM,iBAAiB,CAAC;AACxB,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM;AAC5B,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACpD,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACzC,KAAK,MAAM;AACX,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACnC;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;AAC3B;AACA,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAClC;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM;AAChC,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACxD,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AACrC,GAAG,CAAC;AACJ,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM;AAChC,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACxD,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AACrC,GAAG,CAAC;AACJ,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,WAAW,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE;AAClC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK;AACtB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,KAAK,CAAC,GAAG;AACb,MAAM;AACN,QAAQ,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACtD,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,OAAO;AACP,MAAM,CAAC,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK;AAC/B,QAAQ,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,EAAE;AACnC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC9D;AACA,KAAK;AACL,IAAI,KAAK,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,OAAO,KAAK;AAC5D,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACvB,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACrD,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACxD;AACA,KAAK,CAAC;AACN;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE,CAAC,CAAC,cAAc,EAAE;AAC3C,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AACzB,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;AACzC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,KAAK;AAC7C,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI;AACtC,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;AAC5D;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO;AACjC,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;AACtC,IAAI,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;AAC3C,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAChC,IAAI,QAAQ,EAAE,IAAI,CAAC,YAAY;AAC/B,IAAI,cAAc,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AAC9F,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;AACvD,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;AACvD,IAAI,YAAY,EAAE,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AAClG,IAAI,CAAC,kBAAkB,GAAG,EAAE;AAC5B;AACA,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,SAAS,EAAE,IAAI,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI;AACN,EAAE,YAAY,GAAG,OAAO,CAAC,MAAM;AAC/B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACzB,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAChI,QAAQ,OAAO,IAAI;AACnB;AACA,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;AACzC,GAAG,CAAC;AACJ,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;AAC9B;AACA,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACrC;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5D,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI;AACpD,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY;AACpC,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY;AACpC,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ;AAC5B,IAAI,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAChC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,SAAS,oBAAoB,CAAC,OAAO,EAAE,aAAa,EAAE;AACtD,EAAE,IAAI,aAAa,EAAE,OAAO,eAAe;AAC3C,EAAE,OAAO,OAAO,GAAG,SAAS,GAAG,WAAW;AAC1C;AACA,MAAM,oBAAoB,GAAG,IAAI,OAAO,CAAC,gBAAgB,CAAC;AAC1D,MAAM,mBAAmB,GAAG,IAAI,OAAO,CAAC,eAAe,CAAC;AACxD,SAAS,eAAe,CAAC,KAAK,EAAE,KAAK,EAAE;AACvC,EAAE,OAAO,mBAAmB,CAAC,GAAG,CAAC,IAAI,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACrE;AACA,SAAS,gBAAgB,GAAG;AAC5B,EAAE,OAAO,IAAI,kBAAkB,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC;AAC1D;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,UAAU,GAAG,gBAAgB,EAAE;AACvC,EAAE,IAAI,UAAU,CAAC,YAAY,EAAE;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7D,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,OAAO,GAAG,KAAK;AACnB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,eAAe;AACnB,IAAI,QAAQ;AACZ,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,IAAI,GAAG,MAAM;AACjB,IAAI,KAAK,GAAG,IAAI;AAChB,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,qBAAqB;AACzB,IAAI,KAAK;AACT,IAAI,IAAI,GAAG,QAAQ;AACnB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,KAAK,GAAG,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC;AAChD,EAAE,IAAI,KAAK,IAAI,KAAK,EAAE;AACtB,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AAClD,MAAM,OAAO,GAAG,IAAI;AACpB,KAAK,MAAM;AACX,MAAM,OAAO,GAAG,KAAK;AACrB;AACA;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,EAAE,MAAM;AAC/B,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE;AACxB,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACpD,QAAQ,OAAO,GAAG,IAAI;AACtB,OAAO,MAAM;AACb,QAAQ,OAAO,GAAG,KAAK;AACvB;AACA;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,eAAe;AACnC,IAAI;AACJ,MAAM,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,EAAE,CAAC,CAAC,KAAK;AAC9C,QAAQ,OAAO,GAAG,CAAC;AACnB,QAAQ,eAAe,GAAG,CAAC,CAAC;AAC5B,OAAO,CAAC;AACR,MAAM,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,CAAC;AACjD,MAAM,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACxC,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAChC,MAAM,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAClC,MAAM,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC5B,MAAM,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC9C,MAAM,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,aAAa,EAAE,CAAC,CAAC,KAAK;AAC1D,QAAQ,aAAa,GAAG,CAAC;AACzB,QAAQ,qBAAqB,GAAG,CAAC,CAAC;AAClC,OAAO,CAAC;AACR,MAAM,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI;AAC/B,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,GAAG,SAAS,EAAE,EAAE,SAAS,CAAC,KAAK,CAAC;AACnE,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,SAAS,CAAC,YAAY,EAAE,CAAC;AACvE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7E,IAAI,QAAQ,GAAG,SAAS,EAAE,SAAS,CAAC,YAAY,CAAC;AACjD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACvC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,cAAc,CAAC,SAAS,CAAC;AAC3B,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa,EAAE,CAAC;AACtD,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AAC/C,EAAE,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAC;AAClD,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;AACrB,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACpC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL,EAAE,GAAG,EAAE;AACP;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO,GAAG,KAAK;AACnB,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI;AACJ,MAAM,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,cAAc,EAAE,EAAE;AAChG,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,yEAAyE,CAAC;AACrG,QAAQ,IAAI,QAAQ,EAAE;AACtB,UAAU,UAAU,CAAC,GAAG,IAAI,UAAU;AACtC,UAAU,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;AAClD,SAAS,MAAM,IAAI,cAAc,EAAE;AACnC,UAAU,UAAU,CAAC,GAAG,IAAI,WAAW;AACvC,UAAU,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;AAClD,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,IAAI,WAAW;AACvC;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC1C,OAAO;AACP,MAAM,UAAU,CAAC,UAAU,EAAE,YAAY,CAAC;AAC1C,QAAQ;AACR,UAAU,WAAW,EAAE,UAAU;AACjC,UAAU,KAAK,EAAE,EAAE,CAAC,8gBAA8gB,EAAE,SAAS;AAC7iB,SAAS;AACT,QAAQ,SAAS;AACjB,QAAQ;AACR,UAAU,IAAI,GAAG,GAAG;AACpB,YAAY,OAAO,GAAG;AACtB,WAAW;AACX,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE;AAC3B,YAAY,GAAG,GAAG,OAAO;AACzB,YAAY,SAAS,GAAG,KAAK;AAC7B,WAAW;AACX,UAAU,IAAI,OAAO,GAAG;AACxB,YAAY,OAAO,OAAO;AAC1B,WAAW;AACX,UAAU,IAAI,OAAO,CAAC,OAAO,EAAE;AAC/B,YAAY,OAAO,GAAG,OAAO;AAC7B,YAAY,SAAS,GAAG,KAAK;AAC7B,WAAW;AACX,UAAU,IAAI,aAAa,GAAG;AAC9B,YAAY,OAAO,aAAa;AAChC,WAAW;AACX,UAAU,IAAI,aAAa,CAAC,OAAO,EAAE;AACrC,YAAY,aAAa,GAAG,OAAO;AACnC,YAAY,SAAS,GAAG,KAAK;AAC7B,WAAW;AACX,UAAU,QAAQ;AAClB,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC;AACA,OAAO,CAAC,CAAC;AACT;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC;AACtD,EAAE,GAAG,EAAE;AACP;;;;"}