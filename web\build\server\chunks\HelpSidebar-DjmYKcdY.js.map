{"version": 3, "file": "HelpSidebar-DjmYKcdY.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/HelpSidebar.js"], "sourcesContent": ["import { S as attr_class, T as clsx, U as ensure_array_like, V as escape_html, R as attr, W as stringify, y as pop, w as push } from \"./index3.js\";\nimport { A as Accordion_root, a as Accordion_item, b as Accordion_trigger, c as Accordion_content } from \"./accordion-trigger.js\";\nimport { S as Scroll_area } from \"./scroll-area.js\";\nimport { c as cn } from \"./utils.js\";\nimport { H as House } from \"./house.js\";\nimport { B as Book_open } from \"./HelpArticleCard.js\";\nimport { F as File_text } from \"./file-text.js\";\nimport { C as Credit_card } from \"./credit-card.js\";\nimport { S as Shield } from \"./shield.js\";\nimport { C as Circle_help } from \"./circle-help.js\";\nfunction HelpSidebar($$payload, $$props) {\n  push();\n  let { categories = [], className = \"\" } = $$props;\n  let expandedCategories = [];\n  let currentPath = \"\";\n  function isActive(path) {\n    return currentPath === path;\n  }\n  $$payload.out += `<div${attr_class(clsx(className))}><!---->`;\n  Scroll_area($$payload, {\n    class: \"h-[calc(100vh-10rem)]\",\n    children: ($$payload2) => {\n      $$payload2.out += `<div class=\"pr-4\"><div class=\"py-2\"><a href=\"/help\"${attr_class(clsx(cn(\"flex items-center gap-2 rounded-md px-3 py-2 text-sm font-medium\", isActive(\"/help\") ? \"bg-accent text-accent-foreground\" : \"hover:bg-accent/50\")))}><!---->`;\n      House($$payload2, { class: \"h-4 w-4\" });\n      $$payload2.out += `<!----> <span>Help Home</span></a></div> <!---->`;\n      Accordion_root($$payload2, {\n        type: \"multiple\",\n        value: expandedCategories,\n        onValueChange: (value) => expandedCategories = value,\n        class: \"space-y-1\",\n        children: ($$payload3) => {\n          const each_array = ensure_array_like(categories);\n          $$payload3.out += `<!--[-->`;\n          for (let $$index_2 = 0, $$length = each_array.length; $$index_2 < $$length; $$index_2++) {\n            let category = each_array[$$index_2];\n            $$payload3.out += `<!---->`;\n            Accordion_item($$payload3, {\n              value: category.id,\n              class: \"border-none\",\n              children: ($$payload4) => {\n                $$payload4.out += `<!---->`;\n                Accordion_trigger($$payload4, {\n                  class: cn(\"flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium\", isActive(`/help/category/${category.slug}`) ? \"bg-accent text-accent-foreground\" : \"hover:bg-accent/50\"),\n                  children: ($$payload5) => {\n                    $$payload5.out += `<div class=\"flex items-center gap-2\">`;\n                    if (category.icon) {\n                      $$payload5.out += \"<!--[-->\";\n                      if (category.icon === \"BookOpen\") {\n                        $$payload5.out += \"<!--[-->\";\n                        $$payload5.out += `<!---->`;\n                        Book_open($$payload5, { class: \"h-4 w-4\" });\n                        $$payload5.out += `<!---->`;\n                      } else if (category.icon === \"FileText\") {\n                        $$payload5.out += \"<!--[1-->\";\n                        $$payload5.out += `<!---->`;\n                        File_text($$payload5, { class: \"h-4 w-4\" });\n                        $$payload5.out += `<!---->`;\n                      } else if (category.icon === \"CreditCard\") {\n                        $$payload5.out += \"<!--[2-->\";\n                        $$payload5.out += `<!---->`;\n                        Credit_card($$payload5, { class: \"h-4 w-4\" });\n                        $$payload5.out += `<!---->`;\n                      } else if (category.icon === \"Shield\") {\n                        $$payload5.out += \"<!--[3-->\";\n                        $$payload5.out += `<!---->`;\n                        Shield($$payload5, { class: \"h-4 w-4\" });\n                        $$payload5.out += `<!---->`;\n                      } else {\n                        $$payload5.out += \"<!--[!-->\";\n                        $$payload5.out += `<!---->`;\n                        Circle_help($$payload5, { class: \"h-4 w-4\" });\n                        $$payload5.out += `<!---->`;\n                      }\n                      $$payload5.out += `<!--]-->`;\n                    } else {\n                      $$payload5.out += \"<!--[!-->\";\n                      $$payload5.out += `<!---->`;\n                      Circle_help($$payload5, { class: \"h-4 w-4\" });\n                      $$payload5.out += `<!---->`;\n                    }\n                    $$payload5.out += `<!--]--> <span>${escape_html(category.name)}</span></div>`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload4.out += `<!----> <!---->`;\n                Accordion_content($$payload4, {\n                  class: \"pb-0 pt-1\",\n                  children: ($$payload5) => {\n                    $$payload5.out += `<div class=\"ml-6 space-y-1\">`;\n                    if (category.children && category.children.length > 0) {\n                      $$payload5.out += \"<!--[-->\";\n                      const each_array_1 = ensure_array_like(category.children);\n                      $$payload5.out += `<!--[-->`;\n                      for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {\n                        let subCategory = each_array_1[$$index];\n                        $$payload5.out += `<a${attr(\"href\", `/help/category/${stringify(subCategory.slug)}`)}${attr_class(clsx(cn(\"flex items-center gap-2 rounded-md px-3 py-2 text-sm\", isActive(`/help/category/${subCategory.slug}`) ? \"bg-accent text-accent-foreground\" : \"hover:bg-accent/50\")))}>${escape_html(subCategory.name)}</a>`;\n                      }\n                      $$payload5.out += `<!--]-->`;\n                    } else {\n                      $$payload5.out += \"<!--[!-->\";\n                    }\n                    $$payload5.out += `<!--]--> `;\n                    if (category.articles && category.articles.length > 0) {\n                      $$payload5.out += \"<!--[-->\";\n                      const each_array_2 = ensure_array_like(category.articles);\n                      $$payload5.out += `<!--[-->`;\n                      for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {\n                        let article = each_array_2[$$index_1];\n                        $$payload5.out += `<a${attr(\"href\", `/help/${stringify(article.slug)}`)}${attr_class(clsx(cn(\"flex items-center gap-2 rounded-md px-3 py-2 text-sm\", isActive(`/help/${article.slug}`) ? \"bg-accent text-accent-foreground\" : \"hover:bg-accent/50\")))}>${escape_html(article.title)}</a>`;\n                      }\n                      $$payload5.out += `<!--]-->`;\n                    } else {\n                      $$payload5.out += \"<!--[!-->\";\n                    }\n                    $$payload5.out += `<!--]--></div>`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload4.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload3.out += `<!---->`;\n          }\n          $$payload3.out += `<!--]-->`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----></div>`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div>`;\n  pop();\n}\nexport {\n  HelpSidebar as H\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAUA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,OAAO;AACnD,EAAE,IAAI,kBAAkB,GAAG,EAAE;AAC7B,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE;AAC1B,IAAI,OAAO,WAAW,KAAK,IAAI;AAC/B;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC;AAC/D,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB,IAAI,KAAK,EAAE,uBAAuB;AAClC,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,mDAAmD,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,kEAAkE,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,kCAAkC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AAC/P,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC7C,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,gDAAgD,CAAC;AAC1E,MAAM,cAAc,CAAC,UAAU,EAAE;AACjC,QAAQ,IAAI,EAAE,UAAU;AACxB,QAAQ,KAAK,EAAE,kBAAkB;AACjC,QAAQ,aAAa,EAAE,CAAC,KAAK,KAAK,kBAAkB,GAAG,KAAK;AAC5D,QAAQ,KAAK,EAAE,WAAW;AAC1B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;AAC1D,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AACnG,YAAY,IAAI,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAC;AAChD,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,YAAY,cAAc,CAAC,UAAU,EAAE;AACvC,cAAc,KAAK,EAAE,QAAQ,CAAC,EAAE;AAChC,cAAc,KAAK,EAAE,aAAa;AAClC,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,gBAAgB,iBAAiB,CAAC,UAAU,EAAE;AAC9C,kBAAkB,KAAK,EAAE,EAAE,CAAC,mFAAmF,EAAE,QAAQ,CAAC,CAAC,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,kCAAkC,GAAG,oBAAoB,CAAC;AACzN,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,qCAAqC,CAAC;AAC7E,oBAAoB,IAAI,QAAQ,CAAC,IAAI,EAAE;AACvC,sBAAsB,UAAU,CAAC,GAAG,IAAI,UAAU;AAClD,sBAAsB,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE;AACxD,wBAAwB,UAAU,CAAC,GAAG,IAAI,UAAU;AACpD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,wBAAwB,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACnE,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,uBAAuB,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE;AAC/D,wBAAwB,UAAU,CAAC,GAAG,IAAI,WAAW;AACrD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,wBAAwB,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACnE,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,uBAAuB,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,EAAE;AACjE,wBAAwB,UAAU,CAAC,GAAG,IAAI,WAAW;AACrD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,wBAAwB,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACrE,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,uBAAuB,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC7D,wBAAwB,UAAU,CAAC,GAAG,IAAI,WAAW;AACrD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,wBAAwB,MAAM,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAChE,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,uBAAuB,MAAM;AAC7B,wBAAwB,UAAU,CAAC,GAAG,IAAI,WAAW;AACrD,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD,wBAAwB,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACrE,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnD;AACA,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClD,qBAAqB,MAAM;AAC3B,sBAAsB,UAAU,CAAC,GAAG,IAAI,WAAW;AACnD,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjD,sBAAsB,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACnE,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjD;AACA,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;AACjG,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACnD,gBAAgB,iBAAiB,CAAC,UAAU,EAAE;AAC9C,kBAAkB,KAAK,EAAE,WAAW;AACpC,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,4BAA4B,CAAC;AACpE,oBAAoB,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3E,sBAAsB,UAAU,CAAC,GAAG,IAAI,UAAU;AAClD,sBAAsB,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC/E,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClD,sBAAsB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,EAAE;AAC7G,wBAAwB,IAAI,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC;AAC/D,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,eAAe,EAAE,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,sDAAsD,EAAE,QAAQ,CAAC,CAAC,eAAe,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,kCAAkC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;AAC9U;AACA,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClD,qBAAqB,MAAM;AAC3B,sBAAsB,UAAU,CAAC,GAAG,IAAI,WAAW;AACnD;AACA,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACjD,oBAAoB,IAAI,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3E,sBAAsB,UAAU,CAAC,GAAG,IAAI,UAAU;AAClD,sBAAsB,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC/E,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClD,sBAAsB,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACnH,wBAAwB,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7D,wBAAwB,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,sDAAsD,EAAE,QAAQ,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,kCAAkC,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACjT;AACA,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClD,qBAAqB,MAAM;AAC3B,sBAAsB,UAAU,CAAC,GAAG,IAAI,WAAW;AACnD;AACA,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACtD,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACvC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,GAAG,EAAE;AACP;;;;"}