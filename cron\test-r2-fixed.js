// Test R2 connection with fixes from Cloudflare community
import { S3Client, ListBucketsCommand } from '@aws-sdk/client-s3';
import https from 'https';

// Force TLS 1.2 via environment
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '1';

console.log('🧪 Testing R2 with SSL fixes...');

// Test different configurations based on Cloudflare community solutions
const configs = [
  {
    name: 'Fixed Endpoint (from env)',
    config: {
      endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
      region: 'auto',
      credentials: {
        accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
        secretAccessKey: '****************************************************************',
      },
      forcePathStyle: true,
    }
  },
  {
    name: 'With Custom HTTPS Agent (TLS 1.2)',
    config: {
      endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
      region: 'auto',
      credentials: {
        accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
        secretAccessKey: '****************************************************************',
      },
      forcePathStyle: true,
      requestHandler: {
        httpsAgent: new https.Agent({
          keepAlive: true,
          rejectUnauthorized: true,
          secureProtocol: 'TLSv1_2_method',
          servername: '46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com'
        })
      }
    }
  },
  {
    name: 'Alternative Region',
    config: {
      endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
      region: 'us-east-1',
      credentials: {
        accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
        secretAccessKey: '****************************************************************',
      },
      forcePathStyle: true,
    }
  }
];

async function testConfigs() {
  for (const { name, config } of configs) {
    console.log(`\n📋 Testing: ${name}`);
    
    try {
      const client = new S3Client(config);
      const command = new ListBucketsCommand({});
      
      console.log('   🔄 Attempting connection...');
      const response = await client.send(command);
      
      console.log(`   ✅ Success! Found ${response.Buckets?.length || 0} buckets`);
      if (response.Buckets && response.Buckets.length > 0) {
        console.log('   📦 Buckets:', response.Buckets.map(b => b.Name).join(', '));
      }
      
      // If this config works, we can stop here
      console.log(`   🎉 ${name} works! Using this configuration.`);
      return { success: true, config, name };
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   🔍 Error code: ${error.code || 'Unknown'}`);
      
      // Check if it's still the same SSL error
      if (error.message.includes('handshake failure')) {
        console.log('   🚨 Still SSL handshake failure');
      }
    }
  }
  
  return { success: false };
}

testConfigs()
  .then((result) => {
    if (result.success) {
      console.log(`\n🎉 Found working configuration: ${result.name}`);
    } else {
      console.log('\n❌ All configurations failed');
    }
  })
  .catch((error) => {
    console.error('💥 Test failed:', error);
  });
