import { p as prisma } from './prisma-Cit_HrSw.js';
import { f as fail, r as redirect } from './index-Ddp2AB5f.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, s as stringType, e as enumType, b as booleanType, a as arrayType, d as anyType } from './types-D78SXuvm.js';
import '@prisma/client';
import './constants-BaiUsPxc.js';
import './_commonjsHelpers-BFTU3MAI.js';

const maintenanceSchema = objectType({
  id: stringType().optional(),
  title: stringType().min(1, { message: "Title is required" }),
  description: stringType().min(1, { message: "Description is required" }),
  startTime: stringType().min(1, { message: "Start time is required" }),
  endTime: stringType().min(1, { message: "End time is required" }),
  status: enumType(["scheduled", "in-progress", "completed", "cancelled"], {
    required_error: "Status is required"
  }),
  severity: enumType(["info", "maintenance", "minor", "major", "critical"], {
    required_error: "Severity is required"
  }),
  affectedServices: arrayType(stringType()).min(1, { message: "At least one affected service is required" }),
  sendNotification: booleanType().default(false),
  comment: stringType().optional(),
  commentStatus: enumType(["investigating", "identified", "in-progress", "monitoring", "resolved"]).optional()
});
objectType({
  eventId: stringType(),
  changeType: enumType(["status_change", "update", "comment"]),
  previousStatus: stringType().optional(),
  newStatus: stringType().optional(),
  comment: stringType().optional(),
  metadata: anyType().optional()
});
const load = async ({ locals }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  const userData = await prisma.user.findUnique({
    where: { id: user.id },
    select: { isAdmin: true, role: true }
  });
  if (!userData || !userData.isAdmin && userData.role !== "admin") {
    throw redirect(302, "/dashboard/settings");
  }
  const createForm = await superValidate(zod(maintenanceSchema));
  const editForm = await superValidate(zod(maintenanceSchema));
  const deleteForm = await superValidate(zod(objectType({ id: stringType() })));
  try {
    let maintenanceEvents = [];
    let upcomingEvents = [];
    let pastEvents = [];
    try {
      try {
        const allEvents = await prisma.$queryRaw`
          SELECT * FROM "web"."MaintenanceEvent"
          ORDER BY "startTime" DESC
        `;
        maintenanceEvents = Array.isArray(allEvents) ? allEvents : [];
        const upcoming = await prisma.$queryRaw`
          SELECT * FROM "web"."MaintenanceEvent"
          WHERE "startTime" >= NOW()
          AND "status" IN ('scheduled', 'in-progress')
          ORDER BY "startTime" ASC
        `;
        upcomingEvents = Array.isArray(upcoming) ? upcoming : [];
        const past = await prisma.$queryRaw`
          SELECT * FROM "web"."MaintenanceEvent"
          WHERE "endTime" < NOW()
          OR "status" IN ('completed', 'cancelled')
          ORDER BY "startTime" DESC
          LIMIT 10
        `;
        pastEvents = Array.isArray(past) ? past : [];
      } catch (sqlError) {
        console.warn("Error fetching maintenance events with raw SQL:", sqlError);
      }
    } catch (dbError) {
      console.warn("MaintenanceEvent table may not exist yet:", dbError);
    }
    return {
      maintenanceEvents,
      upcomingEvents,
      pastEvents,
      createForm,
      editForm,
      deleteForm
    };
  } catch (error) {
    console.error("Error loading maintenance events:", error);
    return {
      maintenanceEvents: [],
      upcomingEvents: [],
      pastEvents: [],
      createForm,
      editForm,
      deleteForm
    };
  }
};
const actions = {
  // Create a new maintenance event
  create: async ({ request, locals, fetch }) => {
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return fail(401, { success: false, error: "Unauthorized" });
    }
    const form = await superValidate(request, zod(maintenanceSchema));
    if (!form.valid) {
      return fail(400, { form, success: false });
    }
    try {
      const response = await fetch("/api/maintenance", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(form.data),
        credentials: "include"
      });
      const result = await response.json();
      if (!response.ok) {
        return fail(response.status, {
          form,
          success: false,
          error: result.error || "Failed to create maintenance event"
        });
      }
      return {
        form,
        success: true
      };
    } catch (error) {
      console.error("Error creating maintenance event:", error);
      return fail(500, {
        form,
        success: false,
        error: "Failed to create maintenance event"
      });
    }
  },
  // Update a maintenance event
  update: async ({ request, locals, fetch }) => {
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return fail(401, { success: false, error: "Unauthorized" });
    }
    const form = await superValidate(request, zod(maintenanceSchema));
    if (!form.valid) {
      return fail(400, { form, success: false });
    }
    try {
      const response = await fetch("/api/maintenance", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(form.data),
        credentials: "include"
      });
      const result = await response.json();
      if (!response.ok) {
        return fail(response.status, {
          form,
          success: false,
          error: result.error || "Failed to update maintenance event"
        });
      }
      return {
        form,
        success: true
      };
    } catch (error) {
      console.error("Error updating maintenance event:", error);
      return fail(500, {
        form,
        success: false,
        error: "Failed to update maintenance event"
      });
    }
  },
  // Delete a maintenance event
  delete: async ({ request, locals, fetch }) => {
    const user = locals.user;
    if (!user || !user.isAdmin) {
      return fail(401, { success: false, error: "Unauthorized" });
    }
    const form = await superValidate(request, zod(objectType({ id: stringType() })));
    if (!form.valid) {
      return fail(400, { form, success: false });
    }
    try {
      const response = await fetch("/api/maintenance", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ id: form.data.id }),
        credentials: "include"
      });
      if (!response.ok) {
        const result = await response.json();
        return fail(response.status, {
          form,
          success: false,
          error: result.error || "Failed to delete maintenance event"
        });
      }
      return {
        form,
        success: true
      };
    } catch (error) {
      console.error("Error deleting maintenance event:", error);
      return fail(500, {
        form,
        success: false,
        error: "Failed to delete maintenance event"
      });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 53;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-BgHaJFIY.js')).default;
const server_id = "src/routes/dashboard/settings/admin/maintenance/+page.server.ts";
const imports = ["_app/immutable/nodes/53.CxZG3bcl.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/B3MJtjra.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/D0KcwhQz.js","_app/immutable/chunks/CKg8MWp_.js","_app/immutable/chunks/BAIxhb6t.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/CTO_B1Jk.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/DvO_AOqy.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/BuYRPDDz.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/QtAhPN2H.js","_app/immutable/chunks/qwsZpUIl.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/CWmzcjye.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/VNuMAkuB.js","_app/immutable/chunks/T7uRAIbG.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/BG1dFdGG.js","_app/immutable/chunks/Z9Zpt0fH.js","_app/immutable/chunks/B2lQHLf_.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/CqJi5rQC.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/DR5zc253.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/DmZyh-PW.js","_app/immutable/chunks/DumgozFE.js","_app/immutable/chunks/C33xR25f.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/index.CV-KWLNP.css","_app/immutable/assets/scroll-area.bHHIbcsu.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=53-DhSI32tb.js.map
