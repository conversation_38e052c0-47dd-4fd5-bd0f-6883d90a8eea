import { p as push, S as store_get, V as copy_payload, W as assign_payload, T as unsubscribe_stores, Q as bind_props, q as pop, M as ensure_array_like, P as stringify, N as attr, J as attr_class, K as fallback, O as escape_html } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { B as Button } from './button-CrucCo1G.js';
import { S as Separator } from './separator-5ooeI4XN.js';
import { L as Loader_circle } from './loader-circle-BG7dEt2I.js';
import { C as Check } from './check-WP_4Msti.js';
import { I as Info } from './info-Ce09B-Yv.js';
import { X } from './x-DwZgpWRG.js';
import { S as Switch } from './switch-CwRjBz3R.js';
import { w as writable } from './index2-Cut0V_vU.js';
import { R as Root, P as Portal, d as Dialog_overlay, D as Dialog_content } from './index7-BURUpWjT.js';
import { S as SignIn } from './SignIn-BT0umdgr.js';
import { D as Dialog_trigger } from './dialog-trigger2-C3bA-tk4.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description, c as Dialog_footer } from './dialog-description-CxPAHL_4.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-id-CcFpwo20.js';
import './Icon-A4vzmk-O.js';
import './context-oepKpCf5.js';
import './hidden-input-1eDzjGOB.js';
import './noop-n4I-x7yK.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './dialog-overlay-CspOQRJq.js';
import './presence-layer-B0FVaAYL.js';
import './checkbox-Bu-4wGff.js';
import './clone-BRGVxGEr.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import '@simplewebauthn/browser';
import './dialog-trigger-CNXm7UD7.js';
import './dialog-description2-rfr-pd9k.js';

function PricingCard($$payload, $$props) {
  push();
  let title = $$props["title"];
  let price = $$props["price"];
  let description = $$props["description"];
  let isPopular = fallback($$props["isPopular"], false);
  let ctaText = $$props["ctaText"];
  let billingCycle = $$props["billingCycle"];
  let limits = fallback($$props["limits"], () => ({}), true);
  let features = fallback($$props["features"], () => [], true);
  let onCtaClick = $$props["onCtaClick"];
  let disabled = fallback($$props["disabled"], false);
  let activePlan = fallback($$props["activePlan"], false);
  let loading = fallback($$props["loading"], false);
  const each_array = ensure_array_like(features);
  $$payload.out += `<div${attr_class(`border-border rounded-lg border shadow-sm ${stringify(isPopular ? "border-primary shadow-primary/20 relative" : "")}`)}>`;
  if (isPopular) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="absolute inset-x-0 top-0 -translate-y-1/2 transform"><div class="bg-primary text-primary-foreground inline-block rounded-full px-4 py-1 text-xs font-semibold uppercase tracking-wider">Most Popular</div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="p-7"><h3 class="text-foreground text-3xl font-semibold">${escape_html(title)}</h3> <p class="text-muted-foreground mt-4 h-12">${escape_html(description)}</p></div> `;
  Separator($$payload, { class: "bg-border my-4" });
  $$payload.out += `<!----> <div class="align-center mt-4 flex min-h-20 flex-col justify-center text-center"><div><span class="font-semi-bold text-5xl">`;
  if (title.toLowerCase() === "free") {
    $$payload.out += "<!--[-->";
    $$payload.out += `Free`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span class="mr-1 align-super text-sm">$</span>${escape_html(price)} <span class="text-lg">/ month</span>`;
  }
  $$payload.out += `<!--]--></span> `;
  if (title.toLowerCase() === "custom") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span>/ seat</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> `;
  if (title.toLowerCase() !== "free") {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="text-muted-foreground mt-2 text-sm">Billed ${escape_html(billingCycle)}</span>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="mt-6 px-8">`;
  Button($$payload, {
    class: "w-full",
    variant: isPopular ? "default" : "outline",
    disabled: disabled || loading,
    onclick: onCtaClick,
    children: ($$payload2) => {
      if (loading) {
        $$payload2.out += "<!--[-->";
        Loader_circle($$payload2, { class: "mr-2 h-4 w-4 animate-spin" });
        $$payload2.out += `<!----> Processing...`;
      } else if (activePlan) {
        $$payload2.out += "<!--[1-->";
        $$payload2.out += `Current Plan`;
      } else {
        $$payload2.out += "<!--[!-->";
        $$payload2.out += `${escape_html(ctaText)}`;
      }
      $$payload2.out += `<!--]-->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> `;
  Separator($$payload, { class: "bg-border mt-4" });
  $$payload.out += `<!----> <div class="mt-6 px-8"><div class="min-h-[300px]"><div class="mb-4"><h4 class="text-muted-foreground mb-2 text-xs font-medium uppercase">Plan Limits</h4> <div class="grid grid-cols-1 gap-2 sm:grid-cols-3"><div class="rounded-md border p-3 text-center"><p class="text-muted-foreground mb-1 text-xs">Resume Scans</p> <p class="flex items-center justify-center text-lg font-semibold">`;
  if (title.toLowerCase() === "free") {
    $$payload.out += "<!--[-->";
    $$payload.out += `10<span class="ml-1 text-xs font-normal">/mo</span>`;
  } else if (limits?.resumesPerMonth !== void 0 && limits.resumesPerMonth !== null && limits.resumesPerMonth !== "U") {
    $$payload.out += "<!--[1-->";
    $$payload.out += `${escape_html(limits.resumesPerMonth)} <span class="ml-1 text-xs font-normal">/mo</span>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span class="text-lg">∞</span>`;
  }
  $$payload.out += `<!--]--></p></div> <div class="rounded-md border p-3 text-center"><p class="text-muted-foreground mb-1 text-xs">Job Profiles</p> <p class="flex items-center justify-center text-lg font-semibold">`;
  if (title.toLowerCase() === "free") {
    $$payload.out += "<!--[-->";
    $$payload.out += `1`;
  } else if (limits?.profiles !== void 0 && limits.profiles !== null && limits.profiles !== "U") {
    $$payload.out += "<!--[1-->";
    $$payload.out += `${escape_html(limits.profiles)}`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span class="text-lg">∞</span>`;
  }
  $$payload.out += `<!--]--></p></div> <div class="rounded-md border p-3 text-center"><p class="text-muted-foreground mb-1 text-xs">`;
  if (title.toLowerCase() === "free") {
    $$payload.out += "<!--[-->";
    $$payload.out += `AI Credits`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `Team Seats`;
  }
  $$payload.out += `<!--]--></p> <p class="flex items-center justify-center text-lg font-semibold">`;
  if (title.toLowerCase() === "free") {
    $$payload.out += "<!--[-->";
    $$payload.out += `5<span class="ml-1 text-xs font-normal">/mo</span>`;
  } else if (limits?.seats !== void 0 && limits.seats !== null && limits.seats !== "U") {
    $$payload.out += "<!--[1-->";
    $$payload.out += `${escape_html(limits.seats)}`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<span class="text-lg">∞</span>`;
  }
  $$payload.out += `<!--]--></p></div></div></div> <div><h4 class="text-muted-foreground mb-2 text-xs font-medium uppercase">Key Features</h4> <table class="w-full table-auto border-collapse text-sm"><tbody><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let feature = each_array[$$index];
    $$payload.out += `<tr class="border-border border-t"><td class="text-foreground py-3 pr-4">`;
    if (feature.featureId === "resume_scanner") {
      $$payload.out += "<!--[-->";
      $$payload.out += `Resume Scanner`;
    } else if (feature.featureId === "resume_builder") {
      $$payload.out += "<!--[1-->";
      $$payload.out += `Resume Builder`;
    } else if (feature.featureId === "resume_ai") {
      $$payload.out += "<!--[2-->";
      $$payload.out += `Resume AI`;
    } else if (feature.featureId === "job_search_profiles") {
      $$payload.out += "<!--[3-->";
      $$payload.out += `Job Search Profiles`;
    } else if (feature.featureId === "job_save") {
      $$payload.out += "<!--[4-->";
      $$payload.out += `Save Jobs`;
    } else if (feature.featureId === "job_alerts") {
      $$payload.out += "<!--[5-->";
      $$payload.out += `Job Alerts`;
    } else if (feature.featureId === "tracker" || feature.featureId === "application_tracker") {
      $$payload.out += "<!--[6-->";
      $$payload.out += `Application Tracker`;
    } else if (feature.featureId === "cover_letter_generator") {
      $$payload.out += "<!--[7-->";
      $$payload.out += `Cover Letter Generator`;
    } else if (feature.featureId === "dashboard") {
      $$payload.out += "<!--[8-->";
      $$payload.out += `Dashboard Access`;
    } else if (feature.featureId === "profile") {
      $$payload.out += "<!--[9-->";
      $$payload.out += `User Profile`;
    } else if (feature.featureId === "documents") {
      $$payload.out += "<!--[10-->";
      $$payload.out += `Document Storage`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `${escape_html(feature.featureId.split("_").map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join(" "))}`;
    }
    $$payload.out += `<!--]--></td><td class="py-3 pl-4 text-right">`;
    if (feature.accessLevel === "included") {
      $$payload.out += "<!--[-->";
      Check($$payload, { class: "text-success ml-auto h-4 w-4" });
    } else if (feature.accessLevel === "limited") {
      $$payload.out += "<!--[1-->";
      $$payload.out += `<div class="flex items-center"><span class="mr-1 text-xs text-amber-500">Limited</span> <div class="tooltip-wrapper svelte-12270ft">`;
      Info($$payload, {
        class: "text-muted-foreground h-3 w-3 cursor-help"
      });
      $$payload.out += `<!----> <div class="tooltip svelte-12270ft"><p class="text-xs">This feature has usage limits based on your plan</p></div></div></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      X($$payload, { class: "text-destructive ml-auto h-4 w-4" });
    }
    $$payload.out += `<!--]--></td></tr>`;
  }
  $$payload.out += `<!--]--></tbody></table></div></div></div> `;
  Separator($$payload, { class: "bg-border mt-4" });
  $$payload.out += `<!----></div>`;
  bind_props($$props, {
    title,
    price,
    description,
    isPopular,
    ctaText,
    billingCycle,
    limits,
    features,
    onCtaClick,
    disabled,
    activePlan,
    loading
  });
  pop();
}
function _page($$payload, $$props) {
  push();
  var $$store_subs;
  let billingCycle;
  let data = $$props["data"];
  let selectedPlanId = data.preselectedPlanId || null;
  const urlParams = typeof window !== "undefined" ? new URLSearchParams(window.location.search) : null;
  const sectionParam = urlParams?.get("section");
  let activeTab = sectionParam === "teams" ? "teams" : data.preselectedSection === "teams" ? "teams" : "pro";
  let billingCycleStore = writable(data.preselectedBillingCycle === "annual" ? "annual" : "monthly");
  let isAnnual = store_get($$store_subs ??= {}, "$billingCycleStore", billingCycleStore) === "annual";
  let pendingCheckout = null;
  const user = data.user;
  let plans = data.plans;
  console.log("All plans:", plans);
  console.log("Individual plans:", plans.filter((p) => p.section === "pro"));
  console.log("Team plans:", plans.filter((p) => p.section === "teams"));
  console.log("Plans from server:", plans);
  console.log("Individual plans:", plans.filter((p) => p.section === "pro").map((p) => p.id));
  console.log("Team plans:", plans.filter((p) => p.section === "teams").map((p) => p.id));
  let isDialogOpen = false;
  let isLoading = false;
  function formatPrice(cents) {
    return `${(cents / 100).toFixed(0)}`;
  }
  async function openStripeCheckout(planId, billingCycle2) {
    isLoading = true;
    try {
      console.log("Creating checkout session", { planId, billingCycle: billingCycle2 });
      const res = await fetch("/api/billing/create-checkout-session", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ planId, billingCycle: billingCycle2 })
      });
      if (!res.ok) {
        const errorText = await res.text();
        console.error("Failed to create Stripe session", {
          status: res.status,
          statusText: res.statusText,
          errorText
        });
        alert(`Error: ${errorText || "Failed to create checkout session"}`);
        isLoading = false;
        return;
      }
      const data2 = await res.json();
      if (!data2.url) {
        console.error("No URL returned from checkout session", data2);
        alert("Error: No checkout URL returned");
        isLoading = false;
        return;
      }
      console.log("Redirecting to Stripe", { url: data2.url });
      window.location.href = data2.url;
    } catch (error) {
      console.error("Failed to create Stripe session", error);
      alert(`Error: ${error.message || "Failed to create checkout session"}`);
      isLoading = false;
    }
  }
  async function loginWithEmail(email, password) {
    const res = await fetch("/api/auth/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password })
    });
    if (res.ok) {
      isLoading = false;
      if (pendingCheckout) {
        const { planId, billingCycle: billingCycle2 } = pendingCheckout;
        pendingCheckout = null;
        selectedPlanId = planId;
        openStripeCheckout(planId, billingCycle2);
      } else {
        window.location.href = "/dashboard";
      }
    }
  }
  function openLoginDialog() {
    isDialogOpen = true;
  }
  function closeLoginDialog() {
    isDialogOpen = false;
  }
  billingCycle = store_get($$store_subs ??= {}, "$billingCycleStore", billingCycleStore);
  {
    billingCycleStore.set(isAnnual ? "annual" : "monthly");
  }
  if (selectedPlanId) {
    setTimeout(
      () => {
        const planElement = document.getElementById(`plan-${selectedPlanId}`);
        if (planElement) {
          planElement.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      },
      500
    );
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    const each_array = ensure_array_like(plans.filter((p) => p.section === activeTab).sort((a, b) => {
      if (a.id === "free") return -1;
      if (b.id === "free") return 1;
      return a.monthlyPrice - b.monthlyPrice;
    }));
    SEO($$payload2, {
      title: "Pricing | Hirli",
      description: "Simple, transparent pricing plans for all your job application automation needs. Choose the plan that fits your career goals.",
      keywords: "pricing, subscription plans, job application tools, career services, job search automation, resume optimization"
    });
    $$payload2.out += `<!----> <section><div class="container mx-auto"><div class="py-14 text-center"><h2 class="mb-4 text-3xl font-semibold">Simple, Transparent Pricing</h2> <p class="text-muted-foreground font-lg mx-auto mt-2 max-w-2xl">Start with a free account to speed up your job hunt or boost your entire team to scale
        hiring process with resume automation.</p> <div class="mt-8 flex flex-col items-center justify-between gap-4 px-4 md:flex-row"><div class="flex justify-start space-x-4 md:justify-center">`;
    Button($$payload2, {
      onclick: () => activeTab = "pro",
      class: `border-border rounded-full border px-4 py-2 text-sm font-medium transition ${stringify(activeTab === "pro" ? "border-primary bg-primary text-primary-foreground" : "bg-background text-foreground hover:bg-muted")}`,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Individual`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----> `;
    Button($$payload2, {
      onclick: () => activeTab = "teams",
      class: `border-border rounded-full border px-4 py-2 text-sm font-medium transition ${stringify(activeTab === "teams" ? "border-primary bg-primary text-primary-foreground" : "bg-background text-foreground hover:bg-muted")}`,
      children: ($$payload3) => {
        $$payload3.out += `<!---->Teams`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!----></div> <div class="text-muted-foreground flex items-center gap-3 text-sm">`;
    Switch($$payload2, {
      get checked() {
        return isAnnual;
      },
      set checked($$value) {
        isAnnual = $$value;
        $$settled = false;
      }
    });
    $$payload2.out += `<!----> <span>Annual Billing</span></div></div></div> <div class="mt-10 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let plan = each_array[$$index];
      $$payload2.out += `<div${attr("id", `plan-${stringify(plan.id)}`)}${attr_class(`relative ${stringify(selectedPlanId === plan.id ? "ring-primary ring-2 ring-offset-2" : "")}`)}>`;
      PricingCard($$payload2, {
        title: plan.name,
        price: formatPrice(billingCycle === "monthly" ? plan.monthlyPrice : plan.annualPrice / 12),
        description: plan.description,
        limits: plan.limits,
        features: plan.features,
        billingCycle,
        isPopular: plan.id === "pro" || plan.id === "startup" || plan.popular || selectedPlanId === plan.id,
        activePlan: user && user.role === plan.id,
        loading: selectedPlanId === plan.id && isLoading,
        ctaText: user ? user.role === plan.id ? "Current Plan" : "Upgrade" : plan.id === "free" ? "Start Free" : "Choose Plan",
        disabled: isLoading || user && user.role === plan.id,
        onCtaClick: () => {
          if (plan.id === "free") {
            window.location.href = "/auth/sign-up";
            return;
          }
          if (!user) {
            pendingCheckout = { planId: plan.id, billingCycle };
            openLoginDialog();
            return;
          }
          selectedPlanId = plan.id;
          openStripeCheckout(plan.id, billingCycle);
        }
      });
      $$payload2.out += `<!----></div>`;
    }
    $$payload2.out += `<!--]--></div> <div class="border-border bg-card text-card-foreground mt-16 rounded-lg border p-6 text-center shadow-sm"><h3 class="mb-2 text-xl font-semibold">Need a custom solution?</h3> <p class="text-muted-foreground mb-4">Get in touch for enterprise or partner solutions tailored to your needs.</p> <a href="/contact" class="text-primary font-medium hover:underline">Talk to Sales →</a></div></div></section> `;
    Root($$payload2, {
      open: isDialogOpen,
      onOpenChange: closeLoginDialog,
      children: ($$payload3) => {
        Dialog_trigger($$payload3, {});
        $$payload3.out += `<!----> `;
        Portal($$payload3, {
          children: ($$payload4) => {
            Dialog_overlay($$payload4, {});
            $$payload4.out += `<!----> `;
            Dialog_content($$payload4, {
              class: `md:w-[375px]`,
              children: ($$payload5) => {
                Dialog_header($$payload5, {
                  children: ($$payload6) => {
                    Dialog_title($$payload6, {
                      class: "mb-2 text-2xl",
                      children: ($$payload7) => {
                        $$payload7.out += `<!---->Sign In`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!----> `;
                    Dialog_description($$payload6, {
                      children: ($$payload7) => {
                        $$payload7.out += `<p class="text-muted-foreground text-md mb-6">You need to sign in to update your account plan.</p> `;
                        SignIn($$payload7, {
                          isLoading,
                          onEmailPasswordLogin: loginWithEmail
                        });
                        $$payload7.out += `<!---->`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload6.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_footer($$payload5, {});
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!---->`;
          }
        });
        $$payload3.out += `<!---->`;
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  if ($$store_subs) unsubscribe_stores($$store_subs);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CjS45z2I.js.map
