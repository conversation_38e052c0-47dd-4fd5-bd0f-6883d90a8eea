{"version": 3, "file": "38-pBfvXkHH.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/matches/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/38.js"], "sourcesContent": ["import { r as redirect } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst load = async ({ locals, url }) => {\n  const user = locals.user;\n  if (!user) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const profiles = await prisma.profile.findMany({\n    where: {\n      OR: [\n        { userId: user.id },\n        {\n          team: {\n            members: {\n              some: { userId: user.id }\n            }\n          }\n        }\n      ]\n    },\n    include: {\n      data: true,\n      team: true,\n      // Include the default document (resume)\n      defaultDocument: true\n    }\n  });\n  const selectedProfileId = url.searchParams.get(\"profileId\") || (profiles.length > 0 ? profiles[0].id : null);\n  if (!selectedProfileId) {\n    return {\n      user,\n      profiles,\n      selectedProfileId: null,\n      matches: [],\n      pagination: {\n        page: 1,\n        limit: 20,\n        totalCount: 0,\n        totalPages: 0,\n        hasMore: false\n      }\n    };\n  }\n  const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n  const limit = parseInt(url.searchParams.get(\"limit\") || \"20\");\n  const skip = (page - 1) * limit;\n  const matches = await prisma.job_match_result.findMany({\n    where: {\n      userId: user.id,\n      profileId: selectedProfileId\n    },\n    include: {\n      job_listing: true\n    },\n    orderBy: {\n      matchScore: \"desc\"\n    },\n    skip,\n    take: limit\n  });\n  const totalCount = await prisma.job_match_result.count({\n    where: {\n      userId: user.id,\n      profileId: selectedProfileId\n    }\n  });\n  const jobAlerts = await prisma.jobAlert.findMany({\n    where: {\n      userId: user.id\n    },\n    orderBy: {\n      createdAt: \"desc\"\n    }\n  });\n  const recommendations = await prisma.job_match_result.findMany({\n    where: {\n      userId: user.id,\n      profileId: selectedProfileId,\n      // Only get matches with high scores that weren't from alerts\n      matchScore: {\n        gte: 0.8\n      }\n    },\n    include: {\n      job_listing: true\n    },\n    orderBy: {\n      matchScore: \"desc\"\n    },\n    take: 10\n  });\n  const savedJobs = await prisma.savedJob.findMany({\n    where: {\n      userId: user.id\n    },\n    orderBy: {\n      createdAt: \"desc\"\n    }\n  });\n  const jobIds = savedJobs.map((job) => job.jobId);\n  const jobListings = jobIds.length > 0 ? await prisma.job_listing.findMany({\n    where: {\n      id: {\n        in: jobIds\n      }\n    }\n  }) : [];\n  const savedJobsWithListings = savedJobs.map((savedJob) => {\n    const jobListing = jobListings.find((job) => job.id === savedJob.jobId);\n    return {\n      ...savedJob,\n      job_listing: jobListing || null\n    };\n  });\n  return {\n    user,\n    profiles,\n    selectedProfileId,\n    matches,\n    jobAlerts,\n    recommendations,\n    savedJobs: savedJobsWithListings,\n    pagination: {\n      page,\n      limit,\n      totalCount,\n      totalPages: Math.ceil(totalCount / limit),\n      hasMore: skip + matches.length < totalCount\n    },\n    recommendationPagination: {\n      page: 1,\n      limit: 10,\n      totalCount: recommendations.length,\n      totalPages: Math.ceil(recommendations.length / 10),\n      hasMore: false\n    }\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/matches/_page.server.ts.js';\n\nexport const index = 38;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/matches/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/matches/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/38.DbprAlzy.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/DETxXRrJ.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/WD4kvFhR.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/hrXlVaSN.js\",\"_app/immutable/chunks/BnikQ10_.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/hA0h0kTo.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/DdoUfFy4.js\",\"_app/immutable/chunks/Z6UAQTuv.js\",\"_app/immutable/chunks/Ce4BqqU6.js\",\"_app/immutable/chunks/DumgozFE.js\",\"_app/immutable/chunks/Dz4exfp3.js\",\"_app/immutable/chunks/C33xR25f.js\",\"_app/immutable/chunks/VYoCKyli.js\",\"_app/immutable/chunks/CWmzcjye.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/BvvicRXk.js\",\"_app/immutable/chunks/CGK0g3x_.js\",\"_app/immutable/chunks/DrQfh6BY.js\",\"_app/immutable/chunks/DxW95yuQ.js\",\"_app/immutable/chunks/BjCTmJLi.js\",\"_app/immutable/chunks/D9yI7a4E.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BuYRPDDz.js\",\"_app/immutable/chunks/B2lQHLf_.js\",\"_app/immutable/chunks/B3MJtjra.js\",\"_app/immutable/chunks/ChRM_Un0.js\",\"_app/immutable/chunks/CDnvByek.js\",\"_app/immutable/chunks/CwgkX8t9.js\",\"_app/immutable/chunks/6BxQgNmX.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/zNKWipEG.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/CTn0v-X8.js\",\"_app/immutable/chunks/BPvdPoic.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/DW7T7T22.js\",\"_app/immutable/chunks/CIPPbbaT.js\",\"_app/immutable/chunks/DmZyh-PW.js\",\"_app/immutable/chunks/BoNCRmBc.js\",\"_app/immutable/chunks/qwsZpUIl.js\",\"_app/immutable/chunks/DkmCSZhC.js\",\"_app/immutable/chunks/KVutzy_p.js\",\"_app/immutable/chunks/CKg8MWp_.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\",\"_app/immutable/assets/index.CV-KWLNP.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE;AACV,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;AAC3B,QAAQ;AACR,UAAU,IAAI,EAAE;AAChB,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE;AACrC;AACA;AACA;AACA;AACA,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,IAAI,EAAE,IAAI;AAChB;AACA,MAAM,eAAe,EAAE;AACvB;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,iBAAiB,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC;AAC9G,EAAE,IAAI,CAAC,iBAAiB,EAAE;AAC1B,IAAI,OAAO;AACX,MAAM,IAAI;AACV,MAAM,QAAQ;AACd,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,OAAO,EAAE,EAAE;AACjB,MAAM,UAAU,EAAE;AAClB,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,KAAK,EAAE,EAAE;AACjB,QAAQ,UAAU,EAAE,CAAC;AACrB,QAAQ,UAAU,EAAE,CAAC;AACrB,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK;AACL;AACA,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;AAC5D,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AAC/D,EAAE,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK;AACjC,EAAE,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACzD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI,IAAI;AACR,IAAI,IAAI,EAAE;AACV,GAAG,CAAC;AACJ,EAAE,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC;AACzD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,SAAS,EAAE;AACjB;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACnD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,SAAS,EAAE;AACjB;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC;AACjE,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;AACrB,MAAM,SAAS,EAAE,iBAAiB;AAClC;AACA,MAAM,UAAU,EAAE;AAClB,QAAQ,GAAG,EAAE;AACb;AACA,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACnD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,SAAS,EAAE;AACjB;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC;AAClD,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAC5E,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE;AACV,QAAQ,EAAE,EAAE;AACZ;AACA;AACA,GAAG,CAAC,GAAG,EAAE;AACT,EAAE,MAAM,qBAAqB,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK;AAC5D,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,KAAK,QAAQ,CAAC,KAAK,CAAC;AAC3E,IAAI,OAAO;AACX,MAAM,GAAG,QAAQ;AACjB,MAAM,WAAW,EAAE,UAAU,IAAI;AACjC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,QAAQ;AACZ,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,SAAS,EAAE,qBAAqB;AACpC,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI;AACV,MAAM,KAAK;AACX,MAAM,UAAU;AAChB,MAAM,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC/C,MAAM,OAAO,EAAE,IAAI,GAAG,OAAO,CAAC,MAAM,GAAG;AACvC,KAAK;AACL,IAAI,wBAAwB,EAAE;AAC9B,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,KAAK,EAAE,EAAE;AACf,MAAM,UAAU,EAAE,eAAe,CAAC,MAAM;AACxC,MAAM,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,EAAE,CAAC;AACxD,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,CAAC;;;;;;;ACvIW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAoD,CAAC,EAAE;AAElH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACz2G,MAAC,WAAW,GAAG,CAAC,4CAA4C,CAAC,0CAA0C;AACvG,MAAC,KAAK,GAAG;;;;"}