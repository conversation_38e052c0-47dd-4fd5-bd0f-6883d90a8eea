{"version": 3, "file": "context-oepKpCf5.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/context.js"], "sourcesContent": ["import { a6 as has<PERSON>ontext, a7 as getContext, x as setContext } from \"./index3.js\";\nclass Context {\n  #name;\n  #key;\n  /**\n   * @param name The name of the context.\n   * This is used for generating the context key and error messages.\n   */\n  constructor(name) {\n    this.#name = name;\n    this.#key = Symbol(name);\n  }\n  /**\n   * The key used to get and set the context.\n   *\n   * It is not recommended to use this value directly.\n   * Instead, use the methods provided by this class.\n   */\n  get key() {\n    return this.#key;\n  }\n  /**\n   * Checks whether this has been set in the context of a parent component.\n   *\n   * Must be called during component initialisation.\n   */\n  exists() {\n    return hasContext(this.#key);\n  }\n  /**\n   * Retrieves the context that belongs to the closest parent component.\n   *\n   * Must be called during component initialisation.\n   *\n   * @throws An error if the context does not exist.\n   */\n  get() {\n    const context = getContext(this.#key);\n    if (context === void 0) {\n      throw new Error(`Context \"${this.#name}\" not found`);\n    }\n    return context;\n  }\n  /**\n   * Retrieves the context that belongs to the closest parent component,\n   * or the given fallback value if the context does not exist.\n   *\n   * Must be called during component initialisation.\n   */\n  getOr(fallback) {\n    const context = getContext(this.#key);\n    if (context === void 0) {\n      return fallback;\n    }\n    return context;\n  }\n  /**\n   * Associates the given value with the current component and returns it.\n   *\n   * Must be called during component initialisation.\n   */\n  set(context) {\n    return setContext(this.#key, context);\n  }\n}\nexport {\n  Context as C\n};\n"], "names": [], "mappings": ";;AACA,MAAM,OAAO,CAAC;AACd,EAAE,KAAK;AACP,EAAE,IAAI;AACN;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI;AACrB,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,IAAI;AACpB;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,GAAG;AACX,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,GAAG;AACR,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACzC,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;AAC1D;AACA,IAAI,OAAO,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,QAAQ,EAAE;AAClB,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACzC,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,MAAM,OAAO,QAAQ;AACrB;AACA,IAAI,OAAO,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,OAAO,EAAE;AACf,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACzC;AACA;;;;"}