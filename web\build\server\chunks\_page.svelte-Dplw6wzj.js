import { p as push, O as escape_html, M as ensure_array_like, N as attr, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { B as Button } from './button-CrucCo1G.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './sanityClient-BQ6Z_2a-.js';
import '@sanity/client';
import './html-FW6Ia4bL.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const { pressPage, pressReleases } = data;
  function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  }
  SEO($$payload, {
    title: pressPage?.seo?.metaTitle || "Press & Media | Hirli",
    description: pressPage?.seo?.metaDescription || "Hirli press releases, media resources, and company information for journalists and media professionals.",
    keywords: pressPage?.seo?.keywords?.join(", ") || "Hirli press, media kit, press releases, company news, media resources, brand assets"
  });
  $$payload.out += `<!----> <div><div class="mb-16"><div class="border-border bg-card text-card-foreground mb-12 rounded-lg border p-8 shadow-sm">`;
  if (pressPage?.content) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="text-muted-foreground mb-6 text-lg">`;
    PortableText($$payload, { value: pressPage.content });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="text-muted-foreground mb-6 text-lg">Hirli is an AI-powered job application platform that automates the job search and
          application process. Our mission is to help job seekers find and apply to relevant
          opportunities more efficiently, saving them time and increasing their chances of landing
          the right job.</p> <p class="text-muted-foreground mb-6 text-lg">Founded in 2022, Hirli has helped over 100,000 job seekers apply to millions of jobs
          across various industries. Our platform uses advanced AI to match candidates with suitable
          positions, automatically fill out applications, and track progress—all with a single
          click.</p>`;
  }
  $$payload.out += `<!--]--> `;
  if (pressPage?.companyInfo) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-6 mt-6"><h3 class="mb-3 text-xl font-semibold">Company Information</h3> <div class="grid grid-cols-1 gap-4 md:grid-cols-2">`;
    if (pressPage.companyInfo.foundedYear) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex items-center gap-2">`;
      Calendar($$payload, { class: "text-muted-foreground h-5 w-5" });
      $$payload.out += `<!----> <span>Founded: ${escape_html(pressPage.companyInfo.foundedYear)}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (pressPage.companyInfo.headquarters) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex items-center gap-2">`;
      Map_pin($$payload, { class: "text-muted-foreground h-5 w-5" });
      $$payload.out += `<!----> <span>Headquarters: ${escape_html(pressPage.companyInfo.headquarters)}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div> `;
    if (pressPage.companyInfo.boilerplate) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="mt-4"><h4 class="text-muted-foreground mb-2 text-sm font-medium">Company Boilerplate</h4> <p class="text-muted-foreground text-sm">${escape_html(pressPage.companyInfo.boilerplate)}</p></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="mt-8 flex flex-wrap gap-4">`;
  if (pressReleases && pressReleases.length > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<a href="/press/releases">`;
    Button($$payload, {
      variant: "outline",
      class: "flex items-center gap-2 border-2",
      children: ($$payload2) => {
        File_text($$payload2, { class: "h-4 w-4" });
        $$payload2.out += `<!----> <span>Press Releases</span>`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----></a>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <a href="/press/images">`;
  Button($$payload, {
    variant: "outline",
    class: "flex items-center gap-2 border-2",
    children: ($$payload2) => {
      File_text($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!----> <span>Images</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></a> <a href="/press/media-kit">`;
  Button($$payload, {
    variant: "outline",
    class: "flex items-center gap-2 border-2",
    children: ($$payload2) => {
      File_text($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!----> <span>Media Kit</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></a> <a href="/press/contact">`;
  Button($$payload, {
    variant: "outline",
    class: "flex items-center gap-2 border-2",
    children: ($$payload2) => {
      File_text($$payload2, { class: "h-4 w-4" });
      $$payload2.out += `<!----> <span>Contact</span>`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></a></div></div></div> `;
  if (pressReleases && pressReleases.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(pressReleases.slice(0, 3));
    $$payload.out += `<div class="mt-16"><div class="mb-8 flex items-center justify-between"><h2 class="text-3xl font-semibold">Press Releases</h2> <a href="/press/releases" class="text-primary inline-flex items-center text-sm font-medium hover:underline">View All `;
    External_link($$payload, { class: "ml-1 h-3 w-3" });
    $$payload.out += `<!----></a></div> <div class="space-y-6"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let release = each_array[$$index];
      $$payload.out += `<div class="border-b pb-6"><div class="flex flex-col"><div class="mb-2 flex items-center gap-2">`;
      Calendar($$payload, { class: "text-muted-foreground h-4 w-4" });
      $$payload.out += `<!----> <p class="text-muted-foreground text-sm">${escape_html(formatDate(release.publishedAt))}</p> `;
      if (release.location) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<span class="text-muted-foreground mx-1">•</span> `;
        Map_pin($$payload, { class: "text-muted-foreground h-4 w-4" });
        $$payload.out += `<!----> <p class="text-muted-foreground text-sm">${escape_html(release.location)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <h3 class="mb-2 text-xl font-medium">${escape_html(release.title)}</h3> `;
      if (release.subtitle) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-muted-foreground mb-2 text-lg">${escape_html(release.subtitle)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> `;
      if (release.excerpt) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="text-muted-foreground mb-4">${escape_html(release.excerpt)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <div class="mt-2"><a${attr("href", `/press/releases/${release.slug.current}`)} class="text-primary inline-flex items-center text-sm font-medium hover:underline">Read More `;
      External_link($$payload, { class: "ml-1 h-3 w-3" });
      $$payload.out += `<!----></a></div></div></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-Dplw6wzj.js.map
