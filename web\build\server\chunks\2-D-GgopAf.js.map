{"version": 3, "file": "2-D-GgopAf.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/auth/_layout.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/2.js"], "sourcesContent": ["import { r as redirect } from \"../../../chunks/index.js\";\nconst load = async ({ locals, url }) => {\n  if (locals.user) {\n    throw redirect(302, \"/dashboard\");\n  }\n  return {\n    user: locals.user,\n    currentPath: url.pathname\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/auth/_layout.server.ts.js';\n\nexport const index = 2;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/auth/_layout.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/auth/+layout.server.ts\";\nexport const imports = [\"_app/immutable/nodes/2.D2p4hE94.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/B3MJtjra.js\",\"_app/immutable/chunks/Z9Zpt0fH.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;AACA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK;AACxC,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;AACnB,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,YAAY,CAAC;AACrC;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI;AACrB,IAAI,WAAW,EAAE,GAAG,CAAC;AACrB,GAAG;AACH,CAAC;;;;;;;ACPW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,8BAAyC,CAAC,EAAE;AAEvG,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,oCAAoC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC5b,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}