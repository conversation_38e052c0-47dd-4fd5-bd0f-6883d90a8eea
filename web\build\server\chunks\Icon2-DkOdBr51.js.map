{"version": 3, "file": "Icon2-DkOdBr51.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/Icon2.js"], "sourcesContent": ["import { U as ensure_array_like, M as spread_attributes, T as clsx, a4 as element, y as pop, w as push } from \"./index3.js\";\n/**\n * @license @lucide/svelte v0.482.0 - ISC\n *\n * ISC License\n * \n * Copyright (c) for portions of Lucide are held by <PERSON> 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.\n * \n * Permission to use, copy, modify, and/or distribute this software for any\n * purpose with or without fee is hereby granted, provided that the above\n * copyright notice and this permission notice appear in all copies.\n * \n * THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n * ACTION OF CONTRACT, NEG<PERSON>IGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n * \n */\nconst defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  \"stroke-width\": 2,\n  \"stroke-linecap\": \"round\",\n  \"stroke-linejoin\": \"round\"\n};\nfunction Icon($$payload, $$props) {\n  push();\n  const {\n    name,\n    color = \"currentColor\",\n    size = 24,\n    strokeWidth = 2,\n    absoluteStrokeWidth = false,\n    iconNode = [],\n    children,\n    $$slots,\n    $$events,\n    ...props\n  } = $$props;\n  const each_array = ensure_array_like(iconNode);\n  $$payload.out += `<svg${spread_attributes(\n    {\n      ...defaultAttributes,\n      ...props,\n      width: size,\n      height: size,\n      stroke: color,\n      \"stroke-width\": absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      class: clsx([\n        \"lucide-icon lucide\",\n        name && `lucide-${name}`,\n        props.class\n      ])\n    },\n    null,\n    void 0,\n    void 0,\n    3\n  )}><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let [tag, attrs] = each_array[$$index];\n    element($$payload, tag, () => {\n      $$payload.out += `${spread_attributes({ ...attrs }, null, void 0, void 0, 3)}`;\n    });\n  }\n  $$payload.out += `<!--]-->`;\n  children?.($$payload);\n  $$payload.out += `<!----></svg>`;\n  pop();\n}\nexport {\n  Icon as I\n};\n"], "names": [], "mappings": ";;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,iBAAiB,GAAG;AAC1B,EAAE,KAAK,EAAE,4BAA4B;AACrC,EAAE,KAAK,EAAE,EAAE;AACX,EAAE,MAAM,EAAE,EAAE;AACZ,EAAE,OAAO,EAAE,WAAW;AACtB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,MAAM,EAAE,cAAc;AACxB,EAAE,cAAc,EAAE,CAAC;AACnB,EAAE,gBAAgB,EAAE,OAAO;AAC3B,EAAE,iBAAiB,EAAE;AACrB,CAAC;AACD,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM;AACR,IAAI,IAAI;AACR,IAAI,KAAK,GAAG,cAAc;AAC1B,IAAI,IAAI,GAAG,EAAE;AACb,IAAI,WAAW,GAAG,CAAC;AACnB,IAAI,mBAAmB,GAAG,KAAK;AAC/B,IAAI,QAAQ,GAAG,EAAE;AACjB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AAChD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,GAAG,iBAAiB;AAC1B,MAAM,GAAG,KAAK;AACd,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,cAAc,EAAE,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,WAAW;AACjG,MAAM,KAAK,EAAE,IAAI,CAAC;AAClB,QAAQ,oBAAoB;AAC5B,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAChC,QAAQ,KAAK,CAAC;AACd,OAAO;AACP,KAAK;AACL,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,CAAC,SAAS,CAAC;AACd,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC;AAC1C,IAAI,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,iBAAiB,CAAC,EAAE,GAAG,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AACpF,KAAK,CAAC;AACN;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,GAAG,EAAE;AACP;;;;"}