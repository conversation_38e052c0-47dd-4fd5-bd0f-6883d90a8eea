{"version": 3, "file": "stripe2-B7NQEZZC.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/stripe2.js"], "sourcesContent": ["import { g as getPlansFromDatabase } from \"./plan-sync.js\";\nasync function mapStripePriceIdToPlanId(priceId) {\n  console.log(\"🔍 Mapping Stripe price ID to plan ID\", { priceId });\n  try {\n    const plans = await getPlansFromDatabase();\n    const plan = plans.find(\n      (p) => p.stripePriceMonthlyId === priceId || p.stripePriceYearlyId === priceId\n    );\n    if (plan) {\n      console.log(\"✅ Found plan by direct match\", {\n        priceId,\n        planId: plan.id,\n        planName: plan.name\n      });\n      return plan.id;\n    }\n    console.log(\"⚠️ No direct match found in database plans, trying fallback mapping\");\n  } catch (error) {\n    console.error(\"Error getting plans from database:\", error);\n    console.log(\"⚠️ Error getting plans from database, trying fallback mapping\");\n  }\n  const priceMap = {\n    price_1R9WTXPvxCOa4C056MICymub: \"casual\",\n    // Monthly\n    price_1R9WWUPvxCOa4C05NtpXaOq6: \"casual\",\n    // Yearly\n    price_1R9WVBPvxCOa4C05b5VrNJiw: \"active\",\n    // Monthly\n    price_1R9WUrPvxCOa4C05EspLpJR3: \"active\",\n    // Yearly\n    price_1R9WXPPvxCOa4C05MDynzxE2: \"daily\",\n    // Monthly\n    price_1R9WXxPvxCOa4C05hGzFxoDM: \"daily\",\n    // Yearly\n    price_1R9WZPPvxCOa4C05Z9D9ry48: \"power\",\n    // Monthly\n    price_1R9WZoPvxCOa4C05azkzddOJ: \"power\",\n    // Yearly\n    price_1R9WaFPvxCOa4C05xDSA2Ytb: \"startup\",\n    // Monthly\n    price_1R9WbEPvxCOa4C05ZYdSV7Ni: \"startup\",\n    // Yearly\n    price_1R9WcIPvxCOa4C05Yx9Ixvxl: \"medium\",\n    // Monthly\n    price_1R9WcgPvxCOa4C05Yx9Ixvxl: \"medium\",\n    // Yearly\n    price_1R9WfbPvxCOa4C055tBNHGf7: \"enterprise\",\n    // Monthly\n    price_1R9WfwPvxCOa4C05cWj25Amd: \"enterprise\",\n    // Yearly\n    price_1R9WhYPvxCOa4C05gnaa26BH: \"custom\",\n    // Monthly\n    price_1R9XC0PvxCOa4C053xed8EkB: \"custom\"\n    // Yearly\n  };\n  const planId = priceMap[priceId];\n  if (planId) {\n    console.log(\"✅ Found plan in fallback mapping\", { priceId, planId });\n    return planId;\n  }\n  console.error(\"❌ Could not map price ID to plan ID\", {\n    priceId,\n    availablePriceIds: [...Object.keys(priceMap)]\n  });\n  return null;\n}\nasync function getStripePriceId(planId, billingCycle) {\n  try {\n    const plans = await getPlansFromDatabase();\n    const plan = plans.find((p) => p.id === planId);\n    if (!plan) return null;\n    return billingCycle === \"annual\" ? plan.stripePriceYearlyId : plan.stripePriceMonthlyId;\n  } catch (error) {\n    console.error(\"Error getting plan from database:\", error);\n    return null;\n  }\n}\nexport {\n  getStripePriceId as g,\n  mapStripePriceIdToPlanId as m\n};\n"], "names": [], "mappings": ";;AACA,eAAe,wBAAwB,CAAC,OAAO,EAAE;AACjD,EAAE,OAAO,CAAC,GAAG,CAAC,uCAAuC,EAAE,EAAE,OAAO,EAAE,CAAC;AACnE,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,oBAAoB,EAAE;AAC9C,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI;AAC3B,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,oBAAoB,KAAK,OAAO,IAAI,CAAC,CAAC,mBAAmB,KAAK;AAC7E,KAAK;AACL,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE;AAClD,QAAQ,OAAO;AACf,QAAQ,MAAM,EAAE,IAAI,CAAC,EAAE;AACvB,QAAQ,QAAQ,EAAE,IAAI,CAAC;AACvB,OAAO,CAAC;AACR,MAAM,OAAO,IAAI,CAAC,EAAE;AACpB;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,qEAAqE,CAAC;AACtF,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,CAAC,GAAG,CAAC,+DAA+D,CAAC;AAChF;AACA,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,8BAA8B,EAAE,QAAQ;AAC5C;AACA,IAAI,8BAA8B,EAAE,QAAQ;AAC5C;AACA,IAAI,8BAA8B,EAAE,QAAQ;AAC5C;AACA,IAAI,8BAA8B,EAAE,QAAQ;AAC5C;AACA,IAAI,8BAA8B,EAAE,OAAO;AAC3C;AACA,IAAI,8BAA8B,EAAE,OAAO;AAC3C;AACA,IAAI,8BAA8B,EAAE,OAAO;AAC3C;AACA,IAAI,8BAA8B,EAAE,OAAO;AAC3C;AACA,IAAI,8BAA8B,EAAE,SAAS;AAC7C;AACA,IAAI,8BAA8B,EAAE,SAAS;AAC7C;AACA,IAAI,8BAA8B,EAAE,QAAQ;AAC5C;AACA,IAAI,8BAA8B,EAAE,QAAQ;AAC5C;AACA,IAAI,8BAA8B,EAAE,YAAY;AAChD;AACA,IAAI,8BAA8B,EAAE,YAAY;AAChD;AACA,IAAI,8BAA8B,EAAE,QAAQ;AAC5C;AACA,IAAI,8BAA8B,EAAE;AACpC;AACA,GAAG;AACH,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC;AAClC,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC;AACxE,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE;AACvD,IAAI,OAAO;AACX,IAAI,iBAAiB,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAChD,GAAG,CAAC;AACJ,EAAE,OAAO,IAAI;AACb;AACA,eAAe,gBAAgB,CAAC,MAAM,EAAE,YAAY,EAAE;AACtD,EAAE,IAAI;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,oBAAoB,EAAE;AAC9C,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC;AACnD,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI;AAC1B,IAAI,OAAO,YAAY,KAAK,QAAQ,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,oBAAoB;AAC3F,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO,IAAI;AACf;AACA;;;;"}