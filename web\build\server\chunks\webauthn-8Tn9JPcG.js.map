{"version": 3, "file": "webauthn-8Tn9JPcG.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/webauthn.js"], "sourcesContent": ["import { generateRegistrationOptions, verifyRegistrationResponse, generateAuthenticationOptions, verifyAuthenticationResponse } from \"@simplewebauthn/server\";\nconst RP_NAME = \"Hirli\";\nconst isDev = process.env.NODE_ENV !== \"production\";\nconst RP_ID = isDev ? \"localhost\" : \"hirli.co\";\nconst EXPECTED_ORIGIN = isDev ? [\"http://localhost:5173\"] : [\"https://hirli.co\"];\nasync function generatePasskeyRegistrationOptions(userId, username, existingCredentials = []) {\n  console.log(\"generatePasskeyRegistrationOptions called\");\n  console.log(\"userId:\", userId);\n  console.log(\"username:\", username);\n  console.log(\"existingCredentials:\", existingCredentials);\n  try {\n    const options = {\n      rpName: RP_NAME,\n      rpID: RP_ID,\n      userID: userId,\n      userName: username,\n      timeout: 6e4,\n      attestationType: \"none\",\n      // Skip excludeCredentials for now to simplify the process\n      excludeCredentials: [],\n      authenticatorSelection: {\n        authenticatorAttachment: \"platform\",\n        userVerification: \"required\",\n        residentKey: \"preferred\",\n        requireResidentKey: false\n      },\n      supportedAlgorithmIDs: [-7, -257]\n      // ES256, RS256\n    };\n    console.log(\"Options prepared:\", options);\n    const result = generateRegistrationOptions(options);\n    console.log(\"Registration options generated successfully\");\n    return result;\n  } catch (error) {\n    console.error(\"Error in generatePasskeyRegistrationOptions:\", error);\n    console.error(\"Error stack:\", error.stack);\n    throw error;\n  }\n}\nasync function verifyPasskeyRegistration(response, expectedChallenge) {\n  console.log(\"verifyPasskeyRegistration called\");\n  console.log(\"Response:\", JSON.stringify(response, null, 2));\n  console.log(\"Expected challenge:\", expectedChallenge);\n  try {\n    const verification = {\n      response,\n      expectedChallenge,\n      expectedOrigin: EXPECTED_ORIGIN[0],\n      expectedRPID: RP_ID\n    };\n    console.log(\"Verification options:\", JSON.stringify(verification, null, 2));\n    const verificationResult = await verifyRegistrationResponse(verification);\n    console.log(\"Verification result:\", JSON.stringify(verificationResult, null, 2));\n    return {\n      verified: verificationResult.verified,\n      registrationInfo: verificationResult.registrationInfo\n    };\n  } catch (error) {\n    console.error(\"Error verifying registration:\", error);\n    console.error(\"Error stack:\", error.stack);\n    return { verified: false, error: error.message };\n  }\n}\nasync function generatePasskeyAuthenticationOptions(existingPasskeys = []) {\n  console.log(\n    \"generatePasskeyAuthenticationOptions called with\",\n    existingPasskeys.length,\n    \"passkeys\"\n  );\n  try {\n    const options = {\n      rpID: RP_ID,\n      timeout: 6e4,\n      userVerification: \"preferred\"\n      // Use 'preferred' for better compatibility\n    };\n    if (existingPasskeys.length > 0) {\n      console.log(\"Including allowCredentials for\", existingPasskeys.length, \"passkeys\");\n      const normalizedPasskeys = existingPasskeys.map((passkey) => {\n        return {\n          id: passkey.id || passkey.credentialID || \"unknown-id\",\n          credentialID: passkey.credentialID || passkey.id || \"unknown-id\",\n          credentialPublicKey: passkey.credentialPublicKey || passkey.publicKey || \"\",\n          transports: passkey.transports || []\n        };\n      });\n      options.allowCredentials = normalizedPasskeys.map((passkey) => {\n        const credId = passkey.credentialID || passkey.id;\n        console.log(\"Processing credential ID:\", credId);\n        let idBuffer;\n        try {\n          idBuffer = Buffer.from(credId, \"base64url\");\n        } catch (e) {\n          console.log(\"Failed to decode as base64url, trying base64. Error:\", e);\n          idBuffer = Buffer.from(credId, \"base64\");\n        }\n        return {\n          id: idBuffer,\n          type: \"public-key\",\n          transports: passkey.transports || [\"internal\"]\n        };\n      });\n    } else {\n      console.log(\"No passkeys provided, browser will show all available passkeys\");\n    }\n    console.log(\"Authentication options:\", {\n      ...options,\n      allowCredentials: options.allowCredentials ? `${options.allowCredentials.length} credentials` : \"omitted\"\n    });\n    const result = generateAuthenticationOptions(options);\n    console.log(\"Generated authentication options\");\n    return result;\n  } catch (error) {\n    console.error(\"Error generating authentication options:\", error);\n    console.error(\"Error stack:\", error.stack);\n    throw error;\n  }\n}\nasync function verifyPasskeyAuthentication(response, expectedChallenge, credentialPublicKey, credentialCounter) {\n  console.log(\"verifyPasskeyAuthentication called\");\n  console.log(\"Response:\", JSON.stringify(response, null, 2));\n  console.log(\"Expected challenge:\", expectedChallenge);\n  try {\n    let credentialIDBuffer;\n    try {\n      credentialIDBuffer = Buffer.from(response.id, \"base64url\");\n    } catch {\n      console.log(\"Failed to decode credential ID as base64url, trying base64\");\n      credentialIDBuffer = Buffer.from(response.id.replace(/-/g, \"+\").replace(/_/g, \"/\"), \"base64\");\n    }\n    let credentialPublicKeyBuffer;\n    try {\n      credentialPublicKeyBuffer = Buffer.from(credentialPublicKey, \"base64url\");\n    } catch {\n      console.log(\"Failed to decode public key as base64url, trying base64\");\n      credentialPublicKeyBuffer = Buffer.from(\n        credentialPublicKey.replace(/-/g, \"+\").replace(/_/g, \"/\"),\n        \"base64\"\n      );\n    }\n    const verification = {\n      response,\n      expectedChallenge,\n      expectedOrigin: EXPECTED_ORIGIN[0],\n      expectedRPID: RP_ID,\n      authenticator: {\n        credentialID: credentialIDBuffer,\n        credentialPublicKey: credentialPublicKeyBuffer,\n        counter: credentialCounter\n      }\n    };\n    console.log(\"Verification options:\", JSON.stringify(verification, null, 2));\n    return await verifyAuthenticationResponse(verification);\n  } catch (error) {\n    console.error(\"Error verifying authentication:\", error);\n    return { verified: false, error: error.message };\n  }\n}\nexport {\n  generatePasskeyAuthenticationOptions as a,\n  verifyPasskeyAuthentication as b,\n  generatePasskeyRegistrationOptions as g,\n  verifyPasskeyRegistration as v\n};\n"], "names": [], "mappings": ";;AACA,MAAM,OAAO,GAAG,OAAO;AACvB,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACnD,MAAM,KAAK,GAAG,KAAK,GAAG,WAAW,GAAG,UAAU;AAC9C,MAAM,eAAe,GAAG,KAAK,GAAG,CAAC,uBAAuB,CAAC,GAAG,CAAC,kBAAkB,CAAC;AAChF,eAAe,kCAAkC,CAAC,MAAM,EAAE,QAAQ,EAAE,mBAAmB,GAAG,EAAE,EAAE;AAC9F,EAAE,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC;AAC1D,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;AAChC,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,QAAQ,CAAC;AACpC,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,mBAAmB,CAAC;AAC1D,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,OAAO,EAAE,GAAG;AAClB,MAAM,eAAe,EAAE,MAAM;AAC7B;AACA,MAAM,kBAAkB,EAAE,EAAE;AAC5B,MAAM,sBAAsB,EAAE;AAC9B,QAAQ,uBAAuB,EAAE,UAAU;AAC3C,QAAQ,gBAAgB,EAAE,UAAU;AACpC,QAAQ,WAAW,EAAE,WAAW;AAChC,QAAQ,kBAAkB,EAAE;AAC5B,OAAO;AACP,MAAM,qBAAqB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG;AACtC;AACA,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,OAAO,CAAC;AAC7C,IAAI,MAAM,MAAM,GAAG,2BAA2B,CAAC,OAAO,CAAC;AACvD,IAAI,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC;AAC9D,IAAI,OAAO,MAAM;AACjB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC;AACxE,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,KAAK;AACf;AACA;AACA,eAAe,yBAAyB,CAAC,QAAQ,EAAE,iBAAiB,EAAE;AACtE,EAAE,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACjD,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7D,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,iBAAiB,CAAC;AACvD,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG;AACzB,MAAM,QAAQ;AACd,MAAM,iBAAiB;AACvB,MAAM,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC;AACxC,MAAM,YAAY,EAAE;AACpB,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC/E,IAAI,MAAM,kBAAkB,GAAG,MAAM,0BAA0B,CAAC,YAAY,CAAC;AAC7E,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,kBAAkB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACpF,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,kBAAkB,CAAC,QAAQ;AAC3C,MAAM,gBAAgB,EAAE,kBAAkB,CAAC;AAC3C,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AAC9C,IAAI,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE;AACpD;AACA;AACA,eAAe,oCAAoC,CAAC,gBAAgB,GAAG,EAAE,EAAE;AAC3E,EAAE,OAAO,CAAC,GAAG;AACb,IAAI,kDAAkD;AACtD,IAAI,gBAAgB,CAAC,MAAM;AAC3B,IAAI;AACJ,GAAG;AACH,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,OAAO,EAAE,GAAG;AAClB,MAAM,gBAAgB,EAAE;AACxB;AACA,KAAK;AACL,IAAI,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AACrC,MAAM,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,gBAAgB,CAAC,MAAM,EAAE,UAAU,CAAC;AACxF,MAAM,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AACnE,QAAQ,OAAO;AACf,UAAU,EAAE,EAAE,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,YAAY,IAAI,YAAY;AAChE,UAAU,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,EAAE,IAAI,YAAY;AAC1E,UAAU,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,SAAS,IAAI,EAAE;AACrF,UAAU,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI;AAC5C,SAAS;AACT,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,gBAAgB,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AACrE,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,EAAE;AACzD,QAAQ,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,MAAM,CAAC;AACxD,QAAQ,IAAI,QAAQ;AACpB,QAAQ,IAAI;AACZ,UAAU,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC;AACrD,SAAS,CAAC,OAAO,CAAC,EAAE;AACpB,UAAU,OAAO,CAAC,GAAG,CAAC,sDAAsD,EAAE,CAAC,CAAC;AAChF,UAAU,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC;AAClD;AACA,QAAQ,OAAO;AACf,UAAU,EAAE,EAAE,QAAQ;AACtB,UAAU,IAAI,EAAE,YAAY;AAC5B,UAAU,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,CAAC,UAAU;AACvD,SAAS;AACT,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,GAAG,CAAC,gEAAgE,CAAC;AACnF;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE;AAC3C,MAAM,GAAG,OAAO;AAChB,MAAM,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,GAAG,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG;AACtG,KAAK,CAAC;AACN,IAAI,MAAM,MAAM,GAAG,6BAA6B,CAAC,OAAO,CAAC;AACzD,IAAI,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC;AACnD,IAAI,OAAO,MAAM;AACjB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC;AACpE,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,KAAK;AACf;AACA;AACA,eAAe,2BAA2B,CAAC,QAAQ,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE;AAChH,EAAE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;AACnD,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC7D,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,iBAAiB,CAAC;AACvD,EAAE,IAAI;AACN,IAAI,IAAI,kBAAkB;AAC1B,IAAI,IAAI;AACR,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,WAAW,CAAC;AAChE,KAAK,CAAC,MAAM;AACZ,MAAM,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC;AAC/E,MAAM,kBAAkB,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,QAAQ,CAAC;AACnG;AACA,IAAI,IAAI,yBAAyB;AACjC,IAAI,IAAI;AACR,MAAM,yBAAyB,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC;AAC/E,KAAK,CAAC,MAAM;AACZ,MAAM,OAAO,CAAC,GAAG,CAAC,yDAAyD,CAAC;AAC5E,MAAM,yBAAyB,GAAG,MAAM,CAAC,IAAI;AAC7C,QAAQ,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AACjE,QAAQ;AACR,OAAO;AACP;AACA,IAAI,MAAM,YAAY,GAAG;AACzB,MAAM,QAAQ;AACd,MAAM,iBAAiB;AACvB,MAAM,cAAc,EAAE,eAAe,CAAC,CAAC,CAAC;AACxC,MAAM,YAAY,EAAE,KAAK;AACzB,MAAM,aAAa,EAAE;AACrB,QAAQ,YAAY,EAAE,kBAAkB;AACxC,QAAQ,mBAAmB,EAAE,yBAAyB;AACtD,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK;AACL,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAC/E,IAAI,OAAO,MAAM,4BAA4B,CAAC,YAAY,CAAC;AAC3D,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE;AACpD;AACA;;;;"}