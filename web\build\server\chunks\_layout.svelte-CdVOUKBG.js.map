{"version": 3, "file": "_layout.svelte-CdVOUKBG.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/admin/email/_layout.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../../../../../chunks/index3.js\";\nimport \"../../../../../../chunks/client.js\";\nfunction _layout($$payload, $$props) {\n  push();\n  {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"container mx-auto py-8\"><div class=\"flex h-64 items-center justify-center\"><div class=\"border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent\"></div></div></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  pop();\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;;;AAGA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,qMAAqM,CAAC;AAC5N;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP;;;;"}