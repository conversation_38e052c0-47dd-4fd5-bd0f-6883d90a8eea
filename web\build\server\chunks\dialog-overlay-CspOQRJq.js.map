{"version": 3, "file": "dialog-overlay-CspOQRJq.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/dialog-overlay.js"], "sourcesContent": ["import { J as derived, w as push, M as spread_attributes, N as bind_props, y as pop } from \"./index3.js\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { u as useId } from \"./use-id.js\";\nimport \"clsx\";\nimport { C as Context } from \"./context.js\";\nimport { S as SPACE, i as ENTER, d as getDataOpenClosed, c as getAriaExpanded } from \"./kbd-constants.js\";\nimport { P as Presence_layer } from \"./presence-layer.js\";\nfunction createAttrs(variant) {\n  return {\n    content: `data-${variant}-content`,\n    trigger: `data-${variant}-trigger`,\n    overlay: `data-${variant}-overlay`,\n    title: `data-${variant}-title`,\n    description: `data-${variant}-description`,\n    close: `data-${variant}-close`,\n    cancel: `data-${variant}-cancel`,\n    action: `data-${variant}-action`\n  };\n}\nclass DialogRootState {\n  opts;\n  triggerNode = null;\n  contentNode = null;\n  descriptionNode = null;\n  contentId = void 0;\n  titleId = void 0;\n  triggerId = void 0;\n  descriptionId = void 0;\n  cancelNode = null;\n  #attrs = derived(() => createAttrs(this.opts.variant.current));\n  get attrs() {\n    return this.#attrs();\n  }\n  set attrs($$value) {\n    return this.#attrs($$value);\n  }\n  constructor(opts) {\n    this.opts = opts;\n    this.handleOpen = this.handleOpen.bind(this);\n    this.handleClose = this.handleClose.bind(this);\n  }\n  handleOpen() {\n    if (this.opts.open.current) return;\n    this.opts.open.current = true;\n  }\n  handleClose() {\n    if (!this.opts.open.current) return;\n    this.opts.open.current = false;\n  }\n  #sharedProps = derived(() => ({\n    \"data-state\": getDataOpenClosed(this.opts.open.current)\n  }));\n  get sharedProps() {\n    return this.#sharedProps();\n  }\n  set sharedProps($$value) {\n    return this.#sharedProps($$value);\n  }\n}\nclass DialogTriggerState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById({\n      ...opts,\n      onRefChange: (node) => {\n        this.root.triggerNode = node;\n        this.root.triggerId = node?.id;\n      }\n    });\n    this.onclick = this.onclick.bind(this);\n    this.onkeydown = this.onkeydown.bind(this);\n  }\n  onclick(e) {\n    if (this.opts.disabled.current) return;\n    if (e.button > 0) return;\n    this.root.handleOpen();\n  }\n  onkeydown(e) {\n    if (this.opts.disabled.current) return;\n    if (e.key === SPACE || e.key === ENTER) {\n      e.preventDefault();\n      this.root.handleOpen();\n    }\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"aria-haspopup\": \"dialog\",\n    \"aria-expanded\": getAriaExpanded(this.root.opts.open.current),\n    \"aria-controls\": this.root.contentId,\n    [this.root.attrs.trigger]: \"\",\n    onkeydown: this.onkeydown,\n    onclick: this.onclick,\n    disabled: this.opts.disabled.current ? true : void 0,\n    ...this.root.sharedProps\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass DialogCloseState {\n  opts;\n  root;\n  #attr = derived(() => this.root.attrs[this.opts.variant.current]);\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    this.onclick = this.onclick.bind(this);\n    this.onkeydown = this.onkeydown.bind(this);\n    useRefById({\n      ...opts,\n      deps: () => this.root.opts.open.current\n    });\n  }\n  onclick(e) {\n    if (this.opts.disabled.current) return;\n    if (e.button > 0) return;\n    this.root.handleClose();\n  }\n  onkeydown(e) {\n    if (this.opts.disabled.current) return;\n    if (e.key === SPACE || e.key === ENTER) {\n      e.preventDefault();\n      this.root.handleClose();\n    }\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    [this.#attr()]: \"\",\n    onclick: this.onclick,\n    onkeydown: this.onkeydown,\n    disabled: this.opts.disabled.current ? true : void 0,\n    tabindex: 0,\n    ...this.root.sharedProps\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass DialogActionState {\n  opts;\n  root;\n  #attr = derived(() => this.root.attrs.action);\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    [this.#attr()]: \"\",\n    ...this.root.sharedProps\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass DialogTitleState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById({\n      ...opts,\n      onRefChange: (node) => {\n        this.root.titleId = node?.id;\n      },\n      deps: () => this.root.opts.open.current\n    });\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"heading\",\n    \"aria-level\": this.opts.level.current,\n    [this.root.attrs.title]: \"\",\n    ...this.root.sharedProps\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass DialogDescriptionState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById({\n      ...opts,\n      deps: () => this.root.opts.open.current,\n      onRefChange: (node) => {\n        this.root.descriptionNode = node;\n        this.root.descriptionId = node?.id;\n      }\n    });\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    [this.root.attrs.description]: \"\",\n    ...this.root.sharedProps\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass DialogContentState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById({\n      ...opts,\n      deps: () => this.root.opts.open.current,\n      onRefChange: (node) => {\n        this.root.contentNode = node;\n        this.root.contentId = node?.id;\n      }\n    });\n  }\n  #snippetProps = derived(() => ({ open: this.root.opts.open.current }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: this.root.opts.variant.current === \"alert-dialog\" ? \"alertdialog\" : \"dialog\",\n    \"aria-modal\": \"true\",\n    \"aria-describedby\": this.root.descriptionId,\n    \"aria-labelledby\": this.root.titleId,\n    [this.root.attrs.content]: \"\",\n    style: {\n      pointerEvents: \"auto\",\n      outline: this.root.opts.variant.current === \"alert-dialog\" ? \"none\" : void 0\n    },\n    tabindex: this.root.opts.variant.current === \"alert-dialog\" ? -1 : void 0,\n    ...this.root.sharedProps\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass DialogOverlayState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById({\n      ...opts,\n      deps: () => this.root.opts.open.current\n    });\n  }\n  #snippetProps = derived(() => ({ open: this.root.opts.open.current }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    [this.root.attrs.overlay]: \"\",\n    style: { pointerEvents: \"auto\" },\n    ...this.root.sharedProps\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass AlertDialogCancelState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    this.onclick = this.onclick.bind(this);\n    this.onkeydown = this.onkeydown.bind(this);\n    useRefById({\n      ...opts,\n      deps: () => this.root.opts.open.current,\n      onRefChange: (node) => {\n        this.root.cancelNode = node;\n      }\n    });\n  }\n  onclick(e) {\n    if (this.opts.disabled.current) return;\n    if (e.button > 0) return;\n    this.root.handleClose();\n  }\n  onkeydown(e) {\n    if (this.opts.disabled.current) return;\n    if (e.key === SPACE || e.key === ENTER) {\n      e.preventDefault();\n      this.root.handleClose();\n    }\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    [this.root.attrs.cancel]: \"\",\n    onclick: this.onclick,\n    onkeydown: this.onkeydown,\n    tabindex: 0,\n    ...this.root.sharedProps\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst DialogRootContext = new Context(\"Dialog.Root\");\nfunction useDialogRoot(props) {\n  return DialogRootContext.set(new DialogRootState(props));\n}\nfunction useDialogTrigger(props) {\n  return new DialogTriggerState(props, DialogRootContext.get());\n}\nfunction useDialogTitle(props) {\n  return new DialogTitleState(props, DialogRootContext.get());\n}\nfunction useDialogContent(props) {\n  return new DialogContentState(props, DialogRootContext.get());\n}\nfunction useDialogOverlay(props) {\n  return new DialogOverlayState(props, DialogRootContext.get());\n}\nfunction useDialogDescription(props) {\n  return new DialogDescriptionState(props, DialogRootContext.get());\n}\nfunction useDialogClose(props) {\n  return new DialogCloseState(props, DialogRootContext.get());\n}\nfunction useAlertDialogCancel(props) {\n  return new AlertDialogCancelState(props, DialogRootContext.get());\n}\nfunction useAlertDialogAction(props) {\n  return new DialogActionState(props, DialogRootContext.get());\n}\nfunction Dialog_title($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    ref = null,\n    child,\n    children,\n    level = 2,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const titleState = useDialogTitle({\n    id: box.with(() => id),\n    level: box.with(() => level),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, titleState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction shouldTrapFocus({ forceMount, present, trapFocus, open }) {\n  if (forceMount) {\n    return open && trapFocus;\n  }\n  return present && trapFocus && open;\n}\nfunction Dialog_overlay($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    forceMount = false,\n    child,\n    children,\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const overlayState = useDialogOverlay({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, overlayState.props);\n  {\n    let presence = function($$payload2) {\n      if (child) {\n        $$payload2.out += \"<!--[-->\";\n        child($$payload2, {\n          props: mergeProps(mergedProps),\n          ...overlayState.snippetProps\n        });\n        $$payload2.out += `<!---->`;\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n        $$payload2.out += `<div${spread_attributes({ ...mergeProps(mergedProps) }, null)}>`;\n        children?.($$payload2, overlayState.snippetProps);\n        $$payload2.out += `<!----></div>`;\n      }\n      $$payload2.out += `<!--]-->`;\n    };\n    Presence_layer($$payload, {\n      id,\n      present: overlayState.root.opts.open.current || forceMount,\n      presence\n    });\n  }\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Dialog_overlay as D,\n  useDialogClose as a,\n  useDialogContent as b,\n  Dialog_title as c,\n  useDialogDescription as d,\n  useAlertDialogAction as e,\n  useAlertDialogCancel as f,\n  useDialogTrigger as g,\n  shouldTrapFocus as s,\n  useDialogRoot as u\n};\n"], "names": [], "mappings": ";;;;;;;;;AASA,SAAS,WAAW,CAAC,OAAO,EAAE;AAC9B,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;AACtC,IAAI,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;AACtC,IAAI,OAAO,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC;AACtC,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,WAAW,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC;AAC9C,IAAI,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC;AACpC,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO;AACnC,GAAG;AACH;AACA,MAAM,eAAe,CAAC;AACtB,EAAE,IAAI;AACN,EAAE,WAAW,GAAG,IAAI;AACpB,EAAE,WAAW,GAAG,IAAI;AACpB,EAAE,eAAe,GAAG,IAAI;AACxB,EAAE,SAAS,GAAG,MAAM;AACpB,EAAE,OAAO,GAAG,MAAM;AAClB,EAAE,SAAS,GAAG,MAAM;AACpB,EAAE,aAAa,GAAG,MAAM;AACxB,EAAE,UAAU,GAAG,IAAI;AACnB,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChE,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAChD,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AAClD;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI;AACjC;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK;AAClC;AACA,EAAE,YAAY,GAAG,OAAO,CAAC,OAAO;AAChC,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAC1D,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;AAC9B;AACA,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACrC;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI;AACpC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,EAAE,EAAE;AACtC;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC1B;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AAC5C,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC5B;AACA;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,eAAe,EAAE,QAAQ;AAC7B,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACjE,IAAI,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS;AACxC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE;AACjC,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AAC7B,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,GAAG,MAAM;AACxD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACnE,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,KAAK,CAAC;AACN;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC3B;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AAC5C,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC7B;AACA;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;AACtB,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AAC7B,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,GAAG,MAAM;AACxD,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,iBAAiB,CAAC;AACxB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,KAAK,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;AAC/C,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;AACtB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,EAAE,EAAE;AACpC,OAAO;AACP,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,KAAK,CAAC;AACN;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACzC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;AAC/B,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,sBAAsB,CAAC;AAC7B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAC7C,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI;AACxC,QAAQ,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,IAAI,EAAE,EAAE;AAC1C;AACA,KAAK,CAAC;AACN;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,EAAE;AACrC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAC7C,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI;AACpC,QAAQ,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,EAAE,EAAE;AACtC;AACA,KAAK,CAAC;AACN;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;AACxE,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,cAAc,GAAG,aAAa,GAAG,QAAQ;AACtF,IAAI,YAAY,EAAE,MAAM;AACxB,IAAI,kBAAkB,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa;AAC/C,IAAI,iBAAiB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO;AACxC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE;AACjC,IAAI,KAAK,EAAE;AACX,MAAM,aAAa,EAAE,MAAM;AAC3B,MAAM,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,cAAc,GAAG,MAAM,GAAG;AAC5E,KAAK;AACL,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,cAAc,GAAG,EAAE,GAAG,MAAM;AAC7E,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,KAAK,CAAC;AACN;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;AACxE,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE;AACjC,IAAI,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE;AACpC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,sBAAsB,CAAC;AAC7B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAC7C,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;AACnC;AACA,KAAK,CAAC;AACN;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC3B;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AAC5C,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC7B;AACA;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;AAChC,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AAC7B,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,iBAAiB,GAAG,IAAI,OAAO,CAAC,aAAa,CAAC;AACpD,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,EAAE,OAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC1D;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,EAAE,OAAO,IAAI,kBAAkB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC/D;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC7D;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,EAAE,OAAO,IAAI,kBAAkB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC/D;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,EAAE,OAAO,IAAI,kBAAkB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC/D;AACA,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACrC,EAAE,OAAO,IAAI,sBAAsB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AACnE;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC7D;AACA,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACrC,EAAE,OAAO,IAAI,sBAAsB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AACnE;AACA,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACrC,EAAE,OAAO,IAAI,iBAAiB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC9D;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC;AACpC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC;AAC7D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,eAAe,CAAC,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE;AACnE,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,OAAO,IAAI,IAAI,SAAS;AAC5B;AACA,EAAE,OAAO,OAAO,IAAI,SAAS,IAAI,IAAI;AACrC;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,gBAAgB,CAAC;AACxC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC;AAC/D,EAAE;AACF,IAAI,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE;AACxC,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,KAAK,CAAC,UAAU,EAAE;AAC1B,UAAU,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC;AACxC,UAAU,GAAG,YAAY,CAAC;AAC1B,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,UAAU,CAAC,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3F,QAAQ,QAAQ,GAAG,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC;AACzD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,EAAE;AAC9B,MAAM,EAAE;AACR,MAAM,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,UAAU;AAChE,MAAM;AACN,KAAK,CAAC;AACN;AACA,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}