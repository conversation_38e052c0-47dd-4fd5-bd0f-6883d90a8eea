import { r as redirect } from './index-Ddp2AB5f.js';

const load = async ({ locals }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  if (!user.isAdmin) {
    throw redirect(302, "/dashboard");
  }
  return {};
};

var _layout_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 6;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-CdVOUKBG.js')).default;
const server_id = "src/routes/dashboard/settings/admin/email/+layout.server.ts";
const imports = ["_app/immutable/nodes/6.YTUNd3Bs.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/B3MJtjra.js","_app/immutable/chunks/D8pQCLOH.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/B-Xjo-Yt.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _layout_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=6-DlZakOxs.js.map
