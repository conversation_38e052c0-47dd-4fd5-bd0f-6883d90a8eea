import { p as push, O as escape_html, Q as bind_props, q as pop, M as ensure_array_like } from './index3-CqUPEnZw.js';
import { B as Button } from './button-CrucCo1G.js';
import { B as Badge } from './badge-C9pSznab.js';
import { A as Accordion_root, a as Accordion_item, b as Accordion_trigger, c as Accordion_content } from './accordion-trigger-DwieKZVA.js';
import { S as Scroll_area, a as Scroll_area_scrollbar } from './scroll-area-Dn69zlyp.js';
import { g as goto } from './client-dNyMPa8V.js';
import { A as Arrow_left } from './arrow-left-DyZbJRhp.js';
import { U as User } from './user-DpDpidvb.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import { G as Graduation_cap, W as Wrench, L as Languages, B as Badge_check } from './wrench-B9xVvhnf.js';
import { A as Award } from './award-DpjdNTEA.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './chevron-down-xGjWLrZH.js';
import './Icon-A4vzmk-O.js';
import './use-debounce.svelte-gxToHznJ.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const profile = data.profile;
  const profileData = data.profileData || {};
  const tabs = [
    { id: "profile", label: "Profile", icon: User },
    {
      id: "personal",
      label: "Personal Info",
      icon: File_text
    },
    {
      id: "preferences",
      label: "Job Preferences",
      icon: Briefcase
    },
    {
      id: "experience",
      label: "Experience",
      icon: Briefcase
    },
    {
      id: "education",
      label: "Education",
      icon: Graduation_cap
    },
    { id: "skills", label: "Skills", icon: Wrench },
    {
      id: "languages",
      label: "Languages",
      icon: Languages
    },
    {
      id: "achievements",
      label: "Achievements",
      icon: Award
    },
    {
      id: "certifications",
      label: "Certifications",
      icon: Badge_check
    }
  ];
  let activeTab = "profile";
  function handleTabChange(tabId) {
    activeTab = tabId;
  }
  function goBack() {
    goto();
  }
  $$payload.out += `<div class="container mx-auto p-4"><div class="mb-6"><div class="mb-2 flex items-center gap-2">`;
  Button($$payload, {
    variant: "ghost",
    size: "sm",
    class: "h-auto p-0",
    onclick: goBack,
    children: ($$payload2) => {
      Arrow_left($$payload2, { class: "mr-1 h-4 w-4" });
      $$payload2.out += `<!----> Back`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> <span class="text-muted-foreground">/</span> <span class="text-muted-foreground max-w-[200px] truncate">${escape_html(profile?.name || "Profile")}</span></div> <div class="flex items-center justify-between"><h1 class="text-2xl font-bold">${escape_html(profile?.name || "Profile")}</h1></div></div> <div class="grid grid-cols-1 gap-6 md:grid-cols-4"><div>`;
  Scroll_area($$payload, {
    class: "h-[calc(100vh-200px)]",
    children: ($$payload2) => {
      const each_array = ensure_array_like(tabs);
      $$payload2.out += `<div class="space-y-1 pr-4"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let tab = each_array[$$index];
        Button($$payload2, {
          variant: activeTab === tab.id ? "default" : "ghost",
          class: "w-full justify-start",
          onclick: () => handleTabChange(tab.id),
          children: ($$payload3) => {
            $$payload3.out += `<!---->`;
            tab.icon?.($$payload3, { class: "mr-2 h-4 w-4" });
            $$payload3.out += `<!----> ${escape_html(tab.label)}`;
          },
          $$slots: { default: true }
        });
      }
      $$payload2.out += `<!--]--></div> `;
      Scroll_area_scrollbar($$payload2, { orientation: "vertical" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="md:col-span-3">`;
  Scroll_area($$payload, {
    class: "h-[calc(100vh-200px)]",
    children: ($$payload2) => {
      $$payload2.out += `<div class="h-full w-full">`;
      if (activeTab === "profile") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Profile Overview</h2> <div class="rounded-lg border p-4"><div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div><h3 class="text-muted-foreground text-sm font-medium">Full Name</h3> <p>${escape_html(profileData.fullName || profileData.personalInfo?.fullName || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Email</h3> <p>${escape_html(profileData.email || profileData.personalInfo?.email || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Job Title</h3> <p>${escape_html(profileData.jobType || profileData.personalInfo?.jobTitle || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Industry</h3> <p>${escape_html(profileData.industry || "Not specified")}</p></div></div></div> <h3 class="text-lg font-medium">Summary</h3> <div class="rounded-lg border p-4">`;
        if (profileData.summary || profileData.personalInfo?.summary) {
          $$payload2.out += "<!--[-->";
          $$payload2.out += `<p class="whitespace-pre-wrap">${escape_html(profileData.summary || profileData.personalInfo?.summary)}</p>`;
        } else {
          $$payload2.out += "<!--[!-->";
          $$payload2.out += `<p class="text-muted-foreground italic">No summary provided</p>`;
        }
        $$payload2.out += `<!--]--></div></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (activeTab === "personal") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Personal Information</h2> `;
        Accordion_root($$payload2, {
          type: "single",
          collapsible: true,
          children: ($$payload3) => {
            Accordion_item($$payload3, {
              value: "personal-info",
              children: ($$payload4) => {
                Accordion_trigger($$payload4, {
                  class: "flex w-full items-center justify-between px-4 py-2",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex items-center gap-2">`;
                    User($$payload5, { class: "h-5 w-5" });
                    $$payload5.out += `<!----> <span class="text-lg font-medium">Contact Details</span></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Accordion_content($$payload4, {
                  class: "px-4 pb-4",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="grid grid-cols-1 gap-4 md:grid-cols-2"><div><h3 class="text-muted-foreground text-sm font-medium">Full Name</h3> <p>${escape_html(profileData.fullName || profileData.personalInfo?.fullName || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Email</h3> <p>${escape_html(profileData.email || profileData.personalInfo?.email || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Phone</h3> <p>${escape_html(profileData.phone || profileData.personalInfo?.phone || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Location</h3> <p>${escape_html(profileData.location || profileData.personalInfo?.location || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Website</h3> <p>${escape_html(profileData.website || profileData.personalInfo?.website || "Not specified")}</p></div></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (activeTab === "preferences") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Job Preferences</h2> `;
        Accordion_root($$payload2, {
          type: "single",
          collapsible: true,
          children: ($$payload3) => {
            Accordion_item($$payload3, {
              value: "job-preferences",
              children: ($$payload4) => {
                Accordion_trigger($$payload4, {
                  class: "flex w-full items-center justify-between px-4 py-2",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex items-center gap-2">`;
                    Briefcase($$payload5, { class: "h-5 w-5" });
                    $$payload5.out += `<!----> <span class="text-lg font-medium">Career Preferences</span></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Accordion_content($$payload4, {
                  class: "px-4 pb-4",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="grid grid-cols-1 gap-4 md:grid-cols-2">`;
                    if (profileData.jobPreferences) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<div><h3 class="text-muted-foreground text-sm font-medium">Desired Roles</h3> <p>${escape_html(profileData.jobPreferences.interestedRoles?.join(", ") || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Preferred Locations</h3> <p>${escape_html(profileData.jobPreferences.preferredLocations?.join(", ") || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Remote Preference</h3> <p>${escape_html(profileData.jobPreferences.remotePreference || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Desired Industries</h3> <p>${escape_html(profileData.jobPreferences.desiredIndustries?.join(", ") || "Not specified")}</p></div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<div class="text-muted-foreground col-span-2 italic">No job preferences specified</div>`;
                    }
                    $$payload5.out += `<!--]--></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (activeTab === "experience") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Work Experience</h2> `;
        if (profileData.workExperience && profileData.workExperience.length > 0) {
          $$payload2.out += "<!--[-->";
          Accordion_root($$payload2, {
            type: "multiple",
            children: ($$payload3) => {
              const each_array_1 = ensure_array_like(profileData.workExperience);
              $$payload3.out += `<!--[-->`;
              for (let i = 0, $$length = each_array_1.length; i < $$length; i++) {
                let exp = each_array_1[i];
                Accordion_item($$payload3, {
                  value: `exp-${i}`,
                  children: ($$payload4) => {
                    Accordion_trigger($$payload4, {
                      class: "flex w-full items-center justify-between px-4 py-2",
                      children: ($$payload5) => {
                        $$payload5.out += `<div class="flex items-center gap-2">`;
                        Briefcase($$payload5, { class: "h-5 w-5" });
                        $$payload5.out += `<!----> <span class="text-lg font-medium">${escape_html(exp.title || exp.jobTitle)} at ${escape_html(exp.company)}</span></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!----> `;
                    Accordion_content($$payload4, {
                      class: "px-4 pb-4",
                      children: ($$payload5) => {
                        $$payload5.out += `<div class="space-y-2"><div><h3 class="text-muted-foreground text-sm font-medium">Company</h3> <p>${escape_html(exp.company || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Title</h3> <p>${escape_html(exp.title || exp.jobTitle || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Duration</h3> <p>${escape_html(exp.startDate || "Unknown")} - ${escape_html(exp.current ? "Present" : exp.endDate || "Unknown")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Description</h3> <p class="whitespace-pre-wrap">${escape_html(exp.description || "No description provided")}</p></div></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload3.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
        } else {
          $$payload2.out += "<!--[!-->";
          $$payload2.out += `<div class="text-muted-foreground rounded-lg border p-4 italic">No work experience added</div>`;
        }
        $$payload2.out += `<!--]--></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (activeTab === "education") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Education</h2> `;
        if (profileData.education && profileData.education.length > 0) {
          $$payload2.out += "<!--[-->";
          Accordion_root($$payload2, {
            type: "multiple",
            children: ($$payload3) => {
              const each_array_2 = ensure_array_like(profileData.education);
              $$payload3.out += `<!--[-->`;
              for (let i = 0, $$length = each_array_2.length; i < $$length; i++) {
                let edu = each_array_2[i];
                Accordion_item($$payload3, {
                  value: `edu-${i}`,
                  children: ($$payload4) => {
                    Accordion_trigger($$payload4, {
                      class: "flex w-full items-center justify-between px-4 py-2",
                      children: ($$payload5) => {
                        $$payload5.out += `<div class="flex items-center gap-2">`;
                        Graduation_cap($$payload5, { class: "h-5 w-5" });
                        $$payload5.out += `<!----> <span class="text-lg font-medium">${escape_html(edu.degree || "Degree")} at ${escape_html(edu.school || edu.institution)}</span></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!----> `;
                    Accordion_content($$payload4, {
                      class: "px-4 pb-4",
                      children: ($$payload5) => {
                        $$payload5.out += `<div class="space-y-2"><div><h3 class="text-muted-foreground text-sm font-medium">School</h3> <p>${escape_html(edu.school || edu.institution || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Degree</h3> <p>${escape_html(edu.degree || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Field of Study</h3> <p>${escape_html(edu.field || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Duration</h3> <p>${escape_html(edu.startDate || "Unknown")} - ${escape_html(edu.current ? "Present" : edu.endDate || "Unknown")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Description</h3> <p class="whitespace-pre-wrap">${escape_html(edu.description || "No description provided")}</p></div></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload3.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
        } else {
          $$payload2.out += "<!--[!-->";
          $$payload2.out += `<div class="text-muted-foreground rounded-lg border p-4 italic">No education added</div>`;
        }
        $$payload2.out += `<!--]--></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (activeTab === "skills") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Skills</h2> `;
        Accordion_root($$payload2, {
          type: "single",
          collapsible: true,
          children: ($$payload3) => {
            Accordion_item($$payload3, {
              value: "skills",
              children: ($$payload4) => {
                Accordion_trigger($$payload4, {
                  class: "flex w-full items-center justify-between px-4 py-2",
                  children: ($$payload5) => {
                    $$payload5.out += `<div class="flex items-center gap-2">`;
                    Wrench($$payload5, { class: "h-5 w-5" });
                    $$payload5.out += `<!----> <span class="text-lg font-medium">Skills &amp; Expertise</span></div>`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!----> `;
                Accordion_content($$payload4, {
                  class: "px-4 pb-4",
                  children: ($$payload5) => {
                    if (profileData.skills || profileData.skillsData) {
                      $$payload5.out += "<!--[-->";
                      $$payload5.out += `<div class="space-y-4">`;
                      if (profileData.skillsData?.technical && profileData.skillsData.technical.length > 0) {
                        $$payload5.out += "<!--[-->";
                        const each_array_3 = ensure_array_like(profileData.skillsData.technical);
                        $$payload5.out += `<div><h3 class="text-muted-foreground mb-2 text-sm font-medium">Technical Skills</h3> <div class="flex flex-wrap gap-2"><!--[-->`;
                        for (let $$index_3 = 0, $$length = each_array_3.length; $$index_3 < $$length; $$index_3++) {
                          let skill = each_array_3[$$index_3];
                          Badge($$payload5, {
                            variant: "secondary",
                            children: ($$payload6) => {
                              $$payload6.out += `<!---->${escape_html(skill)}`;
                            },
                            $$slots: { default: true }
                          });
                        }
                        $$payload5.out += `<!--]--></div></div>`;
                      } else {
                        $$payload5.out += "<!--[!-->";
                      }
                      $$payload5.out += `<!--]--> `;
                      if (profileData.skillsData?.soft && profileData.skillsData.soft.length > 0) {
                        $$payload5.out += "<!--[-->";
                        const each_array_4 = ensure_array_like(profileData.skillsData.soft);
                        $$payload5.out += `<div><h3 class="text-muted-foreground mb-2 text-sm font-medium">Soft Skills</h3> <div class="flex flex-wrap gap-2"><!--[-->`;
                        for (let $$index_4 = 0, $$length = each_array_4.length; $$index_4 < $$length; $$index_4++) {
                          let skill = each_array_4[$$index_4];
                          Badge($$payload5, {
                            variant: "secondary",
                            children: ($$payload6) => {
                              $$payload6.out += `<!---->${escape_html(skill)}`;
                            },
                            $$slots: { default: true }
                          });
                        }
                        $$payload5.out += `<!--]--></div></div>`;
                      } else {
                        $$payload5.out += "<!--[!-->";
                      }
                      $$payload5.out += `<!--]--> `;
                      if ((!profileData.skillsData?.technical || profileData.skillsData.technical.length === 0) && (!profileData.skillsData?.soft || profileData.skillsData.soft.length === 0) && profileData.skills) {
                        $$payload5.out += "<!--[-->";
                        const each_array_5 = ensure_array_like(Array.isArray(profileData.skills) ? profileData.skills : [profileData.skills]);
                        $$payload5.out += `<div><h3 class="text-muted-foreground mb-2 text-sm font-medium">Skills</h3> <div class="flex flex-wrap gap-2"><!--[-->`;
                        for (let $$index_5 = 0, $$length = each_array_5.length; $$index_5 < $$length; $$index_5++) {
                          let skill = each_array_5[$$index_5];
                          Badge($$payload5, {
                            variant: "secondary",
                            children: ($$payload6) => {
                              $$payload6.out += `<!---->${escape_html(skill)}`;
                            },
                            $$slots: { default: true }
                          });
                        }
                        $$payload5.out += `<!--]--></div></div>`;
                      } else {
                        $$payload5.out += "<!--[!-->";
                      }
                      $$payload5.out += `<!--]--></div>`;
                    } else {
                      $$payload5.out += "<!--[!-->";
                      $$payload5.out += `<div class="text-muted-foreground italic">No skills specified</div>`;
                    }
                    $$payload5.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload4.out += `<!---->`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (activeTab === "languages") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Languages</h2> `;
        if (profileData.languages && profileData.languages.length > 0) {
          $$payload2.out += "<!--[-->";
          Accordion_root($$payload2, {
            type: "single",
            collapsible: true,
            children: ($$payload3) => {
              Accordion_item($$payload3, {
                value: "languages",
                children: ($$payload4) => {
                  Accordion_trigger($$payload4, {
                    class: "flex w-full items-center justify-between px-4 py-2",
                    children: ($$payload5) => {
                      $$payload5.out += `<div class="flex items-center gap-2">`;
                      Languages($$payload5, { class: "h-5 w-5" });
                      $$payload5.out += `<!----> <span class="text-lg font-medium">Language Proficiency</span></div>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!----> `;
                  Accordion_content($$payload4, {
                    class: "px-4 pb-4",
                    children: ($$payload5) => {
                      const each_array_6 = ensure_array_like(profileData.languages);
                      $$payload5.out += `<div class="space-y-2"><!--[-->`;
                      for (let $$index_6 = 0, $$length = each_array_6.length; $$index_6 < $$length; $$index_6++) {
                        let lang = each_array_6[$$index_6];
                        $$payload5.out += `<div class="flex items-center justify-between"><span>${escape_html(lang.name)}</span> `;
                        Badge($$payload5, {
                          children: ($$payload6) => {
                            $$payload6.out += `<!---->${escape_html(lang.proficiency)}`;
                          },
                          $$slots: { default: true }
                        });
                        $$payload5.out += `<!----></div>`;
                      }
                      $$payload5.out += `<!--]--></div>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                },
                $$slots: { default: true }
              });
            },
            $$slots: { default: true }
          });
        } else {
          $$payload2.out += "<!--[!-->";
          $$payload2.out += `<div class="text-muted-foreground rounded-lg border p-4 italic">No languages added</div>`;
        }
        $$payload2.out += `<!--]--></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (activeTab === "achievements") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Achievements</h2> `;
        if (profileData.achievements && profileData.achievements.length > 0) {
          $$payload2.out += "<!--[-->";
          Accordion_root($$payload2, {
            type: "single",
            collapsible: true,
            children: ($$payload3) => {
              Accordion_item($$payload3, {
                value: "achievements",
                children: ($$payload4) => {
                  Accordion_trigger($$payload4, {
                    class: "flex w-full items-center justify-between px-4 py-2",
                    children: ($$payload5) => {
                      $$payload5.out += `<div class="flex items-center gap-2">`;
                      Award($$payload5, { class: "h-5 w-5" });
                      $$payload5.out += `<!----> <span class="text-lg font-medium">Awards &amp; Achievements</span></div>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!----> `;
                  Accordion_content($$payload4, {
                    class: "px-4 pb-4",
                    children: ($$payload5) => {
                      const each_array_7 = ensure_array_like(profileData.achievements);
                      $$payload5.out += `<ul class="list-disc space-y-2 pl-5"><!--[-->`;
                      for (let $$index_7 = 0, $$length = each_array_7.length; $$index_7 < $$length; $$index_7++) {
                        let achievement = each_array_7[$$index_7];
                        $$payload5.out += `<li>${escape_html(achievement.title || achievement)}</li>`;
                      }
                      $$payload5.out += `<!--]--></ul>`;
                    },
                    $$slots: { default: true }
                  });
                  $$payload4.out += `<!---->`;
                },
                $$slots: { default: true }
              });
            },
            $$slots: { default: true }
          });
        } else {
          $$payload2.out += "<!--[!-->";
          $$payload2.out += `<div class="text-muted-foreground rounded-lg border p-4 italic">No achievements added</div>`;
        }
        $$payload2.out += `<!--]--></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--> `;
      if (activeTab === "certifications") {
        $$payload2.out += "<!--[-->";
        $$payload2.out += `<div class="space-y-4"><h2 class="text-xl font-semibold">Certifications</h2> `;
        if (profileData.certifications && profileData.certifications.length > 0) {
          $$payload2.out += "<!--[-->";
          Accordion_root($$payload2, {
            type: "multiple",
            children: ($$payload3) => {
              const each_array_8 = ensure_array_like(profileData.certifications);
              $$payload3.out += `<!--[-->`;
              for (let i = 0, $$length = each_array_8.length; i < $$length; i++) {
                let cert = each_array_8[i];
                Accordion_item($$payload3, {
                  value: `cert-${i}`,
                  children: ($$payload4) => {
                    Accordion_trigger($$payload4, {
                      class: "flex w-full items-center justify-between px-4 py-2",
                      children: ($$payload5) => {
                        $$payload5.out += `<div class="flex items-center gap-2">`;
                        Badge_check($$payload5, { class: "h-5 w-5" });
                        $$payload5.out += `<!----> <span class="text-lg font-medium">${escape_html(cert.name)}</span></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!----> `;
                    Accordion_content($$payload4, {
                      class: "px-4 pb-4",
                      children: ($$payload5) => {
                        $$payload5.out += `<div class="space-y-2"><div><h3 class="text-muted-foreground text-sm font-medium">Issuing Organization</h3> <p>${escape_html(cert.issuer || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Issue Date</h3> <p>${escape_html(cert.issueDate || "Not specified")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Expiration Date</h3> <p>${escape_html(cert.expirationDate || "No expiration")}</p></div> <div><h3 class="text-muted-foreground text-sm font-medium">Credential ID</h3> <p>${escape_html(cert.credentialId || "Not specified")}</p></div></div>`;
                      },
                      $$slots: { default: true }
                    });
                    $$payload4.out += `<!---->`;
                  },
                  $$slots: { default: true }
                });
              }
              $$payload3.out += `<!--]-->`;
            },
            $$slots: { default: true }
          });
        } else {
          $$payload2.out += "<!--[!-->";
          $$payload2.out += `<div class="text-muted-foreground rounded-lg border p-4 italic">No certifications added</div>`;
        }
        $$payload2.out += `<!--]--></div>`;
      } else {
        $$payload2.out += "<!--[!-->";
      }
      $$payload2.out += `<!--]--></div> `;
      Scroll_area_scrollbar($$payload2, { orientation: "vertical" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-cVy1TH26.js.map
