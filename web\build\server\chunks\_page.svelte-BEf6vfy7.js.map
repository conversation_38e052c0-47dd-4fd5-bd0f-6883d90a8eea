{"version": 3, "file": "_page.svelte-BEf6vfy7.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/system-status/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, $ as attr_style, W as stringify, y as pop, w as push, Y as fallback, N as bind_props, U as ensure_array_like } from \"../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { C as Card } from \"../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../chunks/card-content.js\";\nimport { C as Card_description } from \"../../../chunks/card-description.js\";\nimport { C as Card_header } from \"../../../chunks/card-header.js\";\nimport { C as Card_title } from \"../../../chunks/card-title.js\";\nimport { o as onDestroy } from \"../../../chunks/index-server.js\";\nimport \"../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport \"clsx\";\nimport { B as Badge } from \"../../../chunks/badge.js\";\nimport { C as Chart_container, B as BarChart, a as Chart_tooltip } from \"../../../chunks/chart-tooltip.js\";\nimport \"@layerstack/utils\";\nimport \"@layerstack/tailwind\";\nimport \"../../../chunks/Tooltip.svelte_svelte_type_style_lang.js\";\nimport \"@layerstack/utils/object\";\nimport \"d3-interpolate-path\";\nimport \"@dagrejs/dagre\";\nimport \"d3-tile\";\nimport \"d3-sankey\";\nimport { T as Triangle_alert } from \"../../../chunks/triangle-alert.js\";\nimport { C as Clock } from \"../../../chunks/clock.js\";\nimport { C as Circle_x } from \"../../../chunks/circle-x.js\";\nimport { C as Circle_check_big } from \"../../../chunks/circle-check-big.js\";\nimport { A as Accordion_root } from \"../../../chunks/accordion-trigger.js\";\nimport { M as MaintenanceAccordion } from \"../../../chunks/MaintenanceAccordion.js\";\nimport { H as History } from \"../../../chunks/history.js\";\nfunction ServiceTrendChart($$payload, $$props) {\n  push();\n  let {\n    historyData = [],\n    metric = \"successRate\",\n    title = \"Last 30 Days\",\n    height = 100\n  } = $$props;\n  const chartData = () => {\n    if (!historyData || historyData.length === 0) {\n      const mockData = [];\n      const today = /* @__PURE__ */ new Date();\n      for (let i = 29; i >= 0; i--) {\n        const date = /* @__PURE__ */ new Date();\n        date.setDate(today.getDate() - i);\n        const rand = Math.random();\n        let status = \"operational\";\n        if (rand > 0.9) status = \"outage\";\n        else if (rand > 0.8) status = \"degraded\";\n        else if (rand > 0.7) status = \"maintenance\";\n        mockData.push({\n          date: formatDate(date.toISOString()),\n          status,\n          value: status === \"operational\" ? 100 : status === \"degraded\" ? 80 : status === \"maintenance\" ? 60 : 0,\n          statusColor: getStatusColor(status)\n        });\n      }\n      return mockData;\n    }\n    return historyData.map((item) => ({\n      date: formatDate(item.date),\n      status: item.status,\n      value: item[metric] || 0,\n      statusColor: getStatusColor(item.status)\n    }));\n  };\n  function formatDate(dateStr) {\n    const date = new Date(dateStr);\n    return date.toLocaleDateString(\"en-US\", { month: \"short\", day: \"numeric\" });\n  }\n  function getStatusColor(status) {\n    switch (status) {\n      case \"operational\":\n        return \"var(--chart-3)\";\n      case \"degraded\":\n        return \"var(--chart-4)\";\n      case \"outage\":\n        return \"var(--chart-5)\";\n      case \"maintenance\":\n        return \"var(--chart-2)\";\n      default:\n        return \"hsl(var(--muted-foreground))\";\n    }\n  }\n  const chartConfig = {\n    operational: { label: \"Operational\", color: \"var(--chart-3)\" },\n    degraded: { label: \"Degraded\", color: \"var(--chart-4)\" },\n    outage: { label: \"Outage\", color: \"var(--chart-5)\" },\n    maintenance: { label: \"Maintenance\", color: \"var(--chart-2)\" }\n  };\n  $$payload.out += `<div class=\"w-full\"><h4 class=\"mb-2 text-sm font-medium\">${escape_html(title)}</h4> `;\n  if (chartData.length === 0) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"flex items-center justify-center\"${attr_style(`height: ${stringify(height)}px;`)}><div class=\"text-muted-foreground text-sm\">No data available</div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<!---->`;\n    Chart_container($$payload, {\n      config: chartConfig,\n      class: \"w-full\",\n      style: `height: ${stringify(height)}px;`,\n      children: ($$payload2) => {\n        {\n          let tooltip = function($$payload3) {\n            $$payload3.out += `<!---->`;\n            Chart_tooltip($$payload3, {});\n            $$payload3.out += `<!---->`;\n          };\n          BarChart($$payload2, {\n            data: chartData(),\n            x: \"date\",\n            axis: \"x\",\n            series: [\n              {\n                key: \"value\",\n                label: \"Status\",\n                color: \"var(--chart-1)\"\n              }\n            ],\n            props: {\n              xAxis: { format: (d) => d.slice(0, 3) }\n            },\n            tooltip,\n            $$slots: { tooltip: true }\n          });\n        }\n      },\n      $$slots: { default: true }\n    });\n    $$payload.out += `<!----> `;\n    if (historyData && historyData.length > 0) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"text-muted-foreground mt-1 flex justify-between text-xs\"><span>${escape_html(formatDate(historyData[0].date))}</span> <span>${escape_html(formatDate(historyData[historyData.length - 1].date))}</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]-->`;\n  }\n  $$payload.out += `<!--]--></div>`;\n  pop();\n}\nfunction ServiceStatusCard($$payload, $$props) {\n  push();\n  let chartColor;\n  let name = $$props[\"name\"];\n  let status = $$props[\"status\"];\n  let description = fallback($$props[\"description\"], \"\");\n  let historyData = fallback($$props[\"historyData\"], () => [], true);\n  let metrics = fallback($$props[\"metrics\"], () => ({}), true);\n  function getStatusIcon(status2) {\n    switch (status2) {\n      case \"operational\":\n        return Circle_check_big;\n      case \"degraded\":\n        return Triangle_alert;\n      case \"outage\":\n        return Circle_x;\n      case \"maintenance\":\n        return Clock;\n      default:\n        return Triangle_alert;\n    }\n  }\n  function getStatusColor(status2) {\n    switch (status2) {\n      case \"operational\":\n        return \"#4CAF50\";\n      case \"degraded\":\n        return \"#FFC107\";\n      case \"outage\":\n        return \"#F44336\";\n      case \"maintenance\":\n        return \"#2196F3\";\n      default:\n        return \"#9E9E9E\";\n    }\n  }\n  function getStatusVariant(status2) {\n    switch (status2) {\n      case \"operational\":\n        return \"success\";\n      case \"degraded\":\n        return \"warning\";\n      case \"outage\":\n        return \"destructive\";\n      case \"maintenance\":\n        return \"secondary\";\n      default:\n        return \"outline\";\n    }\n  }\n  function formatNumber(num) {\n    return num.toString().replace(/\\B(?=(\\d{3})+(?!\\d))/g, \",\");\n  }\n  function formatTime(ms) {\n    return ms >= 1e3 ? `${(ms / 1e3).toFixed(1)}s` : `${ms}ms`;\n  }\n  chartColor = getStatusColor(status);\n  $$payload.out += `<div class=\"rounded-lg border p-4\"><div class=\"mb-3 flex items-center justify-between\"><div class=\"flex items-center gap-3\"><div class=\"h-3 w-3 rounded-full\"${attr_style(`background-color: ${stringify(chartColor)}`)}></div> <h3 class=\"font-medium\">${escape_html(name)}</h3></div> `;\n  Badge($$payload, {\n    variant: getStatusVariant(status),\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->`;\n      getStatusIcon(status)?.($$payload2, { class: \"mr-1 h-3 w-3\" });\n      $$payload2.out += `<!----> ${escape_html(status.charAt(0).toUpperCase() + status.slice(1))}`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> `;\n  if (description) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<p class=\"text-muted-foreground mb-3 text-sm\">${escape_html(description)}</p>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <div class=\"mb-4\">`;\n  ServiceTrendChart($$payload, {\n    historyData,\n    metric: \"successRate\",\n    title: \"Last 30 Days\",\n    height: 80\n  });\n  $$payload.out += `<!----></div> `;\n  if (metrics && Object.keys(metrics).length > 0) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"grid grid-cols-2 gap-2 text-xs\">`;\n    if (metrics.responseTime !== void 0) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"flex justify-between\"><span class=\"text-muted-foreground\">Response Time:</span> <span class=\"font-medium\">${escape_html(formatTime(metrics.responseTime))}</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--> `;\n    if (metrics.successRate !== void 0) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"flex justify-between\"><span class=\"text-muted-foreground\">Success Rate:</span> <span class=\"font-medium\">${escape_html(metrics.successRate.toFixed(1))}%</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--> `;\n    if (metrics.errorRate !== void 0) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"flex justify-between\"><span class=\"text-muted-foreground\">Error Rate:</span> <span class=\"font-medium\">${escape_html(metrics.errorRate.toFixed(1))}%</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--> `;\n    if (metrics.requestCount !== void 0) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"flex justify-between\"><span class=\"text-muted-foreground\">Requests:</span> <span class=\"font-medium\">${escape_html(formatNumber(metrics.requestCount))}</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--> `;\n    if (metrics.queueSize !== void 0) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"flex justify-between\"><span class=\"text-muted-foreground\">Queue Size:</span> <span class=\"font-medium\">${escape_html(formatNumber(metrics.queueSize))}</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--> `;\n    if (metrics.processingCount !== void 0) {\n      $$payload.out += \"<!--[-->\";\n      $$payload.out += `<div class=\"flex justify-between\"><span class=\"text-muted-foreground\">Processing:</span> <span class=\"font-medium\">${escape_html(formatNumber(metrics.processingCount))}</span></div>`;\n    } else {\n      $$payload.out += \"<!--[!-->\";\n    }\n    $$payload.out += `<!--]--></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div>`;\n  bind_props($$props, {\n    name,\n    status,\n    description,\n    historyData,\n    metrics\n  });\n  pop();\n}\nfunction SystemMetrics($$payload, $$props) {\n  push();\n  const { metrics } = $$props;\n  $$payload.out += `<!---->`;\n  Card($$payload, {\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->`;\n      Card_content($$payload2, {\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"grid gap-4 md:grid-cols-4\"><div class=\"flex flex-col items-center justify-center rounded-lg border p-4\"><div class=\"text-2xl font-bold\">${escape_html(metrics.uptime.toFixed(2))}%</div> <p class=\"text-muted-foreground text-sm\">Uptime (30 days)</p></div> <div class=\"flex flex-col items-center justify-center rounded-lg border p-4\"><div class=\"text-2xl font-bold\">${escape_html(metrics.emailDeliveryRate.toFixed(1))}%</div> <p class=\"text-muted-foreground text-sm\">Email Delivery Rate</p></div> <div class=\"flex flex-col items-center justify-center rounded-lg border p-4\"><div class=\"text-2xl font-bold\">${escape_html(metrics.apiResponseTime)}ms</div> <p class=\"text-muted-foreground text-sm\">API Response Time</p></div> <div class=\"flex flex-col items-center justify-center rounded-lg border p-4\"><div class=\"text-2xl font-bold\">${escape_html(metrics.jobSuccessRate.toFixed(1))}%</div> <p class=\"text-muted-foreground text-sm\">Job Success Rate</p></div></div>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!---->`;\n  pop();\n}\nfunction MaintenanceEvents($$payload, $$props) {\n  push();\n  const { recentIncidents } = $$props;\n  $$payload.out += `<div class=\"max-auto container mb-8 px-8\"><h2 class=\"mb-4 text-xl font-semibold\">Recent Notices</h2> `;\n  if (recentIncidents.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<!---->`;\n    Accordion_root($$payload, {\n      type: \"multiple\",\n      class: \"w-full space-y-4\",\n      children: ($$payload2) => {\n        const each_array = ensure_array_like(recentIncidents);\n        $$payload2.out += `<!--[-->`;\n        for (let i = 0, $$length = each_array.length; i < $$length; i++) {\n          let incident = each_array[i];\n          MaintenanceAccordion($$payload2, { incident, index: i });\n        }\n        $$payload2.out += `<!--]-->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div class=\"rounded-lg border p-6 text-center\"><div class=\"flex items-center justify-center\"><div class=\"mr-2 h-4 w-4 rounded-full bg-green-500\"></div> <p class=\"text-muted-foreground\">No notices reported for the past 7 days</p></div></div>`;\n  }\n  $$payload.out += `<!--]--></div>`;\n  pop();\n}\nfunction StatusPage($$payload, $$props) {\n  push();\n  const { pageData } = $$props;\n  let services = [];\n  let metrics = {\n    uptime: 99.9,\n    emailDeliveryRate: 0,\n    apiResponseTime: 250,\n    jobSuccessRate: 0\n  };\n  let lastUpdated = /* @__PURE__ */ new Date();\n  let recentIncidents = [];\n  function formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    }).format(date);\n  }\n  function getServiceMetrics(serviceName) {\n    const defaultMetrics = {\n      responseTime: 0,\n      successRate: 0,\n      requestCount: 0,\n      errorRate: 0\n    };\n    const serviceHealth = pageData.serviceHealth || {};\n    const serviceMapping = {\n      Matches: { status: \"operational\", details: {} },\n      Jobs: { status: \"operational\", details: {} },\n      Tracker: { status: \"operational\", details: {} },\n      Documents: { status: \"operational\", details: {} },\n      Automation: { status: \"operational\", details: {} },\n      System: { status: \"operational\", details: {} },\n      Website: { status: \"operational\", details: {} }\n    };\n    if (serviceHealth.web) {\n      serviceMapping.Website = serviceHealth.web;\n    }\n    if (serviceHealth.api) {\n      serviceMapping.Jobs = serviceHealth.api;\n      serviceMapping.Matches = serviceHealth.api;\n    }\n    if (serviceHealth.worker) {\n      serviceMapping.Automation = serviceHealth.worker;\n    }\n    if (serviceHealth.database) {\n      serviceMapping.System = serviceHealth.database;\n      serviceMapping.Tracker = serviceHealth.database;\n      serviceMapping.Documents = serviceHealth.database;\n    }\n    const serviceData = serviceMapping[serviceName] || {};\n    const details = serviceData.details || {};\n    return {\n      responseTime: details.responseTime || defaultMetrics.responseTime,\n      successRate: details.successRate || (serviceName === \"Jobs\" ? metrics.jobSuccessRate : serviceName === \"System\" ? serviceHealth.database?.status === \"operational\" ? 100 : 80 : serviceName === \"Website\" ? serviceHealth.web?.status === \"operational\" ? 100 : 80 : defaultMetrics.successRate),\n      requestCount: details.requestCount || defaultMetrics.requestCount,\n      errorRate: details.errorRate || (serviceName === \"Jobs\" ? 100 - metrics.jobSuccessRate : serviceName === \"System\" ? serviceHealth.database?.status === \"operational\" ? 0 : 20 : serviceName === \"Website\" ? serviceHealth.web?.status === \"operational\" ? 0 : 20 : defaultMetrics.errorRate),\n      // Add additional metrics if available\n      ...details.queueSize !== void 0 ? { queueSize: details.queueSize } : {},\n      ...details.processingCount !== void 0 ? { processingCount: details.processingCount } : {},\n      ...details.memoryUsage !== void 0 ? { memoryUsage: details.memoryUsage } : {},\n      ...details.dbSizeMB !== void 0 ? { dbSizeMB: details.dbSizeMB } : {},\n      ...details.activeConnections !== void 0 ? { activeConnections: details.activeConnections } : {},\n      ...details.connectedClients !== void 0 ? { connectedClients: details.connectedClients } : {}\n    };\n  }\n  onDestroy(() => {\n  });\n  $$payload.out += `<div class=\"flex flex-col gap-4\"><div class=\"border-border flex items-center justify-between border-b p-6\"><div class=\"flex flex-col\"><h1 class=\"text-3xl font-bold\">System Status</h1> <p class=\"text-muted-foreground\">Current status of Hirli services</p></div> <div class=\"flex items-center gap-2 self-end\"><p class=\"text-muted-foreground text-sm\">Last updated: ${escape_html(formatDate(lastUpdated))}</p></div></div> `;\n  SystemMetrics($$payload, { metrics });\n  $$payload.out += `<!----> <!---->`;\n  Card($$payload, {\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->`;\n      Card_header($$payload2, {\n        children: ($$payload3) => {\n          $$payload3.out += `<!---->`;\n          Card_title($$payload3, {\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->Service Status`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> <!---->`;\n          Card_description($$payload3, {\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->Current status of core application services`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> <!---->`;\n      Card_content($$payload2, {\n        children: ($$payload3) => {\n          const each_array = ensure_array_like(services);\n          $$payload3.out += `<div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\"><!--[-->`;\n          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n            let service = each_array[$$index];\n            ServiceStatusCard($$payload3, {\n              name: service.name,\n              status: service.status,\n              description: service.description || \"\",\n              historyData: pageData.serviceHistory?.[service.name] || [],\n              metrics: getServiceMetrics(service.name)\n            });\n          }\n          $$payload3.out += `<!--]--></div>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----> `;\n  MaintenanceEvents($$payload, { recentIncidents });\n  $$payload.out += `<!----></div>`;\n  pop();\n}\nfunction _page($$payload, $$props) {\n  let data = $$props[\"data\"];\n  SEO($$payload, {\n    title: \"System Status | Hirli\",\n    description: \"Check the current status of Hirli services and systems.\",\n    keywords: \"system status, service status, uptime, Hirli status\"\n  });\n  $$payload.out += `<!----> `;\n  StatusPage($$payload, { pageData: data });\n  $$payload.out += `<!----> <div class=\"container mb-8\"><a href=\"/system-status/history\" class=\"flex w-full items-center justify-center rounded-lg border p-3 text-center hover:bg-gray-50 dark:hover:bg-gray-900\">`;\n  History($$payload, { class: \"mr-2 h-4 w-4\" });\n  $$payload.out += `<!----> <span>View notice history</span></a></div>`;\n  bind_props($$props, { data });\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,WAAW,GAAG,EAAE;AACpB,IAAI,MAAM,GAAG,aAAa;AAC1B,IAAI,KAAK,GAAG,cAAc;AAC1B,IAAI,MAAM,GAAG;AACb,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,MAAM;AAC1B,IAAI,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AAClD,MAAM,MAAM,QAAQ,GAAG,EAAE;AACzB,MAAM,MAAM,KAAK,mBAAmB,IAAI,IAAI,EAAE;AAC9C,MAAM,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACpC,QAAQ,MAAM,IAAI,mBAAmB,IAAI,IAAI,EAAE;AAC/C,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AACzC,QAAQ,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE;AAClC,QAAQ,IAAI,MAAM,GAAG,aAAa;AAClC,QAAQ,IAAI,IAAI,GAAG,GAAG,EAAE,MAAM,GAAG,QAAQ;AACzC,aAAa,IAAI,IAAI,GAAG,GAAG,EAAE,MAAM,GAAG,UAAU;AAChD,aAAa,IAAI,IAAI,GAAG,GAAG,EAAE,MAAM,GAAG,aAAa;AACnD,QAAQ,QAAQ,CAAC,IAAI,CAAC;AACtB,UAAU,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AAC9C,UAAU,MAAM;AAChB,UAAU,KAAK,EAAE,MAAM,KAAK,aAAa,GAAG,GAAG,GAAG,MAAM,KAAK,UAAU,GAAG,EAAE,GAAG,MAAM,KAAK,aAAa,GAAG,EAAE,GAAG,CAAC;AAChH,UAAU,WAAW,EAAE,cAAc,CAAC,MAAM;AAC5C,SAAS,CAAC;AACV;AACA,MAAM,OAAO,QAAQ;AACrB;AACA,IAAI,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AACtC,MAAM,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACjC,MAAM,MAAM,EAAE,IAAI,CAAC,MAAM;AACzB,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AAC9B,MAAM,WAAW,EAAE,cAAc,CAAC,IAAI,CAAC,MAAM;AAC7C,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,OAAO,EAAE;AAC/B,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC;AAClC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;AAC/E;AACA,EAAE,SAAS,cAAc,CAAC,MAAM,EAAE;AAClC,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,aAAa;AACxB,QAAQ,OAAO,gBAAgB;AAC/B,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,gBAAgB;AAC/B,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,gBAAgB;AAC/B,MAAM,KAAK,aAAa;AACxB,QAAQ,OAAO,gBAAgB;AAC/B,MAAM;AACN,QAAQ,OAAO,8BAA8B;AAC7C;AACA;AACA,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,WAAW,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,gBAAgB,EAAE;AAClE,IAAI,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,gBAAgB,EAAE;AAC5D,IAAI,MAAM,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,gBAAgB,EAAE;AACxD,IAAI,WAAW,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,gBAAgB;AAChE,GAAG;AACH,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yDAAyD,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;AACzG,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,6CAA6C,EAAE,UAAU,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,yEAAyE,CAAC;AAC7L,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,IAAI,eAAe,CAAC,SAAS,EAAE;AAC/B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AAC9C,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ;AACR,UAAU,IAAI,OAAO,GAAG,SAAS,UAAU,EAAE;AAC7C,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,YAAY,aAAa,CAAC,UAAU,EAAE,EAAE,CAAC;AACzC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,WAAW;AACX,UAAU,QAAQ,CAAC,UAAU,EAAE;AAC/B,YAAY,IAAI,EAAE,SAAS,EAAE;AAC7B,YAAY,CAAC,EAAE,MAAM;AACrB,YAAY,IAAI,EAAE,GAAG;AACrB,YAAY,MAAM,EAAE;AACpB,cAAc;AACd,gBAAgB,GAAG,EAAE,OAAO;AAC5B,gBAAgB,KAAK,EAAE,QAAQ;AAC/B,gBAAgB,KAAK,EAAE;AACvB;AACA,aAAa;AACb,YAAY,KAAK,EAAE;AACnB,cAAc,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACnD,aAAa;AACb,YAAY,OAAO;AACnB,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ;AACA,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,IAAI,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/C,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,2EAA2E,EAAE,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,EAAE,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC;AAClP,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,UAAU;AAChB,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;AAChC,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC;AACxD,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AACpE,EAAE,IAAI,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,CAAC;AAC9D,EAAE,SAAS,aAAa,CAAC,OAAO,EAAE;AAClC,IAAI,QAAQ,OAAO;AACnB,MAAM,KAAK,aAAa;AACxB,QAAQ,OAAO,gBAAgB;AAC/B,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,cAAc;AAC7B,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,QAAQ;AACvB,MAAM,KAAK,aAAa;AACxB,QAAQ,OAAO,KAAK;AACpB,MAAM;AACN,QAAQ,OAAO,cAAc;AAC7B;AACA;AACA,EAAE,SAAS,cAAc,CAAC,OAAO,EAAE;AACnC,IAAI,QAAQ,OAAO;AACnB,MAAM,KAAK,aAAa;AACxB,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,aAAa;AACxB,QAAQ,OAAO,SAAS;AACxB,MAAM;AACN,QAAQ,OAAO,SAAS;AACxB;AACA;AACA,EAAE,SAAS,gBAAgB,CAAC,OAAO,EAAE;AACrC,IAAI,QAAQ,OAAO;AACnB,MAAM,KAAK,aAAa;AACxB,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,UAAU;AACrB,QAAQ,OAAO,SAAS;AACxB,MAAM,KAAK,QAAQ;AACnB,QAAQ,OAAO,aAAa;AAC5B,MAAM,KAAK,aAAa;AACxB,QAAQ,OAAO,WAAW;AAC1B,MAAM;AACN,QAAQ,OAAO,SAAS;AACxB;AACA;AACA,EAAE,SAAS,YAAY,CAAC,GAAG,EAAE;AAC7B,IAAI,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,uBAAuB,EAAE,GAAG,CAAC;AAC/D;AACA,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE;AAC1B,IAAI,OAAO,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC;AAC9D;AACA,EAAE,UAAU,GAAG,cAAc,CAAC,MAAM,CAAC;AACrC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,6JAA6J,EAAE,UAAU,CAAC,CAAC,kBAAkB,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,gCAAgC,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC,YAAY,CAAC;AAC7S,EAAE,KAAK,CAAC,SAAS,EAAE;AACnB,IAAI,OAAO,EAAE,gBAAgB,CAAC,MAAM,CAAC;AACrC,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,MAAM,aAAa,CAAC,MAAM,CAAC,GAAG,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACpE,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClG,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,8CAA8C,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC;AACpG,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;AAChD,EAAE,iBAAiB,CAAC,SAAS,EAAE;AAC/B,IAAI,WAAW;AACf,IAAI,MAAM,EAAE,aAAa;AACzB,IAAI,KAAK,EAAE,cAAc;AACzB,IAAI,MAAM,EAAE;AACZ,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,IAAI,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAClD,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,4CAA4C,CAAC;AACnE,IAAI,IAAI,OAAO,CAAC,YAAY,KAAK,MAAM,EAAE;AACzC,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,sHAAsH,EAAE,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC;AAC5M,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,IAAI,IAAI,OAAO,CAAC,WAAW,KAAK,MAAM,EAAE;AACxC,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,qHAAqH,EAAE,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;AAC1M,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,MAAM,EAAE;AACtC,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,mHAAmH,EAAE,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC;AACtM,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,IAAI,IAAI,OAAO,CAAC,YAAY,KAAK,MAAM,EAAE;AACzC,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,iHAAiH,EAAE,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,aAAa,CAAC;AACzM,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,IAAI,IAAI,OAAO,CAAC,SAAS,KAAK,MAAM,EAAE;AACtC,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,mHAAmH,EAAE,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC;AACxM,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAChC,IAAI,IAAI,OAAO,CAAC,eAAe,KAAK,MAAM,EAAE;AAC5C,MAAM,SAAS,CAAC,GAAG,IAAI,UAAU;AACjC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,mHAAmH,EAAE,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,aAAa,CAAC;AAC9M,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,GAAG,IAAI,WAAW;AAClC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,GAAG,EAAE;AACP;AACA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO;AAC7B,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,oJAAoJ,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,yLAAyL,EAAE,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,4LAA4L,EAAE,WAAW,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,2LAA2L,EAAE,WAAW,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,iFAAiF,CAAC;AACx+B,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,OAAO;AACrC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,qGAAqG,CAAC;AAC1H,EAAE,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,IAAI,cAAc,CAAC,SAAS,EAAE;AAC9B,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,KAAK,EAAE,kBAAkB;AAC/B,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,MAAM,UAAU,GAAG,iBAAiB,CAAC,eAAe,CAAC;AAC7D,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACzE,UAAU,IAAI,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC;AACtC,UAAU,oBAAoB,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;AAClE;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,gPAAgP,CAAC;AACvQ;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC9B,EAAE,IAAI,QAAQ,GAAG,EAAE;AACnB,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,MAAM,EAAE,IAAI;AAChB,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,eAAe,EAAE,GAAG;AACxB,IAAI,cAAc,EAAE;AACpB,GAAG;AACH,EAAE,IAAI,WAAW,mBAAmB,IAAI,IAAI,EAAE;AAC9C,EAAE,IAAI,eAAe,GAAG,EAAE;AAC1B,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;AAC5C,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,GAAG,EAAE,SAAS;AACpB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,MAAM,EAAE;AACd,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC;AACnB;AACA,EAAE,SAAS,iBAAiB,CAAC,WAAW,EAAE;AAC1C,IAAI,MAAM,cAAc,GAAG;AAC3B,MAAM,YAAY,EAAE,CAAC;AACrB,MAAM,WAAW,EAAE,CAAC;AACpB,MAAM,YAAY,EAAE,CAAC;AACrB,MAAM,SAAS,EAAE;AACjB,KAAK;AACL,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,IAAI,EAAE;AACtD,IAAI,MAAM,cAAc,GAAG;AAC3B,MAAM,OAAO,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;AACrD,MAAM,IAAI,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;AAClD,MAAM,OAAO,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;AACrD,MAAM,SAAS,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;AACvD,MAAM,UAAU,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;AACxD,MAAM,MAAM,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,EAAE;AACpD,MAAM,OAAO,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE;AACnD,KAAK;AACL,IAAI,IAAI,aAAa,CAAC,GAAG,EAAE;AAC3B,MAAM,cAAc,CAAC,OAAO,GAAG,aAAa,CAAC,GAAG;AAChD;AACA,IAAI,IAAI,aAAa,CAAC,GAAG,EAAE;AAC3B,MAAM,cAAc,CAAC,IAAI,GAAG,aAAa,CAAC,GAAG;AAC7C,MAAM,cAAc,CAAC,OAAO,GAAG,aAAa,CAAC,GAAG;AAChD;AACA,IAAI,IAAI,aAAa,CAAC,MAAM,EAAE;AAC9B,MAAM,cAAc,CAAC,UAAU,GAAG,aAAa,CAAC,MAAM;AACtD;AACA,IAAI,IAAI,aAAa,CAAC,QAAQ,EAAE;AAChC,MAAM,cAAc,CAAC,MAAM,GAAG,aAAa,CAAC,QAAQ;AACpD,MAAM,cAAc,CAAC,OAAO,GAAG,aAAa,CAAC,QAAQ;AACrD,MAAM,cAAc,CAAC,SAAS,GAAG,aAAa,CAAC,QAAQ;AACvD;AACA,IAAI,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,CAAC,IAAI,EAAE;AACzD,IAAI,MAAM,OAAO,GAAG,WAAW,CAAC,OAAO,IAAI,EAAE;AAC7C,IAAI,OAAO;AACX,MAAM,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,cAAc,CAAC,YAAY;AACvE,MAAM,WAAW,EAAE,OAAO,CAAC,WAAW,KAAK,WAAW,KAAK,MAAM,GAAG,OAAO,CAAC,cAAc,GAAG,WAAW,KAAK,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,MAAM,KAAK,aAAa,GAAG,GAAG,GAAG,EAAE,GAAG,WAAW,KAAK,SAAS,GAAG,aAAa,CAAC,GAAG,EAAE,MAAM,KAAK,aAAa,GAAG,GAAG,GAAG,EAAE,GAAG,cAAc,CAAC,WAAW,CAAC;AACtS,MAAM,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,cAAc,CAAC,YAAY;AACvE,MAAM,SAAS,EAAE,OAAO,CAAC,SAAS,KAAK,WAAW,KAAK,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,cAAc,GAAG,WAAW,KAAK,QAAQ,GAAG,aAAa,CAAC,QAAQ,EAAE,MAAM,KAAK,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,WAAW,KAAK,SAAS,GAAG,aAAa,CAAC,GAAG,EAAE,MAAM,KAAK,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,SAAS,CAAC;AAClS;AACA,MAAM,GAAG,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE;AAC7E,MAAM,GAAG,OAAO,CAAC,eAAe,KAAK,MAAM,GAAG,EAAE,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,GAAG,EAAE;AAC/F,MAAM,GAAG,OAAO,CAAC,WAAW,KAAK,MAAM,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,GAAG,EAAE;AACnF,MAAM,GAAG,OAAO,CAAC,QAAQ,KAAK,MAAM,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE;AAC1E,MAAM,GAAG,OAAO,CAAC,iBAAiB,KAAK,MAAM,GAAG,EAAE,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,EAAE,GAAG,EAAE;AACrG,MAAM,GAAG,OAAO,CAAC,gBAAgB,KAAK,MAAM,GAAG,EAAE,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,GAAG;AAChG,KAAK;AACL;AACA,EAAE,SAAS,CAAC,MAAM;AAClB,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yWAAyW,EAAE,WAAW,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC;AACtb,EAAE,aAAa,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC;AACvC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACpC,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,MAAM,WAAW,CAAC,UAAU,EAAE;AAC9B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,UAAU,UAAU,CAAC,UAAU,EAAE;AACjC,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACvD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC7C,UAAU,gBAAgB,CAAC,UAAU,EAAE;AACvC,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,kDAAkD,CAAC;AACpF,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACzC,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AACxD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,8DAA8D,CAAC;AAC5F,UAAU,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC7F,YAAY,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AAC7C,YAAY,iBAAiB,CAAC,UAAU,EAAE;AAC1C,cAAc,IAAI,EAAE,OAAO,CAAC,IAAI;AAChC,cAAc,MAAM,EAAE,OAAO,CAAC,MAAM;AACpC,cAAc,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;AACpD,cAAc,WAAW,EAAE,QAAQ,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE;AACxE,cAAc,OAAO,EAAE,iBAAiB,CAAC,OAAO,CAAC,IAAI;AACrD,aAAa,CAAC;AACd;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC5C,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,iBAAiB,CAAC,SAAS,EAAE,EAAE,eAAe,EAAE,CAAC;AACnD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,uBAAuB;AAClC,IAAI,WAAW,EAAE,yDAAyD;AAC1E,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC3C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,+LAA+L,CAAC;AACpN,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC/C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,kDAAkD,CAAC;AACvE,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B;;;;"}