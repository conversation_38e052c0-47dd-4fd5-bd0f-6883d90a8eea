{"version": 3, "file": "resume-usage-B98ib_h-.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/resume-usage.js"], "sourcesContent": ["import { hasReachedLimit, trackFeatureUsage } from \"./feature-usage.js\";\nasync function trackDocumentUpload(userId) {\n  return trackFeatureUsage(userId, \"document_storage\", \"document_count\", 1);\n}\nasync function trackResumeCreation(userId) {\n  await trackFeatureUsage(userId, \"resume_builder\", \"resume_versions\", 1);\n  return trackDocumentUpload(userId);\n}\nasync function canCreateResume(userId) {\n  if (process.env.NODE_ENV === \"development\" || process.env.VITE_DISABLE_FEATURE_LIMITS === \"true\") {\n    console.log(\"Development mode: Bypassing document limit check\");\n    return true;\n  }\n  return !await hasReachedLimit(userId, \"document_storage\", \"document_count\");\n}\nexport {\n  trackResumeCreation as a,\n  canCreateResume as c,\n  trackDocumentUpload as t\n};\n"], "names": [], "mappings": ";;AACA,eAAe,mBAAmB,CAAC,MAAM,EAAE;AAC3C,EAAE,OAAO,iBAAiB,CAAC,MAAM,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,CAAC,CAAC;AAC3E;AACA,eAAe,mBAAmB,CAAC,MAAM,EAAE;AAC3C,EAAE,MAAM,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,CAAC,CAAC;AACzE,EAAE,OAAO,mBAAmB,CAAC,MAAM,CAAC;AACpC;AACA,eAAe,eAAe,CAAC,MAAM,EAAE;AACvC,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM,EAAE;AACpG,IAAI,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC;AACnE,IAAI,OAAO,IAAI;AACf;AACA,EAAE,OAAO,CAAC,MAAM,eAAe,CAAC,MAAM,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;AAC7E;;;;"}