{"version": 3, "file": "59-ZqMXCpd1.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/billing/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/59.js"], "sourcesContent": ["import { r as redirect } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { i as initializePlansInDatabase, b as getPlanById } from \"../../../../../chunks/plan-sync.js\";\nconst load = async ({ locals }) => {\n  const user = locals.user;\n  if (!user || !user.email) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  const userData = await prisma.user.findUnique({\n    where: { email: user.email },\n    include: {\n      subscriptions: {\n        orderBy: { createdAt: \"desc\" },\n        take: 1\n      }\n    }\n  });\n  if (!userData) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  locals.user = userData;\n  await initializePlansInDatabase();\n  let currentPlan = null;\n  try {\n    currentPlan = await getPlanById(userData.role || \"free\");\n    if (!currentPlan) {\n      console.error(`Plan not found for role: ${userData.role} after initialization.`);\n      throw new Error(`Plan not found for role: ${userData.role}`);\n    }\n  } catch (error) {\n    console.error(\"Error getting plan from database:\", error);\n    throw error;\n  }\n  const startOfMonth = /* @__PURE__ */ new Date();\n  startOfMonth.setDate(1);\n  startOfMonth.setHours(0, 0, 0, 0);\n  const resumeScansUsed = await prisma.documentSubmission.count({\n    where: {\n      userId: userData.id,\n      createdAt: { gte: startOfMonth }\n    }\n  });\n  const profilesUsed = await prisma.profile.count({\n    where: {\n      userId: userData.id\n    }\n  });\n  let formattedUsage = [];\n  try {\n    const tableExists = await prisma.$queryRaw`\n      SELECT EXISTS (\n        SELECT FROM information_schema.tables\n        WHERE table_schema = 'web' AND table_name = 'FeatureUsage'\n      );\n    `;\n    const tablesExist = Array.isArray(tableExists) && tableExists.length > 0 && tableExists[0].exists;\n    if (tablesExist) {\n      const usageRecords = await prisma.featureUsage.findMany({\n        where: {\n          userId: userData.id\n        },\n        orderBy: {\n          updatedAt: \"desc\"\n        }\n      });\n      const featureIds = [...new Set(usageRecords.map((record) => record.featureId))];\n      const limitIds = [...new Set(usageRecords.map((record) => record.limitId))];\n      const features = await prisma.feature.findMany({\n        where: {\n          id: {\n            in: featureIds\n          }\n        }\n      });\n      const limits = await prisma.featureLimit.findMany({\n        where: {\n          id: {\n            in: limitIds\n          }\n        }\n      });\n      const featureMap = features.reduce((acc, feature) => {\n        acc[feature.id] = feature;\n        return acc;\n      }, {});\n      const limitMap = limits.reduce((acc, limit) => {\n        acc[limit.id] = limit;\n        return acc;\n      }, {});\n      const featureUsage = usageRecords.map((usage) => ({\n        id: usage.id,\n        userId: usage.userId,\n        featureId: usage.featureId,\n        limitId: usage.limitId,\n        used: usage.used,\n        period: usage.period,\n        updatedAt: usage.updatedAt,\n        featureName: featureMap[usage.featureId]?.name || \"Unknown Feature\",\n        featureDescription: featureMap[usage.featureId]?.description || \"\",\n        limitName: limitMap[usage.limitId]?.name || \"Unknown Limit\",\n        limitValue: limitMap[usage.limitId]?.defaultValue || \"0\",\n        limitType: limitMap[usage.limitId]?.type || \"monthly\"\n      }));\n      formattedUsage = Array.isArray(featureUsage) ? featureUsage.map((usage) => {\n        let percentUsed = null;\n        if (usage.limitType === \"monthly\") {\n          const limitValue = parseInt(usage.limitValue, 10);\n          percentUsed = limitValue > 0 ? Math.min(100, usage.used / limitValue * 100) : 0;\n        }\n        return {\n          id: usage.id,\n          featureId: usage.featureId,\n          featureName: usage.featureName,\n          limitId: usage.limitId,\n          limitName: usage.limitName,\n          used: usage.used,\n          limit: usage.limitValue,\n          percentUsed,\n          period: usage.period,\n          updatedAt: usage.updatedAt\n        };\n      }) : [];\n    } else {\n      formattedUsage = [];\n    }\n  } catch (error) {\n    console.error(\"Error fetching feature usage:\", error);\n    formattedUsage = [];\n  }\n  let resumeScansLimit = 10;\n  if (currentPlan?.limits?.resumesPerMonth) {\n    const limitValue = String(currentPlan.limits.resumesPerMonth);\n    const parts = limitValue.split(\"/\");\n    resumeScansLimit = parseInt(parts[0], 10) || 10;\n  }\n  let profilesLimit = 1;\n  if (currentPlan?.limits?.profiles) {\n    const limitValue = String(currentPlan.limits.profiles);\n    profilesLimit = parseInt(limitValue, 10) || 1;\n  }\n  const resumeScansRemaining = Math.max(0, resumeScansLimit - resumeScansUsed);\n  let paymentMethods = [];\n  let invoices = [];\n  let upcomingInvoice = null;\n  let subscriptionData = null;\n  let defaultPaymentMethod = null;\n  if (userData.stripeCustomerId) {\n    const isProd = process.env.NODE_ENV === \"production\";\n    const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\n    const Stripe = (await import(\"stripe\")).default;\n    const stripe = new Stripe(stripeSecret, {\n      apiVersion: \"2025-04-30.basil\"\n    });\n    try {\n      const paymentMethodsResponse = await stripe.paymentMethods.list({\n        customer: userData.stripeCustomerId,\n        type: \"card\"\n      });\n      paymentMethods = paymentMethodsResponse.data;\n      const customer = await stripe.customers.retrieve(userData.stripeCustomerId);\n      if (customer && !(\"deleted\" in customer) && customer.invoice_settings?.default_payment_method) {\n        defaultPaymentMethod = customer.invoice_settings.default_payment_method;\n      }\n      paymentMethods = paymentMethods.map((method) => ({\n        ...method,\n        isDefault: method.id === defaultPaymentMethod\n      }));\n      const invoicesResponse = await stripe.invoices.list({\n        customer: userData.stripeCustomerId,\n        limit: 10\n      });\n      invoices = invoicesResponse.data;\n      try {\n        upcomingInvoice = null;\n      } catch (err) {\n        console.log(\"No upcoming invoice found:\", err);\n      }\n      const subscriptionsResponse = await stripe.subscriptions.list({\n        customer: userData.stripeCustomerId,\n        limit: 5,\n        // Get more than just active subscriptions\n        expand: [\"data.default_payment_method\", \"data.items.data.price\"]\n      });\n      if (subscriptionsResponse.data.length > 0) {\n        let subscription = subscriptionsResponse.data.find((sub) => sub.status === \"active\");\n        if (!subscription) {\n          subscription = subscriptionsResponse.data.find((sub) => sub.status === \"trialing\");\n        }\n        if (!subscription) {\n          subscription = subscriptionsResponse.data[0];\n        }\n        let paymentMethodDetails = null;\n        if (subscription.default_payment_method) {\n          const paymentMethod = typeof subscription.default_payment_method === \"string\" ? null : subscription.default_payment_method;\n          if (paymentMethod && paymentMethod.card) {\n            paymentMethodDetails = {\n              id: paymentMethod.id,\n              brand: paymentMethod.card.brand,\n              last4: paymentMethod.card.last4,\n              expMonth: paymentMethod.card.exp_month,\n              expYear: paymentMethod.card.exp_year\n            };\n          }\n        }\n        const subscriptionItem = subscription.items.data[0];\n        const price = subscriptionItem?.price;\n        const productId = price?.product;\n        const interval = price?.recurring?.interval || \"month\";\n        const intervalCount = price?.recurring?.interval_count || 1;\n        const billingCycle = interval === \"year\" || intervalCount > 1 ? \"annual\" : \"monthly\";\n        let productName = null;\n        let productDescription = null;\n        if (productId) {\n          try {\n            const product = await stripe.products.retrieve(productId);\n            productName = product.name;\n            productDescription = product.description;\n          } catch (error) {\n            console.error(\"Error fetching product details:\", error);\n          }\n        }\n        const stripeSubscription = subscription;\n        const pauseCollection = stripeSubscription.pause_collection || null;\n        const metadata = stripeSubscription.metadata || {};\n        const isPaused = pauseCollection !== null || metadata.pause_at_period_end === \"true\" || stripeSubscription.status === \"paused\" || stripeSubscription.status === \"pausing_at_period_end\" || stripeSubscription.cancel_at_period_end && metadata.action_at_period_end === \"pause\";\n        const planChangesOnDate = new Date(stripeSubscription.current_period_end * 1e3);\n        let startTimestamp = stripeSubscription.current_period_start;\n        let endTimestamp = stripeSubscription.current_period_end;\n        if (!startTimestamp && subscription.items?.data?.length > 0) {\n          const firstItem = subscription.items.data[0];\n          if (firstItem.current_period_start) {\n            startTimestamp = firstItem.current_period_start;\n          }\n          if (firstItem.current_period_end) {\n            endTimestamp = firstItem.current_period_end;\n          }\n        }\n        const startDate = startTimestamp ? new Date(startTimestamp * 1e3) : null;\n        const endDate = endTimestamp ? new Date(endTimestamp * 1e3) : null;\n        const isCancelled = stripeSubscription.status === \"canceled\" || stripeSubscription.status === \"cancelled\";\n        let cancelledDate = null;\n        if (isCancelled && stripeSubscription.canceled_at) {\n          cancelledDate = new Date(stripeSubscription.canceled_at * 1e3);\n        }\n        subscriptionData = {\n          id: subscription.id,\n          status: subscription.status,\n          // Include both camelCase and snake_case versions for compatibility\n          currentPeriodStart: startDate,\n          currentPeriodEnd: endDate,\n          current_period_start: startDate,\n          current_period_end: endDate,\n          cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,\n          // Include cancelled date if available\n          canceled_at: cancelledDate,\n          canceledAt: cancelledDate,\n          paymentMethod: paymentMethodDetails,\n          items: subscription.items.data,\n          // Include pause collection data\n          pause_collection: pauseCollection,\n          // Include metadata\n          metadata,\n          // Add a simple flag for paused status\n          isPaused,\n          // Add the date when the plan changes back to free\n          planChangesOnDate,\n          price: {\n            id: price?.id,\n            unitAmount: price?.unit_amount,\n            currency: price?.currency,\n            interval,\n            intervalCount,\n            billingCycle\n          },\n          product: {\n            id: productId,\n            name: productName,\n            description: productDescription\n          }\n        };\n        if (subscriptionData.product.name && (!currentPlan || currentPlan.id === \"free\")) {\n          try {\n            const planId = subscriptionData.product.name.toLowerCase().replace(/\\s+/g, \"_\");\n            const matchingPlan = await getPlanById(planId);\n            if (matchingPlan) {\n              currentPlan = matchingPlan;\n            }\n          } catch (error) {\n            console.error(\"Error finding matching plan:\", error);\n          }\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching Stripe data:\", error);\n    }\n  }\n  const userWithUsage = {\n    id: userData.id,\n    email: userData.email,\n    name: userData.name,\n    role: userData.role,\n    stripeCustomerId: userData.stripeCustomerId,\n    usage: formattedUsage.length > 0 ? formattedUsage : [\n      {\n        featureId: \"resume_scanner\",\n        featureName: \"Resume Scanner\",\n        limitId: \"resume_scans_per_month\",\n        limitName: \"Resume Scans per Month\",\n        used: resumeScansUsed,\n        limit: resumeScansLimit,\n        remaining: resumeScansRemaining,\n        percentUsed: resumeScansUsed / resumeScansLimit * 100\n      },\n      {\n        featureId: \"job_search_profiles\",\n        featureName: \"Job Search Profiles\",\n        limitId: \"job_search_profiles\",\n        limitName: \"Job Search Profiles\",\n        used: profilesUsed,\n        limit: profilesLimit,\n        remaining: Math.max(0, profilesLimit - profilesUsed),\n        percentUsed: profilesUsed / profilesLimit * 100\n      }\n    ]\n  };\n  return {\n    user: userWithUsage,\n    billing: {\n      currentPlan,\n      paymentMethods,\n      invoices,\n      upcomingInvoice,\n      subscription: subscriptionData,\n      defaultPaymentMethod\n    }\n  };\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/billing/_page.server.ts.js';\n\nexport const index = 59;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/billing/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/billing/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/59.cWAU59je.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/BnikQ10_.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/DrHxToS6.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CWmzcjye.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/VYoCKyli.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BhzFx1Wy.js\",\"_app/immutable/chunks/WD4kvFhR.js\",\"_app/immutable/chunks/D-o7ybA5.js\",\"_app/immutable/chunks/D2egQzE8.js\",\"_app/immutable/chunks/Ntteq2n_.js\",\"_app/immutable/chunks/hrXlVaSN.js\",\"_app/immutable/chunks/CdkBcXOf.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/BNEH2jqx.js\",\"_app/immutable/chunks/CxmsTEaf.js\",\"_app/immutable/chunks/DvO_AOqy.js\",\"_app/immutable/chunks/qwsZpUIl.js\",\"_app/immutable/chunks/BoNCRmBc.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/BgDjIxoO.js\",\"_app/immutable/chunks/Dz4exfp3.js\",\"_app/immutable/chunks/Z6UAQTuv.js\",\"_app/immutable/chunks/DOf_JqyE.js\",\"_app/immutable/chunks/BAIxhb6t.js\",\"_app/immutable/chunks/rNI1Perp.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/C33xR25f.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/tr-scC-m.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/DmZyh-PW.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AAC5B,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,IAAI,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;AAChC,IAAI,OAAO,EAAE;AACb,MAAM,aAAa,EAAE;AACrB,QAAQ,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACtC,QAAQ,IAAI,EAAE;AACd;AACA;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,QAAQ,EAAE;AACjB,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,MAAM,CAAC,IAAI,GAAG,QAAQ;AACxB,EAAE,MAAM,yBAAyB,EAAE;AACnC,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,IAAI;AACN,IAAI,WAAW,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,IAAI,MAAM,CAAC;AAC5D,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACtF,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AAClE;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,MAAM,KAAK;AACf;AACA,EAAE,MAAM,YAAY,mBAAmB,IAAI,IAAI,EAAE;AACjD,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC;AACzB,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACnC,EAAE,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC;AAChE,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,QAAQ,CAAC,EAAE;AACzB,MAAM,SAAS,EAAE,EAAE,GAAG,EAAE,YAAY;AACpC;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAClD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,QAAQ,CAAC;AACvB;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,cAAc,GAAG,EAAE;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,SAAS;AAC9C;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC,MAAM;AACrG,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU,MAAM,EAAE,QAAQ,CAAC;AAC3B,SAAS;AACT,QAAQ,OAAO,EAAE;AACjB,UAAU,SAAS,EAAE;AACrB;AACA,OAAO,CAAC;AACR,MAAM,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;AACrF,MAAM,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;AACjF,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACrD,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE;AACd,YAAY,EAAE,EAAE;AAChB;AACA;AACA,OAAO,CAAC;AACR,MAAM,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AACxD,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,EAAE;AACd,YAAY,EAAE,EAAE;AAChB;AACA;AACA,OAAO,CAAC;AACR,MAAM,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,KAAK;AAC3D,QAAQ,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,OAAO;AACjC,QAAQ,OAAO,GAAG;AAClB,OAAO,EAAE,EAAE,CAAC;AACZ,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK,KAAK;AACrD,QAAQ,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK;AAC7B,QAAQ,OAAO,GAAG;AAClB,OAAO,EAAE,EAAE,CAAC;AACZ,MAAM,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM;AACxD,QAAQ,EAAE,EAAE,KAAK,CAAC,EAAE;AACpB,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,WAAW,EAAE,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,IAAI,IAAI,iBAAiB;AAC3E,QAAQ,kBAAkB,EAAE,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,WAAW,IAAI,EAAE;AAC1E,QAAQ,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,IAAI,eAAe;AACnE,QAAQ,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,YAAY,IAAI,GAAG;AAChE,QAAQ,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,IAAI,IAAI;AACpD,OAAO,CAAC,CAAC;AACT,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AACjF,QAAQ,IAAI,WAAW,GAAG,IAAI;AAC9B,QAAQ,IAAI,KAAK,CAAC,SAAS,KAAK,SAAS,EAAE;AAC3C,UAAU,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC;AAC3D,UAAU,WAAW,GAAG,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,GAAG,UAAU,GAAG,GAAG,CAAC,GAAG,CAAC;AACzF;AACA,QAAQ,OAAO;AACf,UAAU,EAAE,EAAE,KAAK,CAAC,EAAE;AACtB,UAAU,SAAS,EAAE,KAAK,CAAC,SAAS;AACpC,UAAU,WAAW,EAAE,KAAK,CAAC,WAAW;AACxC,UAAU,OAAO,EAAE,KAAK,CAAC,OAAO;AAChC,UAAU,SAAS,EAAE,KAAK,CAAC,SAAS;AACpC,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI;AAC1B,UAAU,KAAK,EAAE,KAAK,CAAC,UAAU;AACjC,UAAU,WAAW;AACrB,UAAU,MAAM,EAAE,KAAK,CAAC,MAAM;AAC9B,UAAU,SAAS,EAAE,KAAK,CAAC;AAC3B,SAAS;AACT,OAAO,CAAC,GAAG,EAAE;AACb,KAAK,MAAM;AACX,MAAM,cAAc,GAAG,EAAE;AACzB;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACzD,IAAI,cAAc,GAAG,EAAE;AACvB;AACA,EAAE,IAAI,gBAAgB,GAAG,EAAE;AAC3B,EAAE,IAAI,WAAW,EAAE,MAAM,EAAE,eAAe,EAAE;AAC5C,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC;AACjE,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;AACvC,IAAI,gBAAgB,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE;AACnD;AACA,EAAE,IAAI,aAAa,GAAG,CAAC;AACvB,EAAE,IAAI,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE;AACrC,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC1D,IAAI,aAAa,GAAG,QAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC;AACjD;AACA,EAAE,MAAM,oBAAoB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,GAAG,eAAe,CAAC;AAC9E,EAAE,IAAI,cAAc,GAAG,EAAE;AACzB,EAAE,IAAI,QAAQ,GAAG,EAAE;AACnB,EAAE,IAAI,eAAe,GAAG,IAAI;AAC5B,EAAE,IAAI,gBAAgB,GAAG,IAAI;AAC7B,EAAE,IAAI,oBAAoB,GAAG,IAAI;AACjC,EAAE,IAAI,QAAQ,CAAC,gBAAgB,EAAE;AACjC,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACxD,IAAI,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AAC3J,IAAI,MAAM,MAAM,GAAG,CAAC,MAAM,OAAO,+BAAQ,CAAC,EAAE,OAAO;AACnD,IAAI,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AAC5C,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,IAAI,IAAI;AACR,MAAM,MAAM,sBAAsB,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC;AACtE,QAAQ,QAAQ,EAAE,QAAQ,CAAC,gBAAgB;AAC3C,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,cAAc,GAAG,sBAAsB,CAAC,IAAI;AAClD,MAAM,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AACjF,MAAM,IAAI,QAAQ,IAAI,EAAE,SAAS,IAAI,QAAQ,CAAC,IAAI,QAAQ,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;AACrG,QAAQ,oBAAoB,GAAG,QAAQ,CAAC,gBAAgB,CAAC,sBAAsB;AAC/E;AACA,MAAM,cAAc,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM;AACvD,QAAQ,GAAG,MAAM;AACjB,QAAQ,SAAS,EAAE,MAAM,CAAC,EAAE,KAAK;AACjC,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC1D,QAAQ,QAAQ,EAAE,QAAQ,CAAC,gBAAgB;AAC3C,QAAQ,KAAK,EAAE;AACf,OAAO,CAAC;AACR,MAAM,QAAQ,GAAG,gBAAgB,CAAC,IAAI;AACtC,MAAM,IAAI;AACV,QAAQ,eAAe,GAAG,IAAI;AAC9B,OAAO,CAAC,OAAO,GAAG,EAAE;AACpB,QAAQ,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,GAAG,CAAC;AACtD;AACA,MAAM,MAAM,qBAAqB,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC;AACpE,QAAQ,QAAQ,EAAE,QAAQ,CAAC,gBAAgB;AAC3C,QAAQ,KAAK,EAAE,CAAC;AAChB;AACA,QAAQ,MAAM,EAAE,CAAC,6BAA6B,EAAE,uBAAuB;AACvE,OAAO,CAAC;AACR,MAAM,IAAI,qBAAqB,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACjD,QAAQ,IAAI,YAAY,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,QAAQ,CAAC;AAC5F,QAAQ,IAAI,CAAC,YAAY,EAAE;AAC3B,UAAU,YAAY,GAAG,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,UAAU,CAAC;AAC5F;AACA,QAAQ,IAAI,CAAC,YAAY,EAAE;AAC3B,UAAU,YAAY,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;AACtD;AACA,QAAQ,IAAI,oBAAoB,GAAG,IAAI;AACvC,QAAQ,IAAI,YAAY,CAAC,sBAAsB,EAAE;AACjD,UAAU,MAAM,aAAa,GAAG,OAAO,YAAY,CAAC,sBAAsB,KAAK,QAAQ,GAAG,IAAI,GAAG,YAAY,CAAC,sBAAsB;AACpI,UAAU,IAAI,aAAa,IAAI,aAAa,CAAC,IAAI,EAAE;AACnD,YAAY,oBAAoB,GAAG;AACnC,cAAc,EAAE,EAAE,aAAa,CAAC,EAAE;AAClC,cAAc,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,KAAK;AAC7C,cAAc,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC,KAAK;AAC7C,cAAc,QAAQ,EAAE,aAAa,CAAC,IAAI,CAAC,SAAS;AACpD,cAAc,OAAO,EAAE,aAAa,CAAC,IAAI,CAAC;AAC1C,aAAa;AACb;AACA;AACA,QAAQ,MAAM,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAC3D,QAAQ,MAAM,KAAK,GAAG,gBAAgB,EAAE,KAAK;AAC7C,QAAQ,MAAM,SAAS,GAAG,KAAK,EAAE,OAAO;AACxC,QAAQ,MAAM,QAAQ,GAAG,KAAK,EAAE,SAAS,EAAE,QAAQ,IAAI,OAAO;AAC9D,QAAQ,MAAM,aAAa,GAAG,KAAK,EAAE,SAAS,EAAE,cAAc,IAAI,CAAC;AACnE,QAAQ,MAAM,YAAY,GAAG,QAAQ,KAAK,MAAM,IAAI,aAAa,GAAG,CAAC,GAAG,QAAQ,GAAG,SAAS;AAC5F,QAAQ,IAAI,WAAW,GAAG,IAAI;AAC9B,QAAQ,IAAI,kBAAkB,GAAG,IAAI;AACrC,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,IAAI;AACd,YAAY,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC;AACrE,YAAY,WAAW,GAAG,OAAO,CAAC,IAAI;AACtC,YAAY,kBAAkB,GAAG,OAAO,CAAC,WAAW;AACpD,WAAW,CAAC,OAAO,KAAK,EAAE;AAC1B,YAAY,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AACnE;AACA;AACA,QAAQ,MAAM,kBAAkB,GAAG,YAAY;AAC/C,QAAQ,MAAM,eAAe,GAAG,kBAAkB,CAAC,gBAAgB,IAAI,IAAI;AAC3E,QAAQ,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,IAAI,EAAE;AAC1D,QAAQ,MAAM,QAAQ,GAAG,eAAe,KAAK,IAAI,IAAI,QAAQ,CAAC,mBAAmB,KAAK,MAAM,IAAI,kBAAkB,CAAC,MAAM,KAAK,QAAQ,IAAI,kBAAkB,CAAC,MAAM,KAAK,uBAAuB,IAAI,kBAAkB,CAAC,oBAAoB,IAAI,QAAQ,CAAC,oBAAoB,KAAK,OAAO;AACvR,QAAQ,MAAM,iBAAiB,GAAG,IAAI,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,GAAG,GAAG,CAAC;AACvF,QAAQ,IAAI,cAAc,GAAG,kBAAkB,CAAC,oBAAoB;AACpE,QAAQ,IAAI,YAAY,GAAG,kBAAkB,CAAC,kBAAkB;AAChE,QAAQ,IAAI,CAAC,cAAc,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE;AACrE,UAAU,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACtD,UAAU,IAAI,SAAS,CAAC,oBAAoB,EAAE;AAC9C,YAAY,cAAc,GAAG,SAAS,CAAC,oBAAoB;AAC3D;AACA,UAAU,IAAI,SAAS,CAAC,kBAAkB,EAAE;AAC5C,YAAY,YAAY,GAAG,SAAS,CAAC,kBAAkB;AACvD;AACA;AACA,QAAQ,MAAM,SAAS,GAAG,cAAc,GAAG,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG,IAAI;AAChF,QAAQ,MAAM,OAAO,GAAG,YAAY,GAAG,IAAI,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,GAAG,IAAI;AAC1E,QAAQ,MAAM,WAAW,GAAG,kBAAkB,CAAC,MAAM,KAAK,UAAU,IAAI,kBAAkB,CAAC,MAAM,KAAK,WAAW;AACjH,QAAQ,IAAI,aAAa,GAAG,IAAI;AAChC,QAAQ,IAAI,WAAW,IAAI,kBAAkB,CAAC,WAAW,EAAE;AAC3D,UAAU,aAAa,GAAG,IAAI,IAAI,CAAC,kBAAkB,CAAC,WAAW,GAAG,GAAG,CAAC;AACxE;AACA,QAAQ,gBAAgB,GAAG;AAC3B,UAAU,EAAE,EAAE,YAAY,CAAC,EAAE;AAC7B,UAAU,MAAM,EAAE,YAAY,CAAC,MAAM;AACrC;AACA,UAAU,kBAAkB,EAAE,SAAS;AACvC,UAAU,gBAAgB,EAAE,OAAO;AACnC,UAAU,oBAAoB,EAAE,SAAS;AACzC,UAAU,kBAAkB,EAAE,OAAO;AACrC,UAAU,iBAAiB,EAAE,kBAAkB,CAAC,oBAAoB;AACpE;AACA,UAAU,WAAW,EAAE,aAAa;AACpC,UAAU,UAAU,EAAE,aAAa;AACnC,UAAU,aAAa,EAAE,oBAAoB;AAC7C,UAAU,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,IAAI;AACxC;AACA,UAAU,gBAAgB,EAAE,eAAe;AAC3C;AACA,UAAU,QAAQ;AAClB;AACA,UAAU,QAAQ;AAClB;AACA,UAAU,iBAAiB;AAC3B,UAAU,KAAK,EAAE;AACjB,YAAY,EAAE,EAAE,KAAK,EAAE,EAAE;AACzB,YAAY,UAAU,EAAE,KAAK,EAAE,WAAW;AAC1C,YAAY,QAAQ,EAAE,KAAK,EAAE,QAAQ;AACrC,YAAY,QAAQ;AACpB,YAAY,aAAa;AACzB,YAAY;AACZ,WAAW;AACX,UAAU,OAAO,EAAE;AACnB,YAAY,EAAE,EAAE,SAAS;AACzB,YAAY,IAAI,EAAE,WAAW;AAC7B,YAAY,WAAW,EAAE;AACzB;AACA,SAAS;AACT,QAAQ,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,WAAW,IAAI,WAAW,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE;AAC1F,UAAU,IAAI;AACd,YAAY,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AAC3F,YAAY,MAAM,YAAY,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC;AAC1D,YAAY,IAAI,YAAY,EAAE;AAC9B,cAAc,WAAW,GAAG,YAAY;AACxC;AACA,WAAW,CAAC,OAAO,KAAK,EAAE;AAC1B,YAAY,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AAChE;AACA;AACA;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACzD;AACA;AACA,EAAE,MAAM,aAAa,GAAG;AACxB,IAAI,EAAE,EAAE,QAAQ,CAAC,EAAE;AACnB,IAAI,KAAK,EAAE,QAAQ,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE,QAAQ,CAAC,IAAI;AACvB,IAAI,IAAI,EAAE,QAAQ,CAAC,IAAI;AACvB,IAAI,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;AAC/C,IAAI,KAAK,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,GAAG,cAAc,GAAG;AACxD,MAAM;AACN,QAAQ,SAAS,EAAE,gBAAgB;AACnC,QAAQ,WAAW,EAAE,gBAAgB;AACrC,QAAQ,OAAO,EAAE,wBAAwB;AACzC,QAAQ,SAAS,EAAE,wBAAwB;AAC3C,QAAQ,IAAI,EAAE,eAAe;AAC7B,QAAQ,KAAK,EAAE,gBAAgB;AAC/B,QAAQ,SAAS,EAAE,oBAAoB;AACvC,QAAQ,WAAW,EAAE,eAAe,GAAG,gBAAgB,GAAG;AAC1D,OAAO;AACP,MAAM;AACN,QAAQ,SAAS,EAAE,qBAAqB;AACxC,QAAQ,WAAW,EAAE,qBAAqB;AAC1C,QAAQ,OAAO,EAAE,qBAAqB;AACtC,QAAQ,SAAS,EAAE,qBAAqB;AACxC,QAAQ,IAAI,EAAE,YAAY;AAC1B,QAAQ,KAAK,EAAE,aAAa;AAC5B,QAAQ,SAAS,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,YAAY,CAAC;AAC5D,QAAQ,WAAW,EAAE,YAAY,GAAG,aAAa,GAAG;AACpD;AACA;AACA,GAAG;AACH,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,OAAO,EAAE;AACb,MAAM,WAAW;AACjB,MAAM,cAAc;AACpB,MAAM,QAAQ;AACd,MAAM,eAAe;AACrB,MAAM,YAAY,EAAE,gBAAgB;AACpC,MAAM;AACN;AACA,GAAG;AACH,CAAC;;;;;;;AC9UW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA6D,CAAC,EAAE;AAE3H,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACrnF,MAAC,WAAW,GAAG,CAAC,4CAA4C;AAC5D,MAAC,KAAK,GAAG;;;;"}