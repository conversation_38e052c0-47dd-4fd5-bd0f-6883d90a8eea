import { M as ensure_array_like, O as escape_html } from './index3-CqUPEnZw.js';
import { B as Button } from './button-CrucCo1G.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { N as Network, C as Chart_line } from './network-DDnWc0gP.js';
import { D as Database } from './database-DTyOQm04.js';
import { S as Search } from './search-B0oHlTPS.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import { U as Users } from './users-e7-Uhkka.js';
import { L as Layers } from './layers-Bh452qa5.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';

function _page($$payload) {
  const features = {
    talentNetwork: {
      title: "Expand Your Talent Network",
      description: "Access a vast pool of pre-screened candidates and build your own talent community.",
      secondary: [
        {
          icon: Network,
          title: "Candidate Sourcing",
          description: "Tap into our extensive database of active and passive job seekers across industries."
        },
        {
          icon: Database,
          title: "Talent Pool Management",
          description: "Organize and segment candidates by skills, experience, and availability."
        },
        {
          icon: Search,
          title: "Advanced Matching",
          description: "Our AI matches candidates to job requirements with unprecedented accuracy."
        },
        {
          icon: Shield,
          title: "Compliance Tools",
          description: "Stay compliant with data protection regulations and industry standards."
        }
      ]
    },
    teamCollaboration: {
      title: "Seamless Team Collaboration",
      description: "Empower your recruitment team with tools designed for efficient collaboration and communication.",
      secondary: [
        {
          icon: Users,
          title: "Team Workspace",
          description: "Share candidates, notes, and feedback in a centralized collaborative environment."
        },
        {
          icon: Chart_line,
          title: "Performance Tracking",
          description: "Monitor team metrics and individual recruiter performance with detailed analytics."
        },
        {
          icon: Layers,
          title: "Workflow Automation",
          description: "Automate repetitive tasks and create custom workflows for your team."
        },
        {
          icon: Clock,
          title: "Time-Saving Tools",
          description: "Reduce administrative burden with scheduling assistants and automated follow-ups."
        }
      ]
    }
  };
  const testimonials = [
    {
      quote: "Hirli has revolutionized how our agency operates. We've doubled our placement rate while maintaining the same team size.",
      author: "David Rodriguez",
      position: "Managing Director",
      company: "Elite Talent Partners"
    },
    {
      quote: "The team collaboration features have transformed how we work together. Communication is seamless and our efficiency has improved dramatically.",
      author: "Priya Sharma",
      position: "Senior Recruiter",
      company: "TechTalent Solutions"
    },
    {
      quote: "The candidate matching algorithm is incredibly accurate. We're presenting better candidates to our clients and closing positions faster.",
      author: "James Wilson",
      position: "Recruitment Team Lead",
      company: "Nexus Staffing"
    }
  ];
  const each_array = ensure_array_like(features.talentNetwork.secondary);
  const each_array_1 = ensure_array_like(features.teamCollaboration.secondary);
  const each_array_2 = ensure_array_like(testimonials);
  SEO($$payload, {
    title: "Hirli for Recruiters - Streamline Your Recruitment Process",
    description: "Empower your recruitment team with AI-powered tools for candidate sourcing, matching, and team collaboration.",
    keywords: "recruitment, staffing, talent acquisition, candidate sourcing, team collaboration",
    url: "https://hirli.com/recruiters",
    image: "/assets/og-image-recruiters.jpg"
  });
  $$payload.out += `<!----> <section class="border border-l border-r border-t py-16 md:py-40"><div class="grid grid-cols-10 items-center gap-12"><div class="col-span-4 col-start-2"><div class="leading-tighter mb-8 w-[90%] text-4xl font-light md:text-5xl lg:text-[80px]">Empower Your <span class="gradient-text">Recruitment</span> Team</div> <p class="mb-12 text-gray-600 md:text-2xl">Our platform helps recruitment agencies and teams source better candidates, collaborate
        effectively, and place talent faster.</p> <div class="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">`;
  Button($$payload, {
    class: "rounded-none border border-transparent bg-neutral-200 p-8 text-lg font-medium text-white transition-colors hover:bg-blue-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Book a Demo`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    class: "group flex items-center rounded-none border border-gray-300 p-8 text-lg font-medium transition-colors hover:bg-gray-50",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Learn More `;
      Arrow_right($$payload2, { class: "ml-2 h-4 w-4" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="mt-8 flex items-center text-sm text-gray-500"><div class="mr-3 flex -space-x-2"><img src="https://randomuser.me/api/portraits/women/42.jpg" alt="Recruiter" class="h-8 w-8 rounded-full border-2 border-white"/> <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Recruiter" class="h-8 w-8 rounded-full border-2 border-white"/> <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Recruiter" class="h-8 w-8 rounded-full border-2 border-white"/></div> <span>Trusted by <span class="font-semibold">300+</span> recruitment agencies</span></div></div> <div class="relative col-span-4 col-start-6"><div class="h-[500px] w-full rounded-lg bg-gradient-to-br from-purple-100 to-purple-200"></div></div></div></section> <section class="border border-b border-l border-r py-16"><div class="container mx-auto px-4"><div class="grid grid-cols-1 gap-8 md:grid-cols-3"><div class="text-center"><div class="text-5xl font-bold text-purple-600">2x</div> <p class="mt-2 text-xl text-gray-600">Faster Candidate Placement</p></div> <div class="text-center"><div class="text-5xl font-bold text-purple-600">45%</div> <p class="mt-2 text-xl text-gray-600">Increase in Team Productivity</p></div> <div class="text-center"><div class="text-5xl font-bold text-purple-600">30%</div> <p class="mt-2 text-xl text-gray-600">Higher Client Satisfaction</p></div></div></div></section> <section id="talent-network" class="border border-b border-l border-r border-neutral-200"><div class="flex flex-col"><div class="md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="p-15 text-primary col-span-8 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl">${escape_html(features.talentNetwork.title)}</h3> <p class="typography font-montreal text-xl">${escape_html(features.talentNetwork.description)}</p> <a href="#contact" class="flex w-48 flex-row items-center justify-between rounded-md bg-purple-500 px-6 py-3 text-white transition-colors hover:bg-purple-600">Get Started `;
  Arrow_right($$payload, { class: "ml-2 h-4 w-4" });
  $$payload.out += `<!----></a></div></div></div> <div class="bg-grid border-left-neutral col-span-8 col-start-9 row-span-8 row-start-1 border border-b border-r border-t"></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let feature = each_array[$$index];
    $$payload.out += `<div class="p-22 rounded-none border border-gray-100 bg-white shadow-md transition-shadow duration-300 hover:shadow-lg"><div class="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-purple-500/10"><!---->`;
    feature.icon?.($$payload, { class: "h-6 w-6 text-purple-500" });
    $$payload.out += `<!----></div> <h3 class="font-normal! mb-4 text-3xl">${escape_html(feature.title)}</h3> <p class="text-md text-gray-600">${escape_html(feature.description)}</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></section> <section id="team-collaboration" class="border border-b border-l border-r border-neutral-200"><div class="flex flex-col"><div class="md:grid-cols-16 flex flex-col border border-t-neutral-500 [--column-count:8] md:grid md:grid-rows-8 md:[--column-count:16]"><div class="bg-grid bg-grid-purple-200 dark:bg-grid-purple-600 col-span-8 col-start-1 row-span-8 row-start-1 border border-b border-l border-neutral-200"></div> <div class="p-15 text-text-primary col-span-8 col-start-9 row-span-8 row-start-1 flex aspect-auto flex-col justify-between md:aspect-square md:items-center md:justify-center"><div class="gap-50 flex max-w-[280px] flex-1 flex-col justify-between md:max-w-[400px] md:flex-[unset]"><div class="flex flex-col gap-20"><h3 class="font-light! max-w-3xs text-6xl">${escape_html(features.teamCollaboration.title)}</h3> <p class="typography font-montreal text-xl">${escape_html(features.teamCollaboration.description)}</p> <a href="#contact" class="flex w-48 flex-row items-center justify-between rounded-md bg-purple-500 px-6 py-3 text-white transition-colors hover:bg-purple-600">Learn More `;
  Arrow_right($$payload, { class: "ml-2 h-4 w-4" });
  $$payload.out += `<!----></a></div></div></div></div> <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
  for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
    let feature = each_array_1[$$index_1];
    $$payload.out += `<div class="p-22 rounded-none border border-gray-100 bg-white shadow-md transition-shadow duration-300 hover:shadow-lg"><div class="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-purple-500/10"><!---->`;
    feature.icon?.($$payload, { class: "h-6 w-6 text-purple-500" });
    $$payload.out += `<!----></div> <h3 class="font-normal! mb-4 text-3xl">${escape_html(feature.title)}</h3> <p class="text-md text-gray-600">${escape_html(feature.description)}</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></section> <section class="border border-b border-l border-r py-16"><div class="container mx-auto px-4"><h2 class="mb-12 text-center text-4xl font-light">What Recruiters Say</h2> <div class="grid grid-cols-1 gap-8 md:grid-cols-3"><!--[-->`;
  for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
    let testimonial = each_array_2[$$index_2];
    $$payload.out += `<div class="rounded-lg border border-gray-200 bg-white p-8 shadow-md"><p class="mb-4 text-gray-600">"${escape_html(testimonial.quote)}"</p> <div class="flex items-center"><div class="mr-4 h-12 w-12 rounded-full bg-gray-300"></div> <div><p class="font-semibold">${escape_html(testimonial.author)}</p> <p class="text-sm text-gray-600">${escape_html(testimonial.position)}, ${escape_html(testimonial.company)}</p></div></div></div>`;
  }
  $$payload.out += `<!--]--></div></div></section> <section class="border border-b border-l border-r py-16"><div class="container mx-auto px-4"><h2 class="mb-12 text-center text-4xl font-light">Team Plans</h2> <div class="grid grid-cols-1 gap-8 md:grid-cols-3"><div class="flex flex-col rounded-lg border border-gray-200 bg-white p-8 shadow-md"><h3 class="mb-2 text-2xl font-semibold">Starter</h3> <p class="mb-6 text-gray-600">Perfect for small recruitment teams</p> <div class="mb-6 text-4xl font-bold">$299<span class="text-lg font-normal text-gray-500">/month</span></div> <ul class="mb-8 space-y-3"><li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Up to 5 team members</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>500 candidate searches/month</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Basic analytics</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Email support</span></li></ul> `;
  Button($$payload, {
    class: "mt-auto rounded-none border border-transparent bg-purple-500 p-6 text-white hover:bg-purple-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Get Started`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="flex flex-col rounded-lg border-2 border-purple-500 bg-white p-8 shadow-lg"><div class="-mt-4 mb-4 rounded-full bg-purple-500 px-3 py-1 text-center text-sm font-semibold text-white">MOST POPULAR</div> <h3 class="mb-2 text-2xl font-semibold">Professional</h3> <p class="mb-6 text-gray-600">Ideal for growing recruitment agencies</p> <div class="mb-6 text-4xl font-bold">$599<span class="text-lg font-normal text-gray-500">/month</span></div> <ul class="mb-8 space-y-3"><li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Up to 15 team members</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>2,000 candidate searches/month</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Advanced analytics &amp; reporting</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Priority support</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Custom workflows</span></li></ul> `;
  Button($$payload, {
    class: "mt-auto rounded-none border border-transparent bg-purple-500 p-6 text-white hover:bg-purple-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Get Started`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="flex flex-col rounded-lg border border-gray-200 bg-white p-8 shadow-md"><h3 class="mb-2 text-2xl font-semibold">Enterprise</h3> <p class="mb-6 text-gray-600">For large recruitment organizations</p> <div class="mb-6 text-4xl font-bold">Custom<span class="text-lg font-normal text-gray-500"></span></div> <ul class="mb-8 space-y-3"><li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Unlimited team members</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Unlimited candidate searches</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Custom integrations</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>Dedicated account manager</span></li> <li class="flex items-center">`;
  Circle_check_big($$payload, { class: "mr-2 h-5 w-5 text-green-500" });
  $$payload.out += `<!----> <span>24/7 premium support</span></li></ul> `;
  Button($$payload, {
    class: "mt-auto rounded-none border border-gray-300 bg-white p-6 text-purple-600 hover:bg-gray-50",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Contact Sales`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div></section> <section id="contact" class="border border-b border-l border-r py-16"><div class="container mx-auto px-4 text-center"><h2 class="mb-6 text-4xl font-light">Ready to Transform Your Recruitment Process?</h2> <p class="mx-auto mb-8 max-w-2xl text-xl text-gray-600">Book a demo with our team to see how Hirli can help your recruitment team collaborate better
      and place candidates faster.</p> <div class="flex justify-center">`;
  Button($$payload, {
    class: "rounded-none border border-transparent bg-purple-500 p-8 text-lg font-medium text-white transition-colors hover:bg-purple-600",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Book a Demo`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></section>`;
}

export { _page as default };
//# sourceMappingURL=_page.svelte-3DmXoZ_0.js.map
