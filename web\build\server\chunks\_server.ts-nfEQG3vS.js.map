{"version": 3, "file": "_server.ts-nfEQG3vS.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/jobs/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async ({ url }) => {\n  try {\n    const page = parseInt(url.searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(url.searchParams.get(\"limit\") || \"20\");\n    const title = url.searchParams.get(\"title\") || \"\";\n    const location = url.searchParams.get(\"location\") || \"\";\n    const locationType = url.searchParams.get(\"locationType\") || \"\";\n    const experience = url.searchParams.get(\"experience\") || \"\";\n    const collection = url.searchParams.get(\"collection\") || \"\";\n    const random = url.searchParams.get(\"random\") === \"true\";\n    const skip = (page - 1) * limit;\n    const filters = {\n      isActive: true\n    };\n    if (title) {\n      filters.title = {\n        contains: title,\n        mode: \"insensitive\"\n      };\n    }\n    if (location) {\n      filters.location = {\n        contains: location,\n        mode: \"insensitive\"\n      };\n    }\n    if (locationType) {\n      const types = locationType.split(\",\");\n      if (types.length > 0) {\n        filters.OR = filters.OR || [];\n        types.forEach((type) => {\n          filters.OR.push({\n            remoteType: {\n              contains: type,\n              mode: \"insensitive\"\n            }\n          });\n        });\n      }\n    }\n    if (experience) {\n      const levels = experience.split(\",\");\n      if (levels.length > 0) {\n        filters.OR = filters.OR || [];\n        levels.forEach((level) => {\n          filters.OR.push({\n            experienceLevel: {\n              contains: level,\n              mode: \"insensitive\"\n            }\n          });\n        });\n      }\n    }\n    if (collection) {\n      filters.collection = {\n        equals: collection\n      };\n    }\n    const salary = url.searchParams.get(\"salary\") || \"\";\n    if (salary) {\n      const [min, max] = salary.split(\"-\");\n      if (min && max) {\n        filters.OR = filters.OR || [];\n        filters.OR.push({\n          salary: {\n            contains: `$${parseInt(min).toLocaleString()}-$${parseInt(max).toLocaleString()}`,\n            mode: \"insensitive\"\n          }\n        });\n      } else if (salary.endsWith(\"+\")) {\n        const minValue = parseInt(salary.replace(\"+\", \"\"));\n        filters.OR = filters.OR || [];\n        filters.OR.push({\n          salary: {\n            gte: minValue\n          }\n        });\n      }\n    }\n    const jobs = await prisma.job_listing.findMany({\n      where: filters,\n      take: limit,\n      skip,\n      select: {\n        id: true,\n        platform: true,\n        jobId: true,\n        title: true,\n        company: true,\n        location: true,\n        url: true,\n        isActive: true,\n        createdAt: true,\n        lastCheckedAt: true,\n        employmentType: true,\n        remoteType: true,\n        experienceLevel: true,\n        description: true,\n        postedDate: true,\n        applyLink: true,\n        benefits: true,\n        salary: true,\n        salaryCurrency: true,\n        salaryMax: true,\n        salaryMin: true,\n        // Include company data with R2 logo URLs (optional - many jobs don't have companyId yet)\n        companyRelation: {\n          select: {\n            id: true,\n            name: true,\n            logoUrl: true,\n            // This now contains R2 URLs\n            domain: true,\n            companySize: true,\n            companyStage: true\n          }\n        }\n      },\n      orderBy: random ? void 0 : {\n        postedDate: \"desc\"\n      }\n    });\n    if (random) {\n      for (let i = jobs.length - 1; i > 0; i--) {\n        const j = Math.floor(Math.random() * (i + 1));\n        [jobs[i], jobs[j]] = [jobs[j], jobs[i]];\n      }\n    }\n    const totalCount = await prisma.job_listing.count({\n      where: filters\n    });\n    return json({\n      jobs,\n      pagination: {\n        page,\n        limit,\n        totalCount,\n        totalPages: Math.ceil(totalCount / limit),\n        hasMore: skip + jobs.length < totalCount\n      }\n    });\n  } catch (error) {\n    console.error(\"Error fetching jobs:\", error);\n    return json({ error: \"Failed to fetch jobs\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,GAAG,EAAE,KAAK;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC;AAC9D,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;AACjE,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE;AACrD,IAAI,MAAM,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;AAC3D,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE;AACnE,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;AAC/D,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;AAC/D,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,KAAK,MAAM;AAC5D,IAAI,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK;AACnC,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,QAAQ,EAAE;AAChB,KAAK;AACL,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,OAAO,CAAC,KAAK,GAAG;AACtB,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,IAAI,EAAE;AACd,OAAO;AACP;AACA,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,OAAO,CAAC,QAAQ,GAAG;AACzB,QAAQ,QAAQ,EAAE,QAAQ;AAC1B,QAAQ,IAAI,EAAE;AACd,OAAO;AACP;AACA,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC;AAC3C,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,QAAQ,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,EAAE;AACrC,QAAQ,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAChC,UAAU,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;AAC1B,YAAY,UAAU,EAAE;AACxB,cAAc,QAAQ,EAAE,IAAI;AAC5B,cAAc,IAAI,EAAE;AACpB;AACA,WAAW,CAAC;AACZ,SAAS,CAAC;AACV;AACA;AACA,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;AAC1C,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7B,QAAQ,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,EAAE;AACrC,QAAQ,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAClC,UAAU,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;AAC1B,YAAY,eAAe,EAAE;AAC7B,cAAc,QAAQ,EAAE,KAAK;AAC7B,cAAc,IAAI,EAAE;AACpB;AACA,WAAW,CAAC;AACZ,SAAS,CAAC;AACV;AACA;AACA,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,OAAO,CAAC,UAAU,GAAG;AAC3B,QAAQ,MAAM,EAAE;AAChB,OAAO;AACP;AACA,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;AACvD,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC;AAC1C,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE;AACtB,QAAQ,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,EAAE;AACrC,QAAQ,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,MAAM,EAAE;AAClB,YAAY,QAAQ,EAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,EAAE,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC;AAC7F,YAAY,IAAI,EAAE;AAClB;AACA,SAAS,CAAC;AACV,OAAO,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACvC,QAAQ,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;AAC1D,QAAQ,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,EAAE;AACrC,QAAQ,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;AACxB,UAAU,MAAM,EAAE;AAClB,YAAY,GAAG,EAAE;AACjB;AACA,SAAS,CAAC;AACV;AACA;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AACnD,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,IAAI;AACV,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,aAAa,EAAE,IAAI;AAC3B,QAAQ,cAAc,EAAE,IAAI;AAC5B,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,eAAe,EAAE,IAAI;AAC7B,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,cAAc,EAAE,IAAI;AAC5B,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,SAAS,EAAE,IAAI;AACvB;AACA,QAAQ,eAAe,EAAE;AACzB,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,OAAO,EAAE,IAAI;AACzB;AACA,YAAY,MAAM,EAAE,IAAI;AACxB,YAAY,WAAW,EAAE,IAAI;AAC7B,YAAY,YAAY,EAAE;AAC1B;AACA;AACA,OAAO;AACP,MAAM,OAAO,EAAE,MAAM,GAAG,KAAK,CAAC,GAAG;AACjC,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAChD,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACrD,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC;AACtD,MAAM,KAAK,EAAE;AACb,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,IAAI;AACV,MAAM,UAAU,EAAE;AAClB,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,UAAU;AAClB,QAAQ,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACjD,QAAQ,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,GAAG;AACtC;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC;AAChD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnE;AACA;;;;"}