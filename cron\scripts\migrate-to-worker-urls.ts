// Migrate all company logo URLs from hardcoded R2 account URLs to worker URLs
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

interface UrlMigrationResult {
  companyId: string;
  companyName: string;
  oldUrl: string;
  newUrl: string;
  updated: boolean;
  error?: string;
}

/**
 * Convert R2 account URL to worker URL
 */
function convertToWorkerUrl(oldUrl: string): string | null {
  if (!oldUrl) return null;

  // If it's already a worker URL, return as-is
  if (
    oldUrl.includes(
      "hirli-static-assets.christopher-eugene-rodriguez.workers.dev"
    )
  ) {
    return oldUrl;
  }

  // If it's a Clearbit URL, return as-is
  if (oldUrl.includes("logo.clearbit.com")) {
    return oldUrl;
  }

  // Extract filename from R2 URL
  let filename: string | null = null;

  // Pattern 1: https://pub-{accountId}.r2.dev/logos/filename.webp
  const r2LogosPattern = /https:\/\/pub-[^.]+\.r2\.dev\/logos\/(.+)$/;
  const r2LogosMatch = oldUrl.match(r2LogosPattern);
  if (r2LogosMatch) {
    filename = r2LogosMatch[1];
  }

  // Pattern 2: https://pub-{accountId}.r2.dev/filename.webp (without logos/ prefix)
  if (!filename) {
    const r2DirectPattern =
      /https:\/\/pub-[^.]+\.r2\.dev\/([^/]+\.(webp|png|jpg|jpeg|gif|svg))$/;
    const r2DirectMatch = oldUrl.match(r2DirectPattern);
    if (r2DirectMatch) {
      filename = r2DirectMatch[1];
    }
  }

  // Pattern 3: Any other R2 URL format
  if (!filename) {
    const generalPattern = /https:\/\/[^\/]+\.r2\.dev\/(.+)$/;
    const generalMatch = oldUrl.match(generalPattern);
    if (generalMatch) {
      filename = generalMatch[1].replace(/^logos\//, ""); // Remove logos/ prefix if present
    }
  }

  if (!filename) {
    logger.warn(`Could not extract filename from URL: ${oldUrl}`);
    return null;
  }

  // Generate worker URL
  const workerUrl =
    "https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev";
  return `${workerUrl}/logos/${filename}`;
}

async function migrateToWorkerUrls() {
  logger.info("🚀 Starting migration from R2 account URLs to worker URLs");

  try {
    // Get all companies with logo URLs that need migration
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: {
          not: null,
          // Only migrate R2 URLs, not Clearbit or worker URLs
          contains: ".r2.dev/",
        },
      },
      select: {
        id: true,
        name: true,
        logoUrl: true,
      },
    });

    logger.info(
      `📊 Found ${companies.length} companies with R2 logo URLs to migrate`
    );

    if (companies.length === 0) {
      logger.info("✅ No companies need logo URL migration");
      return;
    }

    const results: UrlMigrationResult[] = [];
    let updated = 0;
    let skipped = 0;
    let errors = 0;

    for (const company of companies) {
      try {
        if (!company.logoUrl) {
          skipped++;
          continue;
        }

        const newUrl = convertToWorkerUrl(company.logoUrl);

        const result: UrlMigrationResult = {
          companyId: company.id,
          companyName: company.name,
          oldUrl: company.logoUrl,
          newUrl: newUrl || company.logoUrl,
          updated: false,
        };

        if (!newUrl) {
          logger.warn(
            `⚠️ Could not convert URL for ${company.name}: ${company.logoUrl}`
          );
          skipped++;
          result.error = "Could not convert URL format";
          results.push(result);
          continue;
        }

        if (newUrl === company.logoUrl) {
          logger.info(
            `⏭️ URL already correct for ${company.name}: ${company.logoUrl}`
          );
          skipped++;
          results.push(result);
          continue;
        }

        // Update the company with the new worker URL
        await prisma.company.update({
          where: { id: company.id },
          data: { logoUrl: newUrl },
        });

        result.updated = true;
        updated++;

        logger.info(`✅ Migrated logo URL for "${company.name}"`);
        logger.info(`   Old: ${company.logoUrl}`);
        logger.info(`   New: ${newUrl}`);

        results.push(result);

        // Add a small delay to avoid overwhelming the database
        await new Promise((resolve) => setTimeout(resolve, 10));
      } catch (error) {
        errors++;
        logger.error(`❌ Error processing company ${company.name}:`, error);

        results.push({
          companyId: company.id,
          companyName: company.name,
          oldUrl: company.logoUrl || "",
          newUrl: company.logoUrl || "",
          updated: false,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    // Summary
    logger.info("\n🎉 Migration completed!");
    logger.info(`   ✅ Successfully migrated: ${updated}`);
    logger.info(`   ⏭️ Skipped (already correct): ${skipped}`);
    logger.info(`   ❌ Errors: ${errors}`);
    logger.info(`   📊 Total processed: ${companies.length}`);

    // Log some examples of successful migrations
    const successfulMigrations = results.filter((r) => r.updated).slice(0, 5);
    if (successfulMigrations.length > 0) {
      logger.info("\n📝 Example migrations:");
      successfulMigrations.forEach((result) => {
        logger.info(`   ${result.companyName}:`);
        logger.info(`     Old: ${result.oldUrl}`);
        logger.info(`     New: ${result.newUrl}`);
      });
    }

    if (updated > 0) {
      logger.info(`\n🔗 Test a migrated logo:`);
      const testUrl = successfulMigrations[0]?.newUrl;
      if (testUrl) {
        logger.info(`   ${testUrl}`);
      }
    }
  } catch (error) {
    logger.error("❌ Migration failed:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateToWorkerUrls()
  .then(() => {
    logger.info("✅ Migration script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Migration script failed:", error);
    process.exit(1);
  });

export { migrateToWorkerUrls };
