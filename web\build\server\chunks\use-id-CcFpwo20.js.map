{"version": 3, "file": "use-id-CcFpwo20.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/use-id.js"], "sourcesContent": ["globalThis.bitsIdCounter ??= { current: 0 };\nfunction useId(prefix = \"bits\") {\n  globalThis.bitsIdCounter.current++;\n  return `${prefix}-${globalThis.bitsIdCounter.current}`;\n}\nexport {\n  useId as u\n};\n"], "names": [], "mappings": "AAAA,UAAU,CAAC,aAAa,KAAK,EAAE,OAAO,EAAE,CAAC,EAAE;AAC3C,SAAS,KAAK,CAAC,MAAM,GAAG,MAAM,EAAE;AAChC,EAAE,UAAU,CAAC,aAAa,CAAC,OAAO,EAAE;AACpC,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACxD;;;;"}