{"version": 3, "file": "utils-pWl1tgmi.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport { format, formatDistance } from \"date-fns\";\nfunction cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\nfunction formatDistanceToNow(date) {\n  if (!date) return \"\";\n  return formatDistance(new Date(date), /* @__PURE__ */ new Date(), { addSuffix: false });\n}\nfunction debounce(func, wait) {\n  let timeout = null;\n  return function(...args) {\n    const later = () => {\n      timeout = null;\n      func(...args);\n    };\n    if (timeout !== null) {\n      clearTimeout(timeout);\n    }\n    timeout = setTimeout(later, wait);\n  };\n}\nfunction formatDate(date) {\n  if (!date) return \"\";\n  return format(new Date(date), \"MMM d, yyyy\");\n}\nexport {\n  formatDate as a,\n  cn as c,\n  debounce as d,\n  formatDistanceToNow as f\n};\n"], "names": [], "mappings": ";;;;AAGA,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE;AACvB,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC9B;AACA,SAAS,mBAAmB,CAAC,IAAI,EAAE;AACnC,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACtB,EAAE,OAAO,cAAc,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AACzF;AACA,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE;AAC9B,EAAE,IAAI,OAAO,GAAG,IAAI;AACpB,EAAE,OAAO,SAAS,GAAG,IAAI,EAAE;AAC3B,IAAI,MAAM,KAAK,GAAG,MAAM;AACxB,MAAM,OAAO,GAAG,IAAI;AACpB,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;AAC1B,MAAM,YAAY,CAAC,OAAO,CAAC;AAC3B;AACA,IAAI,OAAO,GAAG,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC;AACrC,GAAG;AACH;AACA,SAAS,UAAU,CAAC,IAAI,EAAE;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACtB,EAAE,OAAO,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC;AAC9C;;;;"}