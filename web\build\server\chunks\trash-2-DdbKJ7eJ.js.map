{"version": 3, "file": "trash-2-DdbKJ7eJ.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/trash-2.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Trash_2($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\"path\", { \"d\": \"M3 6h18\" }],\n    [\n      \"path\",\n      { \"d\": \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\" }\n    ],\n    [\n      \"path\",\n      { \"d\": \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\" }\n    ],\n    [\n      \"line\",\n      {\n        \"x1\": \"10\",\n        \"x2\": \"10\",\n        \"y1\": \"11\",\n        \"y2\": \"17\"\n      }\n    ],\n    [\n      \"line\",\n      {\n        \"x1\": \"14\",\n        \"x2\": \"14\",\n        \"y1\": \"11\",\n        \"y2\": \"17\"\n      }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"trash-2\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Trash_2 as T\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;AAChC,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM,EAAE,GAAG,EAAE,uCAAuC;AACpD,KAAK;AACL,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM,EAAE,GAAG,EAAE,oCAAoC;AACjD,KAAK;AACL,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE;AACd;AACA;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;AACvB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}