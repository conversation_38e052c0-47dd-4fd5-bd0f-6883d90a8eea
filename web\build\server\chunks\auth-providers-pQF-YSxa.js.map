{"version": 3, "file": "auth-providers-pQF-YSxa.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/auth-providers.js"], "sourcesContent": ["import { OAuth2Client } from \"google-auth-library\";\nimport { p as prisma } from \"./prisma.js\";\nimport { c as createSessionToken } from \"./auth.js\";\nconst GOOGLE_CLIENT_ID = process.env.GOOGLE_ID || \"\";\nconst LINKEDIN_CLIENT_ID = process.env.LINKEDIN_ID || \"\";\nconst LINKEDIN_CLIENT_SECRET = process.env.LINKEDIN_CLIENT_SECRET || \"\";\nconst googleClient = new OAuth2Client(GOOGLE_CLIENT_ID);\nasync function verifyGoogleToken(idToken, request) {\n  try {\n    const ticket = await googleClient.verifyIdToken({\n      idToken,\n      audience: GOOGLE_CLIENT_ID\n    });\n    const payload = ticket.getPayload();\n    if (!payload) {\n      throw new Error(\"No payload in Google token\");\n    }\n    const { email, name, picture, sub } = payload;\n    if (!email) {\n      throw new Error(\"No email in Google token payload\");\n    }\n    let user = await prisma.user.findUnique({ where: { email } });\n    if (user) {\n      user = await prisma.user.update({\n        where: { id: user.id },\n        data: {\n          provider: \"google\",\n          providerId: sub,\n          name: user.name || name,\n          image: user.image || picture,\n          emailVerified: true\n          // Google accounts are pre-verified\n        }\n      });\n    } else {\n      user = await prisma.user.create({\n        data: {\n          email,\n          name: name || \"\",\n          image: picture,\n          provider: \"google\",\n          providerId: sub,\n          emailVerified: true,\n          // Google accounts are pre-verified\n          role: \"free\"\n          // Default role\n        }\n      });\n    }\n    const sessionToken = createSessionToken(\n      {\n        email: user.email,\n        name: user.name || \"\",\n        picture: user.image || \"\",\n        role: user.role,\n        id: user.id\n      },\n      void 0\n      // We don't have access to the request object here\n    );\n    return {\n      success: true,\n      sessionToken,\n      user: {\n        email: user.email,\n        name: user.name,\n        image: user.image\n      }\n    };\n  } catch (error) {\n    console.error(\"Google authentication error:\", error);\n    return { success: false, error: \"Authentication failed\" };\n  }\n}\nasync function verifyLinkedInCode(code, redirectUri) {\n  try {\n    const tokenResponse = await fetch(\"https://www.linkedin.com/oauth/v2/accessToken\", {\n      method: \"POST\",\n      headers: {\n        \"Content-Type\": \"application/x-www-form-urlencoded\"\n      },\n      body: new URLSearchParams({\n        grant_type: \"authorization_code\",\n        code,\n        redirect_uri: redirectUri,\n        client_id: LINKEDIN_CLIENT_ID,\n        client_secret: LINKEDIN_CLIENT_SECRET\n      }).toString()\n    });\n    if (!tokenResponse.ok) {\n      const errorData = await tokenResponse.text();\n      console.error(\"LinkedIn token error:\", errorData);\n      throw new Error(\"Failed to get LinkedIn access token\");\n    }\n    const tokenData = await tokenResponse.json();\n    const accessToken = tokenData.access_token;\n    const profileResponse = await fetch(\"https://api.linkedin.com/v2/me\", {\n      headers: {\n        Authorization: `Bearer ${accessToken}`\n      }\n    });\n    if (!profileResponse.ok) {\n      throw new Error(\"Failed to get LinkedIn profile\");\n    }\n    const profileData = await profileResponse.json();\n    const emailResponse = await fetch(\n      \"https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))\",\n      {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      }\n    );\n    if (!emailResponse.ok) {\n      throw new Error(\"Failed to get LinkedIn email\");\n    }\n    const emailData = await emailResponse.json();\n    const email = emailData.elements[0][\"handle~\"].emailAddress;\n    if (!email) {\n      throw new Error(\"Email not provided by LinkedIn\");\n    }\n    const pictureResponse = await fetch(\n      \"https://api.linkedin.com/v2/me?projection=(id,profilePicture(displayImage~:playableStreams))\",\n      {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      }\n    );\n    let picture = \"\";\n    if (pictureResponse.ok) {\n      const pictureData = await pictureResponse.json();\n      if (pictureData.profilePicture && pictureData.profilePicture[\"displayImage~\"] && pictureData.profilePicture[\"displayImage~\"].elements && pictureData.profilePicture[\"displayImage~\"].elements.length > 0) {\n        picture = pictureData.profilePicture[\"displayImage~\"].elements[0].identifiers[0].identifier;\n      }\n    }\n    let user = await prisma.user.findUnique({ where: { email } });\n    if (user) {\n      user = await prisma.user.update({\n        where: { id: user.id },\n        data: {\n          provider: \"linkedin\",\n          providerId: profileData.id,\n          name: user.name || `${profileData.localizedFirstName} ${profileData.localizedLastName}`,\n          image: user.image || picture,\n          emailVerified: true\n          // LinkedIn accounts are pre-verified\n        }\n      });\n    } else {\n      user = await prisma.user.create({\n        data: {\n          email,\n          name: `${profileData.localizedFirstName} ${profileData.localizedLastName}`,\n          image: picture,\n          provider: \"linkedin\",\n          providerId: profileData.id,\n          emailVerified: true,\n          // LinkedIn accounts are pre-verified\n          role: \"free\"\n          // Default role\n        }\n      });\n    }\n    const sessionToken = createSessionToken(\n      {\n        email: user.email,\n        name: user.name || \"\",\n        picture: user.image || \"\",\n        role: user.role,\n        id: user.id\n      },\n      void 0\n      // We don't have access to the request object here\n    );\n    return {\n      success: true,\n      sessionToken,\n      user: {\n        email: user.email,\n        name: user.name,\n        image: user.image\n      }\n    };\n  } catch (error) {\n    console.error(\"LinkedIn authentication error:\", error);\n    return { success: false, error: \"Authentication failed\" };\n  }\n}\nexport {\n  verifyLinkedInCode as a,\n  verifyGoogleToken as v\n};\n"], "names": [], "mappings": ";;;;AAGA,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE;AACpD,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;AACxD,MAAM,sBAAsB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,EAAE;AACvE,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,gBAAgB,CAAC;AACvD,eAAe,iBAAiB,CAAC,OAAO,EAAE,OAAO,EAAE;AACnD,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,aAAa,CAAC;AACpD,MAAM,OAAO;AACb,MAAM,QAAQ,EAAE;AAChB,KAAK,CAAC;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE;AACvC,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC;AACnD;AACA,IAAI,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,OAAO;AACjD,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC;AACzD;AACA,IAAI,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;AACjE,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACtC,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC9B,QAAQ,IAAI,EAAE;AACd,UAAU,QAAQ,EAAE,QAAQ;AAC5B,UAAU,UAAU,EAAE,GAAG;AACzB,UAAU,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI;AACjC,UAAU,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO;AACtC,UAAU,aAAa,EAAE;AACzB;AACA;AACA,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACtC,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK;AACf,UAAU,IAAI,EAAE,IAAI,IAAI,EAAE;AAC1B,UAAU,KAAK,EAAE,OAAO;AACxB,UAAU,QAAQ,EAAE,QAAQ;AAC5B,UAAU,UAAU,EAAE,GAAG;AACzB,UAAU,aAAa,EAAE,IAAI;AAC7B;AACA,UAAU,IAAI,EAAE;AAChB;AACA;AACA,OAAO,CAAC;AACR;AACA,IAAI,MAAM,YAAY,GAAG,kBAAkB;AAC3C,MAAM;AACN,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;AAC7B,QAAQ,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;AACjC,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,EAAE,EAAE,IAAI,CAAC;AACjB,OAAO;AACP,MAAM,KAAK;AACX;AACA,KAAK;AACL,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,YAAY;AAClB,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,KAAK,EAAE,IAAI,CAAC;AACpB;AACA,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAC7D;AACA;AACA,eAAe,kBAAkB,CAAC,IAAI,EAAE,WAAW,EAAE;AACrD,EAAE,IAAI;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,+CAA+C,EAAE;AACvF,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,OAAO,EAAE;AACf,QAAQ,cAAc,EAAE;AACxB,OAAO;AACP,MAAM,IAAI,EAAE,IAAI,eAAe,CAAC;AAChC,QAAQ,UAAU,EAAE,oBAAoB;AACxC,QAAQ,IAAI;AACZ,QAAQ,YAAY,EAAE,WAAW;AACjC,QAAQ,SAAS,EAAE,kBAAkB;AACrC,QAAQ,aAAa,EAAE;AACvB,OAAO,CAAC,CAAC,QAAQ;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE;AAC3B,MAAM,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE;AAClD,MAAM,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,SAAS,CAAC;AACvD,MAAM,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;AAC5D;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE;AAChD,IAAI,MAAM,WAAW,GAAG,SAAS,CAAC,YAAY;AAC9C,IAAI,MAAM,eAAe,GAAG,MAAM,KAAK,CAAC,gCAAgC,EAAE;AAC1E,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;AAC7C;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE;AAC7B,MAAM,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC;AACvD;AACA,IAAI,MAAM,WAAW,GAAG,MAAM,eAAe,CAAC,IAAI,EAAE;AACpD,IAAI,MAAM,aAAa,GAAG,MAAM,KAAK;AACrC,MAAM,oFAAoF;AAC1F,MAAM;AACN,QAAQ,OAAO,EAAE;AACjB,UAAU,aAAa,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;AAC/C;AACA;AACA,KAAK;AACL,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE;AAC3B,MAAM,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC;AACrD;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,aAAa,CAAC,IAAI,EAAE;AAChD,IAAI,MAAM,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,YAAY;AAC/D,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC;AACvD;AACA,IAAI,MAAM,eAAe,GAAG,MAAM,KAAK;AACvC,MAAM,8FAA8F;AACpG,MAAM;AACN,QAAQ,OAAO,EAAE;AACjB,UAAU,aAAa,EAAE,CAAC,OAAO,EAAE,WAAW,CAAC;AAC/C;AACA;AACA,KAAK;AACL,IAAI,IAAI,OAAO,GAAG,EAAE;AACpB,IAAI,IAAI,eAAe,CAAC,EAAE,EAAE;AAC5B,MAAM,MAAM,WAAW,GAAG,MAAM,eAAe,CAAC,IAAI,EAAE;AACtD,MAAM,IAAI,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,CAAC,eAAe,CAAC,IAAI,WAAW,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,QAAQ,IAAI,WAAW,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAChN,QAAQ,OAAO,GAAG,WAAW,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAU;AACnG;AACA;AACA,IAAI,IAAI,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;AACjE,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACtC,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC9B,QAAQ,IAAI,EAAE;AACd,UAAU,QAAQ,EAAE,UAAU;AAC9B,UAAU,UAAU,EAAE,WAAW,CAAC,EAAE;AACpC,UAAU,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC,EAAE,WAAW,CAAC,iBAAiB,CAAC,CAAC;AACjG,UAAU,KAAK,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO;AACtC,UAAU,aAAa,EAAE;AACzB;AACA;AACA,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACtC,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK;AACf,UAAU,IAAI,EAAE,CAAC,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC,EAAE,WAAW,CAAC,iBAAiB,CAAC,CAAC;AACpF,UAAU,KAAK,EAAE,OAAO;AACxB,UAAU,QAAQ,EAAE,UAAU;AAC9B,UAAU,UAAU,EAAE,WAAW,CAAC,EAAE;AACpC,UAAU,aAAa,EAAE,IAAI;AAC7B;AACA,UAAU,IAAI,EAAE;AAChB;AACA;AACA,OAAO,CAAC;AACR;AACA,IAAI,MAAM,YAAY,GAAG,kBAAkB;AAC3C,MAAM;AACN,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;AAC7B,QAAQ,OAAO,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE;AACjC,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,EAAE,EAAE,IAAI,CAAC;AACjB,OAAO;AACP,MAAM,KAAK;AACX;AACA,KAAK;AACL,IAAI,OAAO;AACX,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,YAAY;AAClB,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,KAAK,EAAE,IAAI,CAAC;AACpB;AACA,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE;AAC7D;AACA;;;;"}