{"version": 3, "file": "HelpSearch-8uDSfRza.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/HelpSearch.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, N as bind_props, y as pop, w as push, S as attr_class, U as ensure_array_like, R as attr, V as escape_html, W as stringify } from \"./index3.js\";\nimport { g as goto } from \"./client.js\";\nimport { S as Search_input } from \"./search-input.js\";\nimport \"./client2.js\";\nimport \"clsx\";\nimport { R as Root, P as Popover_trigger, a as Popover_content } from \"./index14.js\";\nimport { S as Scroll_area } from \"./scroll-area.js\";\nimport { A as Arrow_right } from \"./arrow-right.js\";\nfunction HelpSearch($$payload, $$props) {\n  push();\n  let { className = \"\", searchQuery = \"\" } = $$props;\n  let searchResults = [];\n  let isOpen = false;\n  function handleSearch() {\n    if (searchQuery.trim()) {\n      goto(`/help/search?q=${encodeURIComponent(searchQuery.trim())}`);\n      isOpen = false;\n    }\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<div${attr_class(`help-search-container ${stringify(className)}`)}><!---->`;\n    Root($$payload2, {\n      get open() {\n        return isOpen;\n      },\n      set open($$value) {\n        isOpen = $$value;\n        $$settled = false;\n      },\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->`;\n        Popover_trigger($$payload3, {\n          children: ($$payload4) => {\n            $$payload4.out += `<div class=\"w-full\">`;\n            Search_input($$payload4, {\n              placeholder: \"Search help articles...\",\n              onSearch: handleSearch,\n              className: \"w-full\",\n              get value() {\n                return searchQuery;\n              },\n              set value($$value) {\n                searchQuery = $$value;\n                $$settled = false;\n              }\n            });\n            $$payload4.out += `<!----></div>`;\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out += `<!----> <!---->`;\n        Popover_content($$payload3, {\n          class: \"w-[var(--radix-popover-trigger-width)] p-0\",\n          align: \"start\",\n          sideOffset: 5,\n          children: ($$payload4) => {\n            if (searchResults.length > 0) {\n              $$payload4.out += \"<!--[1-->\";\n              $$payload4.out += `<!---->`;\n              Scroll_area($$payload4, {\n                class: \"h-[300px]\",\n                children: ($$payload5) => {\n                  const each_array = ensure_array_like(searchResults);\n                  $$payload5.out += `<div class=\"p-2\"><div class=\"space-y-1\"><!--[-->`;\n                  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n                    let result = each_array[$$index];\n                    $$payload5.out += `<a${attr(\"href\", `/help/${stringify(result.slug)}`)} class=\"hover:bg-accent flex items-center justify-between rounded-md p-2\"><div><div class=\"font-medium\">${escape_html(result.title)}</div> `;\n                    if (result.excerpt) {\n                      $$payload5.out += \"<!--[-->\";\n                      $$payload5.out += `<div class=\"text-muted-foreground line-clamp-1 text-sm\">${escape_html(result.excerpt)}</div>`;\n                    } else {\n                      $$payload5.out += \"<!--[!-->\";\n                    }\n                    $$payload5.out += `<!--]--></div> `;\n                    Arrow_right($$payload5, { class: \"text-muted-foreground h-4 w-4\" });\n                    $$payload5.out += `<!----></a>`;\n                  }\n                  $$payload5.out += `<!--]--></div> `;\n                  {\n                    $$payload5.out += \"<!--[!-->\";\n                  }\n                  $$payload5.out += `<!--]--></div>`;\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!---->`;\n            } else if (searchQuery.length >= 2) {\n              $$payload4.out += \"<!--[2-->\";\n              $$payload4.out += `<div class=\"text-muted-foreground p-4 text-center text-sm\">No results found for \"${escape_html(searchQuery)}\"</div>`;\n            } else {\n              $$payload4.out += \"<!--[!-->\";\n              $$payload4.out += `<div class=\"text-muted-foreground p-4 text-center text-sm\">Type at least 2 characters to search</div>`;\n            }\n            $$payload4.out += `<!--]-->`;\n          },\n          $$slots: { default: true }\n        });\n        $$payload3.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----></div>`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { searchQuery });\n  pop();\n}\nexport {\n  HelpSearch as H\n};\n"], "names": [], "mappings": ";;;;;;;;AAQA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,SAAS,GAAG,EAAE,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,OAAO;AACpD,EAAE,IAAI,aAAa,GAAG,EAAE;AACxB,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE;AAC5B,MAAM,IAAI,CAAC,CAAC,eAAe,EAAE,kBAAkB,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AACtE,MAAM,MAAM,GAAG,KAAK;AACpB;AACA;AACA,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;AAClG,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,IAAI,GAAG;AACjB,QAAQ,OAAO,MAAM;AACrB,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,MAAM,GAAG,OAAO;AACxB,QAAQ,SAAS,GAAG,KAAK;AACzB,OAAO;AACP,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,eAAe,CAAC,UAAU,EAAE;AACpC,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACpD,YAAY,YAAY,CAAC,UAAU,EAAE;AACrC,cAAc,WAAW,EAAE,yBAAyB;AACpD,cAAc,QAAQ,EAAE,YAAY;AACpC,cAAc,SAAS,EAAE,QAAQ;AACjC,cAAc,IAAI,KAAK,GAAG;AAC1B,gBAAgB,OAAO,WAAW;AAClC,eAAe;AACf,cAAc,IAAI,KAAK,CAAC,OAAO,EAAE;AACjC,gBAAgB,WAAW,GAAG,OAAO;AACrC,gBAAgB,SAAS,GAAG,KAAK;AACjC;AACA,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC7C,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC3C,QAAQ,eAAe,CAAC,UAAU,EAAE;AACpC,UAAU,KAAK,EAAE,4CAA4C;AAC7D,UAAU,KAAK,EAAE,OAAO;AACxB,UAAU,UAAU,EAAE,CAAC;AACvB,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1C,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,cAAc,WAAW,CAAC,UAAU,EAAE;AACtC,gBAAgB,KAAK,EAAE,WAAW;AAClC,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,MAAM,UAAU,GAAG,iBAAiB,CAAC,aAAa,CAAC;AACrE,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,gDAAgD,CAAC;AACtF,kBAAkB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrG,oBAAoB,IAAI,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC;AACpD,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,wGAAwG,EAAE,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AACvO,oBAAoB,IAAI,MAAM,CAAC,OAAO,EAAE;AACxC,sBAAsB,UAAU,CAAC,GAAG,IAAI,UAAU;AAClD,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,wDAAwD,EAAE,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;AACtI,qBAAqB,MAAM;AAC3B,sBAAsB,UAAU,CAAC,GAAG,IAAI,WAAW;AACnD;AACA,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACvD,oBAAoB,WAAW,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;AACvF,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AACnD;AACA,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACrD,kBAAkB;AAClB,oBAAoB,UAAU,CAAC,GAAG,IAAI,WAAW;AACjD;AACA,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACpD,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa,MAAM,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE;AAChD,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,iFAAiF,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC;AACrJ,aAAa,MAAM;AACnB,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,qGAAqG,CAAC;AACvI;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACxC,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACrC;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,WAAW,EAAE,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;;;;"}