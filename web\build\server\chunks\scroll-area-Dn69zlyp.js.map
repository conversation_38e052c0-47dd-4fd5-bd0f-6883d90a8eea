{"version": 3, "file": "scroll-area-Dn69zlyp.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/scroll-area.js"], "sourcesContent": ["import { J as derived, w as push, M as spread_attributes, N as bind_props, y as pop, Q as spread_props, O as copy_payload, P as assign_payload } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport \"clsx\";\nimport { u as useDebounce } from \"./use-debounce.svelte.js\";\nimport { C as Context } from \"./context.js\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { u as useStateMachine, P as Presence_layer } from \"./presence-layer.js\";\nimport { u as useId } from \"./use-id.js\";\nclass IsMounted {\n  #isMounted = false;\n  constructor() {\n  }\n  get current() {\n    return this.#isMounted;\n  }\n}\nfunction useResizeObserver(node, onResize) {\n}\nfunction clamp(n, min, max) {\n  return Math.min(max, Math.max(min, n));\n}\nconst SCROLL_AREA_ROOT_ATTR = \"data-scroll-area-root\";\nconst SCROLL_AREA_VIEWPORT_ATTR = \"data-scroll-area-viewport\";\nconst SCROLL_AREA_CORNER_ATTR = \"data-scroll-area-corner\";\nconst SCROLL_AREA_THUMB_ATTR = \"data-scroll-area-thumb\";\nconst SCROLL_AREA_SCROLLBAR_ATTR = \"data-scroll-area-scrollbar\";\nclass ScrollAreaRootState {\n  opts;\n  scrollAreaNode = null;\n  viewportNode = null;\n  contentNode = null;\n  scrollbarXNode = null;\n  scrollbarYNode = null;\n  cornerWidth = 0;\n  cornerHeight = 0;\n  scrollbarXEnabled = false;\n  scrollbarYEnabled = false;\n  constructor(opts) {\n    this.opts = opts;\n    useRefById({\n      ...opts,\n      onRefChange: (node) => {\n        this.scrollAreaNode = node;\n      }\n    });\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    dir: this.opts.dir.current,\n    style: {\n      position: \"relative\",\n      \"--bits-scroll-area-corner-height\": `${this.cornerHeight}px`,\n      \"--bits-scroll-area-corner-width\": `${this.cornerWidth}px`\n    },\n    [SCROLL_AREA_ROOT_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass ScrollAreaViewportState {\n  opts;\n  root;\n  #contentId = box(useId());\n  #contentRef = box(null);\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById({\n      ...opts,\n      onRefChange: (node) => {\n        this.root.viewportNode = node;\n      }\n    });\n    useRefById({\n      id: this.#contentId,\n      ref: this.#contentRef,\n      onRefChange: (node) => {\n        this.root.contentNode = node;\n      }\n    });\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    style: {\n      overflowX: this.root.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n      overflowY: this.root.scrollbarYEnabled ? \"scroll\" : \"hidden\"\n    },\n    [SCROLL_AREA_VIEWPORT_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n  #contentProps = derived(() => ({\n    id: this.#contentId.current,\n    \"data-scroll-area-content\": \"\",\n    /**\n     * When horizontal scrollbar is visible: this element should be at least\n     * as wide as its children for size calculations to work correctly.\n     *\n     * When horizontal scrollbar is NOT visible: this element's width should\n     * be constrained by the parent container to enable `text-overflow: ellipsis`\n     */\n    style: {\n      minWidth: this.root.scrollbarXEnabled ? \"fit-content\" : void 0\n    }\n  }));\n  get contentProps() {\n    return this.#contentProps();\n  }\n  set contentProps($$value) {\n    return this.#contentProps($$value);\n  }\n}\nclass ScrollAreaScrollbarState {\n  opts;\n  root;\n  #isHorizontal = derived(() => this.opts.orientation.current === \"horizontal\");\n  get isHorizontal() {\n    return this.#isHorizontal();\n  }\n  set isHorizontal($$value) {\n    return this.#isHorizontal($$value);\n  }\n  hasThumb = false;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n  }\n}\nclass ScrollAreaScrollbarHoverState {\n  scrollbar;\n  root;\n  isVisible = false;\n  constructor(scrollbar) {\n    this.scrollbar = scrollbar;\n    this.root = scrollbar.root;\n  }\n  #props = derived(() => ({\n    \"data-state\": this.isVisible ? \"visible\" : \"hidden\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass ScrollAreaScrollbarScrollState {\n  scrollbar;\n  root;\n  machine = useStateMachine(\"hidden\", {\n    hidden: { SCROLL: \"scrolling\" },\n    scrolling: {\n      SCROLL_END: \"idle\",\n      POINTER_ENTER: \"interacting\"\n    },\n    interacting: { SCROLL: \"interacting\", POINTER_LEAVE: \"idle\" },\n    idle: {\n      HIDE: \"hidden\",\n      SCROLL: \"scrolling\",\n      POINTER_ENTER: \"interacting\"\n    }\n  });\n  #isHidden = derived(() => this.machine.state.current === \"hidden\");\n  get isHidden() {\n    return this.#isHidden();\n  }\n  set isHidden($$value) {\n    return this.#isHidden($$value);\n  }\n  constructor(scrollbar) {\n    this.scrollbar = scrollbar;\n    this.root = scrollbar.root;\n    useDebounce(() => this.machine.dispatch(\"SCROLL_END\"), 100);\n    this.onpointerenter = this.onpointerenter.bind(this);\n    this.onpointerleave = this.onpointerleave.bind(this);\n  }\n  onpointerenter(_) {\n    this.machine.dispatch(\"POINTER_ENTER\");\n  }\n  onpointerleave(_) {\n    this.machine.dispatch(\"POINTER_LEAVE\");\n  }\n  #props = derived(() => ({\n    \"data-state\": this.machine.state.current === \"hidden\" ? \"hidden\" : \"visible\",\n    onpointerenter: this.onpointerenter,\n    onpointerleave: this.onpointerleave\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass ScrollAreaScrollbarAutoState {\n  scrollbar;\n  root;\n  isVisible = false;\n  constructor(scrollbar) {\n    this.scrollbar = scrollbar;\n    this.root = scrollbar.root;\n    useDebounce(\n      () => {\n        const viewportNode = this.root.viewportNode;\n        if (!viewportNode) return;\n        const isOverflowX = viewportNode.offsetWidth < viewportNode.scrollWidth;\n        const isOverflowY = viewportNode.offsetHeight < viewportNode.scrollHeight;\n        this.isVisible = this.scrollbar.isHorizontal ? isOverflowX : isOverflowY;\n      },\n      10\n    );\n  }\n  #props = derived(() => ({\n    \"data-state\": this.isVisible ? \"visible\" : \"hidden\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass ScrollAreaScrollbarVisibleState {\n  scrollbar;\n  root;\n  thumbNode = null;\n  pointerOffset = 0;\n  sizes = {\n    content: 0,\n    viewport: 0,\n    scrollbar: { size: 0, paddingStart: 0, paddingEnd: 0 }\n  };\n  #thumbRatio = derived(() => getThumbRatio(this.sizes.viewport, this.sizes.content));\n  get thumbRatio() {\n    return this.#thumbRatio();\n  }\n  set thumbRatio($$value) {\n    return this.#thumbRatio($$value);\n  }\n  #hasThumb = derived(() => Boolean(this.thumbRatio > 0 && this.thumbRatio < 1));\n  get hasThumb() {\n    return this.#hasThumb();\n  }\n  set hasThumb($$value) {\n    return this.#hasThumb($$value);\n  }\n  // this needs to be a $state to properly restore the transform style when the scrollbar\n  // goes from a hidden to visible state, otherwise it will start at the beginning of the\n  // scrollbar and flicker to the correct position after\n  prevTransformStyle = \"\";\n  constructor(scrollbar) {\n    this.scrollbar = scrollbar;\n    this.root = scrollbar.root;\n  }\n  setSizes(sizes) {\n    this.sizes = sizes;\n  }\n  getScrollPosition(pointerPos, dir) {\n    return getScrollPositionFromPointer({\n      pointerPos,\n      pointerOffset: this.pointerOffset,\n      sizes: this.sizes,\n      dir\n    });\n  }\n  onThumbPointerUp() {\n    this.pointerOffset = 0;\n  }\n  onThumbPointerDown(pointerPos) {\n    this.pointerOffset = pointerPos;\n  }\n  xOnThumbPositionChange() {\n    if (!(this.root.viewportNode && this.thumbNode)) return;\n    const scrollPos = this.root.viewportNode.scrollLeft;\n    const offset = getThumbOffsetFromScroll({\n      scrollPos,\n      sizes: this.sizes,\n      dir: this.root.opts.dir.current\n    });\n    const transformStyle = `translate3d(${offset}px, 0, 0)`;\n    this.thumbNode.style.transform = transformStyle;\n    this.prevTransformStyle = transformStyle;\n  }\n  xOnWheelScroll(scrollPos) {\n    if (!this.root.viewportNode) return;\n    this.root.viewportNode.scrollLeft = scrollPos;\n  }\n  xOnDragScroll(pointerPos) {\n    if (!this.root.viewportNode) return;\n    this.root.viewportNode.scrollLeft = this.getScrollPosition(pointerPos, this.root.opts.dir.current);\n  }\n  yOnThumbPositionChange() {\n    if (!(this.root.viewportNode && this.thumbNode)) return;\n    const scrollPos = this.root.viewportNode.scrollTop;\n    const offset = getThumbOffsetFromScroll({ scrollPos, sizes: this.sizes });\n    const transformStyle = `translate3d(0, ${offset}px, 0)`;\n    this.thumbNode.style.transform = transformStyle;\n    this.prevTransformStyle = transformStyle;\n  }\n  yOnWheelScroll(scrollPos) {\n    if (!this.root.viewportNode) return;\n    this.root.viewportNode.scrollTop = scrollPos;\n  }\n  yOnDragScroll(pointerPos) {\n    if (!this.root.viewportNode) return;\n    this.root.viewportNode.scrollTop = this.getScrollPosition(pointerPos, this.root.opts.dir.current);\n  }\n}\nclass ScrollAreaScrollbarXState {\n  opts;\n  scrollbarVis;\n  root;\n  computedStyle;\n  scrollbar;\n  constructor(opts, scrollbarVis) {\n    this.opts = opts;\n    this.scrollbarVis = scrollbarVis;\n    this.root = scrollbarVis.root;\n    this.scrollbar = scrollbarVis.scrollbar;\n    useRefById({\n      ...this.scrollbar.opts,\n      onRefChange: (node) => {\n        this.root.scrollbarXNode = node;\n      },\n      deps: () => this.opts.mounted.current\n    });\n  }\n  onThumbPointerDown = (pointerPos) => {\n    this.scrollbarVis.onThumbPointerDown(pointerPos.x);\n  };\n  onDragScroll = (pointerPos) => {\n    this.scrollbarVis.xOnDragScroll(pointerPos.x);\n  };\n  onThumbPointerUp = () => {\n    this.scrollbarVis.onThumbPointerUp();\n  };\n  onThumbPositionChange = () => {\n    this.scrollbarVis.xOnThumbPositionChange();\n  };\n  onWheelScroll = (e, maxScrollPos) => {\n    if (!this.root.viewportNode) return;\n    const scrollPos = this.root.viewportNode.scrollLeft + e.deltaX;\n    this.scrollbarVis.xOnWheelScroll(scrollPos);\n    if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n      e.preventDefault();\n    }\n  };\n  onResize = () => {\n    if (!(this.scrollbar.opts.ref.current && this.root.viewportNode && this.computedStyle)) return;\n    this.scrollbarVis.setSizes({\n      content: this.root.viewportNode.scrollWidth,\n      viewport: this.root.viewportNode.offsetWidth,\n      scrollbar: {\n        size: this.scrollbar.opts.ref.current.clientWidth,\n        paddingStart: toInt(this.computedStyle.paddingLeft),\n        paddingEnd: toInt(this.computedStyle.paddingRight)\n      }\n    });\n  };\n  #thumbSize = derived(() => {\n    return getThumbSize(this.scrollbarVis.sizes);\n  });\n  get thumbSize() {\n    return this.#thumbSize();\n  }\n  set thumbSize($$value) {\n    return this.#thumbSize($$value);\n  }\n  #props = derived(() => ({\n    id: this.scrollbar.opts.id.current,\n    \"data-orientation\": \"horizontal\",\n    style: {\n      bottom: 0,\n      left: this.root.opts.dir.current === \"rtl\" ? \"var(--bits-scroll-area-corner-width)\" : 0,\n      right: this.root.opts.dir.current === \"ltr\" ? \"var(--bits-scroll-area-corner-width)\" : 0,\n      \"--bits-scroll-area-thumb-width\": `${this.thumbSize}px`\n    }\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass ScrollAreaScrollbarYState {\n  opts;\n  scrollbarVis;\n  root;\n  scrollbar;\n  computedStyle;\n  constructor(opts, scrollbarVis) {\n    this.opts = opts;\n    this.scrollbarVis = scrollbarVis;\n    this.root = scrollbarVis.root;\n    this.scrollbar = scrollbarVis.scrollbar;\n    useRefById({\n      ...this.scrollbar.opts,\n      onRefChange: (node) => {\n        this.root.scrollbarYNode = node;\n      },\n      deps: () => this.opts.mounted.current\n    });\n    this.onThumbPointerDown = this.onThumbPointerDown.bind(this);\n    this.onDragScroll = this.onDragScroll.bind(this);\n    this.onThumbPointerUp = this.onThumbPointerUp.bind(this);\n    this.onThumbPositionChange = this.onThumbPositionChange.bind(this);\n    this.onWheelScroll = this.onWheelScroll.bind(this);\n    this.onResize = this.onResize.bind(this);\n  }\n  onThumbPointerDown(pointerPos) {\n    this.scrollbarVis.onThumbPointerDown(pointerPos.y);\n  }\n  onDragScroll(pointerPos) {\n    this.scrollbarVis.yOnDragScroll(pointerPos.y);\n  }\n  onThumbPointerUp() {\n    this.scrollbarVis.onThumbPointerUp();\n  }\n  onThumbPositionChange() {\n    this.scrollbarVis.yOnThumbPositionChange();\n  }\n  onWheelScroll(e, maxScrollPos) {\n    if (!this.root.viewportNode) return;\n    const scrollPos = this.root.viewportNode.scrollTop + e.deltaY;\n    this.scrollbarVis.yOnWheelScroll(scrollPos);\n    if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n      e.preventDefault();\n    }\n  }\n  onResize() {\n    if (!(this.scrollbar.opts.ref.current && this.root.viewportNode && this.computedStyle)) return;\n    this.scrollbarVis.setSizes({\n      content: this.root.viewportNode.scrollHeight,\n      viewport: this.root.viewportNode.offsetHeight,\n      scrollbar: {\n        size: this.scrollbar.opts.ref.current.clientHeight,\n        paddingStart: toInt(this.computedStyle.paddingTop),\n        paddingEnd: toInt(this.computedStyle.paddingBottom)\n      }\n    });\n  }\n  #thumbSize = derived(() => {\n    return getThumbSize(this.scrollbarVis.sizes);\n  });\n  get thumbSize() {\n    return this.#thumbSize();\n  }\n  set thumbSize($$value) {\n    return this.#thumbSize($$value);\n  }\n  #props = derived(() => ({\n    id: this.scrollbar.opts.id.current,\n    \"data-orientation\": \"vertical\",\n    style: {\n      top: 0,\n      right: this.root.opts.dir.current === \"ltr\" ? 0 : void 0,\n      left: this.root.opts.dir.current === \"rtl\" ? 0 : void 0,\n      bottom: \"var(--bits-scroll-area-corner-height)\",\n      \"--bits-scroll-area-thumb-height\": `${this.thumbSize}px`\n    }\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass ScrollAreaScrollbarSharedState {\n  scrollbarState;\n  root;\n  scrollbarVis;\n  scrollbar;\n  rect = null;\n  prevWebkitUserSelect = \"\";\n  handleResize;\n  handleThumbPositionChange;\n  handleWheelScroll;\n  handleThumbPointerDown;\n  handleThumbPointerUp;\n  #maxScrollPos = derived(() => this.scrollbarVis.sizes.content - this.scrollbarVis.sizes.viewport);\n  get maxScrollPos() {\n    return this.#maxScrollPos();\n  }\n  set maxScrollPos($$value) {\n    return this.#maxScrollPos($$value);\n  }\n  constructor(scrollbarState) {\n    this.scrollbarState = scrollbarState;\n    this.root = scrollbarState.root;\n    this.scrollbarVis = scrollbarState.scrollbarVis;\n    this.scrollbar = scrollbarState.scrollbarVis.scrollbar;\n    this.handleResize = useDebounce(() => this.scrollbarState.onResize(), 10);\n    this.handleThumbPositionChange = this.scrollbarState.onThumbPositionChange;\n    this.handleWheelScroll = this.scrollbarState.onWheelScroll;\n    this.handleThumbPointerDown = this.scrollbarState.onThumbPointerDown;\n    this.handleThumbPointerUp = this.scrollbarState.onThumbPointerUp;\n    useResizeObserver(() => this.scrollbar.opts.ref.current, this.handleResize);\n    useResizeObserver(() => this.root.contentNode, this.handleResize);\n    this.onpointerdown = this.onpointerdown.bind(this);\n    this.onpointermove = this.onpointermove.bind(this);\n    this.onpointerup = this.onpointerup.bind(this);\n  }\n  handleDragScroll(e) {\n    if (!this.rect) return;\n    const x = e.clientX - this.rect.left;\n    const y = e.clientY - this.rect.top;\n    this.scrollbarState.onDragScroll({ x, y });\n  }\n  onpointerdown(e) {\n    if (e.button !== 0) return;\n    const target = e.target;\n    target.setPointerCapture(e.pointerId);\n    this.rect = this.scrollbar.opts.ref.current?.getBoundingClientRect() ?? null;\n    this.prevWebkitUserSelect = document.body.style.webkitUserSelect;\n    document.body.style.webkitUserSelect = \"none\";\n    if (this.root.viewportNode) this.root.viewportNode.style.scrollBehavior = \"auto\";\n    this.handleDragScroll(e);\n  }\n  onpointermove(e) {\n    this.handleDragScroll(e);\n  }\n  onpointerup(e) {\n    const target = e.target;\n    if (target.hasPointerCapture(e.pointerId)) {\n      target.releasePointerCapture(e.pointerId);\n    }\n    document.body.style.webkitUserSelect = this.prevWebkitUserSelect;\n    if (this.root.viewportNode) this.root.viewportNode.style.scrollBehavior = \"\";\n    this.rect = null;\n  }\n  #props = derived(() => mergeProps({\n    ...this.scrollbarState.props,\n    style: {\n      position: \"absolute\",\n      ...this.scrollbarState.props.style\n    },\n    [SCROLL_AREA_SCROLLBAR_ATTR]: \"\",\n    onpointerdown: this.onpointerdown,\n    onpointermove: this.onpointermove,\n    onpointerup: this.onpointerup\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass ScrollAreaThumbImplState {\n  opts;\n  scrollbarState;\n  #root;\n  #removeUnlinkedScrollListener;\n  #debounceScrollEnd = useDebounce(\n    () => {\n      if (this.#removeUnlinkedScrollListener) {\n        this.#removeUnlinkedScrollListener();\n        this.#removeUnlinkedScrollListener = void 0;\n      }\n    },\n    100\n  );\n  constructor(opts, scrollbarState) {\n    this.opts = opts;\n    this.scrollbarState = scrollbarState;\n    this.#root = scrollbarState.root;\n    useRefById({\n      ...opts,\n      onRefChange: (node) => {\n        this.scrollbarState.scrollbarVis.thumbNode = node;\n      },\n      deps: () => this.opts.mounted.current\n    });\n    this.onpointerdowncapture = this.onpointerdowncapture.bind(this);\n    this.onpointerup = this.onpointerup.bind(this);\n  }\n  onpointerdowncapture(e) {\n    const thumb = e.target;\n    if (!thumb) return;\n    const thumbRect = thumb.getBoundingClientRect();\n    const x = e.clientX - thumbRect.left;\n    const y = e.clientY - thumbRect.top;\n    this.scrollbarState.handleThumbPointerDown({ x, y });\n  }\n  onpointerup(_) {\n    this.scrollbarState.handleThumbPointerUp();\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"data-state\": this.scrollbarState.scrollbarVis.hasThumb ? \"visible\" : \"hidden\",\n    style: {\n      width: \"var(--bits-scroll-area-thumb-width)\",\n      height: \"var(--bits-scroll-area-thumb-height)\",\n      transform: this.scrollbarState.scrollbarVis.prevTransformStyle\n    },\n    onpointerdowncapture: this.onpointerdowncapture,\n    onpointerup: this.onpointerup,\n    [SCROLL_AREA_THUMB_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass ScrollAreaCornerImplState {\n  opts;\n  root;\n  #width = 0;\n  #height = 0;\n  #hasSize = derived(() => Boolean(this.#width && this.#height));\n  get hasSize() {\n    return this.#hasSize();\n  }\n  set hasSize($$value) {\n    return this.#hasSize($$value);\n  }\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    style: {\n      width: this.#width,\n      height: this.#height,\n      position: \"absolute\",\n      right: this.root.opts.dir.current === \"ltr\" ? 0 : void 0,\n      left: this.root.opts.dir.current === \"rtl\" ? 0 : void 0,\n      bottom: 0\n    },\n    [SCROLL_AREA_CORNER_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst ScrollAreaRootContext = new Context(\"ScrollArea.Root\");\nconst ScrollAreaScrollbarContext = new Context(\"ScrollArea.Scrollbar\");\nconst ScrollAreaScrollbarVisibleContext = new Context(\"ScrollArea.ScrollbarVisible\");\nconst ScrollAreaScrollbarAxisContext = new Context(\"ScrollArea.ScrollbarAxis\");\nconst ScrollAreaScrollbarSharedContext = new Context(\"ScrollArea.ScrollbarShared\");\nfunction useScrollAreaRoot(props) {\n  return ScrollAreaRootContext.set(new ScrollAreaRootState(props));\n}\nfunction useScrollAreaViewport(props) {\n  return new ScrollAreaViewportState(props, ScrollAreaRootContext.get());\n}\nfunction useScrollAreaScrollbar(props) {\n  return ScrollAreaScrollbarContext.set(new ScrollAreaScrollbarState(props, ScrollAreaRootContext.get()));\n}\nfunction useScrollAreaScrollbarVisible() {\n  return ScrollAreaScrollbarVisibleContext.set(new ScrollAreaScrollbarVisibleState(ScrollAreaScrollbarContext.get()));\n}\nfunction useScrollAreaScrollbarAuto() {\n  return new ScrollAreaScrollbarAutoState(ScrollAreaScrollbarContext.get());\n}\nfunction useScrollAreaScrollbarScroll() {\n  return new ScrollAreaScrollbarScrollState(ScrollAreaScrollbarContext.get());\n}\nfunction useScrollAreaScrollbarHover() {\n  return new ScrollAreaScrollbarHoverState(ScrollAreaScrollbarContext.get());\n}\nfunction useScrollAreaScrollbarX(props) {\n  return ScrollAreaScrollbarAxisContext.set(new ScrollAreaScrollbarXState(props, ScrollAreaScrollbarVisibleContext.get()));\n}\nfunction useScrollAreaScrollbarY(props) {\n  return ScrollAreaScrollbarAxisContext.set(new ScrollAreaScrollbarYState(props, ScrollAreaScrollbarVisibleContext.get()));\n}\nfunction useScrollAreaScrollbarShared() {\n  return ScrollAreaScrollbarSharedContext.set(new ScrollAreaScrollbarSharedState(ScrollAreaScrollbarAxisContext.get()));\n}\nfunction useScrollAreaThumb(props) {\n  return new ScrollAreaThumbImplState(props, ScrollAreaScrollbarSharedContext.get());\n}\nfunction useScrollAreaCorner(props) {\n  return new ScrollAreaCornerImplState(props, ScrollAreaRootContext.get());\n}\nfunction toInt(value) {\n  return value ? Number.parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n  const ratio = viewportSize / contentSize;\n  return Number.isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n  const ratio = getThumbRatio(sizes.viewport, sizes.content);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n  return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer({\n  pointerPos,\n  pointerOffset,\n  sizes,\n  dir = \"ltr\"\n}) {\n  const thumbSizePx = getThumbSize(sizes);\n  const thumbCenter = thumbSizePx / 2;\n  const offset = pointerOffset || thumbCenter;\n  const thumbOffsetFromEnd = thumbSizePx - offset;\n  const minPointerPos = sizes.scrollbar.paddingStart + offset;\n  const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const scrollRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const interpolate = linearScale([minPointerPos, maxPointerPos], scrollRange);\n  return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll({ scrollPos, sizes, dir = \"ltr\" }) {\n  const thumbSizePx = getThumbSize(sizes);\n  const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n  const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n  const maxScrollPos = sizes.content - sizes.viewport;\n  const maxThumbPos = scrollbar - thumbSizePx;\n  const scrollClampRange = dir === \"ltr\" ? [0, maxScrollPos] : [maxScrollPos * -1, 0];\n  const scrollWithoutMomentum = clamp(scrollPos, scrollClampRange[0], scrollClampRange[1]);\n  const interpolate = linearScale([0, maxScrollPos], [0, maxThumbPos]);\n  return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n  return (value) => {\n    if (input[0] === input[1] || output[0] === output[1]) return output[0];\n    const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n    return output[0] + ratio * (value - input[0]);\n  };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n  return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nfunction Scroll_area$1($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    id = useId(),\n    type = \"hover\",\n    dir = \"ltr\",\n    scrollHideDelay = 600,\n    children,\n    child,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const rootState = useScrollAreaRoot({\n    type: box.with(() => type),\n    dir: box.with(() => dir),\n    scrollHideDelay: box.with(() => scrollHideDelay),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, rootState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Scroll_area_viewport($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    id = useId(),\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const viewportState = useScrollAreaViewport({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, viewportState.props);\n  const mergedContentProps = mergeProps({}, viewportState.contentProps);\n  $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}><div${spread_attributes({ ...mergedContentProps }, null)}>`;\n  children?.($$payload);\n  $$payload.out += `<!----></div></div>`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Scroll_area_scrollbar_shared($$payload, $$props) {\n  push();\n  let {\n    child,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const scrollbarSharedState = useScrollAreaScrollbarShared();\n  const mergedProps = mergeProps(restProps, scrollbarSharedState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  pop();\n}\nfunction Scroll_area_scrollbar_x($$payload, $$props) {\n  push();\n  let { $$slots, $$events, ...restProps } = $$props;\n  const isMounted = new IsMounted();\n  const scrollbarXState = useScrollAreaScrollbarX({ mounted: box.with(() => isMounted.current) });\n  const mergedProps = mergeProps(restProps, scrollbarXState.props);\n  Scroll_area_scrollbar_shared($$payload, spread_props([mergedProps]));\n  pop();\n}\nfunction Scroll_area_scrollbar_y($$payload, $$props) {\n  push();\n  let { $$slots, $$events, ...restProps } = $$props;\n  const isMounted = new IsMounted();\n  const scrollbarYState = useScrollAreaScrollbarY({ mounted: box.with(() => isMounted.current) });\n  const mergedProps = mergeProps(restProps, scrollbarYState.props);\n  Scroll_area_scrollbar_shared($$payload, spread_props([mergedProps]));\n  pop();\n}\nfunction Scroll_area_scrollbar_visible($$payload, $$props) {\n  push();\n  let { $$slots, $$events, ...restProps } = $$props;\n  const scrollbarVisibleState = useScrollAreaScrollbarVisible();\n  if (scrollbarVisibleState.scrollbar.opts.orientation.current === \"horizontal\") {\n    $$payload.out += \"<!--[-->\";\n    Scroll_area_scrollbar_x($$payload, spread_props([restProps]));\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    Scroll_area_scrollbar_y($$payload, spread_props([restProps]));\n  }\n  $$payload.out += `<!--]-->`;\n  pop();\n}\nfunction Scroll_area_scrollbar_auto($$payload, $$props) {\n  push();\n  let {\n    forceMount = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const scrollbarAutoState = useScrollAreaScrollbarAuto();\n  const mergedProps = mergeProps(restProps, scrollbarAutoState.props);\n  {\n    let presence = function($$payload2) {\n      Scroll_area_scrollbar_visible($$payload2, spread_props([mergedProps]));\n    };\n    Presence_layer($$payload, spread_props([\n      {\n        present: forceMount || scrollbarAutoState.isVisible\n      },\n      mergedProps,\n      { presence, $$slots: { presence: true } }\n    ]));\n  }\n  pop();\n}\nfunction Scroll_area_scrollbar_scroll($$payload, $$props) {\n  push();\n  let {\n    forceMount = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const scrollbarScrollState = useScrollAreaScrollbarScroll();\n  const mergedProps = mergeProps(restProps, scrollbarScrollState.props);\n  {\n    let presence = function($$payload2) {\n      Scroll_area_scrollbar_visible($$payload2, spread_props([mergedProps]));\n    };\n    Presence_layer($$payload, spread_props([\n      mergedProps,\n      {\n        present: forceMount || !scrollbarScrollState.isHidden,\n        presence,\n        $$slots: { presence: true }\n      }\n    ]));\n  }\n  pop();\n}\nfunction Scroll_area_scrollbar_hover($$payload, $$props) {\n  push();\n  let {\n    forceMount = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const scrollbarHoverState = useScrollAreaScrollbarHover();\n  const scrollbarAutoState = useScrollAreaScrollbarAuto();\n  const mergedProps = mergeProps(restProps, scrollbarHoverState.props, scrollbarAutoState.props, {\n    \"data-state\": scrollbarHoverState.isVisible ? \"visible\" : \"hidden\"\n  });\n  const present = forceMount || scrollbarHoverState.isVisible && scrollbarAutoState.isVisible;\n  {\n    let presence = function($$payload2) {\n      Scroll_area_scrollbar_visible($$payload2, spread_props([mergedProps]));\n    };\n    Presence_layer($$payload, spread_props([\n      mergedProps,\n      {\n        present,\n        presence,\n        $$slots: { presence: true }\n      }\n    ]));\n  }\n  pop();\n}\nfunction Scroll_area_scrollbar$1($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    id = useId(),\n    orientation,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const scrollbarState = useScrollAreaScrollbar({\n    orientation: box.with(() => orientation),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const type = scrollbarState.root.opts.type.current;\n  if (type === \"hover\") {\n    $$payload.out += \"<!--[-->\";\n    Scroll_area_scrollbar_hover($$payload, spread_props([restProps, { id }]));\n  } else if (type === \"scroll\") {\n    $$payload.out += \"<!--[1-->\";\n    Scroll_area_scrollbar_scroll($$payload, spread_props([restProps, { id }]));\n  } else if (type === \"auto\") {\n    $$payload.out += \"<!--[2-->\";\n    Scroll_area_scrollbar_auto($$payload, spread_props([restProps, { id }]));\n  } else if (type === \"always\") {\n    $$payload.out += \"<!--[3-->\";\n    Scroll_area_scrollbar_visible($$payload, spread_props([restProps, { id }]));\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Scroll_area_thumb_impl($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    id,\n    child,\n    children,\n    present,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const isMounted = new IsMounted();\n  const thumbState = useScrollAreaThumb({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v),\n    mounted: box.with(() => isMounted.current)\n  });\n  const mergedProps = mergeProps(restProps, thumbState.props, { style: { hidden: !present } });\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Scroll_area_thumb($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    ref = null,\n    forceMount = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const scrollbarState = ScrollAreaScrollbarVisibleContext.get();\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    {\n      let presence = function($$payload3, { present }) {\n        Scroll_area_thumb_impl($$payload3, spread_props([\n          restProps,\n          {\n            id,\n            present: present.current,\n            get ref() {\n              return ref;\n            },\n            set ref($$value) {\n              ref = $$value;\n              $$settled = false;\n            }\n          }\n        ]));\n      };\n      Presence_layer($$payload2, spread_props([\n        { present: forceMount || scrollbarState.hasThumb },\n        restProps,\n        { id, presence, $$slots: { presence: true } }\n      ]));\n    }\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Scroll_area_corner_impl($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    id,\n    children,\n    child,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const cornerState = useScrollAreaCorner({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, cornerState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Scroll_area_corner($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    id = useId(),\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const scrollAreaState = ScrollAreaRootContext.get();\n  const hasBothScrollbarsVisible = Boolean(scrollAreaState.scrollbarXNode && scrollAreaState.scrollbarYNode);\n  const hasCorner = scrollAreaState.opts.type.current !== \"scroll\" && hasBothScrollbarsVisible;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    if (hasCorner) {\n      $$payload2.out += \"<!--[-->\";\n      Scroll_area_corner_impl($$payload2, spread_props([\n        restProps,\n        {\n          id,\n          get ref() {\n            return ref;\n          },\n          set ref($$value) {\n            ref = $$value;\n            $$settled = false;\n          }\n        }\n      ]));\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]-->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Scroll_area_scrollbar($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    orientation = \"vertical\",\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Scroll_area_scrollbar$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"scroll-area-scrollbar\",\n        orientation,\n        class: cn(\"flex touch-none select-none p-px transition-colors\", orientation === \"vertical\" && \"h-full w-2.5 border-l border-l-transparent\", orientation === \"horizontal\" && \"h-2.5 flex-col border-t border-t-transparent\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        },\n        children: ($$payload3) => {\n          children?.($$payload3);\n          $$payload3.out += `<!----> <!---->`;\n          Scroll_area_thumb($$payload3, {\n            \"data-slot\": \"scroll-area-thumb\",\n            class: \"bg-border relative flex-1 rounded-full\"\n          });\n          $$payload3.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Scroll_area($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    orientation = \"vertical\",\n    scrollbarXClasses = \"\",\n    scrollbarYClasses = \"\",\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Scroll_area$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"scroll-area\",\n        class: cn(\"relative\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        },\n        children: ($$payload3) => {\n          $$payload3.out += `<!---->`;\n          Scroll_area_viewport($$payload3, {\n            \"data-slot\": \"scroll-area-viewport\",\n            class: \"ring-ring/10 dark:ring-ring/20 dark:outline-ring/40 outline-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] focus-visible:outline-1 focus-visible:ring-4\",\n            children: ($$payload4) => {\n              children?.($$payload4);\n              $$payload4.out += `<!---->`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> `;\n          if (orientation === \"vertical\" || orientation === \"both\") {\n            $$payload3.out += \"<!--[-->\";\n            Scroll_area_scrollbar($$payload3, {\n              orientation: \"vertical\",\n              class: scrollbarYClasses\n            });\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--> `;\n          if (orientation === \"horizontal\" || orientation === \"both\") {\n            $$payload3.out += \"<!--[-->\";\n            Scroll_area_scrollbar($$payload3, {\n              orientation: \"horizontal\",\n              class: scrollbarXClasses\n            });\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--> <!---->`;\n          Scroll_area_corner($$payload3, {});\n          $$payload3.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Scroll_area as S,\n  Scroll_area_scrollbar as a\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAUA,MAAM,SAAS,CAAC;AAChB,EAAE,UAAU,GAAG,KAAK;AACpB,EAAE,WAAW,GAAG;AAChB;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,UAAU;AAC1B;AACA;AACA,SAAS,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE;AAC3C;AACA,SAAS,KAAK,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE;AAC5B,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxC;AACA,MAAM,qBAAqB,GAAG,uBAAuB;AACrD,MAAM,yBAAyB,GAAG,2BAA2B;AAC7D,MAAM,uBAAuB,GAAG,yBAAyB;AACzD,MAAM,sBAAsB,GAAG,wBAAwB;AACvD,MAAM,0BAA0B,GAAG,4BAA4B;AAC/D,MAAM,mBAAmB,CAAC;AAC1B,EAAE,IAAI;AACN,EAAE,cAAc,GAAG,IAAI;AACvB,EAAE,YAAY,GAAG,IAAI;AACrB,EAAE,WAAW,GAAG,IAAI;AACpB,EAAE,cAAc,GAAG,IAAI;AACvB,EAAE,cAAc,GAAG,IAAI;AACvB,EAAE,WAAW,GAAG,CAAC;AACjB,EAAE,YAAY,GAAG,CAAC;AAClB,EAAE,iBAAiB,GAAG,KAAK;AAC3B,EAAE,iBAAiB,GAAG,KAAK;AAC3B,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,cAAc,GAAG,IAAI;AAClC;AACA,KAAK,CAAC;AACN;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC9B,IAAI,KAAK,EAAE;AACX,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,kCAAkC,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;AAClE,MAAM,iCAAiC,EAAE,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,EAAE;AAC/D,KAAK;AACL,IAAI,CAAC,qBAAqB,GAAG;AAC7B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,uBAAuB,CAAC;AAC9B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,UAAU,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;AAC3B,EAAE,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC;AACzB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI;AACrC;AACA,KAAK,CAAC;AACN,IAAI,UAAU,CAAC;AACf,MAAM,EAAE,EAAE,IAAI,CAAC,UAAU;AACzB,MAAM,GAAG,EAAE,IAAI,CAAC,WAAW;AAC3B,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI;AACpC;AACA,KAAK,CAAC;AACN;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,QAAQ,GAAG,QAAQ;AAClE,MAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,QAAQ,GAAG;AAC1D,KAAK;AACL,IAAI,CAAC,yBAAyB,GAAG;AACjC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO;AACjC,IAAI,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,OAAO;AAC/B,IAAI,0BAA0B,EAAE,EAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,EAAE;AACX,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,aAAa,GAAG;AAC9D;AACA,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA;AACA,MAAM,wBAAwB,CAAC;AAC/B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,YAAY,CAAC;AAC/E,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,QAAQ,GAAG,KAAK;AAClB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB;AACA;AACA,MAAM,6BAA6B,CAAC;AACpC,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,SAAS,GAAG,KAAK;AACnB,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI;AAC9B;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,YAAY,EAAE,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG;AAC/C,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,8BAA8B,CAAC;AACrC,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,OAAO,GAAG,eAAe,CAAC,QAAQ,EAAE;AACtC,IAAI,MAAM,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;AACnC,IAAI,SAAS,EAAE;AACf,MAAM,UAAU,EAAE,MAAM;AACxB,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,WAAW,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,MAAM,EAAE;AACjE,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,aAAa,EAAE;AACrB;AACA,GAAG,CAAC;AACJ,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,KAAK,QAAQ,CAAC;AACpE,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;AAC3B;AACA,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAClC;AACA,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI;AAC9B,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC;AAC/D,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AACxD,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AACxD;AACA,EAAE,cAAc,CAAC,CAAC,EAAE;AACpB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;AAC1C;AACA,EAAE,cAAc,CAAC,CAAC,EAAE;AACpB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;AAC1C;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;AAChF,IAAI,cAAc,EAAE,IAAI,CAAC,cAAc;AACvC,IAAI,cAAc,EAAE,IAAI,CAAC;AACzB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,4BAA4B,CAAC;AACnC,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,SAAS,GAAG,KAAK;AACnB,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI;AAC9B,IAAI,WAAW;AACf,MAAM,MAAM;AACZ,QAAQ,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY;AACnD,QAAQ,IAAI,CAAC,YAAY,EAAE;AAC3B,QAAQ,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,GAAG,YAAY,CAAC,WAAW;AAC/E,QAAQ,MAAM,WAAW,GAAG,YAAY,CAAC,YAAY,GAAG,YAAY,CAAC,YAAY;AACjF,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,WAAW,GAAG,WAAW;AAChF,OAAO;AACP,MAAM;AACN,KAAK;AACL;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,YAAY,EAAE,IAAI,CAAC,SAAS,GAAG,SAAS,GAAG;AAC/C,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,+BAA+B,CAAC;AACtC,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,SAAS,GAAG,IAAI;AAClB,EAAE,aAAa,GAAG,CAAC;AACnB,EAAE,KAAK,GAAG;AACV,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC;AACxD,GAAG;AACH,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACrF,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE;AAC7B;AACA,EAAE,IAAI,UAAU,CAAC,OAAO,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC;AACA,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;AAChF,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;AAC3B;AACA,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAClC;AACA;AACA;AACA;AACA,EAAE,kBAAkB,GAAG,EAAE;AACzB,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI;AAC9B;AACA,EAAE,QAAQ,CAAC,KAAK,EAAE;AAClB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK;AACtB;AACA,EAAE,iBAAiB,CAAC,UAAU,EAAE,GAAG,EAAE;AACrC,IAAI,OAAO,4BAA4B,CAAC;AACxC,MAAM,UAAU;AAChB,MAAM,aAAa,EAAE,IAAI,CAAC,aAAa;AACvC,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM;AACN,KAAK,CAAC;AACN;AACA,EAAE,gBAAgB,GAAG;AACrB,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC;AAC1B;AACA,EAAE,kBAAkB,CAAC,UAAU,EAAE;AACjC,IAAI,IAAI,CAAC,aAAa,GAAG,UAAU;AACnC;AACA,EAAE,sBAAsB,GAAG;AAC3B,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE;AACrD,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU;AACvD,IAAI,MAAM,MAAM,GAAG,wBAAwB,CAAC;AAC5C,MAAM,SAAS;AACf,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AAC9B,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC;AAC3D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,cAAc;AACnD,IAAI,IAAI,CAAC,kBAAkB,GAAG,cAAc;AAC5C;AACA,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,SAAS;AACjD;AACA,EAAE,aAAa,CAAC,UAAU,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;AACtG;AACA,EAAE,sBAAsB,GAAG;AAC3B,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE;AACrD,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS;AACtD,IAAI,MAAM,MAAM,GAAG,wBAAwB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;AAC7E,IAAI,MAAM,cAAc,GAAG,CAAC,eAAe,EAAE,MAAM,CAAC,MAAM,CAAC;AAC3D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,cAAc;AACnD,IAAI,IAAI,CAAC,kBAAkB,GAAG,cAAc;AAC5C;AACA,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,SAAS;AAChD;AACA,EAAE,aAAa,CAAC,UAAU,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;AACrG;AACA;AACA,MAAM,yBAAyB,CAAC;AAChC,EAAE,IAAI;AACN,EAAE,YAAY;AACd,EAAE,IAAI;AACN,EAAE,aAAa;AACf,EAAE,SAAS;AACX,EAAE,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE;AAClC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY;AACpC,IAAI,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI;AACjC,IAAI,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS;AAC3C,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;AAC5B,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI;AACvC,OAAO;AACP,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,KAAK,CAAC;AACN;AACA,EAAE,kBAAkB,GAAG,CAAC,UAAU,KAAK;AACvC,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;AACtD,GAAG;AACH,EAAE,YAAY,GAAG,CAAC,UAAU,KAAK;AACjC,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,gBAAgB,GAAG,MAAM;AAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;AACxC,GAAG;AACH,EAAE,qBAAqB,GAAG,MAAM;AAChC,IAAI,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE;AAC9C,GAAG;AACH,EAAE,aAAa,GAAG,CAAC,CAAC,EAAE,YAAY,KAAK;AACvC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,CAAC,CAAC,MAAM;AAClE,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC;AAC/C,IAAI,IAAI,gCAAgC,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE;AACnE,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB;AACA,GAAG;AACH,EAAE,QAAQ,GAAG,MAAM;AACnB,IAAI,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE;AAC5F,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC/B,MAAM,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW;AACjD,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW;AAClD,MAAM,SAAS,EAAE;AACjB,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW;AACzD,QAAQ,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;AAC3D,QAAQ,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY;AACzD;AACA,KAAK,CAAC;AACN,GAAG;AACH,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM;AAC7B,IAAI,OAAO,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AAChD,GAAG,CAAC;AACJ,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;AAC5B;AACA,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACnC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AACtC,IAAI,kBAAkB,EAAE,YAAY;AACpC,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,sCAAsC,GAAG,CAAC;AAC7F,MAAM,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,sCAAsC,GAAG,CAAC;AAC9F,MAAM,gCAAgC,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;AAC5D;AACA,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,yBAAyB,CAAC;AAChC,EAAE,IAAI;AACN,EAAE,YAAY;AACd,EAAE,IAAI;AACN,EAAE,SAAS;AACX,EAAE,aAAa;AACf,EAAE,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE;AAClC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY;AACpC,IAAI,IAAI,CAAC,IAAI,GAAG,YAAY,CAAC,IAAI;AACjC,IAAI,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS;AAC3C,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI;AAC5B,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI;AACvC,OAAO;AACP,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC;AAChE,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AACpD,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC;AAC5D,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;AACtE,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AAC5C;AACA,EAAE,kBAAkB,CAAC,UAAU,EAAE;AACjC,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;AACtD;AACA,EAAE,YAAY,CAAC,UAAU,EAAE;AAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;AACjD;AACA,EAAE,gBAAgB,GAAG;AACrB,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,EAAE;AACxC;AACA,EAAE,qBAAqB,GAAG;AAC1B,IAAI,IAAI,CAAC,YAAY,CAAC,sBAAsB,EAAE;AAC9C;AACA,EAAE,aAAa,CAAC,CAAC,EAAE,YAAY,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACjC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,CAAC,CAAC,MAAM;AACjE,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC;AAC/C,IAAI,IAAI,gCAAgC,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE;AACnE,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB;AACA;AACA,EAAE,QAAQ,GAAG;AACb,IAAI,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE;AAC5F,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC/B,MAAM,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY;AAClD,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY;AACnD,MAAM,SAAS,EAAE;AACjB,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,YAAY;AAC1D,QAAQ,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;AAC1D,QAAQ,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa;AAC1D;AACA,KAAK,CAAC;AACN;AACA,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM;AAC7B,IAAI,OAAO,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC;AAChD,GAAG,CAAC;AACJ,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;AAC5B;AACA,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACnC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AACtC,IAAI,kBAAkB,EAAE,UAAU;AAClC,IAAI,KAAK,EAAE;AACX,MAAM,GAAG,EAAE,CAAC;AACZ,MAAM,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM;AAC9D,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM;AAC7D,MAAM,MAAM,EAAE,uCAAuC;AACrD,MAAM,iCAAiC,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE;AAC7D;AACA,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,8BAA8B,CAAC;AACrC,EAAE,cAAc;AAChB,EAAE,IAAI;AACN,EAAE,YAAY;AACd,EAAE,SAAS;AACX,EAAE,IAAI,GAAG,IAAI;AACb,EAAE,oBAAoB,GAAG,EAAE;AAC3B,EAAE,YAAY;AACd,EAAE,yBAAyB;AAC3B,EAAE,iBAAiB;AACnB,EAAE,sBAAsB;AACxB,EAAE,oBAAoB;AACtB,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;AACnG,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,WAAW,CAAC,cAAc,EAAE;AAC9B,IAAI,IAAI,CAAC,cAAc,GAAG,cAAc;AACxC,IAAI,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC,IAAI;AACnC,IAAI,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY;AACnD,IAAI,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,YAAY,CAAC,SAAS;AAC1D,IAAI,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;AAC7E,IAAI,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,cAAc,CAAC,qBAAqB;AAC9E,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa;AAC9D,IAAI,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,cAAc,CAAC,kBAAkB;AACxE,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB;AACpE,IAAI,iBAAiB,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,YAAY,CAAC;AAC/E,IAAI,iBAAiB,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC;AACrE,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AAClD;AACA,EAAE,gBAAgB,CAAC,CAAC,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;AACxC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG;AACvC,IAAI,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC9C;AACA,EAAE,aAAa,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACxB,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;AAC3B,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC;AACzC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,qBAAqB,EAAE,IAAI,IAAI;AAChF,IAAI,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB;AACpE,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,MAAM;AACjD,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,GAAG,MAAM;AACpF,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC5B;AACA,EAAE,aAAa,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC5B;AACA,EAAE,WAAW,CAAC,CAAC,EAAE;AACjB,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;AAC3B,IAAI,IAAI,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC,EAAE;AAC/C,MAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC;AAC/C;AACA,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB;AACpE,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,GAAG,EAAE;AAChF,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,UAAU,CAAC;AACpC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;AAChC,IAAI,KAAK,EAAE;AACX,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AACnC,KAAK;AACL,IAAI,CAAC,0BAA0B,GAAG,EAAE;AACpC,IAAI,aAAa,EAAE,IAAI,CAAC,aAAa;AACrC,IAAI,aAAa,EAAE,IAAI,CAAC,aAAa;AACrC,IAAI,WAAW,EAAE,IAAI,CAAC;AACtB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,wBAAwB,CAAC;AAC/B,EAAE,IAAI;AACN,EAAE,cAAc;AAChB,EAAE,KAAK;AACP,EAAE,6BAA6B;AAC/B,EAAE,kBAAkB,GAAG,WAAW;AAClC,IAAI,MAAM;AACV,MAAM,IAAI,IAAI,CAAC,6BAA6B,EAAE;AAC9C,QAAQ,IAAI,CAAC,6BAA6B,EAAE;AAC5C,QAAQ,IAAI,CAAC,6BAA6B,GAAG,MAAM;AACnD;AACA,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE,WAAW,CAAC,IAAI,EAAE,cAAc,EAAE;AACpC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,cAAc,GAAG,cAAc;AACxC,IAAI,IAAI,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI;AACpC,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,SAAS,GAAG,IAAI;AACzD,OAAO;AACP,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC;AACpE,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AAClD;AACA,EAAE,oBAAoB,CAAC,CAAC,EAAE;AAC1B,IAAI,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM;AAC1B,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,qBAAqB,EAAE;AACnD,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,SAAS,CAAC,IAAI;AACxC,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG;AACvC,IAAI,IAAI,CAAC,cAAc,CAAC,sBAAsB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACxD;AACA,EAAE,WAAW,CAAC,CAAC,EAAE;AACjB,IAAI,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAAE;AAC9C;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,YAAY,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,QAAQ,GAAG,SAAS,GAAG,QAAQ;AAClF,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,qCAAqC;AAClD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC;AAClD,KAAK;AACL,IAAI,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;AACnD,IAAI,WAAW,EAAE,IAAI,CAAC,WAAW;AACjC,IAAI,CAAC,sBAAsB,GAAG;AAC9B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,yBAAyB,CAAC;AAChC,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,MAAM,GAAG,CAAC;AACZ,EAAE,OAAO,GAAG,CAAC;AACb,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC;AAChE,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE;AAC1B;AACA,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC;AACA,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM;AACxB,MAAM,MAAM,EAAE,IAAI,CAAC,OAAO;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM;AAC9D,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM;AAC7D,MAAM,MAAM,EAAE;AACd,KAAK;AACL,IAAI,CAAC,uBAAuB,GAAG;AAC/B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,qBAAqB,GAAG,IAAI,OAAO,CAAC,iBAAiB,CAAC;AAC5D,MAAM,0BAA0B,GAAG,IAAI,OAAO,CAAC,sBAAsB,CAAC;AACtE,MAAM,iCAAiC,GAAG,IAAI,OAAO,CAAC,6BAA6B,CAAC;AACpF,MAAM,8BAA8B,GAAG,IAAI,OAAO,CAAC,0BAA0B,CAAC;AAC9E,MAAM,gCAAgC,GAAG,IAAI,OAAO,CAAC,4BAA4B,CAAC;AAClF,SAAS,iBAAiB,CAAC,KAAK,EAAE;AAClC,EAAE,OAAO,qBAAqB,CAAC,GAAG,CAAC,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC;AAClE;AACA,SAAS,qBAAqB,CAAC,KAAK,EAAE;AACtC,EAAE,OAAO,IAAI,uBAAuB,CAAC,KAAK,EAAE,qBAAqB,CAAC,GAAG,EAAE,CAAC;AACxE;AACA,SAAS,sBAAsB,CAAC,KAAK,EAAE;AACvC,EAAE,OAAO,0BAA0B,CAAC,GAAG,CAAC,IAAI,wBAAwB,CAAC,KAAK,EAAE,qBAAqB,CAAC,GAAG,EAAE,CAAC,CAAC;AACzG;AACA,SAAS,6BAA6B,GAAG;AACzC,EAAE,OAAO,iCAAiC,CAAC,GAAG,CAAC,IAAI,+BAA+B,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC,CAAC;AACrH;AACA,SAAS,0BAA0B,GAAG;AACtC,EAAE,OAAO,IAAI,4BAA4B,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC;AAC3E;AACA,SAAS,4BAA4B,GAAG;AACxC,EAAE,OAAO,IAAI,8BAA8B,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC;AAC7E;AACA,SAAS,2BAA2B,GAAG;AACvC,EAAE,OAAO,IAAI,6BAA6B,CAAC,0BAA0B,CAAC,GAAG,EAAE,CAAC;AAC5E;AACA,SAAS,uBAAuB,CAAC,KAAK,EAAE;AACxC,EAAE,OAAO,8BAA8B,CAAC,GAAG,CAAC,IAAI,yBAAyB,CAAC,KAAK,EAAE,iCAAiC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1H;AACA,SAAS,uBAAuB,CAAC,KAAK,EAAE;AACxC,EAAE,OAAO,8BAA8B,CAAC,GAAG,CAAC,IAAI,yBAAyB,CAAC,KAAK,EAAE,iCAAiC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1H;AACA,SAAS,4BAA4B,GAAG;AACxC,EAAE,OAAO,gCAAgC,CAAC,GAAG,CAAC,IAAI,8BAA8B,CAAC,8BAA8B,CAAC,GAAG,EAAE,CAAC,CAAC;AACvH;AACA,SAAS,kBAAkB,CAAC,KAAK,EAAE;AACnC,EAAE,OAAO,IAAI,wBAAwB,CAAC,KAAK,EAAE,gCAAgC,CAAC,GAAG,EAAE,CAAC;AACpF;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAE;AACpC,EAAE,OAAO,IAAI,yBAAyB,CAAC,KAAK,EAAE,qBAAqB,CAAC,GAAG,EAAE,CAAC;AAC1E;AACA,SAAS,KAAK,CAAC,KAAK,EAAE;AACtB,EAAE,OAAO,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC;AAC/C;AACA,SAAS,aAAa,CAAC,YAAY,EAAE,WAAW,EAAE;AAClD,EAAE,MAAM,KAAK,GAAG,YAAY,GAAG,WAAW;AAC1C,EAAE,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK;AACxC;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC;AAC5D,EAAE,MAAM,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU;AACpF,EAAE,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,gBAAgB,IAAI,KAAK;AACrE,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,CAAC;AAChC;AACA,SAAS,4BAA4B,CAAC;AACtC,EAAE,UAAU;AACZ,EAAE,aAAa;AACf,EAAE,KAAK;AACP,EAAE,GAAG,GAAG;AACR,CAAC,EAAE;AACH,EAAE,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC;AACzC,EAAE,MAAM,WAAW,GAAG,WAAW,GAAG,CAAC;AACrC,EAAE,MAAM,MAAM,GAAG,aAAa,IAAI,WAAW;AAC7C,EAAE,MAAM,kBAAkB,GAAG,WAAW,GAAG,MAAM;AACjD,EAAE,MAAM,aAAa,GAAG,KAAK,CAAC,SAAS,CAAC,YAAY,GAAG,MAAM;AAC7D,EAAE,MAAM,aAAa,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,kBAAkB;AAC9F,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,QAAQ;AACrD,EAAE,MAAM,WAAW,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC,CAAC;AAChF,EAAE,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,WAAW,CAAC;AAC9E,EAAE,OAAO,WAAW,CAAC,UAAU,CAAC;AAChC;AACA,SAAS,wBAAwB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,GAAG,KAAK,EAAE,EAAE;AACrE,EAAE,MAAM,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC;AACzC,EAAE,MAAM,gBAAgB,GAAG,KAAK,CAAC,SAAS,CAAC,YAAY,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU;AACpF,EAAE,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,GAAG,gBAAgB;AAC3D,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,QAAQ;AACrD,EAAE,MAAM,WAAW,GAAG,SAAS,GAAG,WAAW;AAC7C,EAAE,MAAM,gBAAgB,GAAG,GAAG,KAAK,KAAK,GAAG,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC,YAAY,GAAG,EAAE,EAAE,CAAC,CAAC;AACrF,EAAE,MAAM,qBAAqB,GAAG,KAAK,CAAC,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAC1F,EAAE,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;AACtE,EAAE,OAAO,WAAW,CAAC,qBAAqB,CAAC;AAC3C;AACA,SAAS,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE;AACpC,EAAE,OAAO,CAAC,KAAK,KAAK;AACpB,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;AAC1E,IAAI,MAAM,KAAK,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACjE,IAAI,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AACjD,GAAG;AACH;AACA,SAAS,gCAAgC,CAAC,SAAS,EAAE,YAAY,EAAE;AACnE,EAAE,OAAO,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,YAAY;AAClD;AACA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,IAAI,GAAG,OAAO;AAClB,IAAI,GAAG,GAAG,KAAK;AACf,IAAI,eAAe,GAAG,GAAG;AACzB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,iBAAiB,CAAC;AACtC,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,eAAe,CAAC;AACpD,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE;AAClD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,aAAa,GAAG,qBAAqB,CAAC;AAC9C,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC;AAChE,EAAE,MAAM,kBAAkB,GAAG,UAAU,CAAC,EAAE,EAAE,aAAa,CAAC,YAAY,CAAC;AACvE,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,kBAAkB,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAClI,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,4BAA4B,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1D,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,oBAAoB,GAAG,4BAA4B,EAAE;AAC7D,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,oBAAoB,CAAC,KAAK,CAAC;AACvE,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,uBAAuB,CAAC,SAAS,EAAE,OAAO,EAAE;AACrD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAG,OAAO;AACnD,EAAE,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE;AACnC,EAAE,MAAM,eAAe,GAAG,uBAAuB,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;AACjG,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,eAAe,CAAC,KAAK,CAAC;AAClE,EAAE,4BAA4B,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AACtE,EAAE,GAAG,EAAE;AACP;AACA,SAAS,uBAAuB,CAAC,SAAS,EAAE,OAAO,EAAE;AACrD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAG,OAAO;AACnD,EAAE,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE;AACnC,EAAE,MAAM,eAAe,GAAG,uBAAuB,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC;AACjG,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,eAAe,CAAC,KAAK,CAAC;AAClE,EAAE,4BAA4B,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AACtE,EAAE,GAAG,EAAE;AACP;AACA,SAAS,6BAA6B,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3D,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAG,OAAO;AACnD,EAAE,MAAM,qBAAqB,GAAG,6BAA6B,EAAE;AAC/D,EAAE,IAAI,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,YAAY,EAAE;AACjF,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,uBAAuB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AACjE,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,uBAAuB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AACjE;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,0BAA0B,CAAC,SAAS,EAAE,OAAO,EAAE;AACxD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,kBAAkB,GAAG,0BAA0B,EAAE;AACzD,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,kBAAkB,CAAC,KAAK,CAAC;AACrE,EAAE;AACF,IAAI,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE;AACxC,MAAM,6BAA6B,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5E,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,EAAE,YAAY,CAAC;AAC3C,MAAM;AACN,QAAQ,OAAO,EAAE,UAAU,IAAI,kBAAkB,CAAC;AAClD,OAAO;AACP,MAAM,WAAW;AACjB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;AAC7C,KAAK,CAAC,CAAC;AACP;AACA,EAAE,GAAG,EAAE;AACP;AACA,SAAS,4BAA4B,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1D,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,oBAAoB,GAAG,4BAA4B,EAAE;AAC7D,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,oBAAoB,CAAC,KAAK,CAAC;AACvE,EAAE;AACF,IAAI,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE;AACxC,MAAM,6BAA6B,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5E,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,EAAE,YAAY,CAAC;AAC3C,MAAM,WAAW;AACjB,MAAM;AACN,QAAQ,OAAO,EAAE,UAAU,IAAI,CAAC,oBAAoB,CAAC,QAAQ;AAC7D,QAAQ,QAAQ;AAChB,QAAQ,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI;AACjC;AACA,KAAK,CAAC,CAAC;AACP;AACA,EAAE,GAAG,EAAE;AACP;AACA,SAAS,2BAA2B,CAAC,SAAS,EAAE,OAAO,EAAE;AACzD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,mBAAmB,GAAG,2BAA2B,EAAE;AAC3D,EAAE,MAAM,kBAAkB,GAAG,0BAA0B,EAAE;AACzD,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,mBAAmB,CAAC,KAAK,EAAE,kBAAkB,CAAC,KAAK,EAAE;AACjG,IAAI,YAAY,EAAE,mBAAmB,CAAC,SAAS,GAAG,SAAS,GAAG;AAC9D,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,UAAU,IAAI,mBAAmB,CAAC,SAAS,IAAI,kBAAkB,CAAC,SAAS;AAC7F,EAAE;AACF,IAAI,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE;AACxC,MAAM,6BAA6B,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5E,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,EAAE,YAAY,CAAC;AAC3C,MAAM,WAAW;AACjB,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,QAAQ;AAChB,QAAQ,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI;AACjC;AACA,KAAK,CAAC,CAAC;AACP;AACA,EAAE,GAAG,EAAE;AACP;AACA,SAAS,uBAAuB,CAAC,SAAS,EAAE,OAAO,EAAE;AACrD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,WAAW;AACf,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,cAAc,GAAG,sBAAsB,CAAC;AAChD,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACpD,EAAE,IAAI,IAAI,KAAK,OAAO,EAAE;AACxB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,2BAA2B,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7E,GAAG,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,4BAA4B,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC9E,GAAG,MAAM,IAAI,IAAI,KAAK,MAAM,EAAE;AAC9B,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,0BAA0B,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC5E,GAAG,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,6BAA6B,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAC/E,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,sBAAsB,CAAC,SAAS,EAAE,OAAO,EAAE;AACpD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE;AACN,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE;AACnC,EAAE,MAAM,UAAU,GAAG,kBAAkB,CAAC;AACxC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC,OAAO;AAC7C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;AAC9F,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,cAAc,GAAG,iCAAiC,CAAC,GAAG,EAAE;AAChE,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI;AACJ,MAAM,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE,EAAE,OAAO,EAAE,EAAE;AACvD,QAAQ,sBAAsB,CAAC,UAAU,EAAE,YAAY,CAAC;AACxD,UAAU,SAAS;AACnB,UAAU;AACV,YAAY,EAAE;AACd,YAAY,OAAO,EAAE,OAAO,CAAC,OAAO;AACpC,YAAY,IAAI,GAAG,GAAG;AACtB,cAAc,OAAO,GAAG;AACxB,aAAa;AACb,YAAY,IAAI,GAAG,CAAC,OAAO,EAAE;AAC7B,cAAc,GAAG,GAAG,OAAO;AAC3B,cAAc,SAAS,GAAG,KAAK;AAC/B;AACA;AACA,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC;AAC9C,QAAQ,EAAE,OAAO,EAAE,UAAU,IAAI,cAAc,CAAC,QAAQ,EAAE;AAC1D,QAAQ,SAAS;AACjB,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;AACnD,OAAO,CAAC,CAAC;AACT;AACA;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,uBAAuB,CAAC,SAAS,EAAE,OAAO,EAAE;AACrD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE;AACN,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,WAAW,GAAG,mBAAmB,CAAC;AAC1C,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC;AAC9D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,eAAe,GAAG,qBAAqB,CAAC,GAAG,EAAE;AACrD,EAAE,MAAM,wBAAwB,GAAG,OAAO,CAAC,eAAe,CAAC,cAAc,IAAI,eAAe,CAAC,cAAc,CAAC;AAC5G,EAAE,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,wBAAwB;AAC9F,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,uBAAuB,CAAC,UAAU,EAAE,YAAY,CAAC;AACvD,QAAQ,SAAS;AACjB,QAAQ;AACR,UAAU,EAAE;AACZ,UAAU,IAAI,GAAG,GAAG;AACpB,YAAY,OAAO,GAAG;AACtB,WAAW;AACX,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE;AAC3B,YAAY,GAAG,GAAG,OAAO;AACzB,YAAY,SAAS,GAAG,KAAK;AAC7B;AACA;AACA,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAE;AACnD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,WAAW,GAAG,UAAU;AAC5B,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,uBAAuB,CAAC,UAAU,EAAE,YAAY,CAAC;AACrD,MAAM;AACN,QAAQ,WAAW,EAAE,uBAAuB;AAC5C,QAAQ,WAAW;AACnB,QAAQ,KAAK,EAAE,EAAE,CAAC,oDAAoD,EAAE,WAAW,KAAK,UAAU,IAAI,4CAA4C,EAAE,WAAW,KAAK,YAAY,IAAI,8CAA8C,EAAE,SAAS;AAC7O,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,QAAQ,GAAG,UAAU,CAAC;AAChC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC7C,UAAU,iBAAiB,CAAC,UAAU,EAAE;AACxC,YAAY,WAAW,EAAE,mBAAmB;AAC5C,YAAY,KAAK,EAAE;AACnB,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,WAAW,GAAG,UAAU;AAC5B,IAAI,iBAAiB,GAAG,EAAE;AAC1B,IAAI,iBAAiB,GAAG,EAAE;AAC1B,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,aAAa,CAAC,UAAU,EAAE,YAAY,CAAC;AAC3C,MAAM;AACN,QAAQ,WAAW,EAAE,aAAa;AAClC,QAAQ,KAAK,EAAE,EAAE,CAAC,UAAU,EAAE,SAAS;AACvC,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,UAAU,oBAAoB,CAAC,UAAU,EAAE;AAC3C,YAAY,WAAW,EAAE,sBAAsB;AAC/C,YAAY,KAAK,EAAE,4KAA4K;AAC/L,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,QAAQ,GAAG,UAAU,CAAC;AACpC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,IAAI,WAAW,KAAK,UAAU,IAAI,WAAW,KAAK,MAAM,EAAE;AACpE,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,qBAAqB,CAAC,UAAU,EAAE;AAC9C,cAAc,WAAW,EAAE,UAAU;AACrC,cAAc,KAAK,EAAE;AACrB,aAAa,CAAC;AACd,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,UAAU,IAAI,WAAW,KAAK,YAAY,IAAI,WAAW,KAAK,MAAM,EAAE;AACtE,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,qBAAqB,CAAC,UAAU,EAAE;AAC9C,cAAc,WAAW,EAAE,YAAY;AACvC,cAAc,KAAK,EAAE;AACrB,aAAa,CAAC;AACd,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC9C,UAAU,kBAAkB,CAAC,UAAU,EAAE,EAAE,CAAC;AAC5C,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}