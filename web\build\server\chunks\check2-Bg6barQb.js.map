{"version": 3, "file": "check2-Bg6barQb.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/check2.js"], "sourcesContent": ["import { w as push, Q as spread_props, y as pop } from \"./index3.js\";\nimport { I as Icon } from \"./Icon2.js\";\nfunction Check($$payload, $$props) {\n  push();\n  let { $$slots, $$events, ...props } = $$props;\n  const iconNode = [[\"path\", { \"d\": \"M20 6 9 17l-5-5\" }]];\n  Icon($$payload, spread_props([\n    { name: \"check\" },\n    props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        props.children?.($$payload2);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n  pop();\n}\nexport {\n  Check as C\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AAC/C,EAAE,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,iBAAiB,EAAE,CAAC,CAAC;AACzD,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;AACrB,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACpC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL,EAAE,GAAG,EAAE;AACP;;;;"}