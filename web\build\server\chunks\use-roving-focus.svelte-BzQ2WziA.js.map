{"version": 3, "file": "use-roving-focus.svelte-BzQ2WziA.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/use-roving-focus.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { f as ARROW_UP, a as ARROW_RIGHT, A as ARROW_LEFT, b as ARROW_DOWN, E as END, H as HOME } from \"./kbd-constants.js\";\nimport { c as isBrowser } from \"./is.js\";\nfunction getElemDirection(elem) {\n  const style = window.getComputedStyle(elem);\n  const direction = style.getPropertyValue(\"direction\");\n  return direction;\n}\nfunction getNextKey(dir = \"ltr\", orientation = \"horizontal\") {\n  return {\n    horizontal: dir === \"rtl\" ? ARROW_LEFT : ARROW_RIGHT,\n    vertical: ARROW_DOWN\n  }[orientation];\n}\nfunction getPrevKey(dir = \"ltr\", orientation = \"horizontal\") {\n  return {\n    horizontal: dir === \"rtl\" ? ARROW_RIGHT : ARROW_LEFT,\n    vertical: ARROW_UP\n  }[orientation];\n}\nfunction getDirectionalKeys(dir = \"ltr\", orientation = \"horizontal\") {\n  if (![\"ltr\", \"rtl\"].includes(dir))\n    dir = \"ltr\";\n  if (![\"horizontal\", \"vertical\"].includes(orientation))\n    orientation = \"horizontal\";\n  return {\n    nextKey: getNextKey(dir, orientation),\n    prevKey: getPrevKey(dir, orientation)\n  };\n}\nfunction useRovingFocus(props) {\n  const currentTabStopId = box(null);\n  function getCandidateNodes() {\n    if (!isBrowser) return [];\n    const node = document.getElementById(props.rootNodeId.current);\n    if (!node) return [];\n    if (props.candidateSelector) {\n      const candidates = Array.from(node.querySelectorAll(props.candidateSelector));\n      return candidates;\n    } else if (props.candidateAttr) {\n      const candidates = Array.from(node.querySelectorAll(`[${props.candidateAttr}]:not([data-disabled])`));\n      return candidates;\n    }\n    return [];\n  }\n  function focusFirstCandidate() {\n    const items = getCandidateNodes();\n    if (!items.length) return;\n    items[0]?.focus();\n  }\n  function handleKeydown(node, e, both = false) {\n    const rootNode = document.getElementById(props.rootNodeId.current);\n    if (!rootNode || !node) return;\n    const items = getCandidateNodes();\n    if (!items.length) return;\n    const currentIndex = items.indexOf(node);\n    const dir = getElemDirection(rootNode);\n    const { nextKey, prevKey } = getDirectionalKeys(dir, props.orientation.current);\n    const loop = props.loop.current;\n    const keyToIndex = {\n      [nextKey]: currentIndex + 1,\n      [prevKey]: currentIndex - 1,\n      [HOME]: 0,\n      [END]: items.length - 1\n    };\n    if (both) {\n      const altNextKey = nextKey === ARROW_DOWN ? ARROW_RIGHT : ARROW_DOWN;\n      const altPrevKey = prevKey === ARROW_UP ? ARROW_LEFT : ARROW_UP;\n      keyToIndex[altNextKey] = currentIndex + 1;\n      keyToIndex[altPrevKey] = currentIndex - 1;\n    }\n    let itemIndex = keyToIndex[e.key];\n    if (itemIndex === void 0) return;\n    e.preventDefault();\n    if (itemIndex < 0 && loop) {\n      itemIndex = items.length - 1;\n    } else if (itemIndex === items.length && loop) {\n      itemIndex = 0;\n    }\n    const itemToFocus = items[itemIndex];\n    if (!itemToFocus) return;\n    itemToFocus.focus();\n    currentTabStopId.current = itemToFocus.id;\n    props.onCandidateFocus?.(itemToFocus);\n    return itemToFocus;\n  }\n  function getTabIndex(node) {\n    const items = getCandidateNodes();\n    const anyActive = currentTabStopId.current !== null;\n    if (node && !anyActive && items[0] === node) {\n      currentTabStopId.current = node.id;\n      return 0;\n    } else if (node?.id === currentTabStopId.current) {\n      return 0;\n    }\n    return -1;\n  }\n  return {\n    setCurrentTabStopId(id) {\n      currentTabStopId.current = id;\n    },\n    getTabIndex,\n    handleKeydown,\n    focusFirstCandidate,\n    currentTabStopId\n  };\n}\nexport {\n  useRovingFocus as u\n};\n"], "names": [], "mappings": ";;;;;;AAKA,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAC7C,EAAE,MAAM,SAAS,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,CAAC;AACvD,EAAE,OAAO,SAAS;AAClB;AACA,SAAS,UAAU,CAAC,GAAG,GAAG,KAAK,EAAE,WAAW,GAAG,YAAY,EAAE;AAC7D,EAAE,OAAO;AACT,IAAI,UAAU,EAAE,GAAG,KAAK,KAAK,GAAG,UAAU,GAAG,WAAW;AACxD,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC,WAAW,CAAC;AAChB;AACA,SAAS,UAAU,CAAC,GAAG,GAAG,KAAK,EAAE,WAAW,GAAG,YAAY,EAAE;AAC7D,EAAE,OAAO;AACT,IAAI,UAAU,EAAE,GAAG,KAAK,KAAK,GAAG,WAAW,GAAG,UAAU;AACxD,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC,WAAW,CAAC;AAChB;AACA,SAAS,kBAAkB,CAAC,GAAG,GAAG,KAAK,EAAE,WAAW,GAAG,YAAY,EAAE;AACrE,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;AACnC,IAAI,GAAG,GAAG,KAAK;AACf,EAAE,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC;AACvD,IAAI,WAAW,GAAG,YAAY;AAC9B,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,UAAU,CAAC,GAAG,EAAE,WAAW,CAAC;AACzC,IAAI,OAAO,EAAE,UAAU,CAAC,GAAG,EAAE,WAAW;AACxC,GAAG;AACH;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,MAAM,gBAAgB,GAAG,GAAG,CAAC,IAAI,CAAC;AACpC,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7B,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC;AAClE,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACxB,IAAI,IAAI,KAAK,CAAC,iBAAiB,EAAE;AACjC,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AACnF,MAAM,OAAO,UAAU;AACvB,KAAK,MAAM,IAAI,KAAK,CAAC,aAAa,EAAE;AACpC,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC3G,MAAM,OAAO,UAAU;AACvB;AACA,IAAI,OAAO,EAAE;AACb;AACA,EAAE,SAAS,mBAAmB,GAAG;AACjC,IAAI,MAAM,KAAK,GAAG,iBAAiB,EAAE;AACrC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACvB,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE;AACrB;AACA,EAAE,SAAS,aAAa,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,GAAG,KAAK,EAAE;AAChD,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC;AACtE,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;AAC5B,IAAI,MAAM,KAAK,GAAG,iBAAiB,EAAE;AACrC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACvB,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5C,IAAI,MAAM,GAAG,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AAC1C,IAAI,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,kBAAkB,CAAC,GAAG,EAAE,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC;AACnF,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO;AACnC,IAAI,MAAM,UAAU,GAAG;AACvB,MAAM,CAAC,OAAO,GAAG,YAAY,GAAG,CAAC;AACjC,MAAM,CAAC,OAAO,GAAG,YAAY,GAAG,CAAC;AACjC,MAAM,CAAC,IAAI,GAAG,CAAC;AACf,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,GAAG;AAC5B,KAAK;AACL,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,MAAM,UAAU,GAAG,OAAO,KAAK,UAAU,GAAG,WAAW,GAAG,UAAU;AAC1E,MAAM,MAAM,UAAU,GAAG,OAAO,KAAK,QAAQ,GAAG,UAAU,GAAG,QAAQ;AACrE,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG,YAAY,GAAG,CAAC;AAC/C,MAAM,UAAU,CAAC,UAAU,CAAC,GAAG,YAAY,GAAG,CAAC;AAC/C;AACA,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AACrC,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE;AAC9B,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,IAAI,SAAS,GAAG,CAAC,IAAI,IAAI,EAAE;AAC/B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;AAClC,KAAK,MAAM,IAAI,SAAS,KAAK,KAAK,CAAC,MAAM,IAAI,IAAI,EAAE;AACnD,MAAM,SAAS,GAAG,CAAC;AACnB;AACA,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC;AACxC,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,IAAI,WAAW,CAAC,KAAK,EAAE;AACvB,IAAI,gBAAgB,CAAC,OAAO,GAAG,WAAW,CAAC,EAAE;AAC7C,IAAI,KAAK,CAAC,gBAAgB,GAAG,WAAW,CAAC;AACzC,IAAI,OAAO,WAAW;AACtB;AACA,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE;AAC7B,IAAI,MAAM,KAAK,GAAG,iBAAiB,EAAE;AACrC,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,KAAK,IAAI;AACvD,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;AACjD,MAAM,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE;AACxC,MAAM,OAAO,CAAC;AACd,KAAK,MAAM,IAAI,IAAI,EAAE,EAAE,KAAK,gBAAgB,CAAC,OAAO,EAAE;AACtD,MAAM,OAAO,CAAC;AACd;AACA,IAAI,OAAO,EAAE;AACb;AACA,EAAE,OAAO;AACT,IAAI,mBAAmB,CAAC,EAAE,EAAE;AAC5B,MAAM,gBAAgB,CAAC,OAAO,GAAG,EAAE;AACnC,KAAK;AACL,IAAI,WAAW;AACf,IAAI,aAAa;AACjB,IAAI,mBAAmB;AACvB,IAAI;AACJ,GAAG;AACH;;;;"}