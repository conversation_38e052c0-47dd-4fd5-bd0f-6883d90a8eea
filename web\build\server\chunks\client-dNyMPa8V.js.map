{"version": 3, "file": "client-dNyMPa8V.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/client.js"], "sourcesContent": ["import \"clsx\";\nimport \"./exports.js\";\nimport { z as noop } from \"./index3.js\";\nfunction get(key, parse = JSON.parse) {\n  try {\n    return parse(sessionStorage[key]);\n  } catch {\n  }\n}\nconst SNAPSHOT_KEY = \"sveltekit:snapshot\";\nconst SCROLL_KEY = \"sveltekit:scroll\";\nconst is_legacy = noop.toString().includes(\"$$\") || /function \\w+\\(\\) \\{\\}/.test(noop.toString());\nif (is_legacy) {\n  ({\n    data: {},\n    form: null,\n    error: null,\n    params: {},\n    route: { id: null },\n    state: {},\n    status: -1,\n    url: new URL(\"https://example.com\")\n  });\n}\nget(SCROLL_KEY) ?? {};\nget(SNAPSHOT_KEY) ?? {};\nlet app;\nfunction goto(url, opts = {}) {\n  {\n    throw new Error(\"Cannot call goto(...) on the server\");\n  }\n}\nfunction invalidateAll() {\n  {\n    throw new Error(\"Cannot call invalidateAll() on the server\");\n  }\n}\nasync function applyAction(result) {\n  {\n    throw new Error(\"Cannot call applyAction(...) on the server\");\n  }\n}\nexport {\n  app as a,\n  applyAction as b,\n  goto as g,\n  invalidateAll as i\n};\n"], "names": [], "mappings": ";;;AAGA,SAAS,GAAG,CAAC,GAAG,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE;AACtC,EAAE,IAAI;AACN,IAAI,OAAO,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACrC,GAAG,CAAC,MAAM;AACV;AACA;AACA,MAAM,YAAY,GAAG,oBAAoB;AACzC,MAAM,UAAU,GAAG,kBAAkB;AACrC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AACjG,IAAI,SAAS,EAAE;AACf,EAAE,CAAC;AACH,IAOI,GAAG,EAAE,IAAI,GAAG,CAAC,qBAAqB;AACtC,GAAG;AACH;AACA,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE;AACrB,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE;AACpB,IAAC;AACJ,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE;AAC9B,EAAE;AACF,IAAI,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC;AAC1D;AACA;AACA,SAAS,aAAa,GAAG;AACzB,EAAE;AACF,IAAI,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC;AAChE;AACA;AACA,eAAe,WAAW,CAAC,MAAM,EAAE;AACnC,EAAE;AACF,IAAI,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC;AACjE;AACA;;;;"}