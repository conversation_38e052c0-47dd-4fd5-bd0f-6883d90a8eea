{"version": 3, "file": "form-label-C5yTxnxS.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/form-label.js"], "sourcesContent": ["import { w as push, M as spread_attributes, N as bind_props, y as pop, O as copy_payload, P as assign_payload, Q as spread_props } from \"./index3.js\";\nimport { L as Label$1 } from \"./label.js\";\nimport { c as cn } from \"./utils.js\";\nimport { u as useId, d as useLabel, c as box, m as mergeProps } from \"./index15.js\";\nimport \"style-to-object\";\nfunction Label($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    ref = null,\n    children,\n    child,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const labelState = useLabel({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, labelState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<label${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></label>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Form_label($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    children,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    {\n      let child = function($$payload3, { props }) {\n        Label$1($$payload3, spread_props([\n          props,\n          {\n            \"data-slot\": \"form-label\",\n            class: cn(\"data-[fs-error]:text-destructive\", className),\n            children: ($$payload4) => {\n              children?.($$payload4);\n              $$payload4.out += `<!---->`;\n            },\n            $$slots: { default: true }\n          }\n        ]));\n      };\n      Label($$payload2, spread_props([\n        restProps,\n        {\n          get ref() {\n            return ref;\n          },\n          set ref($$value) {\n            ref = $$value;\n            $$settled = false;\n          },\n          child,\n          $$slots: { child: true }\n        }\n      ]));\n    }\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Form_label as F\n};\n"], "names": [], "mappings": ";;;;;;AAKA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC;AAC9B,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC;AAC7D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACtC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,QAAQ;AACZ,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI;AACJ,MAAM,IAAI,KAAK,GAAG,SAAS,UAAU,EAAE,EAAE,KAAK,EAAE,EAAE;AAClD,QAAQ,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC;AACzC,UAAU,KAAK;AACf,UAAU;AACV,YAAY,WAAW,EAAE,YAAY;AACrC,YAAY,KAAK,EAAE,EAAE,CAAC,kCAAkC,EAAE,SAAS,CAAC;AACpE,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,QAAQ,GAAG,UAAU,CAAC;AACpC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC;AACA,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC;AACrC,QAAQ,SAAS;AACjB,QAAQ;AACR,UAAU,IAAI,GAAG,GAAG;AACpB,YAAY,OAAO,GAAG;AACtB,WAAW;AACX,UAAU,IAAI,GAAG,CAAC,OAAO,EAAE;AAC3B,YAAY,GAAG,GAAG,OAAO;AACzB,YAAY,SAAS,GAAG,KAAK;AAC7B,WAAW;AACX,UAAU,KAAK;AACf,UAAU,OAAO,EAAE,EAAE,KAAK,EAAE,IAAI;AAChC;AACA,OAAO,CAAC,CAAC;AACT;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}