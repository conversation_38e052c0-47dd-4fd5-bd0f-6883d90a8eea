// Add Clearbit logos for companies that have domains
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";

const prisma = new PrismaClient();

/**
 * Test if a Clearbit logo exists for a domain
 */
async function testClearbitLogo(domain: string): Promise<boolean> {
  try {
    const logoUrl = `https://logo.clearbit.com/${domain}`;
    const response = await fetch(logoUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * Extract domain from website URL
 */
function extractDomain(website: string): string | null {
  try {
    const url = new URL(website.startsWith('http') ? website : `https://${website}`);
    return url.hostname.replace(/^www\./, '');
  } catch (error) {
    return null;
  }
}

async function addClearbitLogos() {
  logger.info("🎨 Adding Clearbit logos for companies");

  try {
    // Get companies without logos that have websites
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: null,
        website: { not: null }
      },
      select: {
        id: true,
        name: true,
        website: true,
        domain: true,
      },
      take: 100, // Process 100 companies
    });

    logger.info(`📊 Found ${companies.length} companies that need logos`);

    if (companies.length === 0) {
      logger.info("✅ No companies need logos");
      return;
    }

    let logosAdded = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const company of companies) {
      try {
        logger.info(`🔍 Processing: ${company.name}`);

        // Try to get domain from company.domain first, then extract from website
        let domain = company.domain;
        if (!domain && company.website) {
          domain = extractDomain(company.website);
        }

        if (!domain) {
          logger.info(`   ⏭️ No domain available for ${company.name}`);
          skippedCount++;
          continue;
        }

        logger.info(`   🌐 Testing domain: ${domain}`);

        // Test if Clearbit logo exists
        const logoExists = await testClearbitLogo(domain);

        if (logoExists) {
          const logoUrl = `https://logo.clearbit.com/${domain}`;
          
          // Update company with Clearbit logo URL
          await prisma.company.update({
            where: { id: company.id },
            data: { 
              logoUrl: logoUrl,
              domain: domain // Also update domain if it wasn't set
            },
          });

          logger.info(`   ✅ Added Clearbit logo: ${logoUrl}`);
          logosAdded++;
        } else {
          logger.info(`   ❌ No Clearbit logo available for ${domain}`);
          skippedCount++;
        }

        // Small delay to be respectful to Clearbit
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        logger.error(`❌ Error processing ${company.name}:`, error);
        errorCount++;
      }
    }

    // Summary
    logger.info("\n🎉 Clearbit logo processing completed!");
    logger.info(`   ✅ Logos added: ${logosAdded}`);
    logger.info(`   ⏭️ Skipped: ${skippedCount}`);
    logger.info(`   ❌ Errors: ${errorCount}`);
    logger.info(`   📊 Total processed: ${companies.length}`);

    if (logosAdded > 0) {
      logger.info(`\n📈 Success rate: ${((logosAdded / companies.length) * 100).toFixed(1)}%`);
      logger.info(`🔗 Example logo: https://logo.clearbit.com/${companies[0]?.domain || 'example.com'}`);
    }

    // Show some examples of companies that got logos
    if (logosAdded > 0) {
      logger.info(`\n✅ Companies with new Clearbit logos:`);
      const companiesWithLogos = await prisma.company.findMany({
        where: {
          logoUrl: { contains: 'logo.clearbit.com' }
        },
        select: { name: true, logoUrl: true },
        take: 10
      });

      companiesWithLogos.forEach(company => {
        logger.info(`   - ${company.name}: ${company.logoUrl}`);
      });
    }

  } catch (error) {
    logger.error("❌ Error adding Clearbit logos:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addClearbitLogos()
  .then(() => {
    logger.info("✅ Clearbit logo script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Clearbit logo script failed:", error);
    process.exit(1);
  });

export { addClearbitLogos };
