import { g as getRedisClient } from './redis-DxlM1ibh.js';
import 'ioredis';

async function broadcastMessage(message) {
  try {
    if (!message.timestamp) {
      message.timestamp = (/* @__PURE__ */ new Date()).toISOString();
    }
    const redis = await getRedisClient();
    if (!redis) {
      console.error("[WebSocket] Redis client not available for broadcasting");
      return;
    }
    await redis.publish("websocket::broadcast", JSON.stringify(message));
    console.log(`[WebSocket] Published message to Redis: ${message.type}`);
  } catch (error) {
    console.error("[WebSocket] Error broadcasting message:", error);
  }
}

export { broadcastMessage };
//# sourceMappingURL=websocket-DV55Ry50.js.map
