{"version": 3, "file": "upload-C2KwXIf1.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/upload.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Upload($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\n      \"path\",\n      {\n        \"d\": \"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"\n      }\n    ],\n    [\"polyline\", { \"points\": \"17 8 12 3 7 8\" }],\n    [\n      \"line\",\n      {\n        \"x1\": \"12\",\n        \"x2\": \"12\",\n        \"y1\": \"3\",\n        \"y2\": \"15\"\n      }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"upload\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Upload as U\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,GAAG,EAAE;AACb;AACA,KAAK;AACL,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC;AAC/C,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,IAAI,EAAE;AACd;AACA;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;AACtB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}