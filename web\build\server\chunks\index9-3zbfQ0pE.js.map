{"version": 3, "file": "index9-3zbfQ0pE.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/index9.js"], "sourcesContent": ["import { J as derived, M as spread_attributes, N as bind_props, y as pop, w as push, O as copy_payload, P as assign_payload, Q as spread_props } from \"./index3.js\";\nimport { S as SvelteMap, w as watch, b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { u as useId } from \"./use-id.js\";\nimport { c as cn } from \"./utils.js\";\nimport \"clsx\";\nimport { C as Context } from \"./context.js\";\nimport { S as SPACE, i as ENTER, g as getDataOrientation, e as getDataDisabled, j as getAriaOrientation, s as getDisabled, r as getAriaSelected, t as getHidden } from \"./kbd-constants.js\";\nimport { u as useRovingFocus } from \"./use-roving-focus.svelte.js\";\nimport { n as noop } from \"./noop.js\";\nconst TABS_ROOT_ATTR = \"data-tabs-root\";\nconst TABS_LIST_ATTR = \"data-tabs-list\";\nconst TABS_TRIGGER_ATTR = \"data-tabs-trigger\";\nconst TABS_CONTENT_ATTR = \"data-tabs-content\";\nclass TabsRootState {\n  opts;\n  rovingFocusGroup;\n  triggerIds = [];\n  // holds the trigger ID for each value to associate it with the content\n  valueToTriggerId = new SvelteMap();\n  // holds the content ID for each value to associate it with the trigger\n  valueToContentId = new SvelteMap();\n  constructor(opts) {\n    this.opts = opts;\n    useRefById(opts);\n    this.rovingFocusGroup = useRovingFocus({\n      candidateAttr: TABS_TRIGGER_ATTR,\n      rootNodeId: this.opts.id,\n      loop: this.opts.loop,\n      orientation: this.opts.orientation\n    });\n  }\n  registerTrigger(id, value) {\n    this.triggerIds.push(id);\n    this.valueToTriggerId.set(value, id);\n    return () => {\n      this.triggerIds = this.triggerIds.filter((triggerId) => triggerId !== id);\n      this.valueToTriggerId.delete(value);\n    };\n  }\n  registerContent(id, value) {\n    this.valueToContentId.set(value, id);\n    return () => {\n      this.valueToContentId.delete(value);\n    };\n  }\n  setValue(v) {\n    this.opts.value.current = v;\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"data-orientation\": getDataOrientation(this.opts.orientation.current),\n    [TABS_ROOT_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass TabsListState {\n  opts;\n  root;\n  #isDisabled = derived(() => this.root.opts.disabled.current);\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"tablist\",\n    \"aria-orientation\": getAriaOrientation(this.root.opts.orientation.current),\n    \"data-orientation\": getDataOrientation(this.root.opts.orientation.current),\n    [TABS_LIST_ATTR]: \"\",\n    \"data-disabled\": getDataDisabled(this.#isDisabled())\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass TabsTriggerState {\n  opts;\n  root;\n  #isActive = derived(() => this.root.opts.value.current === this.opts.value.current);\n  #isDisabled = derived(() => this.opts.disabled.current || this.root.opts.disabled.current);\n  #tabIndex = 0;\n  #ariaControls = derived(() => this.root.valueToContentId.get(this.opts.value.current));\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n    watch(\n      [\n        () => this.opts.id.current,\n        () => this.opts.value.current\n      ],\n      ([id, value]) => {\n        return this.root.registerTrigger(id, value);\n      }\n    );\n    this.onfocus = this.onfocus.bind(this);\n    this.onclick = this.onclick.bind(this);\n    this.onkeydown = this.onkeydown.bind(this);\n  }\n  #activate() {\n    if (this.root.opts.value.current === this.opts.value.current) return;\n    this.root.setValue(this.opts.value.current);\n  }\n  onfocus(_) {\n    if (this.root.opts.activationMode.current !== \"automatic\" || this.#isDisabled()) return;\n    this.#activate();\n  }\n  onclick(_) {\n    if (this.#isDisabled()) return;\n    this.#activate();\n  }\n  onkeydown(e) {\n    if (this.#isDisabled()) return;\n    if (e.key === SPACE || e.key === ENTER) {\n      e.preventDefault();\n      this.#activate();\n      return;\n    }\n    this.root.rovingFocusGroup.handleKeydown(this.opts.ref.current, e);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"tab\",\n    \"data-state\": getTabDataState(this.#isActive()),\n    \"data-value\": this.opts.value.current,\n    \"data-orientation\": getDataOrientation(this.root.opts.orientation.current),\n    \"data-disabled\": getDataDisabled(this.#isDisabled()),\n    \"aria-selected\": getAriaSelected(this.#isActive()),\n    \"aria-controls\": this.#ariaControls(),\n    [TABS_TRIGGER_ATTR]: \"\",\n    disabled: getDisabled(this.#isDisabled()),\n    tabindex: this.#tabIndex,\n    //\n    onclick: this.onclick,\n    onfocus: this.onfocus,\n    onkeydown: this.onkeydown\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass TabsContentState {\n  opts;\n  root;\n  #isActive = derived(() => this.root.opts.value.current === this.opts.value.current);\n  #ariaLabelledBy = derived(() => this.root.valueToTriggerId.get(this.opts.value.current));\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n    watch(\n      [\n        () => this.opts.id.current,\n        () => this.opts.value.current\n      ],\n      ([id, value]) => {\n        return this.root.registerContent(id, value);\n      }\n    );\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"tabpanel\",\n    hidden: getHidden(!this.#isActive()),\n    tabindex: 0,\n    \"data-value\": this.opts.value.current,\n    \"data-state\": getTabDataState(this.#isActive()),\n    \"aria-labelledby\": this.#ariaLabelledBy(),\n    [TABS_CONTENT_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst TabsRootContext = new Context(\"Tabs.Root\");\nfunction useTabsRoot(props) {\n  return TabsRootContext.set(new TabsRootState(props));\n}\nfunction useTabsTrigger(props) {\n  return new TabsTriggerState(props, TabsRootContext.get());\n}\nfunction useTabsList(props) {\n  return new TabsListState(props, TabsRootContext.get());\n}\nfunction useTabsContent(props) {\n  return new TabsContentState(props, TabsRootContext.get());\n}\nfunction getTabDataState(condition) {\n  return condition ? \"active\" : \"inactive\";\n}\nfunction Tabs($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    ref = null,\n    value = \"\",\n    onValueChange = noop,\n    orientation = \"horizontal\",\n    loop = true,\n    activationMode = \"automatic\",\n    disabled = false,\n    children,\n    child,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const rootState = useTabsRoot({\n    id: box.with(() => id),\n    value: box.with(() => value, (v) => {\n      value = v;\n      onValueChange(v);\n    }),\n    orientation: box.with(() => orientation),\n    loop: box.with(() => loop),\n    activationMode: box.with(() => activationMode),\n    disabled: box.with(() => disabled),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, rootState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref, value });\n  pop();\n}\nfunction Tabs_content$1($$payload, $$props) {\n  push();\n  let {\n    children,\n    child,\n    id = useId(),\n    ref = null,\n    value,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const contentState = useTabsContent({\n    value: box.with(() => value),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, contentState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Tabs_list$1($$payload, $$props) {\n  push();\n  let {\n    child,\n    children,\n    id = useId(),\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const listState = useTabsList({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, listState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Tabs_trigger($$payload, $$props) {\n  push();\n  let {\n    child,\n    children,\n    disabled = false,\n    id = useId(),\n    type = \"button\",\n    value,\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const triggerState = useTabsTrigger({\n    id: box.with(() => id),\n    disabled: box.with(() => disabled ?? false),\n    value: box.with(() => value),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, triggerState.props, { type });\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<button${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></button>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Tabs_content($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Tabs_content$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"tabs-content\",\n        class: cn(\"flex-1 outline-none\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Tabs_list($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Tabs_list$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"tabs-list\",\n        class: cn(\"border-border bg-muted/30 text-muted-foreground flex  w-full flex-row items-center justify-center gap-2 divide-x border-b border-t p-[4px] px-2 \", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nconst Root = Tabs;\nexport {\n  Root as R,\n  Tabs_list as T,\n  Tabs_content as a,\n  Tabs_trigger as b\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAWA,MAAM,cAAc,GAAG,gBAAgB;AACvC,MAAM,cAAc,GAAG,gBAAgB;AACvC,MAAM,iBAAiB,GAAG,mBAAmB;AAC7C,MAAM,iBAAiB,GAAG,mBAAmB;AAC7C,MAAM,aAAa,CAAC;AACpB,EAAE,IAAI;AACN,EAAE,gBAAgB;AAClB,EAAE,UAAU,GAAG,EAAE;AACjB;AACA,EAAE,gBAAgB,GAAG,IAAI,SAAS,EAAE;AACpC;AACA,EAAE,gBAAgB,GAAG,IAAI,SAAS,EAAE;AACpC,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC;AAC3C,MAAM,aAAa,EAAE,iBAAiB;AACtC,MAAM,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AAC9B,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;AAC1B,MAAM,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC;AAC7B,KAAK,CAAC;AACN;AACA,EAAE,eAAe,CAAC,EAAE,EAAE,KAAK,EAAE;AAC7B,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC;AACxC,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,KAAK,SAAS,KAAK,EAAE,CAAC;AAC/E,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC;AACzC,KAAK;AACL;AACA,EAAE,eAAe,CAAC,EAAE,EAAE,KAAK,EAAE;AAC7B,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC;AACxC,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,CAAC;AACzC,KAAK;AACL;AACA,EAAE,QAAQ,CAAC,CAAC,EAAE;AACd,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC;AAC/B;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACzE,IAAI,CAAC,cAAc,GAAG;AACtB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,aAAa,CAAC;AACpB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC9D,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC9E,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC9E,IAAI,CAAC,cAAc,GAAG,EAAE;AACxB,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE;AACvD,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACrF,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC5F,EAAE,SAAS,GAAG,CAAC;AACf,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACxF,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,KAAK;AACT,MAAM;AACN,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAClC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,OAAO;AACP,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK;AACvB,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC;AACnD;AACA,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C;AACA,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;AAClE,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAC/C;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK,WAAW,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AACrF,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AAC5B,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AAC5B,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AAC5C,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,MAAM;AACN;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AACtE;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,YAAY,EAAE,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AACnD,IAAI,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACzC,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC9E,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AACxD,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AACtD,IAAI,eAAe,EAAE,IAAI,CAAC,aAAa,EAAE;AACzC,IAAI,CAAC,iBAAiB,GAAG,EAAE;AAC3B,IAAI,QAAQ,EAAE,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AAC7C,IAAI,QAAQ,EAAE,IAAI,CAAC,SAAS;AAC5B;AACA,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,SAAS,EAAE,IAAI,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACrF,EAAE,eAAe,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC1F,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,KAAK;AACT,MAAM;AACN,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAClC,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAC9B,OAAO;AACP,MAAM,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,KAAK;AACvB,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,KAAK,CAAC;AACnD;AACA,KAAK;AACL;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,MAAM,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AACxC,IAAI,QAAQ,EAAE,CAAC;AACf,IAAI,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACzC,IAAI,YAAY,EAAE,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;AACnD,IAAI,iBAAiB,EAAE,IAAI,CAAC,eAAe,EAAE;AAC7C,IAAI,CAAC,iBAAiB,GAAG;AACzB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,eAAe,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC;AAChD,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;AACtD;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC;AAC3D;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,OAAO,IAAI,aAAa,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC;AACxD;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC;AAC3D;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,GAAG,QAAQ,GAAG,UAAU;AAC1C;AACA,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,WAAW,GAAG,YAAY;AAC9B,IAAI,IAAI,GAAG,IAAI;AACf,IAAI,cAAc,GAAG,WAAW;AAChC,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC;AAChC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,KAAK;AACxC,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,aAAa,CAAC,CAAC,CAAC;AACtB,KAAK,CAAC;AACN,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9B,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,CAAC;AAClD,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,cAAc,CAAC;AACtC,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC;AAC/D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC;AAChC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,IAAI,GAAG,QAAQ;AACnB,IAAI,KAAK;AACT,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,cAAc,CAAC;AACtC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,CAAC;AAC/C,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC;AACzE,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACvC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC;AAC5C,MAAM;AACN,QAAQ,WAAW,EAAE,cAAc;AACnC,QAAQ,KAAK,EAAE,EAAE,CAAC,qBAAqB,EAAE,SAAS;AAClD,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,WAAW,CAAC,UAAU,EAAE,YAAY,CAAC;AACzC,MAAM;AACN,QAAQ,WAAW,EAAE,WAAW;AAChC,QAAQ,KAAK,EAAE,EAAE,CAAC,kJAAkJ,EAAE,SAAS;AAC/K,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACK,MAAC,IAAI,GAAG;;;;"}