{"version": 3, "file": "is-mzPc4wSG.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/is.js"], "sourcesContent": ["const isBrowser = typeof document !== \"undefined\";\nconst isIOS = getIsIOS();\nfunction getIsIOS() {\n  return isBrowser && window?.navigator?.userAgent && (/iP(ad|hone|od)/.test(window.navigator.userAgent) || // The new iPad Pro Gen3 does not identify itself as iPad, but as Macintosh.\n  window?.navigator?.maxTouchPoints > 2 && /iPad|Macintosh/.test(window?.navigator.userAgent));\n}\nfunction isHTMLElement(element) {\n  return element instanceof HTMLElement;\n}\nfunction isElement(element) {\n  return element instanceof Element;\n}\nfunction isElementOrSVGElement(element) {\n  return element instanceof Element || element instanceof SVGElement;\n}\nfunction isFocusVisible(element) {\n  return element.matches(\":focus-visible\");\n}\nfunction isNotNull(value) {\n  return value !== null;\n}\nfunction isSelectableInput(element) {\n  return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction isElementHidden(node, stopAt) {\n  if (getComputedStyle(node).visibility === \"hidden\")\n    return true;\n  while (node) {\n    if (stopAt !== void 0 && node === stopAt)\n      return false;\n    if (getComputedStyle(node).display === \"none\")\n      return true;\n    node = node.parentElement;\n  }\n  return false;\n}\nexport {\n  isHTMLElement as a,\n  isElementOrSVGElement as b,\n  isBrowser as c,\n  isSelectableInput as d,\n  isElementHidden as e,\n  isNotNull as f,\n  isFocusVisible as g,\n  isIOS as h,\n  isElement as i\n};\n"], "names": [], "mappings": "AAAK,MAAC,SAAS,GAAG,OAAO,QAAQ,KAAK;AACjC,MAAC,KAAK,GAAG,QAAQ;AACtB,SAAS,QAAQ,GAAG;AACpB,EAAE,OAAO,SAAS,IAAI,MAAM,EAAE,SAAS,EAAE,SAAS,KAAK,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;AACxG,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,GAAG,CAAC,IAAI,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;AAC9F;AACA,SAAS,aAAa,CAAC,OAAO,EAAE;AAChC,EAAE,OAAO,OAAO,YAAY,WAAW;AACvC;AACA,SAAS,SAAS,CAAC,OAAO,EAAE;AAC5B,EAAE,OAAO,OAAO,YAAY,OAAO;AACnC;AACA,SAAS,qBAAqB,CAAC,OAAO,EAAE;AACxC,EAAE,OAAO,OAAO,YAAY,OAAO,IAAI,OAAO,YAAY,UAAU;AACpE;AACA,SAAS,cAAc,CAAC,OAAO,EAAE;AACjC,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC;AAC1C;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,OAAO,KAAK,KAAK,IAAI;AACvB;AACA,SAAS,iBAAiB,CAAC,OAAO,EAAE;AACpC,EAAE,OAAO,OAAO,YAAY,gBAAgB,IAAI,QAAQ,IAAI,OAAO;AACnE;AACA,SAAS,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE;AACvC,EAAE,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,UAAU,KAAK,QAAQ;AACpD,IAAI,OAAO,IAAI;AACf,EAAE,OAAO,IAAI,EAAE;AACf,IAAI,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,KAAK,MAAM;AAC5C,MAAM,OAAO,KAAK;AAClB,IAAI,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,MAAM;AACjD,MAAM,OAAO,IAAI;AACjB,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa;AAC7B;AACA,EAAE,OAAO,KAAK;AACd;;;;"}