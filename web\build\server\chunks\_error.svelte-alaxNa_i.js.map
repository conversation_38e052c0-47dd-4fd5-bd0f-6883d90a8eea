{"version": 3, "file": "_error.svelte-alaxNa_i.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/_error.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"../../chunks/index3.js\";\nimport { g as goto } from \"../../chunks/client.js\";\nimport { S as SEO } from \"../../chunks/SEO.js\";\nimport { B as But<PERSON> } from \"../../chunks/button.js\";\nfunction _error($$payload, $$props) {\n  push();\n  function handleHome() {\n    goto();\n  }\n  SEO($$payload, {\n    title: \"404 - Page Not Found | Hirli\",\n    description: \"The page you are looking for does not exist. You may have mistyped the address or the page may have moved.\",\n    keywords: \"404, page not found, error, Hirli\"\n  });\n  $$payload.out += `<!----> <section class=\"flex h-screen flex-col items-center justify-center\"><div class=\"relative\"><div class=\"animate-pulse text-9xl font-bold\">404</div></div> <p class=\"mt-4 text-xl\">Looks like you’re lost in space…</p> `;\n  Button($$payload, {\n    onclick: handleHome,\n    class: \"border-border mt-6 inline-flex h-10 items-center justify-center rounded-full border px-6 py-3 text-sm font-semibold \",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Take me home`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></section>`;\n  pop();\n}\nexport {\n  _error as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAKA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAI,EAAE;AACV;AACA,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,8BAA8B;AACzC,IAAI,WAAW,EAAE,4GAA4G;AAC7H,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,6NAA6N,CAAC;AAClP,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,UAAU;AACvB,IAAI,KAAK,EAAE,sHAAsH;AACjI,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC7C,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACtC,EAAE,GAAG,EAAE;AACP;;;;"}