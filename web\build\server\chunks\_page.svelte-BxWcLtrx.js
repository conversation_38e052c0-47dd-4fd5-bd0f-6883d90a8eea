import { p as push, M as ensure_array_like, O as escape_html, Q as bind_props, q as pop, N as attr } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { B as Button } from './button-CrucCo1G.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { S as Search } from './search-B0oHlTPS.js';
import { C as Calculator } from './calculator-gH1suEbI.js';
import { B as Briefcase } from './briefcase-DBFF7i-g.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let filteredResources;
  let data = $$props["data"];
  const categories = [
    "All",
    "Templates",
    "Tools",
    "Guides",
    "Reports"
  ];
  const resources = data.resources || [];
  const featuredResources = data.featuredResources || resources.filter((resource) => resource.featured);
  let selectedCategory = "All";
  function getIconComponent(iconName) {
    if (typeof iconName !== "string") return iconName;
    switch (iconName) {
      case "FileText":
        return File_text;
      case "Briefcase":
        return Briefcase;
      case "Calculator":
        return Calculator;
      case "Search":
        return Search;
      default:
        return File_text;
    }
  }
  function getResourceUrl(resource) {
    if (resource.slug && resource.slug.current) {
      return `/resources/${resource.slug.current}`;
    }
    return resource.link || "#";
  }
  filteredResources = resources.filter((resource) => {
    resource.resourceType || resource.type;
    const matchesCategory = selectedCategory === "All";
    return matchesCategory;
  });
  const each_array = ensure_array_like(categories);
  SEO($$payload, {
    title: "Career Resources | Hirli",
    description: "Free tools, templates, and guides to help you succeed in your job search and career development.",
    keywords: "resume templates, cover letter templates, ATS optimization, interview preparation, salary calculator, career planning, job market research"
  });
  $$payload.out += `<!----> <div class="container mx-auto px-4 py-16"><div><div class="mb-12 text-center"><h1 class="mb-4 text-4xl font-bold">Career Resources</h1> <p class="mx-auto max-w-2xl text-lg text-gray-600">Free tools, templates, and guides to help you succeed in your job search and career
        development.</p></div> <div class="mb-12"><div class="flex flex-wrap gap-2"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let category = each_array[$$index];
    Button($$payload, {
      variant: selectedCategory === category ? "default" : "outline",
      size: "sm",
      children: ($$payload2) => {
        $$payload2.out += `<!---->${escape_html(category)}`;
      },
      $$slots: { default: true }
    });
  }
  $$payload.out += `<!--]--></div></div> `;
  if (featuredResources.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_1 = ensure_array_like(featuredResources);
    $$payload.out += `<div class="mb-16"><h2 class="mb-6 text-2xl font-semibold">Featured Resources</h2> <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let resource = each_array_1[$$index_1];
      Card($$payload, {
        class: "flex h-full flex-col overflow-hidden border shadow-md",
        children: ($$payload2) => {
          Card_header($$payload2, {
            class: "bg-primary/5 flex items-center gap-3 p-6",
            children: ($$payload3) => {
              $$payload3.out += `<div class="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full"><!---->`;
              getIconComponent(resource.icon)?.($$payload3, { class: "h-5 w-5" });
              $$payload3.out += `<!----></div> <h3 class="text-xl font-semibold">${escape_html(resource.title)}</h3>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!----> `;
          Card_content($$payload2, {
            class: "flex flex-1 flex-col p-6",
            children: ($$payload3) => {
              $$payload3.out += `<p class="mb-4 flex-1 text-gray-600">${escape_html(resource.description)}</p> <div class="mt-auto flex items-center justify-between"><span class="text-sm capitalize text-gray-500">${escape_html(resource.resourceType || resource.type || "Resource")}</span> <a${attr("href", getResourceUrl(resource))} class="text-primary inline-flex items-center text-sm font-medium hover:underline">Access Resource `;
              Arrow_right($$payload3, { class: "ml-1 h-3 w-3" });
              $$payload3.out += `<!----></a></div>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!---->`;
        },
        $$slots: { default: true }
      });
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div><h2 class="mb-6 text-2xl font-semibold">${escape_html("All Resources")}</h2> `;
  if (filteredResources.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_2 = ensure_array_like(filteredResources);
    $$payload.out += `<div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
    for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
      let resource = each_array_2[$$index_2];
      Card($$payload, {
        class: "flex h-full flex-col overflow-hidden border shadow-md",
        children: ($$payload2) => {
          Card_header($$payload2, {
            class: "bg-primary/5 flex items-center gap-3 p-6",
            children: ($$payload3) => {
              $$payload3.out += `<div class="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full"><!---->`;
              getIconComponent(resource.icon)?.($$payload3, { class: "h-5 w-5" });
              $$payload3.out += `<!----></div> <h3 class="text-xl font-semibold">${escape_html(resource.title)}</h3>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!----> `;
          Card_content($$payload2, {
            class: "flex flex-1 flex-col p-6",
            children: ($$payload3) => {
              $$payload3.out += `<p class="mb-4 flex-1 text-gray-600">${escape_html(resource.description)}</p> <div class="mt-auto flex items-center justify-between"><span class="text-sm capitalize text-gray-500">${escape_html(resource.resourceType || resource.type || "Resource")}</span> <a${attr("href", getResourceUrl(resource))} class="text-primary inline-flex items-center text-sm font-medium hover:underline">Access Resource `;
              Arrow_right($$payload3, { class: "ml-1 h-3 w-3" });
              $$payload3.out += `<!----></a></div>`;
            },
            $$slots: { default: true }
          });
          $$payload2.out += `<!---->`;
        },
        $$slots: { default: true }
      });
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="py-8 text-center"><p class="text-gray-500">No resources found for this category.</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BxWcLtrx.js.map
