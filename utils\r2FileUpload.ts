// utils/r2FileUpload.ts
// Reusable R2 file upload utility for Cloudflare R2 storage operations

import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  CopyObjectCommand,
  HeadBucketCommand,
  CreateBucketCommand,
  PutBucketCorsCommand,
  GetBucketCorsCommand,
} from "@aws-sdk/client-s3";
import path from "path";
import dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), ".env") });

// R2 Configuration
const R2_CONFIG = {
  endpoint:
    process.env.R2_ENDPOINT ||
    "https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com",
  region: "auto", // R2 uses 'auto' as region
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID ?? "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY ?? "",
  },
};

// Initialize R2 client
const r2Client = new S3Client(R2_CONFIG);

// Fixed bucket names for different purposes
export enum BucketType {
  COMPANY = "company",
  RESUMES = "resumes",
  USER = "user",
  JOBS = "jobs",
  ANALYTICS = "analytics",
}

// Fixed bucket configurations - no environment overrides for consistency
const BUCKET_CONFIGS = {
  [BucketType.COMPANY]: {
    name: "hirli-company-logos",
    description: "Company logos, data, and assets",
  },
  [BucketType.RESUMES]: {
    name: "hirli-resume-files",
    description: "User resumes and documents",
  },
  [BucketType.USER]: {
    name: "hirli-user-images",
    description: "User profile pictures and personal files",
  },
  [BucketType.JOBS]: {
    name: "hirli-job-files",
    description: "Job-related files, screenshots, and assets",
  },
  [BucketType.ANALYTICS]: {
    name: "hirli-analytics",
    description: "Analytics reports, exports, and data",
  },
};

// File type configurations with bucket mapping
const FILE_CONFIGS = {
  companyLogos: {
    bucket: BucketType.COMPANY,
    folder: "logos",
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/webp",
      "image/svg+xml",
    ],
  },
  companyData: {
    bucket: BucketType.COMPANY,
    folder: "data",
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ["application/json", "text/csv", "application/pdf"],
  },
  resumes: {
    bucket: BucketType.RESUMES,
    folder: "", // Store in root of bucket, same as company logos
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
  },
  profilePictures: {
    bucket: BucketType.USER,
    folder: "avatars",
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ["image/png", "image/jpeg", "image/jpg", "image/webp"],
  },
  userDocuments: {
    bucket: BucketType.USER,
    folder: "documents",
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ],
  },
  jobScreenshots: {
    bucket: BucketType.JOBS,
    folder: "screenshots",
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ["image/png", "image/jpeg", "image/jpg", "image/webp"],
  },
  jobAssets: {
    bucket: BucketType.JOBS,
    folder: "assets",
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: [
      "image/png",
      "image/jpeg",
      "image/jpg",
      "image/webp",
      "application/pdf",
      "text/html",
      "application/json",
    ],
  },
};

export type FileType = keyof typeof FILE_CONFIGS;

export interface UploadResult {
  success: boolean;
  fileKey?: string;
  publicUrl?: string;
  error?: string;
  fileSize?: number;
  contentType?: string;
  bucketName?: string;
}

export interface FileMetadata {
  originalName: string;
  contentType: string;
  size: number;
  uploadedAt: Date;
  folder: string;
}

export interface DownloadResult {
  success: boolean;
  buffer?: Buffer;
  error?: string;
  contentType?: string;
  fileSize?: number;
}

export interface DeleteResult {
  success: boolean;
  error?: string;
}

/**
 * Get bucket name for a specific bucket type
 */
export function getBucketName(bucketType: BucketType): string {
  return BUCKET_CONFIGS[bucketType].name;
}

/**
 * Get file configuration for a specific file type
 */
export function getFileConfig(fileType: FileType) {
  return FILE_CONFIGS[fileType];
}

/**
 * Get all available file types
 */
export function getAvailableFileTypes(): FileType[] {
  return Object.keys(FILE_CONFIGS) as FileType[];
}

/**
 * Get all available bucket types
 */
export function getAvailableBucketTypes(): BucketType[] {
  return Object.values(BucketType);
}

/**
 * Ensure bucket exists, create if it doesn't
 */
export async function ensureBucketExists(
  bucketType: BucketType
): Promise<boolean> {
  const bucketName = getBucketName(bucketType);

  try {
    // Check if bucket exists
    await r2Client.send(new HeadBucketCommand({ Bucket: bucketName }));
    console.log(`✅ Bucket '${bucketName}' exists and is accessible`);
    return true;
  } catch (error: any) {
    if (error.name === "NotFound" || error.$metadata?.httpStatusCode === 404) {
      // Bucket doesn't exist, try to create it
      try {
        await r2Client.send(new CreateBucketCommand({ Bucket: bucketName }));
        console.log(`✅ Bucket '${bucketName}' created successfully`);
        return true;
      } catch (createError: any) {
        if (
          createError.name === "BucketAlreadyExists" ||
          createError.name === "BucketAlreadyOwnedByYou"
        ) {
          console.log(`✅ Bucket '${bucketName}' already exists`);
          return true;
        } else {
          console.error(
            `❌ Failed to create bucket '${bucketName}':`,
            createError
          );
          return false;
        }
      }
    } else {
      console.error(`❌ Error checking bucket '${bucketName}':`, error);
      return false;
    }
  }
}

/**
 * Initialize all buckets
 */
export async function initializeAllBuckets(): Promise<{
  success: boolean;
  results: Record<BucketType, boolean>;
}> {
  console.log("🏗️ Initializing all R2 buckets...");

  const results: Record<BucketType, boolean> = {} as Record<
    BucketType,
    boolean
  >;
  let allSuccess = true;

  for (const bucketType of Object.values(BucketType)) {
    const success = await ensureBucketExists(bucketType);
    results[bucketType] = success;
    if (!success) allSuccess = false;
  }

  if (allSuccess) {
    console.log("✅ All buckets initialized successfully");
  } else {
    console.warn("⚠️ Some buckets failed to initialize");
  }

  return { success: allSuccess, results };
}

/**
 * Configure CORS policy for a bucket to allow web access
 */
export async function configureBucketCors(bucketType: BucketType): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    const bucketName = getBucketName(bucketType);

    console.log(`🔧 Configuring CORS for bucket: ${bucketName}`);

    const corsConfiguration = {
      CORSRules: [
        {
          ID: "AllowWebAccess",
          AllowedHeaders: ["*"],
          AllowedMethods: ["GET", "HEAD"],
          AllowedOrigins: ["*"], // Allow all origins for public assets
          ExposeHeaders: ["ETag"],
          MaxAgeSeconds: 3600,
        },
      ],
    };

    const corsCommand = new PutBucketCorsCommand({
      Bucket: bucketName,
      CORSConfiguration: corsConfiguration,
    });

    await r2Client.send(corsCommand);

    console.log(`✅ CORS configured successfully for bucket: ${bucketName}`);
    return { success: true };
  } catch (error: any) {
    console.error(`❌ Failed to configure CORS for bucket:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown CORS error",
    };
  }
}

/**
 * Configure CORS for all buckets
 */
export async function configureAllBucketsCors(): Promise<{
  success: boolean;
  results: Record<BucketType, boolean>;
}> {
  console.log("🔧 Configuring CORS for all R2 buckets...");

  const results: Record<BucketType, boolean> = {} as Record<
    BucketType,
    boolean
  >;
  let allSuccess = true;

  for (const bucketType of Object.values(BucketType)) {
    const result = await configureBucketCors(bucketType);
    results[bucketType] = result.success;
    if (!result.success) allSuccess = false;
  }

  if (allSuccess) {
    console.log("✅ CORS configured successfully for all buckets");
  } else {
    console.warn("⚠️ Some buckets failed CORS configuration");
  }

  return { success: allSuccess, results };
}

/**
 * Generate a unique file key for storage
 */
function generateFileKey(
  fileType: FileType,
  originalName: string,
  identifier?: string
): string {
  const extension = path.extname(originalName).toLowerCase();
  const config = FILE_CONFIGS[fileType];

  let uniqueFileName: string;

  if (fileType === "resumes" && identifier) {
    // For resumes, use profile-id or user-id as identifier
    // Format: resume-{identifier}.pdf (will be updated with resume-id after creation)
    uniqueFileName = `resume-${identifier}${extension}`;
  } else if (fileType === "companyLogos" && identifier) {
    // For company logos, use company-id
    const baseName = path
      .basename(originalName, extension)
      .replace(/[^a-zA-Z0-9-_]/g, "-")
      .toLowerCase()
      .substring(0, 30);
    uniqueFileName = `${baseName}-${identifier}${extension}`;
  } else {
    // For other file types, use original name with timestamp
    const baseName = path
      .basename(originalName, extension)
      .replace(/[^a-zA-Z0-9-_]/g, "-")
      .toLowerCase()
      .substring(0, 30);
    const timestamp = Date.now();
    uniqueFileName = `${baseName}-${timestamp}${extension}`;
  }

  // Handle folder structure based on file type
  if (config.folder) {
    // Files with folders: folder/filename
    return `${config.folder}/${uniqueFileName}`;
  } else {
    // Files without folders (like company logos and resumes): just filename in root
    return uniqueFileName;
  }
}

/**
 * Get public URL for a file - Use worker URL for consistent access
 */
export function getPublicUrl(fileKey: string, bucketName?: string): string {
  const customDomain = process.env.R2_CUSTOM_DOMAIN;
  if (customDomain) {
    return `https://${customDomain}/${fileKey}`;
  }

  // Use Cloudflare Worker URL for consistent access without hardcoded account IDs
  const workerUrl =
    process.env.R2_WORKER_URL ||
    "https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev";

  // Determine the path prefix based on bucket type
  let pathPrefix = "";
  if (bucketName === "hirli-company-logos" || fileKey.includes("logo")) {
    pathPrefix = "logos/";
  } else if (bucketName === "hirli-resume-files") {
    pathPrefix = "resumes/";
  } else if (bucketName === "hirli-user-images") {
    pathPrefix = "user/";
  }

  return `${workerUrl}/${pathPrefix}${fileKey}`;
}

/**
 * Validate file before upload
 */
function validateFile(
  buffer: Buffer,
  contentType: string,
  fileType: FileType
): { valid: boolean; error?: string } {
  const config = FILE_CONFIGS[fileType];

  // Check file size
  if (buffer.length > config.maxSize) {
    return {
      valid: false,
      error: `File size ${buffer.length} exceeds maximum ${config.maxSize} bytes for ${fileType}`,
    };
  }

  // Check content type
  if (!config.allowedTypes.includes(contentType)) {
    return {
      valid: false,
      error: `Content type ${contentType} not allowed for ${fileType}. Allowed: ${config.allowedTypes.join(
        ", "
      )}`,
    };
  }

  return { valid: true };
}

/**
 * Upload a file to R2 storage
 */
export async function uploadFile(
  buffer: Buffer,
  originalName: string,
  contentType: string,
  fileType: FileType,
  userId?: string
): Promise<UploadResult> {
  try {
    // Validate file
    const validation = validateFile(buffer, contentType, fileType);
    if (!validation.valid) {
      return {
        success: false,
        error: validation.error,
      };
    }

    const config = FILE_CONFIGS[fileType];
    const bucketName = getBucketName(config.bucket);

    // Skip bucket check for now - proceed directly to upload
    // Note: Bucket existence check requires additional permissions
    console.log(`📤 Uploading to bucket: ${bucketName}`);

    // Generate unique file key
    const fileKey = generateFileKey(fileType, originalName, userId);

    // Prepare upload command
    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
      Body: buffer,
      ContentType: contentType,
      Metadata: {
        originalName,
        uploadedAt: new Date().toISOString(),
        fileType,
        bucketType: config.bucket,
        ...(userId && { userId }),
      },
    });

    // Upload to R2
    await r2Client.send(uploadCommand);

    const publicUrl = getPublicUrl(fileKey, bucketName);

    console.log(
      `✅ File uploaded successfully to ${bucketName}: ${fileKey} (${buffer.length} bytes)`
    );

    return {
      success: true,
      fileKey,
      publicUrl,
      fileSize: buffer.length,
      contentType,
      bucketName,
    };
  } catch (error) {
    console.error(`❌ Failed to upload file ${originalName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown upload error",
    };
  }
}

/**
 * Upload a file directly to a specific bucket (bypassing file type validation)
 */
export async function uploadFileToCustomBucket(
  buffer: Buffer,
  fileKey: string,
  contentType: string,
  bucketType: BucketType,
  metadata?: Record<string, string>
): Promise<UploadResult> {
  try {
    const bucketName = getBucketName(bucketType);

    console.log(
      `📤 Uploading to custom bucket: ${bucketName} with key: ${fileKey}`
    );

    // Prepare upload command
    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
      Body: buffer,
      ContentType: contentType,
      Metadata: {
        uploadedAt: new Date().toISOString(),
        bucketType,
        ...metadata,
      },
    });

    // Upload to R2
    await r2Client.send(uploadCommand);

    const publicUrl = getPublicUrl(fileKey, bucketName);

    console.log(
      `✅ File uploaded successfully to ${bucketName}: ${fileKey} (${buffer.length} bytes)`
    );

    return {
      success: true,
      fileKey,
      publicUrl,
      fileSize: buffer.length,
      contentType,
      bucketName,
    };
  } catch (error) {
    console.error(`❌ Failed to upload file to custom bucket:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown upload error",
    };
  }
}

/**
 * Download a file from R2 storage
 */
export async function downloadFile(
  fileKey: string,
  bucketType?: BucketType
): Promise<DownloadResult> {
  try {
    // If bucket type is provided, use it; otherwise use company bucket as default
    const bucketName = bucketType
      ? getBucketName(bucketType)
      : getBucketName(BucketType.COMPANY);

    const downloadCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    const response = await r2Client.send(downloadCommand);

    if (!response.Body) {
      return {
        success: false,
        error: "No file content received",
      };
    }

    // Convert stream to buffer
    const chunks: Uint8Array[] = [];
    const reader = response.Body.transformToWebStream().getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }

    const buffer = Buffer.concat(chunks);

    console.log(
      `✅ File downloaded successfully from ${bucketName}: ${fileKey} (${buffer.length} bytes)`
    );

    return {
      success: true,
      buffer,
      contentType: response.ContentType,
      fileSize: buffer.length,
    };
  } catch (error) {
    console.error(`❌ Failed to download file ${fileKey}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown download error",
    };
  }
}

/**
 * Delete a file from R2 storage
 */
export async function deleteFile(
  fileKey: string,
  bucketType?: BucketType
): Promise<DeleteResult> {
  try {
    // If bucket type is provided, use it; otherwise use company bucket as default
    const bucketName = bucketType
      ? getBucketName(bucketType)
      : getBucketName(BucketType.COMPANY);

    const deleteCommand = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    await r2Client.send(deleteCommand);

    console.log(`✅ File deleted successfully from ${bucketName}: ${fileKey}`);

    return { success: true };
  } catch (error) {
    console.error(`❌ Failed to delete file ${fileKey}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown delete error",
    };
  }
}

/**
 * Update file key with resume ID after resume creation
 */
export async function updateResumeFileKey(
  oldFileKey: string,
  resumeId: string,
  bucketType: BucketType = BucketType.RESUMES
): Promise<{
  success: boolean;
  newFileKey?: string;
  newPublicUrl?: string;
  error?: string;
}> {
  try {
    const extension = path.extname(oldFileKey);
    const newFileKey = `resume-${resumeId}${extension}`;
    const bucketName = getBucketName(bucketType);

    console.log(
      `🔄 Updating resume file key from ${oldFileKey} to ${newFileKey}`
    );

    // Copy object to new key
    const copyCommand = new CopyObjectCommand({
      Bucket: bucketName,
      CopySource: `${bucketName}/${oldFileKey}`,
      Key: newFileKey,
      MetadataDirective: "COPY",
    });

    await r2Client.send(copyCommand);

    // Delete old object
    const deleteCommand = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: oldFileKey,
    });

    await r2Client.send(deleteCommand);

    const newPublicUrl = getPublicUrl(newFileKey, bucketName);

    console.log(`✅ Resume file key updated successfully: ${newFileKey}`);

    return {
      success: true,
      newFileKey,
      newPublicUrl,
    };
  } catch (error) {
    console.error(`❌ Failed to update resume file key:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Download image from URL and upload to R2
 */
export async function downloadAndUploadImage(
  imageUrl: string,
  fileName: string,
  fileType: FileType,
  userId?: string
): Promise<UploadResult> {
  try {
    console.log(`📥 Downloading image from: ${imageUrl}`);

    // Download image
    const response = await fetch(imageUrl, {
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    });

    if (!response.ok) {
      return {
        success: false,
        error: `Failed to download image: ${response.status} ${response.statusText}`,
      };
    }

    const buffer = Buffer.from(await response.arrayBuffer());
    const contentType = response.headers.get("content-type") || "image/png";

    // Upload to R2
    return await uploadFile(buffer, fileName, contentType, fileType, userId);
  } catch (error) {
    console.error(`❌ Failed to download and upload image ${imageUrl}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Copy a file from one location to another within R2
 */
export async function copyFile(
  sourceFileKey: string,
  destinationFileKey: string,
  sourceBucketType: BucketType,
  destinationBucketType?: BucketType
): Promise<UploadResult> {
  try {
    const sourceBucketName = getBucketName(sourceBucketType);
    const destinationBucketName = getBucketName(
      destinationBucketType || sourceBucketType
    );

    console.log(
      `📋 Copying file from ${sourceBucketName}/${sourceFileKey} to ${destinationBucketName}/${destinationFileKey}`
    );

    // Copy object to new location
    const copyCommand = new CopyObjectCommand({
      Bucket: destinationBucketName,
      CopySource: `${sourceBucketName}/${sourceFileKey}`,
      Key: destinationFileKey,
      MetadataDirective: "COPY",
    });

    await r2Client.send(copyCommand);

    const publicUrl = getPublicUrl(destinationFileKey, destinationBucketName);

    console.log(`✅ File copied successfully: ${destinationFileKey}`);

    return {
      success: true,
      fileKey: destinationFileKey,
      publicUrl,
      bucketName: destinationBucketName,
    };
  } catch (error) {
    console.error(`❌ Failed to copy file:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown copy error",
    };
  }
}

/**
 * Move a file from one location to another within R2 (copy + delete)
 */
export async function moveFile(
  sourceFileKey: string,
  destinationFileKey: string,
  sourceBucketType: BucketType,
  destinationBucketType?: BucketType
): Promise<UploadResult> {
  try {
    // First copy the file
    const copyResult = await copyFile(
      sourceFileKey,
      destinationFileKey,
      sourceBucketType,
      destinationBucketType
    );

    if (!copyResult.success) {
      return copyResult;
    }

    // Then delete the original
    const deleteResult = await deleteFile(sourceFileKey, sourceBucketType);

    if (!deleteResult.success) {
      console.warn(
        `⚠️ File copied but failed to delete original: ${deleteResult.error}`
      );
    }

    console.log(`✅ File moved successfully: ${destinationFileKey}`);

    return copyResult;
  } catch (error) {
    console.error(`❌ Failed to move file:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown move error",
    };
  }
}

/**
 * Check if a file exists in R2 storage
 */
export async function fileExists(
  fileKey: string,
  bucketType: BucketType
): Promise<{ exists: boolean; error?: string }> {
  try {
    const bucketName = getBucketName(bucketType);

    const headCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    await r2Client.send(headCommand);

    return { exists: true };
  } catch (error: any) {
    if (error.name === "NoSuchKey" || error.$metadata?.httpStatusCode === 404) {
      return { exists: false };
    }

    console.error(`❌ Error checking file existence:`, error);
    return {
      exists: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Get file metadata without downloading the content
 */
export async function getFileMetadata(
  fileKey: string,
  bucketType: BucketType
): Promise<{
  success: boolean;
  metadata?: {
    contentType?: string;
    contentLength?: number;
    lastModified?: Date;
    etag?: string;
    customMetadata?: Record<string, string>;
  };
  error?: string;
}> {
  try {
    const bucketName = getBucketName(bucketType);

    const headCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    const response = await r2Client.send(headCommand);

    return {
      success: true,
      metadata: {
        contentType: response.ContentType,
        contentLength: response.ContentLength,
        lastModified: response.LastModified,
        etag: response.ETag,
        customMetadata: response.Metadata,
      },
    };
  } catch (error) {
    console.error(`❌ Failed to get file metadata:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Default export with all functions
export default {
  // Core functions
  uploadFile,
  uploadFileToCustomBucket,
  downloadFile,
  deleteFile,
  copyFile,
  moveFile,

  // Utility functions
  getBucketName,
  getFileConfig,
  getAvailableFileTypes,
  getAvailableBucketTypes,
  getPublicUrl,
  fileExists,
  getFileMetadata,

  // Bucket management
  ensureBucketExists,
  initializeAllBuckets,
  configureBucketCors,
  configureAllBucketsCors,

  // Specialized functions
  updateResumeFileKey,
  downloadAndUploadImage,

  // Enums and types
  BucketType,
  FileType: {} as Record<string, FileType>,
};
