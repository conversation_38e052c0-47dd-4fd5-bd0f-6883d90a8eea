{"version": 3, "file": "78-<PERSON>guui1K1.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/help/_slug_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/78.js"], "sourcesContent": ["import { e as error } from \"../../../../chunks/index.js\";\nimport { e as getHelpArticleBySlug, d as getHelpArticlesByCategory, g as getHelpArticles } from \"../../../../chunks/client2.js\";\nconst load = async ({ params }) => {\n  const { slug } = params;\n  try {\n    const article = await getHelpArticleBySlug(slug);\n    if (!article) {\n      throw error(404, \"Article not found\");\n    }\n    const categoryArticles = await getHelpArticlesByCategory(article.category);\n    const relatedCategoryArticles = categoryArticles.filter((a) => a._id !== article._id).slice(0, 5);\n    const allArticles = await getHelpArticles();\n    const articlesByCategory = allArticles.reduce((acc, article2) => {\n      if (!acc[article2.category]) {\n        acc[article2.category] = {\n          name: getCategoryName(article2.category),\n          slug: article2.category,\n          icon: getCategoryIcon(article2.category),\n          articles: []\n        };\n      }\n      acc[article2.category].articles.push({\n        id: article2._id,\n        title: article2.title,\n        slug: article2.slug.current\n      });\n      return acc;\n    }, {});\n    const categories = Object.values(articlesByCategory).sort(\n      (a, b) => a.name.localeCompare(b.name)\n    );\n    return {\n      article,\n      categoryArticles: relatedCategoryArticles,\n      categories\n    };\n  } catch (err) {\n    console.error(`Error loading help article ${slug}:`, err);\n    throw error(404, \"Article not found\");\n  }\n};\nfunction getCategoryName(slug) {\n  const categoryMap = {\n    \"getting-started\": \"Getting Started\",\n    \"auto-apply\": \"Using Auto Apply\",\n    \"account-billing\": \"Account & Billing\",\n    troubleshooting: \"Troubleshooting\",\n    \"privacy-security\": \"Privacy & Security\"\n  };\n  return categoryMap[slug] || slug;\n}\nfunction getCategoryIcon(slug) {\n  const iconMap = {\n    \"getting-started\": \"BookOpen\",\n    \"auto-apply\": \"FileText\",\n    \"account-billing\": \"CreditCard\",\n    troubleshooting: \"HelpCircle\",\n    \"privacy-security\": \"Shield\"\n  };\n  return iconMap[slug] || \"HelpCircle\";\n}\nexport {\n  load\n};\n", "import * as server from '../entries/pages/help/_slug_/_page.server.ts.js';\n\nexport const index = 78;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/help/_slug_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/help/[slug]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/78.CujgRDdv.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/CBdr9r-W.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/BPr9JIwg.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/BwkAotBa.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/C2MdR6K0.js\",\"_app/immutable/chunks/hQ6uUXJy.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/C8-oZ3V_.js\",\"_app/immutable/chunks/CsOU4yHs.js\",\"_app/immutable/chunks/BJwwRUaF.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/DETxXRrJ.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/CxmsTEaf.js\",\"_app/immutable/chunks/rNI1Perp.js\",\"_app/immutable/chunks/BMgaXnEE.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/DosGZj-c.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/Ce6y1v79.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/7AwcL9ec.js\"];\nexport const stylesheets = [\"_app/immutable/assets/scroll-area.bHHIbcsu.css\",\"_app/immutable/assets/PortableText.DSHKgSkc.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,oBAAoB,CAAC,IAAI,CAAC;AACpD,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC;AAC3C;AACA,IAAI,MAAM,gBAAgB,GAAG,MAAM,yBAAyB,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC9E,IAAI,MAAM,uBAAuB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;AACrG,IAAI,MAAM,WAAW,GAAG,MAAM,eAAe,EAAE;AAC/C,IAAI,MAAM,kBAAkB,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,KAAK;AACrE,MAAM,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACnC,QAAQ,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG;AACjC,UAAU,IAAI,EAAE,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAClD,UAAU,IAAI,EAAE,QAAQ,CAAC,QAAQ;AACjC,UAAU,IAAI,EAAE,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAClD,UAAU,QAAQ,EAAE;AACpB,SAAS;AACT;AACA,MAAM,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC3C,QAAQ,EAAE,EAAE,QAAQ,CAAC,GAAG;AACxB,QAAQ,KAAK,EAAE,QAAQ,CAAC,KAAK;AAC7B,QAAQ,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;AAC5B,OAAO,CAAC;AACR,MAAM,OAAO,GAAG;AAChB,KAAK,EAAE,EAAE,CAAC;AACV,IAAI,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,IAAI;AAC7D,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;AAC3C,KAAK;AACL,IAAI,OAAO;AACX,MAAM,OAAO;AACb,MAAM,gBAAgB,EAAE,uBAAuB;AAC/C,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;AAC7D,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,mBAAmB,CAAC;AACzC;AACA,CAAC;AACD,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,iBAAiB,EAAE,iBAAiB;AACxC,IAAI,YAAY,EAAE,kBAAkB;AACpC,IAAI,iBAAiB,EAAE,mBAAmB;AAC1C,IAAI,eAAe,EAAE,iBAAiB;AACtC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI;AAClC;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,iBAAiB,EAAE,UAAU;AACjC,IAAI,YAAY,EAAE,UAAU;AAC5B,IAAI,iBAAiB,EAAE,YAAY;AACnC,IAAI,eAAe,EAAE,YAAY;AACjC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY;AACtC;;;;;;;AC1DY,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA8C,CAAC,EAAE;AAE5G,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACrjE,MAAC,WAAW,GAAG,CAAC,gDAAgD,CAAC,iDAAiD;AAClH,MAAC,KAAK,GAAG;;;;"}