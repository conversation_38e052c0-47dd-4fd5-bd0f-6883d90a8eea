{"version": 3, "file": "dialog-description2-rfr-pd9k.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/dialog-description2.js"], "sourcesContent": ["import { w as push, M as spread_attributes, N as bind_props, y as pop } from \"./index3.js\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { d as useDialogDescription } from \"./dialog-overlay.js\";\nimport { u as useId } from \"./use-id.js\";\nfunction Dialog_description($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    children,\n    child,\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const descriptionState = useDialogDescription({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, descriptionState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Dialog_description as D\n};\n"], "names": [], "mappings": ";;;;;;AAMA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,gBAAgB,GAAG,oBAAoB,CAAC;AAChD,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,gBAAgB,CAAC,KAAK,CAAC;AACnE,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}