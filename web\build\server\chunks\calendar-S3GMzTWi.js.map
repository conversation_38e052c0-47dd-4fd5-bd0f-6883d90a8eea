{"version": 3, "file": "calendar-S3GMzTWi.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/calendar.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Calendar($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\"path\", { \"d\": \"M8 2v4\" }],\n    [\"path\", { \"d\": \"M16 2v4\" }],\n    [\n      \"rect\",\n      {\n        \"width\": \"18\",\n        \"height\": \"18\",\n        \"x\": \"3\",\n        \"y\": \"4\",\n        \"rx\": \"2\"\n      }\n    ],\n    [\"path\", { \"d\": \"M3 10h18\" }]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"calendar\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Calendar as C\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;AAC/B,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC;AAChC,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,UAAU,EAAE;AAChC,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;AACxB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}