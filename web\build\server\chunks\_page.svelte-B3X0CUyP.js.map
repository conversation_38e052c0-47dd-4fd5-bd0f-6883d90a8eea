{"version": 3, "file": "_page.svelte-B3X0CUyP.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/help/_slug_/_page.svelte.js"], "sourcesContent": ["import { W as stringify, V as escape_html, R as attr, U as ensure_array_like, N as bind_props, y as pop, w as push } from \"../../../../chunks/index3.js\";\nimport { S as SEO } from \"../../../../chunks/SEO.js\";\nimport { H as HelpSidebar } from \"../../../../chunks/HelpSidebar.js\";\nimport { H as HelpArticleCard } from \"../../../../chunks/HelpArticleCard.js\";\nimport { P as PortableText } from \"../../../../chunks/PortableText.js\";\nimport { formatDistanceToNow } from \"date-fns\";\nimport { A as Arrow_left } from \"../../../../chunks/arrow-left.js\";\nimport { C as Calendar } from \"../../../../chunks/calendar.js\";\nimport { E as Eye } from \"../../../../chunks/eye.js\";\nfunction getCategoryName(slug) {\n  const categoryMap = {\n    \"getting-started\": \"Getting Started\",\n    \"auto-apply\": \"Using Auto Apply\",\n    \"account-billing\": \"Account & Billing\",\n    troubleshooting: \"Troubleshooting\",\n    \"privacy-security\": \"Privacy & Security\"\n  };\n  return categoryMap[slug] || slug;\n}\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  let updatedDate = \"\";\n  updatedDate = data.article?.updatedAt ? formatDistanceToNow(new Date(data.article.updatedAt), { addSuffix: true }) : \"Recently\";\n  SEO($$payload, {\n    title: `${stringify(data.article.title)} | Help Center`,\n    description: data.article.description,\n    keywords: `help center, ${stringify(data.article.title.toLowerCase())}, ${stringify(data.article.category)}, support, guides`\n  });\n  $$payload.out += `<!----> <div class=\"container mx-auto px-4 py-12\"><div class=\"grid gap-8 lg:grid-cols-4\"><div class=\"lg:col-span-1\">`;\n  HelpSidebar($$payload, { categories: data.categories });\n  $$payload.out += `<!----></div> <div class=\"lg:col-span-3\"><div class=\"mb-6\"><a href=\"/help\" class=\"text-primary inline-flex items-center text-sm hover:underline\">`;\n  Arrow_left($$payload, { class: \"mr-1 h-4 w-4\" });\n  $$payload.out += `<!----> Back to Help Center</a></div> <article><header class=\"mb-8\"><h1 class=\"mb-4 text-3xl font-bold\">${escape_html(data.article.title)}</h1> <div class=\"text-muted-foreground flex flex-wrap items-center gap-4 text-sm\"><a${attr(\"href\", `/help/category/${stringify(data.article.category)}`)} class=\"bg-primary/10 text-primary rounded-full px-3 py-1\">${escape_html(data.article.category)}</a> <div class=\"flex items-center gap-1\">`;\n  Calendar($$payload, { class: \"h-4 w-4\" });\n  $$payload.out += `<!----> <span>Updated ${escape_html(updatedDate)}</span></div> <div class=\"flex items-center gap-1\">`;\n  Eye($$payload, { class: \"h-4 w-4\" });\n  $$payload.out += `<!----> <span>${escape_html(data.article.viewCount)} view${escape_html(data.article.viewCount !== 1 ? \"s\" : \"\")}</span></div></div></header> <div class=\"prose prose-lg max-w-none\">`;\n  if (data.article.content) {\n    $$payload.out += \"<!--[-->\";\n    PortableText($$payload, { value: data.article.content });\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<p class=\"bg-muted rounded-lg border p-4\">This article doesn't have any content yet. Please check back later.</p>`;\n  }\n  $$payload.out += `<!--]--></div> `;\n  if (data.article.tags && data.article.tags.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array = ensure_array_like(data.article.tags);\n    $$payload.out += `<div class=\"mt-8\"><h2 class=\"mb-2 text-lg font-semibold\">Tags</h2> <div class=\"flex flex-wrap gap-2\"><!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let tag = each_array[$$index];\n      $$payload.out += `<span class=\"bg-muted rounded-full px-3 py-1 text-sm\">${escape_html(tag)}</span>`;\n    }\n    $$payload.out += `<!--]--></div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (data.article.relatedArticles && data.article.relatedArticles.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array_1 = ensure_array_like(data.article.relatedArticles);\n    $$payload.out += `<div class=\"mt-12\"><h2 class=\"mb-6 text-2xl font-semibold\">Related Articles</h2> <div class=\"grid gap-6 md:grid-cols-2 xl:grid-cols-3\"><!--[-->`;\n    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {\n      let article = each_array_1[$$index_1];\n      HelpArticleCard($$payload, { article });\n    }\n    $$payload.out += `<!--]--></div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (data.categoryArticles && data.categoryArticles.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array_2 = ensure_array_like(data.categoryArticles);\n    $$payload.out += `<div class=\"mt-12\"><h2 class=\"mb-6 text-2xl font-semibold\">More in ${escape_html(getCategoryName(data.article.category))}</h2> <div class=\"grid gap-6 md:grid-cols-2 xl:grid-cols-3\"><!--[-->`;\n    for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {\n      let article = each_array_2[$$index_2];\n      HelpArticleCard($$payload, { article });\n    }\n    $$payload.out += `<!--]--></div></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></article></div></div></div>`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,iBAAiB,EAAE,iBAAiB;AACxC,IAAI,YAAY,EAAE,kBAAkB;AACpC,IAAI,iBAAiB,EAAE,mBAAmB;AAC1C,IAAI,eAAe,EAAE,iBAAiB;AACtC,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI;AAClC;AACA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,IAAI,WAAW,GAAG,EAAE;AACtB,EAAE,WAAW,GAAG,IAAI,CAAC,OAAO,EAAE,SAAS,GAAG,mBAAmB,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,GAAG,UAAU;AACjI,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,CAAC,EAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,cAAc,CAAC;AAC3D,IAAI,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;AACzC,IAAI,QAAQ,EAAE,CAAC,aAAa,EAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,iBAAiB;AAChI,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oHAAoH,CAAC;AACzI,EAAE,WAAW,CAAC,SAAS,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;AACzD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iJAAiJ,CAAC;AACtK,EAAE,UAAU,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAClD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wGAAwG,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,qFAAqF,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,2DAA2D,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,0CAA0C,CAAC;AACnc,EAAE,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAC3C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,sBAAsB,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC,mDAAmD,CAAC;AACzH,EAAE,GAAG,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACtC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC,oEAAoE,CAAC;AACzM,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC5B,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,YAAY,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AAC5D,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,iHAAiH,CAAC;AACxI;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACpC,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AACzD,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAC3D,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,6GAA6G,CAAC;AACpI,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC;AACnC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,sDAAsD,EAAE,WAAW,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC;AACzG;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/E,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC;AACxE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,+IAA+I,CAAC;AACtK,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AAC3C,MAAM,eAAe,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC;AAC7C;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AACjE,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC;AACjE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,mEAAmE,EAAE,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,oEAAoE,CAAC;AACpN,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC/F,MAAM,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AAC3C,MAAM,eAAe,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC;AAC7C;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC3C,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oCAAoC,CAAC;AACzD,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}