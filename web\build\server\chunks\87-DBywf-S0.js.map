{"version": 3, "file": "87-DBywf-S0.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/releases/_slug_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/87.js"], "sourcesContent": ["import { c as client } from \"../../../../../chunks/client2.js\";\nimport { e as error } from \"../../../../../chunks/index.js\";\nconst load = async ({ params }) => {\n  const { slug } = params;\n  try {\n    const pressRelease = await client.fetch(\n      `\n      *[_type == \"post\" && postType == \"press\" && slug.current == $slug][0] {\n        _id,\n        title,\n        subtitle,\n        location,\n        publishedAt,\n        updatedAt,\n        body,\n        mainImage,\n        excerpt,\n        contactInfo,\n        boilerplate,\n        pdfAttachment,\n        \"categories\": categories[]->\n      }\n    `,\n      { slug }\n    );\n    if (!pressRelease) {\n      throw error(404, \"Press release not found\");\n    }\n    const relatedPressReleases = await client.fetch(\n      `\n      *[_type == \"post\" && postType == \"press\" && slug.current != $slug] | order(publishedAt desc) [0...3] {\n        _id,\n        title,\n        slug,\n        publishedAt,\n        excerpt\n      }\n    `,\n      { slug }\n    );\n    return {\n      pressRelease,\n      relatedPressReleases\n    };\n  } catch (err) {\n    console.error(\"Error loading press release:\", err);\n    throw error(500, \"Error loading press release\");\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/press/releases/_slug_/_page.server.ts.js';\n\nexport const index = 87;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/press/releases/_slug_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/press/releases/[slug]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/87.B269i1uT.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/jRvHGFcG.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/BMgaXnEE.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/DosGZj-c.js\",\"_app/immutable/chunks/Ce6y1v79.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/CwgkX8t9.js\",\"_app/immutable/chunks/tr-scC-m.js\",\"_app/immutable/chunks/zNKWipEG.js\"];\nexport const stylesheets = [\"_app/immutable/assets/PortableText.DSHKgSkc.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,KAAK;AAC3C,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,MAAM,EAAE,IAAI;AACZ,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,yBAAyB,CAAC;AACjD;AACA,IAAI,MAAM,oBAAoB,GAAG,MAAM,MAAM,CAAC,KAAK;AACnD,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,MAAM,EAAE,IAAI;AACZ,KAAK;AACL,IAAI,OAAO;AACX,MAAM,YAAY;AAClB,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC;AACtD,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,6BAA6B,CAAC;AACnD;AACA,CAAC;;;;;;;AC9CW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAwD,CAAC,EAAE;AAEtH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzmC,MAAC,WAAW,GAAG,CAAC,iDAAiD;AACjE,MAAC,KAAK,GAAG;;;;"}