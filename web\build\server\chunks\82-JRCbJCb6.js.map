{"version": 3, "file": "82-JRCbJCb6.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/legal/_slug_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/82.js"], "sourcesContent": ["import { c as client } from \"../../../../chunks/client2.js\";\nimport { e as error } from \"../../../../chunks/index.js\";\nconst load = async ({ params, parent }) => {\n  const { slug } = params;\n  const layoutData = await parent();\n  const isValidLegalPage = layoutData.legalPages.some((page) => page.slug === slug);\n  if (!isValidLegalPage) {\n    throw error(404, \"Legal page not found\");\n  }\n  try {\n    const legalPage = await client.fetch(\n      `\n      *[_type == \"page\" && pageType == \"legal\" && slug.current == $slug][0] {\n        title,\n        description,\n        content,\n        \"updatedAt\": coalesce(updatedAt, _updatedAt),\n        \"slug\": slug.current\n      }\n    `,\n      { slug }\n    );\n    if (!legalPage) {\n      const fallbackPage = layoutData.legalPages.find((page) => page.slug === slug);\n      if (fallbackPage) {\n        return {\n          legalPage: {\n            title: fallbackPage.title,\n            description: fallbackPage.description || \"\",\n            content: null,\n            slug: fallbackPage.slug,\n            updatedAt: (/* @__PURE__ */ new Date()).toISOString()\n          }\n        };\n      }\n      throw error(404, \"Legal page not found\");\n    }\n    return {\n      legalPage\n    };\n  } catch (err) {\n    console.error(\"Error loading legal page data:\", err);\n    throw error(404, \"Legal page not found\");\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/legal/_slug_/_page.server.ts.js';\n\nexport const index = 82;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/legal/_slug_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/legal/[slug]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/82.Cl_8FW4g.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BMgaXnEE.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/DosGZj-c.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\"];\nexport const stylesheets = [\"_app/immutable/assets/PortableText.DSHKgSkc.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC3C,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;AACzB,EAAE,MAAM,UAAU,GAAG,MAAM,MAAM,EAAE;AACnC,EAAE,MAAM,gBAAgB,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AACnF,EAAE,IAAI,CAAC,gBAAgB,EAAE;AACzB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,sBAAsB,CAAC;AAC5C;AACA,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,KAAK;AACxC,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,MAAM,EAAE,IAAI;AACZ,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,MAAM,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;AACnF,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,OAAO;AACf,UAAU,SAAS,EAAE;AACrB,YAAY,KAAK,EAAE,YAAY,CAAC,KAAK;AACrC,YAAY,WAAW,EAAE,YAAY,CAAC,WAAW,IAAI,EAAE;AACvD,YAAY,OAAO,EAAE,IAAI;AACzB,YAAY,IAAI,EAAE,YAAY,CAAC,IAAI;AACnC,YAAY,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AAC/D;AACA,SAAS;AACT;AACA,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,sBAAsB,CAAC;AAC9C;AACA,IAAI,OAAO;AACX,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,GAAG,CAAC;AACxD,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,sBAAsB,CAAC;AAC5C;AACA,CAAC;;;;;;;AC1CW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA+C,CAAC,EAAE;AAE7G,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzrB,MAAC,WAAW,GAAG,CAAC,iDAAiD;AACjE,MAAC,KAAK,GAAG;;;;"}