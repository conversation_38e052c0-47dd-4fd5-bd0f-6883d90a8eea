// Test R2 with SSL bypass (for debugging only)
import { S3Client, ListBucketsCommand } from '@aws-sdk/client-s3';
import https from 'https';

console.log('🧪 Testing R2 with SSL bypass (debugging only)...');

// Temporarily disable SSL verification for testing
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

const config = {
  endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
  region: 'auto',
  credentials: {
    accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
    secretAccessKey: '****************************************************************',
  },
  forcePathStyle: true,
  requestHandler: {
    httpsAgent: new https.Agent({
      rejectUnauthorized: false, // Bypass SSL verification
      keepAlive: true,
    })
  }
};

async function testBypassSSL() {
  try {
    console.log('🔄 Testing with SSL verification disabled...');
    
    const client = new S3Client(config);
    const command = new ListBucketsCommand({});
    
    const response = await client.send(command);
    
    console.log('✅ SUCCESS! SSL bypass worked');
    console.log(`📦 Found ${response.Buckets?.length || 0} buckets`);
    
    if (response.Buckets && response.Buckets.length > 0) {
      console.log('📋 Buckets:', response.Buckets.map(b => b.Name).join(', '));
    }
    
    console.log('\n🔍 This suggests the issue is SSL certificate validation');
    console.log('💡 Possible solutions:');
    console.log('   1. Update Node.js SSL certificate store');
    console.log('   2. Use a different TLS configuration');
    console.log('   3. Implement worker-based upload as alternative');
    
    return true;
    
  } catch (error) {
    console.log('❌ Even with SSL bypass, connection failed');
    console.log('🔍 Error:', error.message);
    console.log('💭 This suggests a deeper network or credential issue');
    
    return false;
  }
}

// Test with different credential formats
async function testCredentialFormats() {
  console.log('\n🔑 Testing different credential formats...');
  
  const credentialTests = [
    {
      name: 'Original credentials',
      accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
      secretAccessKey: '****************************************************************',
    },
    {
      name: 'Trimmed credentials',
      accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5'.trim(),
      secretAccessKey: '****************************************************************'.trim(),
    }
  ];
  
  for (const creds of credentialTests) {
    console.log(`\n📋 Testing: ${creds.name}`);
    
    try {
      const testConfig = {
        ...config,
        credentials: creds
      };
      
      const client = new S3Client(testConfig);
      const command = new ListBucketsCommand({});
      
      const response = await client.send(command);
      console.log(`✅ ${creds.name} worked!`);
      return true;
      
    } catch (error) {
      console.log(`❌ ${creds.name} failed: ${error.message}`);
    }
  }
  
  return false;
}

async function runTests() {
  const sslBypassResult = await testBypassSSL();
  
  if (!sslBypassResult) {
    await testCredentialFormats();
  }
  
  // Re-enable SSL verification
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '1';
  
  console.log('\n🔒 SSL verification re-enabled');
  console.log('⚠️ SSL bypass was only for debugging - do not use in production');
}

runTests();
