{"version": 3, "file": "button-CrucCo1G.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/button.js"], "sourcesContent": ["import { M as spread_attributes, T as clsx, N as bind_props, y as pop, w as push } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { tv } from \"tailwind-variants\";\nconst buttonVariants = tv({\n  base: \"cursor-pointer focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex shrink-0 items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium outline-none transition-all focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&_svg:not([class*='size-'])]:size-4 [&_svg]:pointer-events-none [&_svg]:shrink-0\",\n  variants: {\n    variant: {\n      default: \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n      destructive: \"bg-destructive shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white\",\n      outline: \"bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border\",\n      secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n      ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n      link: \"text-primary underline-offset-4 hover:underline\"\n    },\n    size: {\n      default: \"h-9 px-4 py-2\",\n      sm: \"h-8 gap-1.5 rounded-md px-3\",\n      lg: \"h-10 rounded-md px-6\",\n      icon: \"size-9\"\n    }\n  },\n  defaultVariants: { variant: \"default\", size: \"default\" }\n});\nfunction Button($$payload, $$props) {\n  push();\n  let {\n    class: className,\n    variant = \"default\",\n    size = \"default\",\n    ref = null,\n    href = void 0,\n    type = \"button\",\n    disabled,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  if (href) {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<a${spread_attributes(\n      {\n        \"data-slot\": \"button\",\n        class: clsx(cn(buttonVariants({ variant, size }), className)),\n        href: disabled ? void 0 : href,\n        \"aria-disabled\": disabled,\n        role: disabled ? \"link\" : void 0,\n        tabindex: disabled ? -1 : void 0,\n        ...restProps\n      },\n      null\n    )}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></a>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<button${spread_attributes(\n      {\n        \"data-slot\": \"button\",\n        class: clsx(cn(buttonVariants({ variant, size }), className)),\n        type,\n        disabled,\n        ...restProps\n      },\n      null\n    )}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></button>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Button as B,\n  buttonVariants as b\n};\n"], "names": ["tv"], "mappings": ";;;;AAGK,MAAC,cAAc,GAAGA,EAAE,CAAC;AAC1B,EAAE,IAAI,EAAE,ugBAAugB;AAC/gB,EAAE,QAAQ,EAAE;AACZ,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,kEAAkE;AACjF,MAAM,WAAW,EAAE,6JAA6J;AAChL,MAAM,OAAO,EAAE,uIAAuI;AACtJ,MAAM,SAAS,EAAE,wEAAwE;AACzF,MAAM,KAAK,EAAE,sEAAsE;AACnF,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,OAAO,EAAE,eAAe;AAC9B,MAAM,EAAE,EAAE,6BAA6B;AACvC,MAAM,EAAE,EAAE,sBAAsB;AAChC,MAAM,IAAI,EAAE;AACZ;AACA,GAAG;AACH,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS;AACxD,CAAC;AACD,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO,GAAG,SAAS;AACvB,IAAI,IAAI,GAAG,SAAS;AACpB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,IAAI,GAAG,MAAM;AACjB,IAAI,IAAI,GAAG,QAAQ;AACnB,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,IAAI,EAAE;AACZ,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,iBAAiB;AAC3C,MAAM;AACN,QAAQ,WAAW,EAAE,QAAQ;AAC7B,QAAQ,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AACrE,QAAQ,IAAI,EAAE,QAAQ,GAAG,MAAM,GAAG,IAAI;AACtC,QAAQ,eAAe,EAAE,QAAQ;AACjC,QAAQ,IAAI,EAAE,QAAQ,GAAG,MAAM,GAAG,MAAM;AACxC,QAAQ,QAAQ,EAAE,QAAQ,GAAG,EAAE,GAAG,MAAM;AACxC,QAAQ,GAAG;AACX,OAAO;AACP,MAAM;AACN,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AAClC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,iBAAiB;AAChD,MAAM;AACN,QAAQ,WAAW,EAAE,QAAQ;AAC7B,QAAQ,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AACrE,QAAQ,IAAI;AACZ,QAAQ,QAAQ;AAChB,QAAQ,GAAG;AACX,OAAO;AACP,MAAM;AACN,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACvC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}