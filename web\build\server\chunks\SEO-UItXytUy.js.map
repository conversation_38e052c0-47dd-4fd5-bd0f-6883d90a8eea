{"version": 3, "file": "SEO-UItXytUy.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/SEO.js"], "sourcesContent": ["import { Y as fallback, X as head, N as bind_props, V as escape_html, R as attr } from \"./index3.js\";\nfunction SEO($$payload, $$props) {\n  let title = fallback($$props[\"title\"], \"Auto Apply - Your job application automation assistant.\");\n  let description = fallback($$props[\"description\"], \"Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress.\");\n  let keywords = fallback($$props[\"keywords\"], \"job application, automation, resume, job search, AI, career, employment\");\n  let image = fallback($$props[\"image\"], \"/assets/og-image.jpg\");\n  let url = fallback($$props[\"url\"], \"https://autoapply.io\");\n  let type = fallback($$props[\"type\"], \"website\");\n  let twitterCard = fallback($$props[\"twitterCard\"], \"summary_large_image\");\n  let author = fallback($$props[\"author\"], \"Auto Apply\");\n  let locale = fallback($$props[\"locale\"], \"en_US\");\n  let themeColor = fallback($$props[\"themeColor\"], \"#4f46e5\");\n  head($$payload, ($$payload2) => {\n    $$payload2.title = `<title>${escape_html(title)}</title>`;\n    $$payload2.out += `<meta name=\"description\"${attr(\"content\", description)}/> <meta name=\"keywords\"${attr(\"content\", keywords)}/> <meta name=\"author\"${attr(\"content\", author)}/> <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/> <meta name=\"robots\" content=\"index, follow\"/> <meta name=\"theme-color\"${attr(\"content\", themeColor)}/> <meta name=\"application-name\" content=\"Auto Apply\"/> <meta name=\"apple-mobile-web-app-title\" content=\"Auto Apply\"/> <meta name=\"mobile-web-app-capable\" content=\"yes\"/> <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\"/> <meta property=\"og:type\"${attr(\"content\", type)}/> <meta property=\"og:url\"${attr(\"content\", url)}/> <meta property=\"og:title\"${attr(\"content\", title)}/> <meta property=\"og:description\"${attr(\"content\", description)}/> <meta property=\"og:image\"${attr(\"content\", image)}/> <meta property=\"og:locale\"${attr(\"content\", locale)}/> <meta property=\"og:site_name\" content=\"Auto Apply\"/> <meta property=\"twitter:card\"${attr(\"content\", twitterCard)}/> <meta property=\"twitter:url\"${attr(\"content\", url)}/> <meta property=\"twitter:title\"${attr(\"content\", title)}/> <meta property=\"twitter:description\"${attr(\"content\", description)}/> <meta property=\"twitter:image\"${attr(\"content\", image)}/> <meta property=\"twitter:site\" content=\"@autoapplyio\"/> <link rel=\"canonical\"${attr(\"href\", url)}/> <link rel=\"icon\" type=\"image/png\" sizes=\"32x32\" href=\"/assets/favicon/favicon-32x32.png\"/> <link rel=\"icon\" type=\"image/png\" sizes=\"16x16\" href=\"/assets/favicon/favicon-16x16.png\"/> <link rel=\"apple-touch-icon\" sizes=\"180x180\" href=\"/assets/favicon/apple-touch-icon.png\"/> <link rel=\"manifest\" href=\"/site.webmanifest\"/> <script type=\"application/ld+json\">\n    {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"Organization\",\n      \"url\": \"https://www.autoapply.io\",\n      \"sameAs\": [\"https://twitter.com/autoapplyio\", \"https://www.linkedin.com/company/autoapply\"],\n      \"logo\": \"https://www.autoapply.io/assets/logo.png\",\n      \"name\": \"Auto Apply\",\n      \"description\": \"Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress.\",\n      \"email\": \"<EMAIL>\"\n    }\n  <\\/script> <script type=\"application/ld+json\">\n    {\n      \"@context\": \"https://schema.org\",\n      \"@type\": \"WebPage\",\n      \"url\": \"{url}\",\n      \"name\": \"{title}\",\n      \"description\": \"{description}\",\n      \"inLanguage\": \"en-US\",\n      \"isPartOf\": {\n        \"@type\": \"WebSite\",\n        \"url\": \"https://www.autoapply.io\",\n        \"name\": \"Auto Apply\",\n        \"description\": \"Apply to hundreds of jobs with just one click. Our AI matches your resume to job listings, auto-fills applications, and tracks your progress.\"\n      },\n      \"potentialAction\": {\n        \"@type\": \"ReadAction\",\n        \"target\": [\"{url}\"]\n      }\n    }\n  <\\/script>`;\n  });\n  bind_props($$props, {\n    title,\n    description,\n    keywords,\n    image,\n    url,\n    type,\n    twitterCard,\n    author,\n    locale,\n    themeColor\n  });\n}\nexport {\n  SEO as S\n};\n"], "names": [], "mappings": ";;AACA,SAAS,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE;AACjC,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,yDAAyD,CAAC;AACnG,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,+IAA+I,CAAC;AACrM,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,yEAAyE,CAAC;AACzH,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,sBAAsB,CAAC;AAChE,EAAE,IAAI,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,sBAAsB,CAAC;AAC5D,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC;AACjD,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,qBAAqB,CAAC;AAC3E,EAAE,IAAI,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,YAAY,CAAC;AACxD,EAAE,IAAI,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC;AACnD,EAAE,IAAI,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC;AAC7D,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,UAAU,KAAK;AAClC,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAC7D,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,iJAAiJ,EAAE,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,0QAA0Q,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,0BAA0B,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,kCAAkC,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,6BAA6B,EAAE,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,qFAAqF,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,uCAAuC,EAAE,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,iCAAiC,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,+EAA+E,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC71C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,CAAC;AACb,GAAG,CAAC;AACJ,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,GAAG;AACP,IAAI,IAAI;AACR,IAAI,WAAW;AACf,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,CAAC;AACJ;;;;"}