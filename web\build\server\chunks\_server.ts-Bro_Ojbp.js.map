{"version": 3, "file": "_server.ts-Bro_Ojbp.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/test-companies/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../chunks/prisma.js\";\nconst GET = async () => {\n  try {\n    console.log(\"🔍 Testing database connection...\");\n    if (!prisma) {\n      console.error(\"❌ Prisma client is not initialized\");\n      return json({ error: \"Database connection not available\" }, { status: 500 });\n    }\n    console.log(\"✅ Prisma client is available\");\n    const totalCompanies = await prisma.company.count();\n    console.log(`📊 Total companies: ${totalCompanies}`);\n    const companies = await prisma.company.findMany({\n      take: 5,\n      select: {\n        id: true,\n        name: true,\n        logoUrl: true,\n        activeJobCount: true\n      }\n    });\n    console.log(`📋 Sample companies:`, companies);\n    return json({\n      success: true,\n      totalCompanies,\n      sampleCompanies: companies\n    });\n  } catch (error) {\n    console.error(\"💥 Database test error:\", error);\n    return json({ error: \"Database test failed\", details: error }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACpD,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,CAAC,KAAK,CAAC,oCAAoC,CAAC;AACzD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;AAC/C,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;AACvD,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC,CAAC;AACxD,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACpD,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,cAAc,EAAE;AACxB;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,oBAAoB,CAAC,EAAE,SAAS,CAAC;AAClD,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,cAAc;AACpB,MAAM,eAAe,EAAE;AACvB,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC;AACnD,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,sBAAsB,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnF;AACA;;;;"}