{"version": 3, "file": "plan-sync-CZNz1Ayv.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/plan-sync.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nimport { a as FeatureCategory, L as LimitType, F as FeatureAccessLevel } from \"./features.js\";\nimport { a as syncPlanWithStripe } from \"./stripe.js\";\nasync function getPlansFromDatabase() {\n  try {\n    const dbPlans = await prisma.plan.findMany({\n      orderBy: {\n        monthlyPrice: \"asc\"\n      },\n      include: {\n        features: {\n          include: {\n            limits: true\n          }\n        }\n      }\n    });\n    if (dbPlans.length === 0) {\n      console.log(\"No plans found in the database. Initializing with default plans...\");\n      await initializePlansInDatabase();\n      return getPlansFromDatabase();\n    }\n    return dbPlans.map(dbPlanToPlanTier);\n  } catch (error) {\n    console.error(\"Error getting plans from database:\", error);\n    return [];\n  }\n}\nasync function getPlanById(planId) {\n  try {\n    const dbPlan = await prisma.plan.findUnique({\n      where: { id: planId },\n      include: {\n        features: {\n          include: {\n            limits: true\n          }\n        }\n      }\n    });\n    if (!dbPlan) {\n      return null;\n    }\n    return dbPlanToPlanTier(dbPlan);\n  } catch (error) {\n    console.error(`Error getting plan ${planId}:`, error);\n    return null;\n  }\n}\nfunction dbPlanToPlanTier(dbPlan) {\n  return {\n    id: dbPlan.id,\n    name: dbPlan.name,\n    description: dbPlan.description,\n    section: dbPlan.section,\n    monthlyPrice: dbPlan.monthlyPrice,\n    annualPrice: dbPlan.annualPrice,\n    stripePriceMonthlyId: dbPlan.stripePriceMonthlyId,\n    stripePriceYearlyId: dbPlan.stripePriceYearlyId,\n    popular: dbPlan.popular,\n    features: dbPlan.features.map((feature) => ({\n      featureId: feature.featureId,\n      accessLevel: feature.accessLevel,\n      limits: feature.limits?.map((limit) => ({\n        limitId: limit.limitId,\n        value: limit.value === \"unlimited\" ? \"unlimited\" : parseInt(limit.value, 10)\n      }))\n    }))\n  };\n}\nasync function initializePlansInDatabase() {\n  try {\n    const existingPlansCount = await prisma.plan.count();\n    if (existingPlansCount > 0) {\n      console.log(\n        `${existingPlansCount} plans already exist in the database. Skipping initialization.`\n      );\n      return 0;\n    }\n    console.log(\"No plans found in the database. Initializing with default plans...\");\n    const defaultFeatures = [\n      {\n        id: \"dashboard\",\n        name: \"Dashboard\",\n        description: \"Access to the main dashboard\",\n        category: FeatureCategory.Core,\n        icon: \"dashboard\"\n      },\n      {\n        id: \"profile\",\n        name: \"User Profile\",\n        description: \"User profile management\",\n        category: FeatureCategory.Core,\n        icon: \"user\"\n      },\n      {\n        id: \"resume_scanner\",\n        name: \"Resume Scanner\",\n        description: \"Scan and analyze resumes\",\n        category: FeatureCategory.Resume,\n        icon: \"scanner\",\n        limits: [\n          {\n            id: \"resume_scans_per_month\",\n            name: \"Resume Scans per Month\",\n            description: \"Maximum number of resume scans per month\",\n            defaultValue: 5,\n            type: LimitType.Monthly,\n            unit: \"scans\",\n            resetDay: 1\n          }\n        ]\n      },\n      {\n        id: \"resume_builder\",\n        name: \"Resume Builder\",\n        description: \"Create and edit resumes\",\n        category: FeatureCategory.Resume,\n        icon: \"file-edit\",\n        limits: [\n          {\n            id: \"resume_versions\",\n            name: \"Resume Versions\",\n            description: \"Maximum number of resume versions\",\n            defaultValue: 1,\n            type: LimitType.Total,\n            unit: \"versions\"\n          },\n          {\n            id: \"resume_templates\",\n            name: \"Resume Templates\",\n            description: \"Available resume templates\",\n            defaultValue: 5,\n            type: LimitType.Total,\n            unit: \"templates\"\n          }\n        ]\n      },\n      {\n        id: \"resume_ai\",\n        name: \"Resume AI\",\n        description: \"AI-powered resume improvements\",\n        category: FeatureCategory.Advanced,\n        icon: \"sparkles\"\n      },\n      {\n        id: \"job_search_profiles\",\n        name: \"Job Search Profiles\",\n        description: \"Create job search profiles\",\n        category: FeatureCategory.JobSearch,\n        icon: \"search\",\n        limits: [\n          {\n            id: \"job_search_profiles_limit\",\n            name: \"Job Search Profiles Limit\",\n            description: \"Maximum number of job search profiles\",\n            defaultValue: 1,\n            type: LimitType.Total,\n            unit: \"profiles\"\n          }\n        ]\n      },\n      {\n        id: \"application_tracker\",\n        name: \"Application Tracker\",\n        description: \"Track job applications\",\n        category: FeatureCategory.Applications,\n        icon: \"list-check\"\n      }\n    ];\n    for (const feature of defaultFeatures) {\n      await syncFeature(feature);\n    }\n    let count = 0;\n    const freePlan = {\n      id: \"free\",\n      name: \"Free\",\n      description: \"Basic features for personal use\",\n      section: \"pro\",\n      monthlyPrice: 0,\n      annualPrice: 0,\n      features: [\n        // Add some basic features\n        { featureId: \"dashboard\", accessLevel: FeatureAccessLevel.Included },\n        { featureId: \"profile\", accessLevel: FeatureAccessLevel.Included },\n        {\n          featureId: \"resume_scanner\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [{ limitId: \"resume_scans_per_month\", value: 5 }]\n        },\n        {\n          featureId: \"resume_builder\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [\n            { limitId: \"resume_versions\", value: 1 },\n            { limitId: \"resume_templates\", value: 5 }\n          ]\n        },\n        { featureId: \"resume_ai\", accessLevel: FeatureAccessLevel.NotIncluded },\n        {\n          featureId: \"ats_optimization\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [{ limitId: \"ats_scans_monthly\", value: 2 }]\n        }\n      ]\n    };\n    await syncPlan(freePlan);\n    count++;\n    const basicPlan = {\n      id: \"basic\",\n      name: \"Basic\",\n      description: \"Essential features for individuals\",\n      section: \"pro\",\n      monthlyPrice: 999,\n      annualPrice: 9990,\n      features: [\n        { featureId: \"dashboard\", accessLevel: FeatureAccessLevel.Included },\n        { featureId: \"profile\", accessLevel: FeatureAccessLevel.Included },\n        {\n          featureId: \"resume_scanner\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [{ limitId: \"resume_scans_per_month\", value: 20 }]\n        },\n        {\n          featureId: \"resume_builder\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [\n            { limitId: \"resume_versions\", value: 3 },\n            { limitId: \"resume_templates\", value: 10 }\n          ]\n        },\n        { featureId: \"resume_ai\", accessLevel: FeatureAccessLevel.NotIncluded },\n        {\n          featureId: \"ats_optimization\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [{ limitId: \"ats_scans_monthly\", value: 5 }]\n        },\n        {\n          featureId: \"job_search_profiles\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [{ limitId: \"job_search_profiles_limit\", value: 3 }]\n        },\n        { featureId: \"application_tracker\", accessLevel: FeatureAccessLevel.Included }\n      ]\n    };\n    await syncPlan(basicPlan);\n    count++;\n    const proPlan = {\n      id: \"pro\",\n      name: \"Professional\",\n      description: \"Advanced features for professionals\",\n      section: \"pro\",\n      monthlyPrice: 1999,\n      annualPrice: 19990,\n      popular: true,\n      features: [\n        { featureId: \"dashboard\", accessLevel: FeatureAccessLevel.Included },\n        { featureId: \"profile\", accessLevel: FeatureAccessLevel.Included },\n        {\n          featureId: \"resume_scanner\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [{ limitId: \"resume_scans_per_month\", value: 50 }]\n        },\n        {\n          featureId: \"resume_builder\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [\n            { limitId: \"resume_versions\", value: 10 },\n            { limitId: \"resume_templates\", value: 20 }\n          ]\n        },\n        { featureId: \"resume_ai\", accessLevel: FeatureAccessLevel.Included },\n        {\n          featureId: \"ats_optimization\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [{ limitId: \"ats_scans_monthly\", value: 20 }]\n        },\n        {\n          featureId: \"job_search_profiles\",\n          accessLevel: FeatureAccessLevel.Limited,\n          limits: [{ limitId: \"job_search_profiles_limit\", value: 5 }]\n        },\n        { featureId: \"application_tracker\", accessLevel: FeatureAccessLevel.Included }\n      ]\n    };\n    await syncPlan(proPlan);\n    count++;\n    console.log(`Successfully initialized ${count} default plans in the database.`);\n    return count;\n  } catch (error) {\n    console.error(\"Error initializing plans in database:\", error);\n    throw error;\n  }\n}\nasync function syncPlan(plan) {\n  const existingPlan = await prisma.plan.findUnique({\n    where: { id: plan.id }\n  });\n  if (existingPlan) {\n    const updatedPlan = await prisma.plan.update({\n      where: { id: plan.id },\n      data: {\n        name: plan.name,\n        description: plan.description,\n        section: plan.section,\n        monthlyPrice: plan.monthlyPrice,\n        annualPrice: plan.annualPrice,\n        stripePriceMonthlyId: plan.stripePriceMonthlyId,\n        stripePriceYearlyId: plan.stripePriceYearlyId,\n        popular: plan.popular || false\n      }\n    });\n    for (const feature of plan.features) {\n      await syncPlanFeature(feature, plan.id);\n    }\n    return updatedPlan;\n  } else {\n    const newPlan = await prisma.plan.create({\n      data: {\n        id: plan.id,\n        name: plan.name,\n        description: plan.description,\n        section: plan.section,\n        monthlyPrice: plan.monthlyPrice,\n        annualPrice: plan.annualPrice,\n        stripePriceMonthlyId: plan.stripePriceMonthlyId,\n        stripePriceYearlyId: plan.stripePriceYearlyId,\n        popular: plan.popular || false,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    for (const feature of plan.features) {\n      await syncPlanFeature(feature, plan.id);\n    }\n    return newPlan;\n  }\n}\nasync function syncFeature(feature) {\n  const existingFeature = await prisma.feature.findUnique({\n    where: { id: feature.id }\n  });\n  if (existingFeature) {\n    return await prisma.feature.update({\n      where: { id: feature.id },\n      data: {\n        name: feature.name,\n        description: feature.description,\n        category: feature.category,\n        icon: feature.icon,\n        beta: feature.beta || false,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n  } else {\n    const newFeature = await prisma.feature.create({\n      data: {\n        id: feature.id,\n        name: feature.name,\n        description: feature.description,\n        category: feature.category,\n        icon: feature.icon,\n        beta: feature.beta || false,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    if (feature.limits) {\n      for (const limit of feature.limits) {\n        await syncFeatureLimit(limit, newFeature.id);\n      }\n    }\n    return newFeature;\n  }\n}\nasync function syncPlanFeature(feature, planId) {\n  const existingFeature = await prisma.planFeature.findFirst({\n    where: {\n      planId,\n      featureId: feature.featureId\n    }\n  });\n  if (existingFeature) {\n    const updatedFeature = await prisma.planFeature.update({\n      where: { id: existingFeature.id },\n      data: {\n        accessLevel: feature.accessLevel\n      }\n    });\n    if (feature.limits) {\n      for (const limit of feature.limits) {\n        await syncPlanFeatureLimit(limit, updatedFeature.id);\n      }\n    }\n    return updatedFeature;\n  } else {\n    const newFeature = await prisma.planFeature.create({\n      data: {\n        id: `${planId}_${feature.featureId}`,\n        planId,\n        featureId: feature.featureId,\n        accessLevel: feature.accessLevel,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n    if (feature.limits) {\n      for (const limit of feature.limits) {\n        await syncPlanFeatureLimit(limit, newFeature.id);\n      }\n    }\n    return newFeature;\n  }\n}\nasync function syncFeatureLimit(limit, featureId) {\n  const existingLimit = await prisma.featureLimit.findUnique({\n    where: { id: limit.id }\n  });\n  if (existingLimit) {\n    return await prisma.featureLimit.update({\n      where: { id: limit.id },\n      data: {\n        name: limit.name,\n        description: limit.description,\n        defaultValue: limit.defaultValue.toString(),\n        type: limit.type,\n        unit: limit.unit,\n        resetDay: limit.resetDay\n      }\n    });\n  } else {\n    return await prisma.featureLimit.create({\n      data: {\n        id: limit.id,\n        featureId,\n        name: limit.name,\n        description: limit.description,\n        defaultValue: limit.defaultValue.toString(),\n        type: limit.type,\n        unit: limit.unit,\n        resetDay: limit.resetDay,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n  }\n}\nasync function syncPlanFeatureLimit(limit, planFeatureId) {\n  const existingLimit = await prisma.planFeatureLimit.findFirst({\n    where: {\n      planFeatureId,\n      limitId: limit.limitId\n    }\n  });\n  if (existingLimit) {\n    return await prisma.planFeatureLimit.update({\n      where: { id: existingLimit.id },\n      data: {\n        value: limit.value.toString()\n      }\n    });\n  } else {\n    return await prisma.planFeatureLimit.create({\n      data: {\n        id: `${planFeatureId}_${limit.limitId}`,\n        planFeatureId,\n        limitId: limit.limitId,\n        value: limit.value.toString(),\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n  }\n}\nasync function syncPlanWithStripeAndUpdateDb(planId) {\n  const plan = await getPlanById(planId);\n  if (!plan) return null;\n  if (plan.monthlyPrice === 0 && plan.annualPrice === 0) {\n    return plan;\n  }\n  try {\n    const updatedPlan = await syncPlanWithStripe(plan);\n    await prisma.plan.update({\n      where: { id: planId },\n      data: {\n        stripePriceMonthlyId: updatedPlan.stripePriceMonthlyId,\n        stripePriceYearlyId: updatedPlan.stripePriceYearlyId\n      }\n    });\n    return updatedPlan;\n  } catch (error) {\n    console.error(`Error syncing plan ${planId} with Stripe:`, error);\n    return plan;\n  }\n}\nexport {\n  syncPlanWithStripeAndUpdateDb as a,\n  getPlanById as b,\n  getPlansFromDatabase as g,\n  initializePlansInDatabase as i,\n  syncPlan as s\n};\n"], "names": [], "mappings": ";;;;AAGA,eAAe,oBAAoB,GAAG;AACtC,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC/C,MAAM,OAAO,EAAE;AACf,QAAQ,YAAY,EAAE;AACtB,OAAO;AACP,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,MAAM,OAAO,CAAC,GAAG,CAAC,oEAAoE,CAAC;AACvF,MAAM,MAAM,yBAAyB,EAAE;AACvC,MAAM,OAAO,oBAAoB,EAAE;AACnC;AACA,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;AACxC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,EAAE;AACb;AACA;AACA,eAAe,WAAW,CAAC,MAAM,EAAE;AACnC,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAChD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE;AACnB,YAAY,MAAM,EAAE;AACpB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,OAAO,gBAAgB,CAAC,MAAM,CAAC;AACnC,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AACzD,IAAI,OAAO,IAAI;AACf;AACA;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE;AAClC,EAAE,OAAO;AACT,IAAI,EAAE,EAAE,MAAM,CAAC,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI;AACrB,IAAI,WAAW,EAAE,MAAM,CAAC,WAAW;AACnC,IAAI,OAAO,EAAE,MAAM,CAAC,OAAO;AAC3B,IAAI,YAAY,EAAE,MAAM,CAAC,YAAY;AACrC,IAAI,WAAW,EAAE,MAAM,CAAC,WAAW;AACnC,IAAI,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;AACrD,IAAI,mBAAmB,EAAE,MAAM,CAAC,mBAAmB;AACnD,IAAI,OAAO,EAAE,MAAM,CAAC,OAAO;AAC3B,IAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AAChD,MAAM,SAAS,EAAE,OAAO,CAAC,SAAS;AAClC,MAAM,WAAW,EAAE,OAAO,CAAC,WAAW;AACtC,MAAM,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,KAAK,MAAM;AAC9C,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK,KAAK,WAAW,GAAG,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE;AACnF,OAAO,CAAC;AACR,KAAK,CAAC;AACN,GAAG;AACH;AACA,eAAe,yBAAyB,GAAG;AAC3C,EAAE,IAAI;AACN,IAAI,MAAM,kBAAkB,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE;AACxD,IAAI,IAAI,kBAAkB,GAAG,CAAC,EAAE;AAChC,MAAM,OAAO,CAAC,GAAG;AACjB,QAAQ,CAAC,EAAE,kBAAkB,CAAC,8DAA8D;AAC5F,OAAO;AACP,MAAM,OAAO,CAAC;AACd;AACA,IAAI,OAAO,CAAC,GAAG,CAAC,oEAAoE,CAAC;AACrF,IAAI,MAAM,eAAe,GAAG;AAC5B,MAAM;AACN,QAAQ,EAAE,EAAE,WAAW;AACvB,QAAQ,IAAI,EAAE,WAAW;AACzB,QAAQ,WAAW,EAAE,8BAA8B;AACnD,QAAQ,QAAQ,EAAE,eAAe,CAAC,IAAI;AACtC,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,SAAS;AACrB,QAAQ,IAAI,EAAE,cAAc;AAC5B,QAAQ,WAAW,EAAE,yBAAyB;AAC9C,QAAQ,QAAQ,EAAE,eAAe,CAAC,IAAI;AACtC,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,gBAAgB;AAC5B,QAAQ,IAAI,EAAE,gBAAgB;AAC9B,QAAQ,WAAW,EAAE,0BAA0B;AAC/C,QAAQ,QAAQ,EAAE,eAAe,CAAC,MAAM;AACxC,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,MAAM,EAAE;AAChB,UAAU;AACV,YAAY,EAAE,EAAE,wBAAwB;AACxC,YAAY,IAAI,EAAE,wBAAwB;AAC1C,YAAY,WAAW,EAAE,0CAA0C;AACnE,YAAY,YAAY,EAAE,CAAC;AAC3B,YAAY,IAAI,EAAE,SAAS,CAAC,OAAO;AACnC,YAAY,IAAI,EAAE,OAAO;AACzB,YAAY,QAAQ,EAAE;AACtB;AACA;AACA,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,gBAAgB;AAC5B,QAAQ,IAAI,EAAE,gBAAgB;AAC9B,QAAQ,WAAW,EAAE,yBAAyB;AAC9C,QAAQ,QAAQ,EAAE,eAAe,CAAC,MAAM;AACxC,QAAQ,IAAI,EAAE,WAAW;AACzB,QAAQ,MAAM,EAAE;AAChB,UAAU;AACV,YAAY,EAAE,EAAE,iBAAiB;AACjC,YAAY,IAAI,EAAE,iBAAiB;AACnC,YAAY,WAAW,EAAE,mCAAmC;AAC5D,YAAY,YAAY,EAAE,CAAC;AAC3B,YAAY,IAAI,EAAE,SAAS,CAAC,KAAK;AACjC,YAAY,IAAI,EAAE;AAClB,WAAW;AACX,UAAU;AACV,YAAY,EAAE,EAAE,kBAAkB;AAClC,YAAY,IAAI,EAAE,kBAAkB;AACpC,YAAY,WAAW,EAAE,4BAA4B;AACrD,YAAY,YAAY,EAAE,CAAC;AAC3B,YAAY,IAAI,EAAE,SAAS,CAAC,KAAK;AACjC,YAAY,IAAI,EAAE;AAClB;AACA;AACA,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,WAAW;AACvB,QAAQ,IAAI,EAAE,WAAW;AACzB,QAAQ,WAAW,EAAE,gCAAgC;AACrD,QAAQ,QAAQ,EAAE,eAAe,CAAC,QAAQ;AAC1C,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,qBAAqB;AACjC,QAAQ,IAAI,EAAE,qBAAqB;AACnC,QAAQ,WAAW,EAAE,4BAA4B;AACjD,QAAQ,QAAQ,EAAE,eAAe,CAAC,SAAS;AAC3C,QAAQ,IAAI,EAAE,QAAQ;AACtB,QAAQ,MAAM,EAAE;AAChB,UAAU;AACV,YAAY,EAAE,EAAE,2BAA2B;AAC3C,YAAY,IAAI,EAAE,2BAA2B;AAC7C,YAAY,WAAW,EAAE,uCAAuC;AAChE,YAAY,YAAY,EAAE,CAAC;AAC3B,YAAY,IAAI,EAAE,SAAS,CAAC,KAAK;AACjC,YAAY,IAAI,EAAE;AAClB;AACA;AACA,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,qBAAqB;AACjC,QAAQ,IAAI,EAAE,qBAAqB;AACnC,QAAQ,WAAW,EAAE,wBAAwB;AAC7C,QAAQ,QAAQ,EAAE,eAAe,CAAC,YAAY;AAC9C,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI,KAAK,MAAM,OAAO,IAAI,eAAe,EAAE;AAC3C,MAAM,MAAM,WAAW,CAAC,OAAO,CAAC;AAChC;AACA,IAAI,IAAI,KAAK,GAAG,CAAC;AACjB,IAAI,MAAM,QAAQ,GAAG;AACrB,MAAM,EAAE,EAAE,MAAM;AAChB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,WAAW,EAAE,iCAAiC;AACpD,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,YAAY,EAAE,CAAC;AACrB,MAAM,WAAW,EAAE,CAAC;AACpB,MAAM,QAAQ,EAAE;AAChB;AACA,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC5E,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC1E,QAAQ;AACR,UAAU,SAAS,EAAE,gBAAgB;AACrC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,KAAK,EAAE,CAAC,EAAE;AAClE,SAAS;AACT,QAAQ;AACR,UAAU,SAAS,EAAE,gBAAgB;AACrC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC,EAAE;AACpD,YAAY,EAAE,OAAO,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC;AACnD;AACA,SAAS;AACT,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE;AAC/E,QAAQ;AACR,UAAU,SAAS,EAAE,kBAAkB;AACvC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,EAAE;AAC7D;AACA;AACA,KAAK;AACL,IAAI,MAAM,QAAQ,CAAC,QAAQ,CAAC;AAC5B,IAAI,KAAK,EAAE;AACX,IAAI,MAAM,SAAS,GAAG;AACtB,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,WAAW,EAAE,oCAAoC;AACvD,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,YAAY,EAAE,GAAG;AACvB,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC5E,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC1E,QAAQ;AACR,UAAU,SAAS,EAAE,gBAAgB;AACrC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,KAAK,EAAE,EAAE,EAAE;AACnE,SAAS;AACT,QAAQ;AACR,UAAU,SAAS,EAAE,gBAAgB;AACrC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,CAAC,EAAE;AACpD,YAAY,EAAE,OAAO,EAAE,kBAAkB,EAAE,KAAK,EAAE,EAAE;AACpD;AACA,SAAS;AACT,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE;AAC/E,QAAQ;AACR,UAAU,SAAS,EAAE,kBAAkB;AACvC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,CAAC,EAAE;AAC7D,SAAS;AACT,QAAQ;AACR,UAAU,SAAS,EAAE,qBAAqB;AAC1C,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,2BAA2B,EAAE,KAAK,EAAE,CAAC,EAAE;AACrE,SAAS;AACT,QAAQ,EAAE,SAAS,EAAE,qBAAqB,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ;AACpF;AACA,KAAK;AACL,IAAI,MAAM,QAAQ,CAAC,SAAS,CAAC;AAC7B,IAAI,KAAK,EAAE;AACX,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,EAAE,EAAE,KAAK;AACf,MAAM,IAAI,EAAE,cAAc;AAC1B,MAAM,WAAW,EAAE,qCAAqC;AACxD,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE;AAChB,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC5E,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC1E,QAAQ;AACR,UAAU,SAAS,EAAE,gBAAgB;AACrC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,wBAAwB,EAAE,KAAK,EAAE,EAAE,EAAE;AACnE,SAAS;AACT,QAAQ;AACR,UAAU,SAAS,EAAE,gBAAgB;AACrC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,EAAE,EAAE;AACrD,YAAY,EAAE,OAAO,EAAE,kBAAkB,EAAE,KAAK,EAAE,EAAE;AACpD;AACA,SAAS;AACT,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC5E,QAAQ;AACR,UAAU,SAAS,EAAE,kBAAkB;AACvC,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,KAAK,EAAE,EAAE,EAAE;AAC9D,SAAS;AACT,QAAQ;AACR,UAAU,SAAS,EAAE,qBAAqB;AAC1C,UAAU,WAAW,EAAE,kBAAkB,CAAC,OAAO;AACjD,UAAU,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,2BAA2B,EAAE,KAAK,EAAE,CAAC,EAAE;AACrE,SAAS;AACT,QAAQ,EAAE,SAAS,EAAE,qBAAqB,EAAE,WAAW,EAAE,kBAAkB,CAAC,QAAQ;AACpF;AACA,KAAK;AACL,IAAI,MAAM,QAAQ,CAAC,OAAO,CAAC;AAC3B,IAAI,KAAK,EAAE;AACX,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,yBAAyB,EAAE,KAAK,CAAC,+BAA+B,CAAC,CAAC;AACnF,IAAI,OAAO,KAAK;AAChB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC;AACjE,IAAI,MAAM,KAAK;AACf;AACA;AACA,eAAe,QAAQ,CAAC,IAAI,EAAE;AAC9B,EAAE,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACpD,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE;AACxB,GAAG,CAAC;AACJ,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AACjD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;AAC5B,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW;AACrC,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO;AAC7B,QAAQ,YAAY,EAAE,IAAI,CAAC,YAAY;AACvC,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW;AACrC,QAAQ,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;AACvD,QAAQ,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;AACrD,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI;AACjC;AACA,KAAK,CAAC;AACN,IAAI,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AACzC,MAAM,MAAM,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;AAC7C;AACA,IAAI,OAAO,WAAW;AACtB,GAAG,MAAM;AACT,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7C,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,IAAI,CAAC,EAAE;AACnB,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW;AACrC,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO;AAC7B,QAAQ,YAAY,EAAE,IAAI,CAAC,YAAY;AACvC,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW;AACrC,QAAQ,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;AACvD,QAAQ,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;AACrD,QAAQ,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,KAAK;AACtC,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE;AACzC,MAAM,MAAM,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC;AAC7C;AACA,IAAI,OAAO,OAAO;AAClB;AACA;AACA,eAAe,WAAW,CAAC,OAAO,EAAE;AACpC,EAAE,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AAC1D,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE;AAC3B,GAAG,CAAC;AACJ,EAAE,IAAI,eAAe,EAAE;AACvB,IAAI,OAAO,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACvC,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,EAAE,EAAE;AAC/B,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,WAAW,EAAE,OAAO,CAAC,WAAW;AACxC,QAAQ,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAClC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;AACnC,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACnD,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,OAAO,CAAC,EAAE;AACtB,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,WAAW,EAAE,OAAO,CAAC,WAAW;AACxC,QAAQ,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAClC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,KAAK;AACnC,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;AACxB,MAAM,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC1C,QAAQ,MAAM,gBAAgB,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;AACpD;AACA;AACA,IAAI,OAAO,UAAU;AACrB;AACA;AACA,eAAe,eAAe,CAAC,OAAO,EAAE,MAAM,EAAE;AAChD,EAAE,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,SAAS,CAAC;AAC7D,IAAI,KAAK,EAAE;AACX,MAAM,MAAM;AACZ,MAAM,SAAS,EAAE,OAAO,CAAC;AACzB;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,eAAe,EAAE;AACvB,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AAC3D,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;AACvC,MAAM,IAAI,EAAE;AACZ,QAAQ,WAAW,EAAE,OAAO,CAAC;AAC7B;AACA,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;AACxB,MAAM,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC1C,QAAQ,MAAM,oBAAoB,CAAC,KAAK,EAAE,cAAc,CAAC,EAAE,CAAC;AAC5D;AACA;AACA,IAAI,OAAO,cAAc;AACzB,GAAG,MAAM;AACT,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC;AACvD,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;AAC5C,QAAQ,MAAM;AACd,QAAQ,SAAS,EAAE,OAAO,CAAC,SAAS;AACpC,QAAQ,WAAW,EAAE,OAAO,CAAC,WAAW;AACxC,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;AACxB,MAAM,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC1C,QAAQ,MAAM,oBAAoB,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;AACxD;AACA;AACA,IAAI,OAAO,UAAU;AACrB;AACA;AACA,eAAe,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE;AAClD,EAAE,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AAC7D,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE;AACzB,GAAG,CAAC;AACJ,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,OAAO,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC5C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE;AAC7B,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,WAAW,EAAE,KAAK,CAAC,WAAW;AACtC,QAAQ,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE;AACnD,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,QAAQ,EAAE,KAAK,CAAC;AACxB;AACA,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,OAAO,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC5C,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,KAAK,CAAC,EAAE;AACpB,QAAQ,SAAS;AACjB,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,WAAW,EAAE,KAAK,CAAC,WAAW;AACtC,QAAQ,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE;AACnD,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAChC,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN;AACA;AACA,eAAe,oBAAoB,CAAC,KAAK,EAAE,aAAa,EAAE;AAC1D,EAAE,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;AAChE,IAAI,KAAK,EAAE;AACX,MAAM,aAAa;AACnB,MAAM,OAAO,EAAE,KAAK,CAAC;AACrB;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,OAAO,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAChD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE;AACrC,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ;AACnC;AACA,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,OAAO,MAAM,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;AAChD,MAAM,IAAI,EAAE;AACZ,QAAQ,EAAE,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/C,QAAQ,aAAa;AACrB,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE;AACrC,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN;AACA;AACA,eAAe,6BAA6B,CAAC,MAAM,EAAE;AACrD,EAAE,MAAM,IAAI,GAAG,MAAM,WAAW,CAAC,MAAM,CAAC;AACxC,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI;AACxB,EAAE,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;AACzD,IAAI,OAAO,IAAI;AACf;AACA,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,CAAC,IAAI,CAAC;AACtD,IAAI,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC7B,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,IAAI,EAAE;AACZ,QAAQ,oBAAoB,EAAE,WAAW,CAAC,oBAAoB;AAC9D,QAAQ,mBAAmB,EAAE,WAAW,CAAC;AACzC;AACA,KAAK,CAAC;AACN,IAAI,OAAO,WAAW;AACtB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,mBAAmB,EAAE,MAAM,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC;AACrE,IAAI,OAAO,IAAI;AACf;AACA;;;;"}