{"version": 3, "file": "index6-D2_psKnf.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/index6.js"], "sourcesContent": ["import { J as derived, w as push, N as bind_props, y as pop, O as copy_payload, P as assign_payload, Q as spread_props, M as spread_attributes } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { b as getDocument, F as FocusScopeContext, f as focusFirst, C as CustomEventDispatcher, P as Portal } from \"./scroll-lock.js\";\nimport { b as box, w as watch } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { u as useId } from \"./use-id.js\";\nimport { n as noop } from \"./noop.js\";\nimport { F as Floating_layer, P as Popper_layer_force_mount, a as Popper_layer, g as getFloatingContentCSSVars, b as Floating_layer_anchor } from \"./popper-layer-force-mount.js\";\nimport { u as useDOMTypeahead, M as Mounted } from \"./mounted.js\";\nimport \"clsx\";\nimport { a as afterTick } from \"./after-tick.js\";\nimport { C as Context } from \"./context.js\";\nimport { b as ARROW_DOWN, P as PAGE_UP, H as HOME, f as ARROW_UP, h as PAGE_DOWN, E as END, i as ENTER, S as SPACE, a as ARROW_RIGHT, A as ARROW_LEFT, T as TAB, d as getDataOpenClosed, j as getAriaOrientation, e as getDataDisabled, k as getAriaDisabled, c as getAriaExpanded } from \"./kbd-constants.js\";\nimport { a as isHTMLElement, i as isElement, b as isElementOrSVGElement } from \"./is.js\";\nimport { u as useRovingFocus } from \"./use-roving-focus.svelte.js\";\nimport { u as useGraceArea } from \"./use-grace-area.svelte.js\";\nimport { isTabbable, tabbable, isFocusable, focusable } from \"tabbable\";\nconst SELECTION_KEYS = [ENTER, SPACE];\nconst FIRST_KEYS = [ARROW_DOWN, PAGE_UP, HOME];\nconst LAST_KEYS = [ARROW_UP, PAGE_DOWN, END];\nconst FIRST_LAST_KEYS = [...FIRST_KEYS, ...LAST_KEYS];\nconst SUB_OPEN_KEYS = {\n  ltr: [...SELECTION_KEYS, ARROW_RIGHT],\n  rtl: [...SELECTION_KEYS, ARROW_LEFT]\n};\nconst SUB_CLOSE_KEYS = {\n  ltr: [ARROW_LEFT],\n  rtl: [ARROW_RIGHT]\n};\nfunction isMouseEvent(event) {\n  return event.pointerType === \"mouse\";\n}\nfunction getTabbableOptions() {\n  return {\n    getShadowRoot: true,\n    displayCheck: (\n      // JSDOM does not support the `tabbable` library. To solve this we can\n      // check if `ResizeObserver` is a real function (not polyfilled), which\n      // determines if the current environment is JSDOM-like.\n      typeof ResizeObserver === \"function\" && ResizeObserver.toString().includes(\"[native code]\") ? \"full\" : \"none\"\n    )\n  };\n}\nfunction getTabbableFrom(currentNode, direction) {\n  if (!isTabbable(currentNode, getTabbableOptions())) {\n    return getTabbableFromFocusable(currentNode, direction);\n  }\n  const allTabbable = tabbable(getDocument(currentNode).body, getTabbableOptions());\n  if (direction === \"prev\")\n    allTabbable.reverse();\n  const activeIndex = allTabbable.indexOf(currentNode);\n  if (activeIndex === -1)\n    return document.body;\n  const nextTabbableElements = allTabbable.slice(activeIndex + 1);\n  return nextTabbableElements[0];\n}\nfunction getTabbableFromFocusable(currentNode, direction) {\n  if (!isFocusable(currentNode, getTabbableOptions()))\n    return document.body;\n  const allFocusable = focusable(getDocument(currentNode).body, getTabbableOptions());\n  if (direction === \"prev\")\n    allFocusable.reverse();\n  const activeIndex = allFocusable.indexOf(currentNode);\n  if (activeIndex === -1)\n    return document.body;\n  const nextFocusableElements = allFocusable.slice(activeIndex + 1);\n  return nextFocusableElements.find((node) => isTabbable(node, getTabbableOptions())) ?? document.body;\n}\nconst MenuRootContext = new Context(\"Menu.Root\");\nconst MenuMenuContext = new Context(\"Menu.Root | Menu.Sub\");\nconst MenuContentContext = new Context(\"Menu.Content\");\nconst MenuGroupContext = new Context(\"Menu.Group | Menu.RadioGroup\");\nconst MenuOpenEvent = new CustomEventDispatcher(\"bitsmenuopen\", { bubbles: false, cancelable: true });\nclass MenuRootState {\n  opts;\n  isUsingKeyboard = new IsUsingKeyboard();\n  ignoreCloseAutoFocus = false;\n  isPointerInTransit = false;\n  constructor(opts) {\n    this.opts = opts;\n  }\n  getAttr(name) {\n    return `data-${this.opts.variant.current}-${name}`;\n  }\n}\nclass MenuMenuState {\n  opts;\n  root;\n  parentMenu;\n  contentId = box.with(() => \"\");\n  contentNode = null;\n  triggerNode = null;\n  constructor(opts, root, parentMenu) {\n    this.opts = opts;\n    this.root = root;\n    this.parentMenu = parentMenu;\n    if (parentMenu) {\n      watch(() => parentMenu.opts.open.current, () => {\n        if (parentMenu.opts.open.current) return;\n        this.opts.open.current = false;\n      });\n    }\n  }\n  toggleOpen() {\n    this.opts.open.current = !this.opts.open.current;\n  }\n  onOpen() {\n    this.opts.open.current = true;\n  }\n  onClose() {\n    this.opts.open.current = false;\n  }\n}\nclass MenuContentState {\n  opts;\n  parentMenu;\n  search = \"\";\n  #timer = 0;\n  #handleTypeaheadSearch;\n  rovingFocusGroup;\n  mounted = false;\n  #isSub;\n  constructor(opts, parentMenu) {\n    this.opts = opts;\n    this.parentMenu = parentMenu;\n    parentMenu.contentId = opts.id;\n    this.#isSub = opts.isSub ?? false;\n    this.onkeydown = this.onkeydown.bind(this);\n    this.onblur = this.onblur.bind(this);\n    this.onfocus = this.onfocus.bind(this);\n    this.handleInteractOutside = this.handleInteractOutside.bind(this);\n    useRefById({\n      ...opts,\n      deps: () => this.parentMenu.opts.open.current,\n      onRefChange: (node) => {\n        if (this.parentMenu.contentNode !== node) {\n          this.parentMenu.contentNode = node;\n        }\n      }\n    });\n    useGraceArea({\n      contentNode: () => this.parentMenu.contentNode,\n      triggerNode: () => this.parentMenu.triggerNode,\n      enabled: () => this.parentMenu.opts.open.current && Boolean(this.parentMenu.triggerNode?.hasAttribute(this.parentMenu.root.getAttr(\"sub-trigger\"))),\n      onPointerExit: () => {\n        this.parentMenu.opts.open.current = false;\n      },\n      setIsPointerInTransit: (value) => {\n        this.parentMenu.root.isPointerInTransit = value;\n      }\n    });\n    this.#handleTypeaheadSearch = useDOMTypeahead().handleTypeaheadSearch;\n    this.rovingFocusGroup = useRovingFocus({\n      rootNodeId: this.parentMenu.contentId,\n      candidateAttr: this.parentMenu.root.getAttr(\"item\"),\n      loop: this.opts.loop,\n      orientation: box.with(() => \"vertical\")\n    });\n    watch(() => this.parentMenu.contentNode, (contentNode) => {\n      if (!contentNode) return;\n      const handler = () => {\n        afterTick(() => {\n          if (!this.parentMenu.root.isUsingKeyboard.current) return;\n          this.rovingFocusGroup.focusFirstCandidate();\n        });\n      };\n      return MenuOpenEvent.listen(contentNode, handler);\n    });\n  }\n  #getCandidateNodes() {\n    const node = this.parentMenu.contentNode;\n    if (!node) return [];\n    const candidates = Array.from(node.querySelectorAll(`[${this.parentMenu.root.getAttr(\"item\")}]:not([data-disabled])`));\n    return candidates;\n  }\n  #isPointerMovingToSubmenu() {\n    return this.parentMenu.root.isPointerInTransit;\n  }\n  onCloseAutoFocus = (e) => {\n    this.opts.onCloseAutoFocus.current(e);\n    if (e.defaultPrevented || this.#isSub) return;\n    if (this.parentMenu.triggerNode && isTabbable(this.parentMenu.triggerNode)) {\n      this.parentMenu.triggerNode.focus();\n    }\n  };\n  handleTabKeyDown(e) {\n    let rootMenu = this.parentMenu;\n    while (rootMenu.parentMenu !== null) {\n      rootMenu = rootMenu.parentMenu;\n    }\n    if (!rootMenu.triggerNode) {\n      return;\n    }\n    e.preventDefault();\n    const nodeToFocus = getTabbableFrom(rootMenu.triggerNode, e.shiftKey ? \"prev\" : \"next\");\n    if (nodeToFocus) {\n      this.parentMenu.root.ignoreCloseAutoFocus = true;\n      rootMenu.onClose();\n      afterTick(() => {\n        nodeToFocus.focus();\n        afterTick(() => {\n          this.parentMenu.root.ignoreCloseAutoFocus = false;\n        });\n      });\n    } else {\n      document.body.focus();\n    }\n  }\n  onkeydown(e) {\n    if (e.defaultPrevented) return;\n    if (e.key === TAB) {\n      this.handleTabKeyDown(e);\n      return;\n    }\n    const target = e.target;\n    const currentTarget = e.currentTarget;\n    if (!isHTMLElement(target) || !isHTMLElement(currentTarget)) return;\n    const isKeydownInside = target.closest(`[${this.parentMenu.root.getAttr(\"content\")}]`)?.id === this.parentMenu.contentId.current;\n    const isModifierKey = e.ctrlKey || e.altKey || e.metaKey;\n    const isCharacterKey = e.key.length === 1;\n    const kbdFocusedEl = this.rovingFocusGroup.handleKeydown(target, e);\n    if (kbdFocusedEl) return;\n    if (e.code === \"Space\") return;\n    const candidateNodes = this.#getCandidateNodes();\n    if (isKeydownInside) {\n      if (!isModifierKey && isCharacterKey) {\n        this.#handleTypeaheadSearch(e.key, candidateNodes);\n      }\n    }\n    if (e.target?.id !== this.parentMenu.contentId.current) return;\n    if (!FIRST_LAST_KEYS.includes(e.key)) return;\n    e.preventDefault();\n    if (LAST_KEYS.includes(e.key)) {\n      candidateNodes.reverse();\n    }\n    focusFirst(candidateNodes);\n  }\n  onblur(e) {\n    if (!isElement(e.currentTarget)) return;\n    if (!isElement(e.target)) return;\n    if (!e.currentTarget.contains?.(e.target)) {\n      window.clearTimeout(this.#timer);\n      this.search = \"\";\n    }\n  }\n  onfocus(_) {\n    if (!this.parentMenu.root.isUsingKeyboard.current) return;\n    afterTick(() => this.rovingFocusGroup.focusFirstCandidate());\n  }\n  onItemEnter() {\n    return this.#isPointerMovingToSubmenu();\n  }\n  onItemLeave(e) {\n    if (e.currentTarget.hasAttribute(this.parentMenu.root.getAttr(\"sub-trigger\"))) return;\n    if (this.#isPointerMovingToSubmenu() || this.parentMenu.root.isUsingKeyboard.current) return;\n    const contentNode = this.parentMenu.contentNode;\n    contentNode?.focus();\n    this.rovingFocusGroup.setCurrentTabStopId(\"\");\n  }\n  onTriggerLeave() {\n    if (this.#isPointerMovingToSubmenu()) return true;\n    return false;\n  }\n  onOpenAutoFocus = (e) => {\n    if (e.defaultPrevented) return;\n    e.preventDefault();\n    const contentNode = this.parentMenu.contentNode;\n    contentNode?.focus();\n  };\n  handleInteractOutside(e) {\n    if (!isElementOrSVGElement(e.target)) return;\n    const triggerId = this.parentMenu.triggerNode?.id;\n    if (e.target.id === triggerId) {\n      e.preventDefault();\n      return;\n    }\n    if (e.target.closest(`#${triggerId}`)) {\n      e.preventDefault();\n    }\n  }\n  #snippetProps = derived(() => ({ open: this.parentMenu.opts.open.current }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"menu\",\n    \"aria-orientation\": getAriaOrientation(\"vertical\"),\n    [this.parentMenu.root.getAttr(\"content\")]: \"\",\n    \"data-state\": getDataOpenClosed(this.parentMenu.opts.open.current),\n    onkeydown: this.onkeydown,\n    onblur: this.onblur,\n    onfocus: this.onfocus,\n    dir: this.parentMenu.root.opts.dir.current,\n    style: { pointerEvents: \"auto\" }\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n  popperProps = {\n    onCloseAutoFocus: (e) => this.onCloseAutoFocus(e)\n  };\n}\nclass MenuItemSharedState {\n  opts;\n  content;\n  #isFocused = false;\n  constructor(opts, content) {\n    this.opts = opts;\n    this.content = content;\n    this.onpointermove = this.onpointermove.bind(this);\n    this.onpointerleave = this.onpointerleave.bind(this);\n    this.onfocus = this.onfocus.bind(this);\n    this.onblur = this.onblur.bind(this);\n    useRefById({ ...opts, deps: () => this.content.mounted });\n  }\n  onpointermove(e) {\n    if (e.defaultPrevented) return;\n    if (!isMouseEvent(e)) return;\n    if (this.opts.disabled.current) {\n      this.content.onItemLeave(e);\n    } else {\n      const defaultPrevented = this.content.onItemEnter();\n      if (defaultPrevented) return;\n      const item = e.currentTarget;\n      if (!isHTMLElement(item)) return;\n      item.focus();\n    }\n  }\n  onpointerleave(e) {\n    if (e.defaultPrevented) return;\n    if (!isMouseEvent(e)) return;\n    this.content.onItemLeave(e);\n  }\n  onfocus(e) {\n    afterTick(() => {\n      if (e.defaultPrevented || this.opts.disabled.current) return;\n      this.#isFocused = true;\n    });\n  }\n  onblur(e) {\n    afterTick(() => {\n      if (e.defaultPrevented) return;\n      this.#isFocused = false;\n    });\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    tabindex: -1,\n    role: \"menuitem\",\n    \"aria-disabled\": getAriaDisabled(this.opts.disabled.current),\n    \"data-disabled\": getDataDisabled(this.opts.disabled.current),\n    \"data-highlighted\": this.#isFocused ? \"\" : void 0,\n    [this.content.parentMenu.root.getAttr(\"item\")]: \"\",\n    //\n    onpointermove: this.onpointermove,\n    onpointerleave: this.onpointerleave,\n    onfocus: this.onfocus,\n    onblur: this.onblur\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass MenuItemState {\n  opts;\n  item;\n  #isPointerDown = false;\n  root;\n  constructor(opts, item) {\n    this.opts = opts;\n    this.item = item;\n    this.root = item.content.parentMenu.root;\n    this.onkeydown = this.onkeydown.bind(this);\n    this.onclick = this.onclick.bind(this);\n    this.onpointerdown = this.onpointerdown.bind(this);\n    this.onpointerup = this.onpointerup.bind(this);\n  }\n  #handleSelect() {\n    if (this.item.opts.disabled.current) return;\n    const selectEvent = new CustomEvent(\"menuitemselect\", { bubbles: true, cancelable: true });\n    this.opts.onSelect.current(selectEvent);\n    afterTick(() => {\n      if (selectEvent.defaultPrevented) {\n        this.item.content.parentMenu.root.isUsingKeyboard.current = false;\n        return;\n      }\n      if (this.opts.closeOnSelect.current) {\n        this.item.content.parentMenu.root.opts.onClose();\n      }\n    });\n  }\n  onkeydown(e) {\n    const isTypingAhead = this.item.content.search !== \"\";\n    if (this.item.opts.disabled.current || isTypingAhead && e.key === SPACE) return;\n    if (SELECTION_KEYS.includes(e.key)) {\n      if (!isHTMLElement(e.currentTarget)) return;\n      e.currentTarget.click();\n      e.preventDefault();\n    }\n  }\n  onclick(_) {\n    if (this.item.opts.disabled.current) return;\n    this.#handleSelect();\n  }\n  onpointerup(e) {\n    if (e.defaultPrevented) return;\n    if (!this.#isPointerDown) {\n      if (!isHTMLElement(e.currentTarget)) return;\n      e.currentTarget?.click();\n    }\n  }\n  onpointerdown(_) {\n    this.#isPointerDown = true;\n  }\n  #props = derived(() => mergeProps(this.item.props, {\n    onclick: this.onclick,\n    onpointerdown: this.onpointerdown,\n    onpointerup: this.onpointerup,\n    onkeydown: this.onkeydown\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass MenuSubTriggerState {\n  opts;\n  item;\n  content;\n  submenu;\n  #openTimer = null;\n  constructor(opts, item, content, submenu) {\n    this.opts = opts;\n    this.item = item;\n    this.content = content;\n    this.submenu = submenu;\n    this.onpointerleave = this.onpointerleave.bind(this);\n    this.onpointermove = this.onpointermove.bind(this);\n    this.onkeydown = this.onkeydown.bind(this);\n    this.onclick = this.onclick.bind(this);\n    useRefById({\n      ...item.opts,\n      onRefChange: (node) => {\n        this.submenu.triggerNode = node;\n      }\n    });\n  }\n  #clearOpenTimer() {\n    if (this.#openTimer === null) return;\n    window.clearTimeout(this.#openTimer);\n    this.#openTimer = null;\n  }\n  onpointermove(e) {\n    if (!isMouseEvent(e)) return;\n    if (!this.item.opts.disabled.current && !this.submenu.opts.open.current && !this.#openTimer && !this.content.parentMenu.root.isPointerInTransit) {\n      this.#openTimer = window.setTimeout(\n        () => {\n          this.submenu.onOpen();\n          this.#clearOpenTimer();\n        },\n        100\n      );\n    }\n  }\n  onpointerleave(e) {\n    if (!isMouseEvent(e)) return;\n    this.#clearOpenTimer();\n  }\n  onkeydown(e) {\n    const isTypingAhead = this.content.search !== \"\";\n    if (this.item.opts.disabled.current || isTypingAhead && e.key === SPACE) return;\n    if (SUB_OPEN_KEYS[this.submenu.root.opts.dir.current].includes(e.key)) {\n      e.currentTarget.click();\n      e.preventDefault();\n    }\n  }\n  onclick(e) {\n    if (this.item.opts.disabled.current) return;\n    if (!isHTMLElement(e.currentTarget)) return;\n    e.currentTarget.focus();\n    const selectEvent = new CustomEvent(\"menusubtriggerselect\", { bubbles: true, cancelable: true });\n    this.opts.onSelect.current(selectEvent);\n    if (!this.submenu.opts.open.current) {\n      this.submenu.onOpen();\n      afterTick(() => {\n        const contentNode = this.submenu.contentNode;\n        if (!contentNode) return;\n        MenuOpenEvent.dispatch(contentNode);\n      });\n    }\n  }\n  #props = derived(() => mergeProps(\n    {\n      \"aria-haspopup\": \"menu\",\n      \"aria-expanded\": getAriaExpanded(this.submenu.opts.open.current),\n      \"data-state\": getDataOpenClosed(this.submenu.opts.open.current),\n      \"aria-controls\": this.submenu.opts.open.current ? this.submenu.contentId.current : void 0,\n      [this.submenu.root.getAttr(\"sub-trigger\")]: \"\",\n      onclick: this.onclick,\n      onpointermove: this.onpointermove,\n      onpointerleave: this.onpointerleave,\n      onkeydown: this.onkeydown\n    },\n    this.item.props\n  ));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass MenuGroupState {\n  opts;\n  root;\n  groupHeadingId = void 0;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(this.opts);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"group\",\n    \"aria-labelledby\": this.groupHeadingId,\n    [this.root.getAttr(\"group\")]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass MenuSeparatorState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"group\",\n    [this.root.getAttr(\"separator\")]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass DropdownMenuTriggerState {\n  opts;\n  parentMenu;\n  constructor(opts, parentMenu) {\n    this.opts = opts;\n    this.parentMenu = parentMenu;\n    this.onpointerdown = this.onpointerdown.bind(this);\n    this.onpointerup = this.onpointerup.bind(this);\n    this.onkeydown = this.onkeydown.bind(this);\n    useRefById({\n      ...opts,\n      onRefChange: (ref) => {\n        this.parentMenu.triggerNode = ref;\n      }\n    });\n  }\n  onpointerdown(e) {\n    if (this.opts.disabled.current) return;\n    if (e.pointerType === \"touch\") return e.preventDefault();\n    if (e.button === 0 && e.ctrlKey === false) {\n      this.parentMenu.toggleOpen();\n      if (!this.parentMenu.opts.open.current) e.preventDefault();\n    }\n  }\n  onpointerup(e) {\n    if (this.opts.disabled.current) return;\n    if (e.pointerType === \"touch\") {\n      e.preventDefault();\n      this.parentMenu.toggleOpen();\n    }\n  }\n  onkeydown(e) {\n    if (this.opts.disabled.current) return;\n    if (e.key === SPACE || e.key === ENTER) {\n      this.parentMenu.toggleOpen();\n      e.preventDefault();\n      return;\n    }\n    if (e.key === ARROW_DOWN) {\n      this.parentMenu.onOpen();\n      e.preventDefault();\n    }\n  }\n  #ariaControls = derived(() => {\n    if (this.parentMenu.opts.open.current && this.parentMenu.contentId.current) return this.parentMenu.contentId.current;\n    return void 0;\n  });\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    disabled: this.opts.disabled.current,\n    \"aria-haspopup\": \"menu\",\n    \"aria-expanded\": getAriaExpanded(this.parentMenu.opts.open.current),\n    \"aria-controls\": this.#ariaControls(),\n    \"data-disabled\": getDataDisabled(this.opts.disabled.current),\n    \"data-state\": getDataOpenClosed(this.parentMenu.opts.open.current),\n    [this.parentMenu.root.getAttr(\"trigger\")]: \"\",\n    //\n    onpointerdown: this.onpointerdown,\n    onpointerup: this.onpointerup,\n    onkeydown: this.onkeydown\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nfunction useMenuRoot(props) {\n  const root = new MenuRootState(props);\n  FocusScopeContext.set({\n    get ignoreCloseAutoFocus() {\n      return root.ignoreCloseAutoFocus;\n    }\n  });\n  return MenuRootContext.set(root);\n}\nfunction useMenuMenu(root, props) {\n  return MenuMenuContext.set(new MenuMenuState(props, root, null));\n}\nfunction useMenuSubmenu(props) {\n  const menu = MenuMenuContext.get();\n  return MenuMenuContext.set(new MenuMenuState(props, menu.root, menu));\n}\nfunction useMenuSubTrigger(props) {\n  const content = MenuContentContext.get();\n  const item = new MenuItemSharedState(props, content);\n  const submenu = MenuMenuContext.get();\n  return new MenuSubTriggerState(props, item, content, submenu);\n}\nfunction useMenuDropdownTrigger(props) {\n  return new DropdownMenuTriggerState(props, MenuMenuContext.get());\n}\nfunction useMenuContent(props) {\n  return MenuContentContext.set(new MenuContentState(props, MenuMenuContext.get()));\n}\nfunction useMenuItem(props) {\n  const item = new MenuItemSharedState(props, MenuContentContext.get());\n  return new MenuItemState(props, item);\n}\nfunction useMenuGroup(props) {\n  return MenuGroupContext.set(new MenuGroupState(props, MenuRootContext.get()));\n}\nfunction useMenuSeparator(props) {\n  return new MenuSeparatorState(props, MenuRootContext.get());\n}\nfunction Menu_sub($$payload, $$props) {\n  push();\n  let { open = false, onOpenChange = noop, children } = $$props;\n  useMenuSubmenu({\n    open: box.with(() => open, (v) => {\n      open = v;\n      onOpenChange?.(v);\n    })\n  });\n  Floating_layer($$payload, {\n    children: ($$payload2) => {\n      children?.($$payload2);\n      $$payload2.out += `<!---->`;\n    }\n  });\n  bind_props($$props, { open });\n  pop();\n}\nfunction Menu($$payload, $$props) {\n  push();\n  let {\n    open = false,\n    dir = \"ltr\",\n    onOpenChange = noop,\n    _internal_variant: variant = \"dropdown-menu\",\n    children\n  } = $$props;\n  const root = useMenuRoot({\n    variant: box.with(() => variant),\n    dir: box.with(() => dir),\n    onClose: () => {\n      open = false;\n      onOpenChange(false);\n    }\n  });\n  useMenuMenu(root, {\n    open: box.with(() => open, (v) => {\n      open = v;\n      onOpenChange(v);\n    })\n  });\n  Floating_layer($$payload, {\n    children: ($$payload2) => {\n      children?.($$payload2);\n      $$payload2.out += `<!---->`;\n    }\n  });\n  bind_props($$props, { open });\n  pop();\n}\nfunction Dropdown_menu_content$1($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    child,\n    children,\n    ref = null,\n    loop = true,\n    onInteractOutside = noop,\n    onEscapeKeydown = noop,\n    onCloseAutoFocus = noop,\n    forceMount = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const contentState = useMenuContent({\n    id: box.with(() => id),\n    loop: box.with(() => loop),\n    ref: box.with(() => ref, (v) => ref = v),\n    onCloseAutoFocus: box.with(() => onCloseAutoFocus)\n  });\n  const mergedProps = mergeProps(restProps, contentState.props);\n  function handleInteractOutside(e) {\n    contentState.handleInteractOutside(e);\n    if (e.defaultPrevented) return;\n    onInteractOutside(e);\n    if (e.defaultPrevented) return;\n    contentState.parentMenu.onClose();\n  }\n  function handleEscapeKeydown(e) {\n    onEscapeKeydown(e);\n    if (e.defaultPrevented) return;\n    contentState.parentMenu.onClose();\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    if (forceMount) {\n      $$payload2.out += \"<!--[-->\";\n      {\n        let popper = function($$payload3, { props, wrapperProps }) {\n          const finalProps = mergeProps(props, {\n            style: getFloatingContentCSSVars(\"dropdown-menu\")\n          });\n          if (child) {\n            $$payload3.out += \"<!--[-->\";\n            child($$payload3, {\n              props: finalProps,\n              wrapperProps,\n              ...contentState.snippetProps\n            });\n            $$payload3.out += `<!---->`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n            $$payload3.out += `<div${spread_attributes({ ...wrapperProps }, null)}><div${spread_attributes({ ...finalProps }, null)}>`;\n            children?.($$payload3);\n            $$payload3.out += `<!----></div></div>`;\n          }\n          $$payload3.out += `<!--]--> `;\n          Mounted($$payload3, {\n            get mounted() {\n              return contentState.mounted;\n            },\n            set mounted($$value) {\n              contentState.mounted = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out += `<!---->`;\n        };\n        Popper_layer_force_mount($$payload2, spread_props([\n          mergedProps,\n          contentState.popperProps,\n          {\n            enabled: contentState.parentMenu.opts.open.current,\n            onInteractOutside: handleInteractOutside,\n            onEscapeKeydown: handleEscapeKeydown,\n            trapFocus: true,\n            loop,\n            forceMount: true,\n            id,\n            popper,\n            $$slots: { popper: true }\n          }\n        ]));\n      }\n    } else if (!forceMount) {\n      $$payload2.out += \"<!--[1-->\";\n      {\n        let popper = function($$payload3, { props, wrapperProps }) {\n          const finalProps = mergeProps(props, {\n            style: getFloatingContentCSSVars(\"dropdown-menu\")\n          });\n          if (child) {\n            $$payload3.out += \"<!--[-->\";\n            child($$payload3, {\n              props: finalProps,\n              wrapperProps,\n              ...contentState.snippetProps\n            });\n            $$payload3.out += `<!---->`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n            $$payload3.out += `<div${spread_attributes({ ...wrapperProps }, null)}><div${spread_attributes({ ...finalProps }, null)}>`;\n            children?.($$payload3);\n            $$payload3.out += `<!----></div></div>`;\n          }\n          $$payload3.out += `<!--]--> `;\n          Mounted($$payload3, {\n            get mounted() {\n              return contentState.mounted;\n            },\n            set mounted($$value) {\n              contentState.mounted = $$value;\n              $$settled = false;\n            }\n          });\n          $$payload3.out += `<!---->`;\n        };\n        Popper_layer($$payload2, spread_props([\n          mergedProps,\n          contentState.popperProps,\n          {\n            present: contentState.parentMenu.opts.open.current,\n            onInteractOutside: handleInteractOutside,\n            onEscapeKeydown: handleEscapeKeydown,\n            trapFocus: true,\n            loop,\n            forceMount: false,\n            id,\n            popper,\n            $$slots: { popper: true }\n          }\n        ]));\n      }\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n    }\n    $$payload2.out += `<!--]-->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Menu_trigger($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    ref = null,\n    child,\n    children,\n    disabled = false,\n    type = \"button\",\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const triggerState = useMenuDropdownTrigger({\n    id: box.with(() => id),\n    disabled: box.with(() => disabled ?? false),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, triggerState.props, { type });\n  Floating_layer_anchor($$payload, {\n    id,\n    children: ($$payload2) => {\n      if (child) {\n        $$payload2.out += \"<!--[-->\";\n        child($$payload2, { props: mergedProps });\n        $$payload2.out += `<!---->`;\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n        $$payload2.out += `<button${spread_attributes({ ...mergedProps }, null)}>`;\n        children?.($$payload2);\n        $$payload2.out += `<!----></button>`;\n      }\n      $$payload2.out += `<!--]-->`;\n    }\n  });\n  bind_props($$props, { ref });\n  pop();\n}\nlet isUsingKeyboard = false;\nclass IsUsingKeyboard {\n  static _refs = 0;\n  // Reference counting to avoid multiple listeners.\n  static _cleanup;\n  constructor() {\n  }\n  get current() {\n    return isUsingKeyboard;\n  }\n  set current(value) {\n    isUsingKeyboard = value;\n  }\n}\nfunction Dropdown_menu_content($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    sideOffset = 4,\n    portalProps,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Portal($$payload2, spread_props([\n      portalProps,\n      {\n        children: ($$payload3) => {\n          $$payload3.out += `<!---->`;\n          Dropdown_menu_content$1($$payload3, spread_props([\n            {\n              \"data-slot\": \"dropdown-menu-content\",\n              sideOffset,\n              class: cn(\"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 max-h-(--radix-dropdown-menu-content-available-height) origin-(--radix-dropdown-menu-content-transform-origin) z-50 min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border p-1 shadow-md\", className)\n            },\n            restProps,\n            {\n              get ref() {\n                return ref;\n              },\n              set ref($$value) {\n                ref = $$value;\n                $$settled = false;\n              }\n            }\n          ]));\n          $$payload3.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Dropdown_menu_trigger($$payload, $$props) {\n  push();\n  let { ref = null, $$slots, $$events, ...restProps } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Menu_trigger($$payload2, spread_props([\n      { \"data-slot\": \"dropdown-menu-trigger\" },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nconst Sub = Menu_sub;\nconst Root = Menu;\nexport {\n  Dropdown_menu_trigger as D,\n  MenuOpenEvent as M,\n  Root as R,\n  SUB_CLOSE_KEYS as S,\n  Dropdown_menu_content as a,\n  useMenuGroup as b,\n  useMenuContent as c,\n  useMenuSubTrigger as d,\n  Sub as e,\n  useMenuSeparator as f,\n  useMenuItem as u\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAkBA,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;AACrC,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC;AAC9C,MAAM,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,GAAG,CAAC;AAC5C,MAAM,eAAe,GAAG,CAAC,GAAG,UAAU,EAAE,GAAG,SAAS,CAAC;AACrD,MAAM,aAAa,GAAG;AACtB,EAAE,GAAG,EAAE,CAAC,GAAG,cAAc,EAAE,WAAW,CAAC;AACvC,EAAE,GAAG,EAAE,CAAC,GAAG,cAAc,EAAE,UAAU;AACrC,CAAC;AACI,MAAC,cAAc,GAAG;AACvB,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC;AACnB,EAAE,GAAG,EAAE,CAAC,WAAW;AACnB;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,OAAO,KAAK,CAAC,WAAW,KAAK,OAAO;AACtC;AACA,SAAS,kBAAkB,GAAG;AAC9B,EAAE,OAAO;AACT,IAAI,aAAa,EAAE,IAAI;AACvB,IAAI,YAAY;AAChB;AACA;AACA;AACA,MAAM,OAAO,cAAc,KAAK,UAAU,IAAI,cAAc,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,MAAM,GAAG;AAC7G;AACA,GAAG;AACH;AACA,SAAS,eAAe,CAAC,WAAW,EAAE,SAAS,EAAE;AACjD,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,kBAAkB,EAAE,CAAC,EAAE;AACtD,IAAI,OAAO,wBAAwB,CAAC,WAAW,EAAE,SAAS,CAAC;AAC3D;AACA,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC;AACnF,EAAE,IAAI,SAAS,KAAK,MAAM;AAC1B,IAAI,WAAW,CAAC,OAAO,EAAE;AACzB,EAAE,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC;AACtD,EAAE,IAAI,WAAW,KAAK,EAAE;AACxB,IAAI,OAAO,QAAQ,CAAC,IAAI;AACxB,EAAE,MAAM,oBAAoB,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC;AACjE,EAAE,OAAO,oBAAoB,CAAC,CAAC,CAAC;AAChC;AACA,SAAS,wBAAwB,CAAC,WAAW,EAAE,SAAS,EAAE;AAC1D,EAAE,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,kBAAkB,EAAE,CAAC;AACrD,IAAI,OAAO,QAAQ,CAAC,IAAI;AACxB,EAAE,MAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC;AACrF,EAAE,IAAI,SAAS,KAAK,MAAM;AAC1B,IAAI,YAAY,CAAC,OAAO,EAAE;AAC1B,EAAE,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,WAAW,CAAC;AACvD,EAAE,IAAI,WAAW,KAAK,EAAE;AACxB,IAAI,OAAO,QAAQ,CAAC,IAAI;AACxB,EAAE,MAAM,qBAAqB,GAAG,YAAY,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,CAAC;AACnE,EAAE,OAAO,qBAAqB,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI;AACtG;AACA,MAAM,eAAe,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC;AAChD,MAAM,eAAe,GAAG,IAAI,OAAO,CAAC,sBAAsB,CAAC;AAC3D,MAAM,kBAAkB,GAAG,IAAI,OAAO,CAAC,cAAc,CAAC;AACtD,MAAM,gBAAgB,GAAG,IAAI,OAAO,CAAC,8BAA8B,CAAC;AAC/D,MAAC,aAAa,GAAG,IAAI,qBAAqB,CAAC,cAAc,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE;AACpG,MAAM,aAAa,CAAC;AACpB,EAAE,IAAI;AACN,EAAE,eAAe,GAAG,IAAI,eAAe,EAAE;AACzC,EAAE,oBAAoB,GAAG,KAAK;AAC9B,EAAE,kBAAkB,GAAG,KAAK;AAC5B,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB;AACA,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACtD;AACA;AACA,MAAM,aAAa,CAAC;AACpB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,UAAU;AACZ,EAAE,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAChC,EAAE,WAAW,GAAG,IAAI;AACpB,EAAE,WAAW,GAAG,IAAI;AACpB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;AACtC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU;AAChC,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,KAAK,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;AACtD,QAAQ,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC1C,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK;AACtC,OAAO,CAAC;AACR;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACpD;AACA,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI;AACjC;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK;AAClC;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,UAAU;AACZ,EAAE,MAAM,GAAG,EAAE;AACb,EAAE,MAAM,GAAG,CAAC;AACZ,EAAE,sBAAsB;AACxB,EAAE,gBAAgB;AAClB,EAAE,OAAO,GAAG,KAAK;AACjB,EAAE,MAAM;AACR,EAAE,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE;AAChC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU;AAChC,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,EAAE;AAClC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,KAAK;AACrC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AACxC,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC;AACtE,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACnD,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,KAAK,IAAI,EAAE;AAClD,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;AAC5C;AACA;AACA,KAAK,CAAC;AACN,IAAI,YAAY,CAAC;AACjB,MAAM,WAAW,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW;AACpD,MAAM,WAAW,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW;AACpD,MAAM,OAAO,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;AACzJ,MAAM,aAAa,EAAE,MAAM;AAC3B,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK;AACjD,OAAO;AACP,MAAM,qBAAqB,EAAE,CAAC,KAAK,KAAK;AACxC,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,GAAG,KAAK;AACvD;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,sBAAsB,GAAG,eAAe,EAAE,CAAC,qBAAqB;AACzE,IAAI,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC;AAC3C,MAAM,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,SAAS;AAC3C,MAAM,aAAa,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;AACzD,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;AAC1B,MAAM,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,UAAU;AAC5C,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,WAAW,KAAK;AAC9D,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,MAAM,MAAM,OAAO,GAAG,MAAM;AAC5B,QAAQ,SAAS,CAAC,MAAM;AACxB,UAAU,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;AAC7D,UAAU,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE;AACrD,SAAS,CAAC;AACV,OAAO;AACP,MAAM,OAAO,aAAa,CAAC,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC;AACvD,KAAK,CAAC;AACN;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW;AAC5C,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;AACxB,IAAI,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC;AAC1H,IAAI,OAAO,UAAU;AACrB;AACA,EAAE,yBAAyB,GAAG;AAC9B,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB;AAClD;AACA,EAAE,gBAAgB,GAAG,CAAC,CAAC,KAAK;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,CAAC,gBAAgB,IAAI,IAAI,CAAC,MAAM,EAAE;AAC3C,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;AAChF,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE;AACzC;AACA,GAAG;AACH,EAAE,gBAAgB,CAAC,CAAC,EAAE;AACtB,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU;AAClC,IAAI,OAAO,QAAQ,CAAC,UAAU,KAAK,IAAI,EAAE;AACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,UAAU;AACpC;AACA,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;AAC/B,MAAM;AACN;AACA,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,MAAM,WAAW,GAAG,eAAe,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;AAC3F,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,GAAG,IAAI;AACtD,MAAM,QAAQ,CAAC,OAAO,EAAE;AACxB,MAAM,SAAS,CAAC,MAAM;AACtB,QAAQ,WAAW,CAAC,KAAK,EAAE;AAC3B,QAAQ,SAAS,CAAC,MAAM;AACxB,UAAU,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,oBAAoB,GAAG,KAAK;AAC3D,SAAS,CAAC;AACV,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE;AAC3B;AACA;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;AACvB,MAAM,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAC9B,MAAM;AACN;AACA,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;AAC3B,IAAI,MAAM,aAAa,GAAG,CAAC,CAAC,aAAa;AACzC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;AACjE,IAAI,MAAM,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO;AACpI,IAAI,MAAM,aAAa,GAAG,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,OAAO;AAC5D,IAAI,MAAM,cAAc,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,KAAK,CAAC;AAC7C,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAC;AACvE,IAAI,IAAI,YAAY,EAAE;AACtB,IAAI,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,EAAE;AAC5B,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE;AACpD,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,IAAI,CAAC,aAAa,IAAI,cAAc,EAAE;AAC5C,QAAQ,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,GAAG,EAAE,cAAc,CAAC;AAC1D;AACA;AACA,IAAI,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE;AAC5D,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC1C,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACnC,MAAM,cAAc,CAAC,OAAO,EAAE;AAC9B;AACA,IAAI,UAAU,CAAC,cAAc,CAAC;AAC9B;AACA,EAAE,MAAM,CAAC,CAAC,EAAE;AACZ,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE;AACrC,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;AAC9B,IAAI,IAAI,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,EAAE;AAC/C,MAAM,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;AACtC,MAAM,IAAI,CAAC,MAAM,GAAG,EAAE;AACtB;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;AACvD,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC;AAChE;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,yBAAyB,EAAE;AAC3C;AACA,EAAE,WAAW,CAAC,CAAC,EAAE;AACjB,IAAI,IAAI,CAAC,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,EAAE;AACnF,IAAI,IAAI,IAAI,CAAC,yBAAyB,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;AAC1F,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW;AACnD,IAAI,WAAW,EAAE,KAAK,EAAE;AACxB,IAAI,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,EAAE,CAAC;AACjD;AACA,EAAE,cAAc,GAAG;AACnB,IAAI,IAAI,IAAI,CAAC,yBAAyB,EAAE,EAAE,OAAO,IAAI;AACrD,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,eAAe,GAAG,CAAC,CAAC,KAAK;AAC3B,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW;AACnD,IAAI,WAAW,EAAE,KAAK,EAAE;AACxB,GAAG;AACH,EAAE,qBAAqB,CAAC,CAAC,EAAE;AAC3B,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;AAC1C,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,EAAE;AACrD,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,KAAK,SAAS,EAAE;AACnC,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM;AACN;AACA,IAAI,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE;AAC3C,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB;AACA;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;AAC9E,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,UAAU,CAAC;AACtD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE;AACjD,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACtE,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AAC7B,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM;AACvB,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC9C,IAAI,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM;AAClC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,gBAAgB,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpD,GAAG;AACH;AACA,MAAM,mBAAmB,CAAC;AAC1B,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,UAAU,GAAG,KAAK;AACpB,EAAE,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE;AAC7B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO;AAC1B,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AACxD,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AACxC,IAAI,UAAU,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AAC7D;AACA,EAAE,aAAa,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AAC1B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AACjC,KAAK,MAAM;AACX,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;AACzD,MAAM,IAAI,gBAAgB,EAAE;AAC5B,MAAM,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa;AAClC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;AAChC,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB;AACA;AACA,EAAE,cAAc,CAAC,CAAC,EAAE;AACpB,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AAC1B,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;AAC/B;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,IAAI,CAAC,CAAC,gBAAgB,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AAC5D,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI;AAC5B,KAAK,CAAC;AACN;AACA,EAAE,MAAM,CAAC,CAAC,EAAE;AACZ,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC9B,MAAM,IAAI,CAAC,UAAU,GAAG,KAAK;AAC7B,KAAK,CAAC;AACN;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAChE,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAChE,IAAI,kBAAkB,EAAE,IAAI,CAAC,UAAU,GAAG,EAAE,GAAG,MAAM;AACrD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE;AACtD;AACA,IAAI,aAAa,EAAE,IAAI,CAAC,aAAa;AACrC,IAAI,cAAc,EAAE,IAAI,CAAC,cAAc;AACvC,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,MAAM,EAAE,IAAI,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,aAAa,CAAC;AACpB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,cAAc,GAAG,KAAK;AACxB,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI;AAC5C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AAClD;AACA,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACzC,IAAI,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,gBAAgB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AAC9F,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;AAC3C,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,IAAI,WAAW,CAAC,gBAAgB,EAAE;AACxC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,KAAK;AACzE,QAAQ;AACR;AACA,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;AAC3C,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACxD;AACA,KAAK,CAAC;AACN;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE;AACzD,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,aAAa,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AAC7E,IAAI,IAAI,cAAc,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACxC,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE;AAC3C,MAAM,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE;AAC7B,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACzC,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB;AACA,EAAE,WAAW,CAAC,CAAC,EAAE;AACjB,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAC9B,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE;AAC3C,MAAM,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE;AAC9B;AACA;AACA,EAAE,aAAa,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI;AAC9B;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrD,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,aAAa,EAAE,IAAI,CAAC,aAAa;AACrC,IAAI,WAAW,EAAE,IAAI,CAAC,WAAW;AACjC,IAAI,SAAS,EAAE,IAAI,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,mBAAmB,CAAC;AAC1B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,UAAU,GAAG,IAAI;AACnB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;AAC5C,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO;AAC1B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO;AAC1B,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AACxD,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI,CAAC,IAAI;AAClB,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI;AACvC;AACA,KAAK,CAAC;AACN;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;AAClC,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC;AACxC,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI;AAC1B;AACA,EAAE,aAAa,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,EAAE;AACrJ,MAAM,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU;AACzC,QAAQ,MAAM;AACd,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AAC/B,UAAU,IAAI,CAAC,eAAe,EAAE;AAChC,SAAS;AACT,QAAQ;AACR,OAAO;AACP;AACA;AACA,EAAE,cAAc,CAAC,CAAC,EAAE;AACpB,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE;AAC1B,IAAI,IAAI,CAAC,eAAe,EAAE;AAC1B;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,EAAE;AACpD,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,aAAa,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AAC7E,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AAC3E,MAAM,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE;AAC7B,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB;AACA;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACzC,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,EAAE;AACzC,IAAI,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE;AAC3B,IAAI,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,sBAAsB,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AACpG,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC;AAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACzC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AAC3B,MAAM,SAAS,CAAC,MAAM;AACtB,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;AACpD,QAAQ,IAAI,CAAC,WAAW,EAAE;AAC1B,QAAQ,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC;AAC3C,OAAO,CAAC;AACR;AACA;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,UAAU;AACnC,IAAI;AACJ,MAAM,eAAe,EAAE,MAAM;AAC7B,MAAM,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACtE,MAAM,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACrE,MAAM,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,GAAG,MAAM;AAC/F,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,EAAE;AACpD,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,aAAa,EAAE,IAAI,CAAC,aAAa;AACvC,MAAM,cAAc,EAAE,IAAI,CAAC,cAAc;AACzC,MAAM,SAAS,EAAE,IAAI,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC;AACd,GAAG,CAAC;AACJ,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,cAAc,CAAC;AACrB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,cAAc,GAAG,MAAM;AACzB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACzB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,iBAAiB,EAAE,IAAI,CAAC,cAAc;AAC1C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG;AAClC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG;AACtC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,wBAAwB,CAAC;AAC/B,EAAE,IAAI;AACN,EAAE,UAAU;AACZ,EAAE,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE;AAChC,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU;AAChC,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AAClD,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,CAAC,GAAG,KAAK;AAC5B,QAAQ,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,GAAG;AACzC;AACA,KAAK,CAAC;AACN;AACA,EAAE,aAAa,CAAC,CAAC,EAAE;AACnB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC,cAAc,EAAE;AAC5D,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK,EAAE;AAC/C,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;AAClC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,cAAc,EAAE;AAChE;AACA;AACA,EAAE,WAAW,CAAC,CAAC,EAAE;AACjB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE;AACnC,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;AAClC;AACA;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AAC5C,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;AAClC,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM;AACN;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,UAAU,EAAE;AAC9B,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAC9B,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB;AACA;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,MAAM;AAChC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO;AACxH,IAAI,OAAO,MAAM;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AACxC,IAAI,eAAe,EAAE,MAAM;AAC3B,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACvE,IAAI,eAAe,EAAE,IAAI,CAAC,aAAa,EAAE;AACzC,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAChE,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACtE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAE;AACjD;AACA,IAAI,aAAa,EAAE,IAAI,CAAC,aAAa;AACrC,IAAI,WAAW,EAAE,IAAI,CAAC,WAAW;AACjC,IAAI,SAAS,EAAE,IAAI,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC;AACvC,EAAE,iBAAiB,CAAC,GAAG,CAAC;AACxB,IAAI,IAAI,oBAAoB,GAAG;AAC/B,MAAM,OAAO,IAAI,CAAC,oBAAoB;AACtC;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC;AAClC;AACA,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AAClC,EAAE,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAClE;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,MAAM,IAAI,GAAG,eAAe,CAAC,GAAG,EAAE;AACpC,EAAE,OAAO,eAAe,CAAC,GAAG,CAAC,IAAI,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACvE;AACA,SAAS,iBAAiB,CAAC,KAAK,EAAE;AAClC,EAAE,MAAM,OAAO,GAAG,kBAAkB,CAAC,GAAG,EAAE;AAC1C,EAAE,MAAM,IAAI,GAAG,IAAI,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC;AACtD,EAAE,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,EAAE;AACvC,EAAE,OAAO,IAAI,mBAAmB,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC;AAC/D;AACA,SAAS,sBAAsB,CAAC,KAAK,EAAE;AACvC,EAAE,OAAO,IAAI,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC;AACnE;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,kBAAkB,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC;AACnF;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,MAAM,IAAI,GAAG,IAAI,mBAAmB,CAAC,KAAK,EAAE,kBAAkB,CAAC,GAAG,EAAE,CAAC;AACvE,EAAE,OAAO,IAAI,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC;AACvC;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,OAAO,gBAAgB,CAAC,GAAG,CAAC,IAAI,cAAc,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC;AAC/E;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,EAAE,OAAO,IAAI,kBAAkB,CAAC,KAAK,EAAE,eAAe,CAAC,GAAG,EAAE,CAAC;AAC7D;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,EAAE,YAAY,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC/D,EAAE,cAAc,CAAC;AACjB,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,KAAK;AACtC,MAAM,IAAI,GAAG,CAAC;AACd,MAAM,YAAY,GAAG,CAAC,CAAC;AACvB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC;AACA,GAAG,CAAC;AACJ,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,IAAI,GAAG,KAAK;AAChB,IAAI,GAAG,GAAG,KAAK;AACf,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,iBAAiB,EAAE,OAAO,GAAG,eAAe;AAChD,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC;AAC3B,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC;AACpC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,IAAI,GAAG,KAAK;AAClB,MAAM,YAAY,CAAC,KAAK,CAAC;AACzB;AACA,GAAG,CAAC;AACJ,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,KAAK;AACtC,MAAM,IAAI,GAAG,CAAC;AACd,MAAM,YAAY,CAAC,CAAC,CAAC;AACrB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC;AACA,GAAG,CAAC;AACJ,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,uBAAuB,CAAC,SAAS,EAAE,OAAO,EAAE;AACrD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,IAAI,GAAG,IAAI;AACf,IAAI,iBAAiB,GAAG,IAAI;AAC5B,IAAI,eAAe,GAAG,IAAI;AAC1B,IAAI,gBAAgB,GAAG,IAAI;AAC3B,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,cAAc,CAAC;AACtC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,gBAAgB;AACrD,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC;AAC/D,EAAE,SAAS,qBAAqB,CAAC,CAAC,EAAE;AACpC,IAAI,YAAY,CAAC,qBAAqB,CAAC,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,iBAAiB,CAAC,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,YAAY,CAAC,UAAU,CAAC,OAAO,EAAE;AACrC;AACA,EAAE,SAAS,mBAAmB,CAAC,CAAC,EAAE;AAClC,IAAI,eAAe,CAAC,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,YAAY,CAAC,UAAU,CAAC,OAAO,EAAE;AACrC;AACA,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM;AACN,QAAQ,IAAI,MAAM,GAAG,SAAS,UAAU,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;AACnE,UAAU,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE;AAC/C,YAAY,KAAK,EAAE,yBAAyB,CAAC,eAAe;AAC5D,WAAW,CAAC;AACZ,UAAU,IAAI,KAAK,EAAE;AACrB,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,KAAK,CAAC,UAAU,EAAE;AAC9B,cAAc,KAAK,EAAE,UAAU;AAC/B,cAAc,YAAY;AAC1B,cAAc,GAAG,YAAY,CAAC;AAC9B,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,YAAY,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACtI,YAAY,QAAQ,GAAG,UAAU,CAAC;AAClC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACnD;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,UAAU,OAAO,CAAC,UAAU,EAAE;AAC9B,YAAY,IAAI,OAAO,GAAG;AAC1B,cAAc,OAAO,YAAY,CAAC,OAAO;AACzC,aAAa;AACb,YAAY,IAAI,OAAO,CAAC,OAAO,EAAE;AACjC,cAAc,YAAY,CAAC,OAAO,GAAG,OAAO;AAC5C,cAAc,SAAS,GAAG,KAAK;AAC/B;AACA,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,wBAAwB,CAAC,UAAU,EAAE,YAAY,CAAC;AAC1D,UAAU,WAAW;AACrB,UAAU,YAAY,CAAC,WAAW;AAClC,UAAU;AACV,YAAY,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAC9D,YAAY,iBAAiB,EAAE,qBAAqB;AACpD,YAAY,eAAe,EAAE,mBAAmB;AAChD,YAAY,SAAS,EAAE,IAAI;AAC3B,YAAY,IAAI;AAChB,YAAY,UAAU,EAAE,IAAI;AAC5B,YAAY,EAAE;AACd,YAAY,MAAM;AAClB,YAAY,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI;AACnC;AACA,SAAS,CAAC,CAAC;AACX;AACA,KAAK,MAAM,IAAI,CAAC,UAAU,EAAE;AAC5B,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC,MAAM;AACN,QAAQ,IAAI,MAAM,GAAG,SAAS,UAAU,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;AACnE,UAAU,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE;AAC/C,YAAY,KAAK,EAAE,yBAAyB,CAAC,eAAe;AAC5D,WAAW,CAAC;AACZ,UAAU,IAAI,KAAK,EAAE;AACrB,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,KAAK,CAAC,UAAU,EAAE;AAC9B,cAAc,KAAK,EAAE,UAAU;AAC/B,cAAc,YAAY;AAC1B,cAAc,GAAG,YAAY,CAAC;AAC9B,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,YAAY,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACtI,YAAY,QAAQ,GAAG,UAAU,CAAC;AAClC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACnD;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,UAAU,OAAO,CAAC,UAAU,EAAE;AAC9B,YAAY,IAAI,OAAO,GAAG;AAC1B,cAAc,OAAO,YAAY,CAAC,OAAO;AACzC,aAAa;AACb,YAAY,IAAI,OAAO,CAAC,OAAO,EAAE;AACjC,cAAc,YAAY,CAAC,OAAO,GAAG,OAAO;AAC5C,cAAc,SAAS,GAAG,KAAK;AAC/B;AACA,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC;AAC9C,UAAU,WAAW;AACrB,UAAU,YAAY,CAAC,WAAW;AAClC,UAAU;AACV,YAAY,OAAO,EAAE,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAC9D,YAAY,iBAAiB,EAAE,qBAAqB;AACpD,YAAY,eAAe,EAAE,mBAAmB;AAChD,YAAY,SAAS,EAAE,IAAI;AAC3B,YAAY,IAAI;AAChB,YAAY,UAAU,EAAE,KAAK;AAC7B,YAAY,EAAE;AACd,YAAY,MAAM;AAClB,YAAY,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI;AACnC;AACA,SAAS,CAAC,CAAC;AACX;AACA,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,IAAI,GAAG,QAAQ;AACnB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,sBAAsB,CAAC;AAC9C,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,IAAI,KAAK,CAAC;AAC/C,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC;AACzE,EAAE,qBAAqB,CAAC,SAAS,EAAE;AACnC,IAAI,EAAE;AACN,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AACjD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAClF,QAAQ,QAAQ,GAAG,UAAU,CAAC;AAC9B,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC5C;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC;AACA,GAAG,CAAC;AACJ,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,IAAI,eAAe,GAAG,KAAK;AAC3B,MAAM,eAAe,CAAC;AACtB,EAAE,OAAO,KAAK,GAAG,CAAC;AAClB;AACA,EAAE,OAAO,QAAQ;AACjB,EAAE,WAAW,GAAG;AAChB;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,eAAe;AAC1B;AACA,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;AACrB,IAAI,eAAe,GAAG,KAAK;AAC3B;AACA;AACA,SAAS,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAE;AACnD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,UAAU,GAAG,CAAC;AAClB,IAAI,WAAW;AACf,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC;AACpC,MAAM,WAAW;AACjB,MAAM;AACN,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,UAAU,uBAAuB,CAAC,UAAU,EAAE,YAAY,CAAC;AAC3D,YAAY;AACZ,cAAc,WAAW,EAAE,uBAAuB;AAClD,cAAc,UAAU;AACxB,cAAc,KAAK,EAAE,EAAE,CAAC,wjBAAwjB,EAAE,SAAS;AAC3lB,aAAa;AACb,YAAY,SAAS;AACrB,YAAY;AACZ,cAAc,IAAI,GAAG,GAAG;AACxB,gBAAgB,OAAO,GAAG;AAC1B,eAAe;AACf,cAAc,IAAI,GAAG,CAAC,OAAO,EAAE;AAC/B,gBAAgB,GAAG,GAAG,OAAO;AAC7B,gBAAgB,SAAS,GAAG,KAAK;AACjC;AACA;AACA,WAAW,CAAC,CAAC;AACb,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAE;AACnD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAG,OAAO;AAC/D,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC;AAC1C,MAAM,EAAE,WAAW,EAAE,uBAAuB,EAAE;AAC9C,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACK,MAAC,GAAG,GAAG;AACP,MAAC,IAAI,GAAG;;;;"}