{"version": 3, "file": "cookie-CTvRQAsw.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/cookie.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction <PERSON>ie($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\n      \"path\",\n      {\n        \"d\": \"M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5\"\n      }\n    ],\n    [\"path\", { \"d\": \"M8.5 8.5v.01\" }],\n    [\"path\", { \"d\": \"M16 15.5v.01\" }],\n    [\"path\", { \"d\": \"M12 12v.01\" }],\n    [\"path\", { \"d\": \"M11 17v.01\" }],\n    [\"path\", { \"d\": \"M7 14v.01\" }]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"cookie\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Cookie as C\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,GAAG,EAAE;AACb;AACA,KAAK;AACL,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC;AACrC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,cAAc,EAAE,CAAC;AACrC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;AACnC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;AACnC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE;AACjC,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE;AACtB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}