{"version": 3, "file": "MaintenanceAccordion-hfzOCBt1.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/MaintenanceAccordion.js"], "sourcesContent": ["import { V as escape_html, U as ensure_array_like, y as pop, w as push } from \"./index3.js\";\nimport { a as Accordion_item, b as Accordion_trigger, c as Accordion_content } from \"./accordion-trigger.js\";\nimport { B as Badge } from \"./badge.js\";\nimport { S as StatusTag, b as SeverityBadge, a as StatusBar } from \"./StatusBar.js\";\nimport { C as Circle_alert } from \"./circle-alert.js\";\nimport { M as Message_square } from \"./message-square.js\";\nfunction MaintenanceAccordion($$payload, $$props) {\n  push();\n  const { incident, index } = $$props;\n  function formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\"\n    }).format(new Date(date));\n  }\n  function formatDateWithYear(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n      month: \"short\",\n      day: \"numeric\",\n      year: \"numeric\"\n    }).format(new Date(date));\n  }\n  function calculateProgress() {\n    if (!incident.startTime || !incident.endTime) return 0;\n    const start = new Date(incident.startTime).getTime();\n    const end = new Date(incident.endTime).getTime();\n    const now = Date.now();\n    if (now <= start) return 0;\n    if (now >= end) return 100;\n    return Math.round((now - start) / (end - start) * 100);\n  }\n  $$payload.out += `<div class=\"overflow-hidden rounded-lg border\"><!---->`;\n  Accordion_item($$payload, {\n    value: `incident-${index}`,\n    class: \"border-0\",\n    children: ($$payload2) => {\n      $$payload2.out += `<div class=\"bg-gray-50 dark:bg-gray-900\"><!---->`;\n      Accordion_trigger($$payload2, {\n        class: \"flex w-full items-center justify-between p-4 text-left\",\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"flex w-full items-center gap-4 pr-4\"><div class=\"flex h-8 w-8 items-center justify-center rounded-full bg-red-100 dark:bg-red-900\">`;\n          Circle_alert($$payload3, {\n            class: \"h-4 w-4 text-red-500 dark:text-red-400\"\n          });\n          $$payload3.out += `<!----></div> <div class=\"flex w-full flex-col gap-1\"><h3 class=\"text-lg font-medium\">${escape_html(incident.title)}</h3> <div class=\"mt-1 flex items-center justify-between gap-2\"><div class=\"flex flex-wrap gap-2\">`;\n          StatusTag($$payload3, { status: incident.status });\n          $$payload3.out += `<!----> `;\n          if (incident.severity) {\n            $$payload3.out += \"<!--[-->\";\n            SeverityBadge($$payload3, { severity: incident.severity });\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--></div> <span class=\"text-muted-foreground text-xs\">Started ${escape_html(formatDate(incident.date || incident.startTime))}</span></div></div></div>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----></div> <!---->`;\n      Accordion_content($$payload2, {\n        class: \"p-4 pt-0\",\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"border-t pt-4\">`;\n          if (incident.startTime && incident.endTime) {\n            $$payload3.out += \"<!--[-->\";\n            $$payload3.out += `<div class=\"mb-4\"><div class=\"mb-2 flex items-center justify-between\"><div class=\"text-xs text-gray-500\">${escape_html(formatDateWithYear(incident.startTime))} → ${escape_html(formatDateWithYear(incident.endTime))}</div> `;\n            StatusTag($$payload3, { status: incident.status });\n            $$payload3.out += `<!----></div> `;\n            StatusBar($$payload3, {\n              startTime: incident.startTime,\n              endTime: incident.endTime,\n              status: incident.status,\n              progress: incident.progress || calculateProgress(),\n              showTimes: false\n            });\n            $$payload3.out += `<!----></div>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--> <p class=\"whitespace-pre-line text-sm\">${escape_html(incident.description)}</p> `;\n          if (incident.affectedServices && incident.affectedServices.length > 0) {\n            $$payload3.out += \"<!--[-->\";\n            const each_array = ensure_array_like(incident.affectedServices);\n            $$payload3.out += `<div class=\"mt-4\"><h4 class=\"mb-2 text-sm font-medium\">Affected Services</h4> <div class=\"flex flex-wrap gap-2\"><!--[-->`;\n            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n              let service = each_array[$$index];\n              Badge($$payload3, {\n                variant: \"outline\",\n                children: ($$payload4) => {\n                  $$payload4.out += `<!---->${escape_html(service)}`;\n                },\n                $$slots: { default: true }\n              });\n            }\n            $$payload3.out += `<!--]--></div></div>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--> `;\n          if (incident.updates && incident.updates.length > 0) {\n            $$payload3.out += \"<!--[-->\";\n            const each_array_1 = ensure_array_like(incident.updates);\n            $$payload3.out += `<div class=\"mt-6\"><h4 class=\"mb-3 text-sm font-medium\">Updates</h4> <div class=\"space-y-4\"><!--[-->`;\n            for (let $$index_2 = 0, $$length = each_array_1.length; $$index_2 < $$length; $$index_2++) {\n              let update = each_array_1[$$index_2];\n              $$payload3.out += `<div class=\"relative pl-6 before:absolute before:left-0 before:top-0 before:h-full before:w-0.5 before:bg-gray-200 dark:before:bg-gray-800\"><div class=\"absolute left-[-4px] top-1 h-2 w-2 rounded-full bg-gray-300 dark:bg-gray-700\"></div> <div class=\"mb-1 flex items-center justify-between\"><div class=\"text-muted-foreground text-xs\">${escape_html(formatDate(update.date || update.timestamp))}</div> `;\n              if (update.status) {\n                $$payload3.out += \"<!--[-->\";\n                StatusTag($$payload3, { status: update.status, className: \"ml-2\" });\n              } else {\n                $$payload3.out += \"<!--[!-->\";\n              }\n              $$payload3.out += `<!--]--></div> <p class=\"text-sm\">${escape_html(update.message)}</p> `;\n              if (update.comments && update.comments.length > 0) {\n                $$payload3.out += \"<!--[-->\";\n                const each_array_2 = ensure_array_like(update.comments);\n                $$payload3.out += `<div class=\"mt-3 border-t pt-2\"><h5 class=\"mb-2 flex items-center gap-1 text-xs font-medium\">`;\n                Message_square($$payload3, { class: \"h-3 w-3\" });\n                $$payload3.out += `<!----> Comments</h5> <div class=\"space-y-2\"><!--[-->`;\n                for (let $$index_1 = 0, $$length2 = each_array_2.length; $$index_1 < $$length2; $$index_1++) {\n                  let comment = each_array_2[$$index_1];\n                  $$payload3.out += `<div class=\"bg-muted rounded-sm p-2 text-xs\"><div class=\"mb-1 flex items-center justify-between\"><span class=\"font-medium\">${escape_html(comment.author || \"System\")}</span> <span class=\"text-muted-foreground text-[10px]\">${escape_html(new Date(comment.timestamp).toLocaleString())}</span></div> <p>${escape_html(comment.text)}</p></div>`;\n                }\n                $$payload3.out += `<!--]--></div></div>`;\n              } else {\n                $$payload3.out += \"<!--[!-->\";\n              }\n              $$payload3.out += `<!--]--></div>`;\n            }\n            $$payload3.out += `<!--]--></div></div>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--></div>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div>`;\n  pop();\n}\nexport {\n  MaintenanceAccordion as M\n};\n"], "names": [], "mappings": ";;;;;;;AAMA,SAAS,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE;AAClD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,OAAO;AACrC,EAAE,SAAS,UAAU,CAAC,IAAI,EAAE;AAC5B,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;AAC5C,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,GAAG,EAAE,SAAS;AACpB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,MAAM,EAAE;AACd,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B;AACA,EAAE,SAAS,kBAAkB,CAAC,IAAI,EAAE;AACpC,IAAI,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;AAC5C,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,GAAG,EAAE,SAAS;AACpB,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B;AACA,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC;AAC1D,IAAI,MAAM,KAAK,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;AACxD,IAAI,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE;AACpD,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1B,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,OAAO,CAAC;AAC9B,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,OAAO,GAAG;AAC9B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,KAAK,KAAK,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC;AAC1D;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,sDAAsD,CAAC;AAC3E,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,KAAK,EAAE,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAC9B,IAAI,KAAK,EAAE,UAAU;AACrB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,gDAAgD,CAAC;AAC1E,MAAM,iBAAiB,CAAC,UAAU,EAAE;AACpC,QAAQ,KAAK,EAAE,wDAAwD;AACvE,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,+IAA+I,CAAC;AAC7K,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,KAAK,EAAE;AACnB,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,sFAAsF,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,kGAAkG,CAAC;AACpP,UAAU,SAAS,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC5D,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACjC,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,aAAa,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACtE,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,mEAAmE,EAAE,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,yBAAyB,CAAC;AACzL,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC/C,MAAM,iBAAiB,CAAC,UAAU,EAAE;AACpC,QAAQ,KAAK,EAAE,UAAU;AACzB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;AACzD,UAAU,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,EAAE;AACtD,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,yGAAyG,EAAE,WAAW,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,kBAAkB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;AAC7P,YAAY,SAAS,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;AAC9D,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC9C,YAAY,SAAS,CAAC,UAAU,EAAE;AAClC,cAAc,SAAS,EAAE,QAAQ,CAAC,SAAS;AAC3C,cAAc,OAAO,EAAE,QAAQ,CAAC,OAAO;AACvC,cAAc,MAAM,EAAE,QAAQ,CAAC,MAAM;AACrC,cAAc,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,iBAAiB,EAAE;AAChE,cAAc,SAAS,EAAE;AACzB,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC7C,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,gDAAgD,EAAE,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC;AACvH,UAAU,IAAI,QAAQ,CAAC,gBAAgB,IAAI,QAAQ,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;AACjF,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC,gBAAgB,CAAC;AAC3E,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,wHAAwH,CAAC;AACxJ,YAAY,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC/F,cAAc,IAAI,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AAC/C,cAAc,KAAK,CAAC,UAAU,EAAE;AAChC,gBAAgB,OAAO,EAAE,SAAS;AAClC,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;AACpE,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACpD,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,UAAU,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/D,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,MAAM,YAAY,GAAG,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC;AACpE,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,mGAAmG,CAAC;AACnI,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AACvG,cAAc,IAAI,MAAM,GAAG,YAAY,CAAC,SAAS,CAAC;AAClD,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,4UAA4U,EAAE,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;AAChb,cAAc,IAAI,MAAM,CAAC,MAAM,EAAE;AACjC,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,SAAS,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;AACnF,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,kCAAkC,EAAE,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;AACvG,cAAc,IAAI,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AACjE,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC;AACvE,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,6FAA6F,CAAC;AACjI,gBAAgB,cAAc,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAChE,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,qDAAqD,CAAC;AACzF,gBAAgB,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AAC7G,kBAAkB,IAAI,OAAO,GAAG,YAAY,CAAC,SAAS,CAAC;AACvD,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,2HAA2H,EAAE,WAAW,CAAC,OAAO,CAAC,MAAM,IAAI,QAAQ,CAAC,CAAC,wDAAwD,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,iBAAiB,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,UAAU,CAAC;AACtX;AACA,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACxD,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAChD;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACpD,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC5C,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,GAAG,EAAE;AACP;;;;"}