{"version": 3, "file": "video-CIe5QbIt.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/video.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Video($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\n      \"path\",\n      {\n        \"d\": \"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5\"\n      }\n    ],\n    [\n      \"rect\",\n      {\n        \"x\": \"2\",\n        \"y\": \"6\",\n        \"width\": \"14\",\n        \"height\": \"12\",\n        \"rx\": \"2\"\n      }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"video\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Video as V\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,GAAG,EAAE;AACb;AACA,KAAK;AACL,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,IAAI,EAAE;AACd;AACA;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE;AACrB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}