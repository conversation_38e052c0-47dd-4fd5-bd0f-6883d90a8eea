{"version": 3, "file": "popper-layer-force-mount-GhIXXB9T.js", "sources": ["../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "../../../node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "../../../.svelte-kit/adapter-node/chunks/popper-layer-force-mount.js"], "sourcesContent": ["/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import \"clsx\";\nimport { J as derived, y as pop, w as push, Q as spread_props } from \"./index3.js\";\nimport { d as defaultWindow, b as box, w as watch, s as styleToString } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { P as Presence_layer } from \"./presence-layer.js\";\nimport { u as useRefById, a as cssToStyleObj, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { S as Scroll_lock, c as Focus_scope, E as Escape_layer, D as Dismissible_layer, T as Text_selection_layer } from \"./scroll-lock.js\";\nimport { computePosition, offset, shift, flip, size, arrow, hide, limitShift } from \"@floating-ui/dom\";\nimport { C as Context } from \"./context.js\";\nimport { f as isNotNull } from \"./is.js\";\nimport { u as useId } from \"./use-id.js\";\nclass ElementSize {\n  #size = { width: 0, height: 0 };\n  constructor(node, options = { box: \"border-box\" }) {\n    options.window ?? defaultWindow;\n    this.#size = {\n      width: options.initialSize?.width ?? 0,\n      height: options.initialSize?.height ?? 0\n    };\n  }\n  get current() {\n    return this.#size;\n  }\n  get width() {\n    return this.#size.width;\n  }\n  get height() {\n    return this.#size.height;\n  }\n}\nfunction get(valueOrGetValue) {\n  return typeof valueOrGetValue === \"function\" ? valueOrGetValue() : valueOrGetValue;\n}\nfunction getDPR(element) {\n  if (typeof window === \"undefined\") return 1;\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\nfunction getFloatingContentCSSVars(name) {\n  return {\n    [`--bits-${name}-content-transform-origin`]: `var(--bits-floating-transform-origin)`,\n    [`--bits-${name}-content-available-width`]: `var(--bits-floating-available-width)`,\n    [`--bits-${name}-content-available-height`]: `var(--bits-floating-available-height)`,\n    [`--bits-${name}-anchor-width`]: `var(--bits-floating-anchor-width)`,\n    [`--bits-${name}-anchor-height`]: `var(--bits-floating-anchor-height)`\n  };\n}\nfunction useFloating(options) {\n  get(options.open) ?? true;\n  const middlewareOption = get(options.middleware);\n  const transformOption = get(options.transform) ?? true;\n  const placementOption = get(options.placement) ?? \"bottom\";\n  const strategyOption = get(options.strategy) ?? \"absolute\";\n  const reference = options.reference;\n  let x = 0;\n  let y = 0;\n  const floating = box(null);\n  let strategy = strategyOption;\n  let placement = placementOption;\n  let middlewareData = {};\n  let isPositioned = false;\n  const floatingStyles = (() => {\n    const initialStyles = { position: strategy, left: \"0\", top: \"0\" };\n    if (!floating.current) {\n      return initialStyles;\n    }\n    const xVal = roundByDPR(floating.current, x);\n    const yVal = roundByDPR(floating.current, y);\n    if (transformOption) {\n      return {\n        ...initialStyles,\n        transform: `translate(${xVal}px, ${yVal}px)`,\n        ...getDPR(floating.current) >= 1.5 && { willChange: \"transform\" }\n      };\n    }\n    return {\n      position: strategy,\n      left: `${xVal}px`,\n      top: `${yVal}px`\n    };\n  })();\n  function update() {\n    if (reference.current === null || floating.current === null) return;\n    computePosition(reference.current, floating.current, {\n      middleware: middlewareOption,\n      placement: placementOption,\n      strategy: strategyOption\n    }).then((position) => {\n      x = position.x;\n      y = position.y;\n      strategy = position.strategy;\n      placement = position.placement;\n      middlewareData = position.middlewareData;\n      isPositioned = true;\n    });\n  }\n  return {\n    floating,\n    reference,\n    get strategy() {\n      return strategy;\n    },\n    get placement() {\n      return placement;\n    },\n    get middlewareData() {\n      return middlewareData;\n    },\n    get isPositioned() {\n      return isPositioned;\n    },\n    get floatingStyles() {\n      return floatingStyles;\n    },\n    get update() {\n      return update;\n    }\n  };\n}\nconst OPPOSITE_SIDE = {\n  top: \"bottom\",\n  right: \"left\",\n  bottom: \"top\",\n  left: \"right\"\n};\nclass FloatingRootState {\n  anchorNode = box(null);\n  customAnchorNode = box(null);\n  triggerNode = box(null);\n  constructor() {\n  }\n}\nclass FloatingContentState {\n  opts;\n  root;\n  // nodes\n  contentRef = box(null);\n  wrapperRef = box(null);\n  arrowRef = box(null);\n  // ids\n  arrowId = box(useId());\n  #transformedStyle = derived(() => {\n    if (typeof this.opts.style === \"string\") return cssToStyleObj(this.opts.style);\n    if (!this.opts.style) return {};\n  });\n  #updatePositionStrategy = void 0;\n  #arrowSize = new ElementSize(() => this.arrowRef.current ?? void 0);\n  #arrowWidth = derived(() => this.#arrowSize?.width ?? 0);\n  #arrowHeight = derived(() => this.#arrowSize?.height ?? 0);\n  #desiredPlacement = derived(() => this.opts.side?.current + (this.opts.align.current !== \"center\" ? `-${this.opts.align.current}` : \"\"));\n  #boundary = derived(() => Array.isArray(this.opts.collisionBoundary.current) ? this.opts.collisionBoundary.current : [this.opts.collisionBoundary.current]);\n  #hasExplicitBoundaries = derived(() => this.#boundary().length > 0);\n  get hasExplicitBoundaries() {\n    return this.#hasExplicitBoundaries();\n  }\n  set hasExplicitBoundaries($$value) {\n    return this.#hasExplicitBoundaries($$value);\n  }\n  #detectOverflowOptions = derived(() => ({\n    padding: this.opts.collisionPadding.current,\n    boundary: this.#boundary().filter(isNotNull),\n    altBoundary: this.hasExplicitBoundaries\n  }));\n  get detectOverflowOptions() {\n    return this.#detectOverflowOptions();\n  }\n  set detectOverflowOptions($$value) {\n    return this.#detectOverflowOptions($$value);\n  }\n  #availableWidth = void 0;\n  #availableHeight = void 0;\n  #anchorWidth = void 0;\n  #anchorHeight = void 0;\n  #middleware = derived(() => [\n    offset({\n      mainAxis: this.opts.sideOffset.current + this.#arrowHeight(),\n      alignmentAxis: this.opts.alignOffset.current\n    }),\n    this.opts.avoidCollisions.current && shift({\n      mainAxis: true,\n      crossAxis: false,\n      limiter: this.opts.sticky.current === \"partial\" ? limitShift() : void 0,\n      ...this.detectOverflowOptions\n    }),\n    this.opts.avoidCollisions.current && flip({ ...this.detectOverflowOptions }),\n    size({\n      ...this.detectOverflowOptions,\n      apply: ({ rects, availableWidth, availableHeight }) => {\n        const { width: anchorWidth, height: anchorHeight } = rects.reference;\n        this.#availableWidth = availableWidth;\n        this.#availableHeight = availableHeight;\n        this.#anchorWidth = anchorWidth;\n        this.#anchorHeight = anchorHeight;\n      }\n    }),\n    this.arrowRef.current && arrow({\n      element: this.arrowRef.current,\n      padding: this.opts.arrowPadding.current\n    }),\n    transformOrigin({\n      arrowWidth: this.#arrowWidth(),\n      arrowHeight: this.#arrowHeight()\n    }),\n    this.opts.hideWhenDetached.current && hide({\n      strategy: \"referenceHidden\",\n      ...this.detectOverflowOptions\n    })\n  ].filter(Boolean));\n  get middleware() {\n    return this.#middleware();\n  }\n  set middleware($$value) {\n    return this.#middleware($$value);\n  }\n  floating;\n  #placedSide = derived(() => getSideFromPlacement(this.floating.placement));\n  get placedSide() {\n    return this.#placedSide();\n  }\n  set placedSide($$value) {\n    return this.#placedSide($$value);\n  }\n  #placedAlign = derived(() => getAlignFromPlacement(this.floating.placement));\n  get placedAlign() {\n    return this.#placedAlign();\n  }\n  set placedAlign($$value) {\n    return this.#placedAlign($$value);\n  }\n  #arrowX = derived(() => this.floating.middlewareData.arrow?.x ?? 0);\n  get arrowX() {\n    return this.#arrowX();\n  }\n  set arrowX($$value) {\n    return this.#arrowX($$value);\n  }\n  #arrowY = derived(() => this.floating.middlewareData.arrow?.y ?? 0);\n  get arrowY() {\n    return this.#arrowY();\n  }\n  set arrowY($$value) {\n    return this.#arrowY($$value);\n  }\n  #cannotCenterArrow = derived(() => this.floating.middlewareData.arrow?.centerOffset !== 0);\n  get cannotCenterArrow() {\n    return this.#cannotCenterArrow();\n  }\n  set cannotCenterArrow($$value) {\n    return this.#cannotCenterArrow($$value);\n  }\n  contentZIndex;\n  #arrowBaseSide = derived(() => OPPOSITE_SIDE[this.placedSide]);\n  get arrowBaseSide() {\n    return this.#arrowBaseSide();\n  }\n  set arrowBaseSide($$value) {\n    return this.#arrowBaseSide($$value);\n  }\n  #wrapperProps = derived(() => ({\n    id: this.opts.wrapperId.current,\n    \"data-bits-floating-content-wrapper\": \"\",\n    style: {\n      ...this.floating.floatingStyles,\n      // keep off page when measuring\n      transform: this.floating.isPositioned ? this.floating.floatingStyles.transform : \"translate(0, -200%)\",\n      minWidth: \"max-content\",\n      zIndex: this.contentZIndex,\n      \"--bits-floating-transform-origin\": `${this.floating.middlewareData.transformOrigin?.x} ${this.floating.middlewareData.transformOrigin?.y}`,\n      \"--bits-floating-available-width\": `${this.#availableWidth}px`,\n      \"--bits-floating-available-height\": `${this.#availableHeight}px`,\n      \"--bits-floating-anchor-width\": `${this.#anchorWidth}px`,\n      \"--bits-floating-anchor-height\": `${this.#anchorHeight}px`,\n      // hide the content if using the hide middleware and should be hidden\n      ...this.floating.middlewareData.hide?.referenceHidden && {\n        visibility: \"hidden\",\n        \"pointer-events\": \"none\"\n      },\n      ...this.#transformedStyle()\n    },\n    // Floating UI calculates logical alignment based the `dir` attribute\n    dir: this.opts.dir.current\n  }));\n  get wrapperProps() {\n    return this.#wrapperProps();\n  }\n  set wrapperProps($$value) {\n    return this.#wrapperProps($$value);\n  }\n  #props = derived(() => ({\n    \"data-side\": this.placedSide,\n    \"data-align\": this.placedAlign,\n    style: styleToString({\n      ...this.#transformedStyle()\n      // if the FloatingContent hasn't been placed yet (not all measurements done)\n    })\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n  #arrowStyle = derived(() => ({\n    position: \"absolute\",\n    left: this.arrowX ? `${this.arrowX}px` : void 0,\n    top: this.arrowY ? `${this.arrowY}px` : void 0,\n    [this.arrowBaseSide]: 0,\n    \"transform-origin\": {\n      top: \"\",\n      right: \"0 0\",\n      bottom: \"center 0\",\n      left: \"100% 0\"\n    }[this.placedSide],\n    transform: {\n      top: \"translateY(100%)\",\n      right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n      bottom: \"rotate(180deg)\",\n      left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n    }[this.placedSide],\n    visibility: this.cannotCenterArrow ? \"hidden\" : void 0\n  }));\n  get arrowStyle() {\n    return this.#arrowStyle();\n  }\n  set arrowStyle($$value) {\n    return this.#arrowStyle($$value);\n  }\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    if (opts.customAnchor) {\n      this.root.customAnchorNode.current = opts.customAnchor.current;\n    }\n    watch(() => opts.customAnchor.current, (customAnchor) => {\n      this.root.customAnchorNode.current = customAnchor;\n    });\n    useRefById({\n      id: this.opts.wrapperId,\n      ref: this.wrapperRef,\n      deps: () => this.opts.enabled.current\n    });\n    useRefById({\n      id: this.opts.id,\n      ref: this.contentRef,\n      deps: () => this.opts.enabled.current\n    });\n    this.floating = useFloating({\n      strategy: () => this.opts.strategy.current,\n      placement: () => this.#desiredPlacement(),\n      middleware: () => this.middleware,\n      reference: this.root.anchorNode,\n      open: () => this.opts.enabled.current\n    });\n    watch(() => this.contentRef.current, (contentNode) => {\n      if (!contentNode) return;\n      this.contentZIndex = window.getComputedStyle(contentNode).zIndex;\n    });\n  }\n}\nclass FloatingArrowState {\n  opts;\n  content;\n  constructor(opts, content) {\n    this.opts = opts;\n    this.content = content;\n    useRefById({\n      ...opts,\n      onRefChange: (node) => {\n        this.content.arrowRef.current = node;\n      },\n      deps: () => this.content.opts.enabled.current\n    });\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    style: this.content.arrowStyle,\n    \"data-side\": this.content.placedSide\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass FloatingAnchorState {\n  opts;\n  root;\n  ref = box(null);\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    if (opts.virtualEl && opts.virtualEl.current) {\n      root.triggerNode = box.from(opts.virtualEl.current);\n    } else {\n      useRefById({\n        id: opts.id,\n        ref: this.ref,\n        onRefChange: (node) => {\n          root.triggerNode.current = node;\n        }\n      });\n    }\n  }\n}\nconst FloatingRootContext = new Context(\"Floating.Root\");\nconst FloatingContentContext = new Context(\"Floating.Content\");\nfunction useFloatingRootState() {\n  return FloatingRootContext.set(new FloatingRootState());\n}\nfunction useFloatingContentState(props) {\n  return FloatingContentContext.set(new FloatingContentState(props, FloatingRootContext.get()));\n}\nfunction useFloatingArrowState(props) {\n  return new FloatingArrowState(props, FloatingContentContext.get());\n}\nfunction useFloatingAnchorState(props) {\n  return new FloatingAnchorState(props, FloatingRootContext.get());\n}\nfunction transformOrigin(options) {\n  return {\n    name: \"transformOrigin\",\n    options,\n    fn(data) {\n      const { placement, rects, middlewareData } = data;\n      const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n      const isArrowHidden = cannotCenterArrow;\n      const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n      const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n      const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n      const noArrowAlign = { start: \"0%\", center: \"50%\", end: \"100%\" }[placedAlign];\n      const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n      const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n      let x = \"\";\n      let y = \"\";\n      if (placedSide === \"bottom\") {\n        x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n        y = `${-arrowHeight}px`;\n      } else if (placedSide === \"top\") {\n        x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n        y = `${rects.floating.height + arrowHeight}px`;\n      } else if (placedSide === \"right\") {\n        x = `${-arrowHeight}px`;\n        y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n      } else if (placedSide === \"left\") {\n        x = `${rects.floating.width + arrowHeight}px`;\n        y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n      }\n      return { data: { x, y } };\n    }\n  };\n}\nfunction getSideAndAlignFromPlacement(placement) {\n  const [side, align = \"center\"] = placement.split(\"-\");\n  return [side, align];\n}\nfunction getSideFromPlacement(placement) {\n  return getSideAndAlignFromPlacement(placement)[0];\n}\nfunction getAlignFromPlacement(placement) {\n  return getSideAndAlignFromPlacement(placement)[1];\n}\nfunction Floating_layer($$payload, $$props) {\n  push();\n  let { children } = $$props;\n  useFloatingRootState();\n  children?.($$payload);\n  $$payload.out += `<!---->`;\n  pop();\n}\nfunction Floating_layer_anchor($$payload, $$props) {\n  push();\n  let { id, children, virtualEl } = $$props;\n  useFloatingAnchorState({\n    id: box.with(() => id),\n    virtualEl: box.with(() => virtualEl)\n  });\n  children?.($$payload);\n  $$payload.out += `<!---->`;\n  pop();\n}\nfunction Floating_layer_content($$payload, $$props) {\n  push();\n  let {\n    content,\n    side = \"bottom\",\n    sideOffset = 0,\n    align = \"center\",\n    alignOffset = 0,\n    id,\n    arrowPadding = 0,\n    avoidCollisions = true,\n    collisionBoundary = [],\n    collisionPadding = 0,\n    hideWhenDetached = false,\n    onPlaced = () => {\n    },\n    sticky = \"partial\",\n    updatePositionStrategy = \"optimized\",\n    strategy = \"fixed\",\n    dir = \"ltr\",\n    style = {},\n    wrapperId = useId(),\n    customAnchor = null,\n    enabled\n  } = $$props;\n  const contentState = useFloatingContentState({\n    side: box.with(() => side),\n    sideOffset: box.with(() => sideOffset),\n    align: box.with(() => align),\n    alignOffset: box.with(() => alignOffset),\n    id: box.with(() => id),\n    arrowPadding: box.with(() => arrowPadding),\n    avoidCollisions: box.with(() => avoidCollisions),\n    collisionBoundary: box.with(() => collisionBoundary),\n    collisionPadding: box.with(() => collisionPadding),\n    hideWhenDetached: box.with(() => hideWhenDetached),\n    onPlaced: box.with(() => onPlaced),\n    sticky: box.with(() => sticky),\n    updatePositionStrategy: box.with(() => updatePositionStrategy),\n    strategy: box.with(() => strategy),\n    dir: box.with(() => dir),\n    style: box.with(() => style),\n    enabled: box.with(() => enabled),\n    wrapperId: box.with(() => wrapperId),\n    customAnchor: box.with(() => customAnchor)\n  });\n  const mergedProps = mergeProps(contentState.wrapperProps, { style: { pointerEvents: \"auto\" } });\n  content?.($$payload, {\n    props: contentState.props,\n    wrapperProps: mergedProps\n  });\n  $$payload.out += `<!---->`;\n  pop();\n}\nfunction Floating_layer_content_static($$payload, $$props) {\n  push();\n  let { content } = $$props;\n  content?.($$payload, { props: {}, wrapperProps: {} });\n  $$payload.out += `<!---->`;\n  pop();\n}\nfunction Popper_content($$payload, $$props) {\n  let {\n    content,\n    isStatic = false,\n    onPlaced,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  if (isStatic) {\n    $$payload.out += \"<!--[-->\";\n    Floating_layer_content_static($$payload, { content });\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    Floating_layer_content($$payload, spread_props([{ content, onPlaced }, restProps]));\n  }\n  $$payload.out += `<!--]-->`;\n}\nfunction Popper_layer_inner($$payload, $$props) {\n  push();\n  let {\n    popper,\n    onEscapeKeydown,\n    escapeKeydownBehavior,\n    preventOverflowTextSelection,\n    id,\n    onPointerDown,\n    onPointerUp,\n    side,\n    sideOffset,\n    align,\n    alignOffset,\n    arrowPadding,\n    avoidCollisions,\n    collisionBoundary,\n    collisionPadding,\n    sticky,\n    hideWhenDetached,\n    updatePositionStrategy,\n    strategy,\n    dir,\n    preventScroll,\n    wrapperId,\n    style,\n    onPlaced,\n    onInteractOutside,\n    onCloseAutoFocus,\n    onOpenAutoFocus,\n    onFocusOutside,\n    interactOutsideBehavior = \"close\",\n    loop,\n    trapFocus = true,\n    isValidEvent = () => false,\n    customAnchor = null,\n    isStatic = false,\n    enabled,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  {\n    let content = function($$payload2, { props: floatingProps, wrapperProps }) {\n      if (restProps.forceMount && enabled) {\n        $$payload2.out += \"<!--[-->\";\n        Scroll_lock($$payload2, { preventScroll });\n      } else if (!restProps.forceMount) {\n        $$payload2.out += \"<!--[1-->\";\n        Scroll_lock($$payload2, { preventScroll });\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n      }\n      $$payload2.out += `<!--]--> `;\n      {\n        let focusScope = function($$payload3, { props: focusScopeProps }) {\n          Escape_layer($$payload3, {\n            onEscapeKeydown,\n            escapeKeydownBehavior,\n            enabled,\n            children: ($$payload4) => {\n              {\n                let children = function($$payload5, { props: dismissibleProps }) {\n                  Text_selection_layer($$payload5, {\n                    id,\n                    preventOverflowTextSelection,\n                    onPointerDown,\n                    onPointerUp,\n                    enabled,\n                    children: ($$payload6) => {\n                      popper?.($$payload6, {\n                        props: mergeProps(restProps, floatingProps, dismissibleProps, focusScopeProps, { style: { pointerEvents: \"auto\" } }),\n                        wrapperProps\n                      });\n                      $$payload6.out += `<!---->`;\n                    }\n                  });\n                };\n                Dismissible_layer($$payload4, {\n                  id,\n                  onInteractOutside,\n                  onFocusOutside,\n                  interactOutsideBehavior,\n                  isValidEvent,\n                  enabled,\n                  children\n                });\n              }\n            }\n          });\n        };\n        Focus_scope($$payload2, {\n          id,\n          onOpenAutoFocus,\n          onCloseAutoFocus,\n          loop,\n          trapFocus: enabled && trapFocus,\n          forceMount: restProps.forceMount,\n          focusScope\n        });\n      }\n      $$payload2.out += `<!---->`;\n    };\n    Popper_content($$payload, {\n      isStatic,\n      id,\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      avoidCollisions,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      updatePositionStrategy,\n      strategy,\n      dir,\n      wrapperId,\n      style,\n      onPlaced,\n      customAnchor,\n      enabled,\n      content,\n      $$slots: { content: true }\n    });\n  }\n  pop();\n}\nfunction Popper_layer($$payload, $$props) {\n  let {\n    popper,\n    present,\n    onEscapeKeydown,\n    escapeKeydownBehavior,\n    preventOverflowTextSelection,\n    id,\n    onPointerDown,\n    onPointerUp,\n    side,\n    sideOffset,\n    align,\n    alignOffset,\n    arrowPadding,\n    avoidCollisions,\n    collisionBoundary,\n    collisionPadding,\n    sticky,\n    hideWhenDetached,\n    updatePositionStrategy,\n    strategy,\n    dir,\n    preventScroll,\n    wrapperId,\n    style,\n    onPlaced,\n    onInteractOutside,\n    onCloseAutoFocus,\n    onOpenAutoFocus,\n    onFocusOutside,\n    interactOutsideBehavior = \"close\",\n    loop,\n    trapFocus = true,\n    isValidEvent = () => false,\n    customAnchor = null,\n    isStatic = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  {\n    let presence = function($$payload2) {\n      Popper_layer_inner($$payload2, spread_props([\n        {\n          popper,\n          onEscapeKeydown,\n          escapeKeydownBehavior,\n          preventOverflowTextSelection,\n          id,\n          onPointerDown,\n          onPointerUp,\n          side,\n          sideOffset,\n          align,\n          alignOffset,\n          arrowPadding,\n          avoidCollisions,\n          collisionBoundary,\n          collisionPadding,\n          sticky,\n          hideWhenDetached,\n          updatePositionStrategy,\n          strategy,\n          dir,\n          preventScroll,\n          wrapperId,\n          style,\n          onPlaced,\n          customAnchor,\n          isStatic,\n          enabled: present,\n          onInteractOutside,\n          onCloseAutoFocus,\n          onOpenAutoFocus,\n          interactOutsideBehavior,\n          loop,\n          trapFocus,\n          isValidEvent,\n          onFocusOutside,\n          forceMount: false\n        },\n        restProps\n      ]));\n    };\n    Presence_layer($$payload, spread_props([\n      { id, present },\n      restProps,\n      { presence, $$slots: { presence: true } }\n    ]));\n  }\n}\nfunction Popper_layer_force_mount($$payload, $$props) {\n  let {\n    popper,\n    onEscapeKeydown,\n    escapeKeydownBehavior,\n    preventOverflowTextSelection,\n    id,\n    onPointerDown,\n    onPointerUp,\n    side,\n    sideOffset,\n    align,\n    alignOffset,\n    arrowPadding,\n    avoidCollisions,\n    collisionBoundary,\n    collisionPadding,\n    sticky,\n    hideWhenDetached,\n    updatePositionStrategy,\n    strategy,\n    dir,\n    preventScroll,\n    wrapperId,\n    style,\n    onPlaced,\n    onInteractOutside,\n    onCloseAutoFocus,\n    onOpenAutoFocus,\n    onFocusOutside,\n    interactOutsideBehavior = \"close\",\n    loop,\n    trapFocus = true,\n    isValidEvent = () => false,\n    customAnchor = null,\n    isStatic = false,\n    enabled,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  Popper_layer_inner($$payload, spread_props([\n    {\n      popper,\n      onEscapeKeydown,\n      escapeKeydownBehavior,\n      preventOverflowTextSelection,\n      id,\n      onPointerDown,\n      onPointerUp,\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      avoidCollisions,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      updatePositionStrategy,\n      strategy,\n      dir,\n      preventScroll,\n      wrapperId,\n      style,\n      onPlaced,\n      customAnchor,\n      isStatic,\n      enabled,\n      onInteractOutside,\n      onCloseAutoFocus,\n      onOpenAutoFocus,\n      interactOutsideBehavior,\n      loop,\n      trapFocus,\n      isValidEvent,\n      onFocusOutside\n    },\n    restProps,\n    { forceMount: true }\n  ]));\n}\nexport {\n  Floating_layer as F,\n  Popper_layer_force_mount as P,\n  Popper_layer as a,\n  Floating_layer_anchor as b,\n  getFloatingContentCSSVars as g,\n  useFloatingArrowState as u\n};\n"], "names": ["computePosition", "arrow", "flip", "hide", "offset", "shift", "limitShift", "size"], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA,MAAM,KAAK,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC;AAGhD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;AACpB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;AACpB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;AAExB,MAAM,YAAY,GAAG,CAAC,KAAK;AAC3B,EAAE,CAAC,EAAE,CAAC;AACN,EAAE,CAAC,EAAE;AACL,CAAC,CAAC;AACF,MAAM,eAAe,GAAG;AACxB,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,GAAG,EAAE;AACP,CAAC;AACD,MAAM,oBAAoB,GAAG;AAC7B,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,GAAG,EAAE;AACP,CAAC;AACD,SAAS,KAAK,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE;AAClC,EAAE,OAAO,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;AACpC;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE;AAChC,EAAE,OAAO,OAAO,KAAK,KAAK,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK;AAC3D;AACA,SAAS,OAAO,CAAC,SAAS,EAAE;AAC5B,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,SAAS,YAAY,CAAC,SAAS,EAAE;AACjC,EAAE,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,OAAO,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACjC;AACA,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,EAAE,OAAO,IAAI,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AAC1C;AACA,SAAS,WAAW,CAAC,SAAS,EAAE;AAChC,EAAE,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;AACnE;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE;AACrC,EAAE,OAAO,eAAe,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAChD;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE;AAClD,EAAE,IAAI,GAAG,KAAK,MAAM,EAAE;AACtB,IAAI,GAAG,GAAG,KAAK;AACf;AACA,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;AAC3C,EAAE,MAAM,aAAa,GAAG,gBAAgB,CAAC,SAAS,CAAC;AACnD,EAAE,MAAM,MAAM,GAAG,aAAa,CAAC,aAAa,CAAC;AAC7C,EAAE,IAAI,iBAAiB,GAAG,aAAa,KAAK,GAAG,GAAG,SAAS,MAAM,GAAG,GAAG,KAAK,GAAG,OAAO,CAAC,GAAG,OAAO,GAAG,MAAM,GAAG,SAAS,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK;AACrJ,EAAE,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;AACxD,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,iBAAiB,CAAC;AAC/D;AACA,EAAE,OAAO,CAAC,iBAAiB,EAAE,oBAAoB,CAAC,iBAAiB,CAAC,CAAC;AACrE;AACA,SAAS,qBAAqB,CAAC,SAAS,EAAE;AAC1C,EAAE,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC;AAC3D,EAAE,OAAO,CAAC,6BAA6B,CAAC,SAAS,CAAC,EAAE,iBAAiB,EAAE,6BAA6B,CAAC,iBAAiB,CAAC,CAAC;AACxH;AACA,SAAS,6BAA6B,CAAC,SAAS,EAAE;AAClD,EAAE,OAAO,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,SAAS,IAAI,oBAAoB,CAAC,SAAS,CAAC,CAAC;AACtF;AACA,SAAS,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE;AACzC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC;AAC9B,EAAE,MAAM,EAAE,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;AAC9B,EAAE,QAAQ,IAAI;AACd,IAAI,KAAK,KAAK;AACd,IAAI,KAAK,QAAQ;AACjB,MAAM,IAAI,GAAG,EAAE,OAAO,OAAO,GAAG,EAAE,GAAG,EAAE;AACvC,MAAM,OAAO,OAAO,GAAG,EAAE,GAAG,EAAE;AAC9B,IAAI,KAAK,MAAM;AACf,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,OAAO,GAAG,EAAE,GAAG,EAAE;AAC9B,IAAI;AACJ,MAAM,OAAO,EAAE;AACf;AACA;AACA,SAAS,yBAAyB,CAAC,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,EAAE;AAC7E,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;AAC3C,EAAE,IAAI,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,SAAS,KAAK,OAAO,EAAE,GAAG,CAAC;AACxE,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,SAAS,CAAC;AACnD,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;AACjE;AACA;AACA,EAAE,OAAO,IAAI;AACb;AACA,SAAS,oBAAoB,CAAC,SAAS,EAAE;AACzC,EAAE,OAAO,SAAS,CAAC,OAAO,CAAC,wBAAwB,EAAE,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;AACnF;AACA,SAAS,mBAAmB,CAAC,OAAO,EAAE;AACtC,EAAE,OAAO;AACT,IAAI,GAAG,EAAE,CAAC;AACV,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,MAAM,EAAE,CAAC;AACb,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,GAAG;AACP,GAAG;AACH;AACA,SAAS,gBAAgB,CAAC,OAAO,EAAE;AACnC,EAAE,OAAO,OAAO,OAAO,KAAK,QAAQ,GAAG,mBAAmB,CAAC,OAAO,CAAC,GAAG;AACtE,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,KAAK,EAAE,OAAO;AAClB,IAAI,MAAM,EAAE,OAAO;AACnB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAE,MAAM;AACR,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,KAAK;AACT,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,GAAG,EAAE,CAAC;AACV,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,KAAK,EAAE,CAAC,GAAG,KAAK;AACpB,IAAI,MAAM,EAAE,CAAC,GAAG,MAAM;AACtB,IAAI,CAAC;AACL,IAAI;AACJ,GAAG;AACH;;ACpIA,SAAS,0BAA0B,CAAC,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE;AAC1D,EAAE,IAAI;AACN,IAAI,SAAS;AACb,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,QAAQ,GAAG,WAAW,CAAC,SAAS,CAAC;AACzC,EAAE,MAAM,aAAa,GAAG,gBAAgB,CAAC,SAAS,CAAC;AACnD,EAAE,MAAM,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC;AAClD,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;AACjC,EAAE,MAAM,UAAU,GAAG,QAAQ,KAAK,GAAG;AACrC,EAAE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC,KAAK,GAAG,CAAC;AACxE,EAAE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC;AAC1E,EAAE,MAAM,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC;AAC5E,EAAE,IAAI,MAAM;AACZ,EAAE,QAAQ,IAAI;AACd,IAAI,KAAK,KAAK;AACd,MAAM,MAAM,GAAG;AACf,QAAQ,CAAC,EAAE,OAAO;AAClB,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC;AAClC,OAAO;AACP,MAAM;AACN,IAAI,KAAK,QAAQ;AACjB,MAAM,MAAM,GAAG;AACf,QAAQ,CAAC,EAAE,OAAO;AAClB,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC;AACnC,OAAO;AACP,MAAM;AACN,IAAI,KAAK,OAAO;AAChB,MAAM,MAAM,GAAG;AACf,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK;AACxC,QAAQ,CAAC,EAAE;AACX,OAAO;AACP,MAAM;AACN,IAAI,KAAK,MAAM;AACf,MAAM,MAAM,GAAG;AACf,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,QAAQ,CAAC,KAAK;AACvC,QAAQ,CAAC,EAAE;AACX,OAAO;AACP,MAAM;AACN,IAAI;AACJ,MAAM,MAAM,GAAG;AACf,QAAQ,CAAC,EAAE,SAAS,CAAC,CAAC;AACtB,QAAQ,CAAC,EAAE,SAAS,CAAC;AACrB,OAAO;AACP;AACA,EAAE,QAAQ,YAAY,CAAC,SAAS,CAAC;AACjC,IAAI,KAAK,OAAO;AAChB,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,IAAI,GAAG,IAAI,UAAU,GAAG,EAAE,GAAG,CAAC,CAAC;AACzE,MAAM;AACN,IAAI,KAAK,KAAK;AACd,MAAM,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,IAAI,GAAG,IAAI,UAAU,GAAG,EAAE,GAAG,CAAC,CAAC;AACzE,MAAM;AACN;AACA,EAAE,OAAO,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMA,iBAAe,GAAG,OAAO,SAAS,EAAE,QAAQ,EAAE,MAAM,KAAK;AAC/D,EAAE,MAAM;AACR,IAAI,SAAS,GAAG,QAAQ;AACxB,IAAI,QAAQ,GAAG,UAAU;AACzB,IAAI,UAAU,GAAG,EAAE;AACnB,IAAI;AACJ,GAAG,GAAG,MAAM;AACZ,EAAE,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC;AACpD,EAAE,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,KAAK,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAChF,EAAE,IAAI,KAAK,GAAG,MAAM,QAAQ,CAAC,eAAe,CAAC;AAC7C,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,IAAI;AACN,IAAI,CAAC;AACL,IAAI;AACJ,GAAG,GAAG,0BAA0B,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,CAAC;AACvD,EAAE,IAAI,iBAAiB,GAAG,SAAS;AACnC,EAAE,IAAI,cAAc,GAAG,EAAE;AACzB,EAAE,IAAI,UAAU,GAAG,CAAC;AACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACnD,IAAI,MAAM;AACV,MAAM,IAAI;AACV,MAAM;AACN,KAAK,GAAG,eAAe,CAAC,CAAC,CAAC;AAC1B,IAAI,MAAM;AACV,MAAM,CAAC,EAAE,KAAK;AACd,MAAM,CAAC,EAAE,KAAK;AACd,MAAM,IAAI;AACV,MAAM;AACN,KAAK,GAAG,MAAM,EAAE,CAAC;AACjB,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,gBAAgB,EAAE,SAAS;AACjC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,QAAQ;AACd,MAAM,cAAc;AACpB,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE;AAChB,QAAQ,SAAS;AACjB,QAAQ;AACR;AACA,KAAK,CAAC;AACN,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC;AACjC,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,GAAG,CAAC;AACjC,IAAI,cAAc,GAAG;AACrB,MAAM,GAAG,cAAc;AACvB,MAAM,CAAC,IAAI,GAAG;AACd,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC;AAC/B,QAAQ,GAAG;AACX;AACA,KAAK;AACL,IAAI,IAAI,KAAK,IAAI,UAAU,IAAI,EAAE,EAAE;AACnC,MAAM,UAAU,EAAE;AAClB,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACrC,QAAQ,IAAI,KAAK,CAAC,SAAS,EAAE;AAC7B,UAAU,iBAAiB,GAAG,KAAK,CAAC,SAAS;AAC7C;AACA,QAAQ,IAAI,KAAK,CAAC,KAAK,EAAE;AACzB,UAAU,KAAK,GAAG,KAAK,CAAC,KAAK,KAAK,IAAI,GAAG,MAAM,QAAQ,CAAC,eAAe,CAAC;AACxE,YAAY,SAAS;AACrB,YAAY,QAAQ;AACpB,YAAY;AACZ,WAAW,CAAC,GAAG,KAAK,CAAC,KAAK;AAC1B;AACA,QAAQ,CAAC;AACT,UAAU,CAAC;AACX,UAAU;AACV,SAAS,GAAG,0BAA0B,CAAC,KAAK,EAAE,iBAAiB,EAAE,GAAG,CAAC;AACrE;AACA,MAAM,CAAC,GAAG,EAAE;AACZ;AACA;AACA,EAAE,OAAO;AACT,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,SAAS,EAAE,iBAAiB;AAChC,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,cAAc,CAAC,KAAK,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,qBAAqB;AAC3B,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;AAC1B,IAAI,OAAO,GAAG,EAAE;AAChB;AACA,EAAE,MAAM;AACR,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG,GAAG,KAAK;AACX,EAAE,MAAM;AACR,IAAI,QAAQ,GAAG,mBAAmB;AAClC,IAAI,YAAY,GAAG,UAAU;AAC7B,IAAI,cAAc,GAAG,UAAU;AAC/B,IAAI,WAAW,GAAG,KAAK;AACvB,IAAI,OAAO,GAAG;AACd,GAAG,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;AAC9B,EAAE,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACjD,EAAE,MAAM,UAAU,GAAG,cAAc,KAAK,UAAU,GAAG,WAAW,GAAG,UAAU;AAC7E,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,GAAG,UAAU,GAAG,cAAc,CAAC;AACrE,EAAE,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,MAAM,QAAQ,CAAC,eAAe,CAAC;AAC7E,IAAI,OAAO,EAAE,CAAC,CAAC,qBAAqB,GAAG,OAAO,QAAQ,CAAC,SAAS,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,qBAAqB,GAAG,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,cAAc,KAAK,OAAO,QAAQ,CAAC,kBAAkB,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;AACvS,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,IAAI,GAAG,cAAc,KAAK,UAAU,GAAG;AAC/C,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK;AAC/B,IAAI,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC;AAC3B,GAAG,GAAG,KAAK,CAAC,SAAS;AACrB,EAAE,MAAM,YAAY,GAAG,OAAO,QAAQ,CAAC,eAAe,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACtH,EAAE,MAAM,WAAW,GAAG,CAAC,OAAO,QAAQ,CAAC,SAAS,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK;AAC3L,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE;AACP,GAAG,GAAG;AACN,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE;AACP,GAAG;AACH,EAAE,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,QAAQ,CAAC,qDAAqD,GAAG,MAAM,QAAQ,CAAC,qDAAqD,CAAC;AACnL,IAAI,QAAQ;AACZ,IAAI,IAAI;AACR,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,CAAC,GAAG,IAAI,CAAC;AACZ,EAAE,OAAO;AACT,IAAI,GAAG,EAAE,CAAC,kBAAkB,CAAC,GAAG,GAAG,iBAAiB,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,IAAI,WAAW,CAAC,CAAC;AAC7F,IAAI,MAAM,EAAE,CAAC,iBAAiB,CAAC,MAAM,GAAG,kBAAkB,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,IAAI,WAAW,CAAC,CAAC;AACzG,IAAI,IAAI,EAAE,CAAC,kBAAkB,CAAC,IAAI,GAAG,iBAAiB,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,IAAI,WAAW,CAAC,CAAC;AACjG,IAAI,KAAK,EAAE,CAAC,iBAAiB,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,IAAI,WAAW,CAAC;AACpG,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAK,GAAG,OAAO,KAAK;AAC1B,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,OAAO;AACT,EAAE,MAAM,EAAE,CAAC,KAAK,EAAE;AAClB,IAAI,MAAM;AACV,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM;AACN,KAAK,GAAG,KAAK;AACb;AACA,IAAI,MAAM;AACV,MAAM,OAAO;AACb,MAAM,OAAO,GAAG;AAChB,KAAK,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,EAAE;AACtC,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;AACzB,MAAM,OAAO,EAAE;AACf;AACA,IAAI,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACnD,IAAI,MAAM,MAAM,GAAG;AACnB,MAAM,CAAC;AACP,MAAM;AACN,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,gBAAgB,CAAC,SAAS,CAAC;AAC5C,IAAI,MAAM,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC;AACtC,IAAI,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AACjE,IAAI,MAAM,OAAO,GAAG,IAAI,KAAK,GAAG;AAChC,IAAI,MAAM,OAAO,GAAG,OAAO,GAAG,KAAK,GAAG,MAAM;AAC5C,IAAI,MAAM,OAAO,GAAG,OAAO,GAAG,QAAQ,GAAG,OAAO;AAChD,IAAI,MAAM,UAAU,GAAG,OAAO,GAAG,cAAc,GAAG,aAAa;AAC/D,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC3G,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;AAC1D,IAAI,MAAM,iBAAiB,GAAG,OAAO,QAAQ,CAAC,eAAe,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACnH,IAAI,IAAI,UAAU,GAAG,iBAAiB,GAAG,iBAAiB,CAAC,UAAU,CAAC,GAAG,CAAC;;AAE1E;AACA,IAAI,IAAI,CAAC,UAAU,IAAI,EAAE,OAAO,QAAQ,CAAC,SAAS,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE;AAC/G,MAAM,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1E;AACA,IAAI,MAAM,iBAAiB,GAAG,OAAO,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC;;AAEzD;AACA;AACA,IAAI,MAAM,sBAAsB,GAAG,UAAU,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACnF,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,sBAAsB,CAAC;AAC1E,IAAI,MAAM,UAAU,GAAG,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,sBAAsB,CAAC;;AAE1E;AACA;AACA,IAAI,MAAM,KAAK,GAAG,UAAU;AAC5B,IAAI,MAAM,GAAG,GAAG,UAAU,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,UAAU;AACjE,IAAI,MAAM,MAAM,GAAG,UAAU,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,iBAAiB;AACnF,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,CAAC;;AAE5C;AACA;AACA;AACA;AACA,IAAI,MAAM,eAAe,GAAG,CAAC,cAAc,CAAC,KAAK,IAAI,YAAY,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,MAAM,GAAG,KAAK,GAAG,UAAU,GAAG,UAAU,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACvN,IAAI,MAAM,eAAe,GAAG,eAAe,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,KAAK,GAAG,MAAM,GAAG,GAAG,GAAG,CAAC;AAChG,IAAI,OAAO;AACX,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,eAAe;AAC5C,MAAM,IAAI,EAAE;AACZ,QAAQ,CAAC,IAAI,GAAG,MAAM;AACtB,QAAQ,YAAY,EAAE,MAAM,GAAG,MAAM,GAAG,eAAe;AACvD,QAAQ,IAAI,eAAe,IAAI;AAC/B,UAAU;AACV,SAAS;AACT,OAAO;AACP,MAAM,KAAK,EAAE;AACb,KAAK;AACL;AACA,CAAC,CAAC;;AAyGF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAI,GAAG,UAAU,OAAO,EAAE;AAChC,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;AAC1B,IAAI,OAAO,GAAG,EAAE;AAChB;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO;AACX,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE;AACpB,MAAM,IAAI,qBAAqB,EAAE,oBAAoB;AACrD,MAAM,MAAM;AACZ,QAAQ,SAAS;AACjB,QAAQ,cAAc;AACtB,QAAQ,KAAK;AACb,QAAQ,gBAAgB;AACxB,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO,GAAG,KAAK;AACf,MAAM,MAAM;AACZ,QAAQ,QAAQ,EAAE,aAAa,GAAG,IAAI;AACtC,QAAQ,SAAS,EAAE,cAAc,GAAG,IAAI;AACxC,QAAQ,kBAAkB,EAAE,2BAA2B;AACvD,QAAQ,gBAAgB,GAAG,SAAS;AACpC,QAAQ,yBAAyB,GAAG,MAAM;AAC1C,QAAQ,aAAa,GAAG,IAAI;AAC5B,QAAQ,GAAG;AACX,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;;AAElC;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,qBAAqB,GAAG,cAAc,CAAC,KAAK,KAAK,IAAI,IAAI,qBAAqB,CAAC,eAAe,EAAE;AAC3G,QAAQ,OAAO,EAAE;AACjB;AACA,MAAM,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;AACrC,MAAM,MAAM,eAAe,GAAG,WAAW,CAAC,gBAAgB,CAAC;AAC3D,MAAM,MAAM,eAAe,GAAG,OAAO,CAAC,gBAAgB,CAAC,KAAK,gBAAgB;AAC5E,MAAM,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,KAAK,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7F,MAAM,MAAM,kBAAkB,GAAG,2BAA2B,KAAK,eAAe,IAAI,CAAC,aAAa,GAAG,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC,GAAG,qBAAqB,CAAC,gBAAgB,CAAC,CAAC;AACxL,MAAM,MAAM,4BAA4B,GAAG,yBAAyB,KAAK,MAAM;AAC/E,MAAM,IAAI,CAAC,2BAA2B,IAAI,4BAA4B,EAAE;AACxE,QAAQ,kBAAkB,CAAC,IAAI,CAAC,GAAG,yBAAyB,CAAC,gBAAgB,EAAE,aAAa,EAAE,yBAAyB,EAAE,GAAG,CAAC,CAAC;AAC9H;AACA,MAAM,MAAM,UAAU,GAAG,CAAC,gBAAgB,EAAE,GAAG,kBAAkB,CAAC;AAClE,MAAM,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,KAAK,EAAE,qBAAqB,CAAC;AACzE,MAAM,MAAM,SAAS,GAAG,EAAE;AAC1B,MAAM,IAAI,aAAa,GAAG,CAAC,CAAC,oBAAoB,GAAG,cAAc,CAAC,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,oBAAoB,CAAC,SAAS,KAAK,EAAE;AAChI,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtC;AACA,MAAM,IAAI,cAAc,EAAE;AAC1B,QAAQ,MAAM,KAAK,GAAG,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,GAAG,CAAC;AAC9D,QAAQ,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D;AACA,MAAM,aAAa,GAAG,CAAC,GAAG,aAAa,EAAE;AACzC,QAAQ,SAAS;AACjB,QAAQ;AACR,OAAO,CAAC;;AAER;AACA,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,EAAE;AAC/C,QAAQ,IAAI,qBAAqB,EAAE,qBAAqB;AACxD,QAAQ,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,qBAAqB,GAAG,cAAc,CAAC,IAAI,KAAK,IAAI,GAAG,MAAM,GAAG,qBAAqB,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC;AACnI,QAAQ,MAAM,aAAa,GAAG,UAAU,CAAC,SAAS,CAAC;AACnD,QAAQ,IAAI,aAAa,EAAE;AAC3B,UAAU,IAAI,eAAe;AAC7B,UAAU,MAAM,uBAAuB,GAAG,cAAc,KAAK,WAAW,GAAG,eAAe,KAAK,WAAW,CAAC,aAAa,CAAC,GAAG,KAAK;AACjI,UAAU,MAAM,0BAA0B,GAAG,CAAC,CAAC,eAAe,GAAG,aAAa,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;AACvI,UAAU,IAAI,CAAC,uBAAuB,IAAI,0BAA0B,EAAE;AACtE;AACA,YAAY,OAAO;AACnB,cAAc,IAAI,EAAE;AACpB,gBAAgB,KAAK,EAAE,SAAS;AAChC,gBAAgB,SAAS,EAAE;AAC3B,eAAe;AACf,cAAc,KAAK,EAAE;AACrB,gBAAgB,SAAS,EAAE;AAC3B;AACA,aAAa;AACb;AACA;;AAEA;AACA;AACA,QAAQ,IAAI,cAAc,GAAG,CAAC,qBAAqB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,qBAAqB,CAAC,SAAS;;AAE3M;AACA,QAAQ,IAAI,CAAC,cAAc,EAAE;AAC7B,UAAU,QAAQ,gBAAgB;AAClC,YAAY,KAAK,SAAS;AAC1B,cAAc;AACd,gBAAgB,IAAI,sBAAsB;AAC1C,gBAAgB,MAAM,SAAS,GAAG,CAAC,sBAAsB,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI;AACtF,kBAAkB,IAAI,4BAA4B,EAAE;AACpD,oBAAoB,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,CAAC,SAAS,CAAC;AACpE,oBAAoB,OAAO,eAAe,KAAK,eAAe;AAC9D;AACA;AACA,oBAAoB,eAAe,KAAK,GAAG;AAC3C;AACA,kBAAkB,OAAO,IAAI;AAC7B,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,QAAQ,KAAK,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,MAAM,GAAG,sBAAsB,CAAC,CAAC,CAAC;AAClN,gBAAgB,IAAI,SAAS,EAAE;AAC/B,kBAAkB,cAAc,GAAG,SAAS;AAC5C;AACA,gBAAgB;AAChB;AACA,YAAY,KAAK,kBAAkB;AACnC,cAAc,cAAc,GAAG,gBAAgB;AAC/C,cAAc;AACd;AACA;AACA,QAAQ,IAAI,SAAS,KAAK,cAAc,EAAE;AAC1C,UAAU,OAAO;AACjB,YAAY,KAAK,EAAE;AACnB,cAAc,SAAS,EAAE;AACzB;AACA,WAAW;AACX;AACA;AACA,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,CAAC;;AAED,SAAS,cAAc,CAAC,QAAQ,EAAE,IAAI,EAAE;AACxC,EAAE,OAAO;AACT,IAAI,GAAG,EAAE,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM;AACnC,IAAI,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;AACtC,IAAI,MAAM,EAAE,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;AACzC,IAAI,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH;AACA,SAAS,qBAAqB,CAAC,QAAQ,EAAE;AACzC,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAI,GAAG,UAAU,OAAO,EAAE;AAChC,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;AAC1B,IAAI,OAAO,GAAG,EAAE;AAChB;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO;AACX,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE;AACpB,MAAM,MAAM;AACZ,QAAQ;AACR,OAAO,GAAG,KAAK;AACf,MAAM,MAAM;AACZ,QAAQ,QAAQ,GAAG,iBAAiB;AACpC,QAAQ,GAAG;AACX,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;AAClC,MAAM,QAAQ,QAAQ;AACtB,QAAQ,KAAK,iBAAiB;AAC9B,UAAU;AACV,YAAY,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,KAAK,EAAE;AACzD,cAAc,GAAG,qBAAqB;AACtC,cAAc,cAAc,EAAE;AAC9B,aAAa,CAAC;AACd,YAAY,MAAM,OAAO,GAAG,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC;AACrE,YAAY,OAAO;AACnB,cAAc,IAAI,EAAE;AACpB,gBAAgB,sBAAsB,EAAE,OAAO;AAC/C,gBAAgB,eAAe,EAAE,qBAAqB,CAAC,OAAO;AAC9D;AACA,aAAa;AACb;AACA,QAAQ,KAAK,SAAS;AACtB,UAAU;AACV,YAAY,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,KAAK,EAAE;AACzD,cAAc,GAAG,qBAAqB;AACtC,cAAc,WAAW,EAAE;AAC3B,aAAa,CAAC;AACd,YAAY,MAAM,OAAO,GAAG,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC;AACpE,YAAY,OAAO;AACnB,cAAc,IAAI,EAAE;AACpB,gBAAgB,cAAc,EAAE,OAAO;AACvC,gBAAgB,OAAO,EAAE,qBAAqB,CAAC,OAAO;AACtD;AACA,aAAa;AACb;AACA,QAAQ;AACR,UAAU;AACV,YAAY,OAAO,EAAE;AACrB;AACA;AACA;AACA,GAAG;AACH,CAAC;;AAqID;AACA;;AAEA,eAAe,oBAAoB,CAAC,KAAK,EAAE,OAAO,EAAE;AACpD,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG,GAAG,KAAK;AACX,EAAE,MAAM,GAAG,GAAG,OAAO,QAAQ,CAAC,KAAK,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACzF,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;AACjC,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;AAC3C,EAAE,MAAM,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG;AACnD,EAAE,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;AAC/D,EAAE,MAAM,cAAc,GAAG,GAAG,IAAI,UAAU,GAAG,EAAE,GAAG,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;;AAE3C;AACA,EAAE,IAAI;AACN,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI;AACJ,GAAG,GAAG,OAAO,QAAQ,KAAK,QAAQ,GAAG;AACrC,IAAI,QAAQ,EAAE,QAAQ;AACtB,IAAI,SAAS,EAAE,CAAC;AAChB,IAAI,aAAa,EAAE;AACnB,GAAG,GAAG;AACN,IAAI,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,CAAC;AACpC,IAAI,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,CAAC;AACtC,IAAI,aAAa,EAAE,QAAQ,CAAC;AAC5B,GAAG;AACH,EAAE,IAAI,SAAS,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;AACtD,IAAI,SAAS,GAAG,SAAS,KAAK,KAAK,GAAG,aAAa,GAAG,EAAE,GAAG,aAAa;AACxE;AACA,EAAE,OAAO,UAAU,GAAG;AACtB,IAAI,CAAC,EAAE,SAAS,GAAG,cAAc;AACjC,IAAI,CAAC,EAAE,QAAQ,GAAG;AAClB,GAAG,GAAG;AACN,IAAI,CAAC,EAAE,QAAQ,GAAG,aAAa;AAC/B,IAAI,CAAC,EAAE,SAAS,GAAG;AACnB,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAM,GAAG,UAAU,OAAO,EAAE;AAClC,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;AAC1B,IAAI,OAAO,GAAG,CAAC;AACf;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,OAAO;AACX,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE;AACpB,MAAM,IAAI,qBAAqB,EAAE,qBAAqB;AACtD,MAAM,MAAM;AACZ,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,SAAS;AACjB,QAAQ;AACR,OAAO,GAAG,KAAK;AACf,MAAM,MAAM,UAAU,GAAG,MAAM,oBAAoB,CAAC,KAAK,EAAE,OAAO,CAAC;;AAEnE;AACA;AACA,MAAM,IAAI,SAAS,MAAM,CAAC,qBAAqB,GAAG,cAAc,CAAC,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,GAAG,cAAc,CAAC,KAAK,KAAK,IAAI,IAAI,qBAAqB,CAAC,eAAe,EAAE;AACjO,QAAQ,OAAO,EAAE;AACjB;AACA,MAAM,OAAO;AACb,QAAQ,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC;AAC3B,QAAQ,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,CAAC;AAC3B,QAAQ,IAAI,EAAE;AACd,UAAU,GAAG,UAAU;AACvB,UAAU;AACV;AACA,OAAO;AACP;AACA,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAK,GAAG,UAAU,OAAO,EAAE;AACjC,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;AAC1B,IAAI,OAAO,GAAG,EAAE;AAChB;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO;AACX,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE;AACpB,MAAM,MAAM;AACZ,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO,GAAG,KAAK;AACf,MAAM,MAAM;AACZ,QAAQ,QAAQ,EAAE,aAAa,GAAG,IAAI;AACtC,QAAQ,SAAS,EAAE,cAAc,GAAG,KAAK;AACzC,QAAQ,OAAO,GAAG;AAClB,UAAU,EAAE,EAAE,IAAI,IAAI;AACtB,YAAY,IAAI;AAChB,cAAc,CAAC;AACf,cAAc;AACd,aAAa,GAAG,IAAI;AACpB,YAAY,OAAO;AACnB,cAAc,CAAC;AACf,cAAc;AACd,aAAa;AACb;AACA,SAAS;AACT,QAAQ,GAAG;AACX,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;AAClC,MAAM,MAAM,MAAM,GAAG;AACrB,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,MAAM,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,KAAK,EAAE,qBAAqB,CAAC;AACzE,MAAM,MAAM,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACvD,MAAM,MAAM,QAAQ,GAAG,eAAe,CAAC,SAAS,CAAC;AACjD,MAAM,IAAI,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC1C,MAAM,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC;AAC5C,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,MAAM,OAAO,GAAG,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;AACzD,QAAQ,MAAM,OAAO,GAAG,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AAC7D,QAAQ,MAAM,GAAG,GAAG,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrD,QAAQ,MAAM,GAAG,GAAG,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrD,QAAQ,aAAa,GAAG,KAAK,CAAC,GAAG,EAAE,aAAa,EAAE,GAAG,CAAC;AACtD;AACA,MAAM,IAAI,cAAc,EAAE;AAC1B,QAAQ,MAAM,OAAO,GAAG,SAAS,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM;AAC1D,QAAQ,MAAM,OAAO,GAAG,SAAS,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AAC9D,QAAQ,MAAM,GAAG,GAAG,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC;AACtD,QAAQ,MAAM,GAAG,GAAG,cAAc,GAAG,QAAQ,CAAC,OAAO,CAAC;AACtD,QAAQ,cAAc,GAAG,KAAK,CAAC,GAAG,EAAE,cAAc,EAAE,GAAG,CAAC;AACxD;AACA,MAAM,MAAM,aAAa,GAAG,OAAO,CAAC,EAAE,CAAC;AACvC,QAAQ,GAAG,KAAK;AAChB,QAAQ,CAAC,QAAQ,GAAG,aAAa;AACjC,QAAQ,CAAC,SAAS,GAAG;AACrB,OAAO,CAAC;AACR,MAAM,OAAO;AACb,QAAQ,GAAG,aAAa;AACxB,QAAQ,IAAI,EAAE;AACd,UAAU,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC;AAChC,UAAU,CAAC,EAAE,aAAa,CAAC,CAAC,GAAG,CAAC;AAChC,UAAU,OAAO,EAAE;AACnB,YAAY,CAAC,QAAQ,GAAG,aAAa;AACrC,YAAY,CAAC,SAAS,GAAG;AACzB;AACA;AACA,OAAO;AACP;AACA,GAAG;AACH,CAAC;AACD;AACA;AACA;AACA,MAAMC,YAAU,GAAG,UAAU,OAAO,EAAE;AACtC,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;AAC1B,IAAI,OAAO,GAAG,EAAE;AAChB;AACA,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,EAAE,CAAC,KAAK,EAAE;AACd,MAAM,MAAM;AACZ,QAAQ,CAAC;AACT,QAAQ,CAAC;AACT,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ;AACR,OAAO,GAAG,KAAK;AACf,MAAM,MAAM;AACZ,QAAQ,MAAM,GAAG,CAAC;AAClB,QAAQ,QAAQ,EAAE,aAAa,GAAG,IAAI;AACtC,QAAQ,SAAS,EAAE,cAAc,GAAG;AACpC,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;AAClC,MAAM,MAAM,MAAM,GAAG;AACrB,QAAQ,CAAC;AACT,QAAQ;AACR,OAAO;AACP,MAAM,MAAM,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AAC9C,MAAM,MAAM,QAAQ,GAAG,eAAe,CAAC,SAAS,CAAC;AACjD,MAAM,IAAI,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC1C,MAAM,IAAI,cAAc,GAAG,MAAM,CAAC,SAAS,CAAC;AAC5C,MAAM,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC;AAC/C,MAAM,MAAM,cAAc,GAAG,OAAO,SAAS,KAAK,QAAQ,GAAG;AAC7D,QAAQ,QAAQ,EAAE,SAAS;AAC3B,QAAQ,SAAS,EAAE;AACnB,OAAO,GAAG;AACV,QAAQ,QAAQ,EAAE,CAAC;AACnB,QAAQ,SAAS,EAAE,CAAC;AACpB,QAAQ,GAAG;AACX,OAAO;AACP,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,MAAM,GAAG,GAAG,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AACzD,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,QAAQ;AAClG,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,QAAQ;AACnG,QAAQ,IAAI,aAAa,GAAG,QAAQ,EAAE;AACtC,UAAU,aAAa,GAAG,QAAQ;AAClC,SAAS,MAAM,IAAI,aAAa,GAAG,QAAQ,EAAE;AAC7C,UAAU,aAAa,GAAG,QAAQ;AAClC;AACA;AACA,MAAM,IAAI,cAAc,EAAE;AAC1B,QAAQ,IAAI,qBAAqB,EAAE,sBAAsB;AACzD,QAAQ,MAAM,GAAG,GAAG,QAAQ,KAAK,GAAG,GAAG,OAAO,GAAG,QAAQ;AACzD,QAAQ,MAAM,YAAY,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACzE,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC,qBAAqB,GAAG,cAAc,CAAC,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,qBAAqB,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,YAAY,GAAG,CAAC,GAAG,cAAc,CAAC,SAAS,CAAC;AAC3P,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,GAAG,cAAc,CAAC,MAAM,KAAK,IAAI,GAAG,MAAM,GAAG,sBAAsB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,YAAY,GAAG,cAAc,CAAC,SAAS,GAAG,CAAC,CAAC;AAC9P,QAAQ,IAAI,cAAc,GAAG,QAAQ,EAAE;AACvC,UAAU,cAAc,GAAG,QAAQ;AACnC,SAAS,MAAM,IAAI,cAAc,GAAG,QAAQ,EAAE;AAC9C,UAAU,cAAc,GAAG,QAAQ;AACnC;AACA;AACA,MAAM,OAAO;AACb,QAAQ,CAAC,QAAQ,GAAG,aAAa;AACjC,QAAQ,CAAC,SAAS,GAAG;AACrB,OAAO;AACP;AACA,GAAG;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAI,GAAG,UAAU,OAAO,EAAE;AAChC,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;AAC1B,IAAI,OAAO,GAAG,EAAE;AAChB;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO;AACX,IAAI,MAAM,EAAE,CAAC,KAAK,EAAE;AACpB,MAAM,IAAI,qBAAqB,EAAE,sBAAsB;AACvD,MAAM,MAAM;AACZ,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,QAAQ;AAChB,QAAQ;AACR,OAAO,GAAG,KAAK;AACf,MAAM,MAAM;AACZ,QAAQ,KAAK,GAAG,MAAM,EAAE;AACxB,QAAQ,GAAG;AACX,OAAO,GAAG,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC;AAClC,MAAM,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC,KAAK,EAAE,qBAAqB,CAAC;AACzE,MAAM,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;AACrC,MAAM,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC;AAC/C,MAAM,MAAM,OAAO,GAAG,WAAW,CAAC,SAAS,CAAC,KAAK,GAAG;AACpD,MAAM,MAAM;AACZ,QAAQ,KAAK;AACb,QAAQ;AACR,OAAO,GAAG,KAAK,CAAC,QAAQ;AACxB,MAAM,IAAI,UAAU;AACpB,MAAM,IAAI,SAAS;AACnB,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,QAAQ,EAAE;AAC/C,QAAQ,UAAU,GAAG,IAAI;AACzB,QAAQ,SAAS,GAAG,SAAS,MAAM,CAAC,OAAO,QAAQ,CAAC,KAAK,IAAI,IAAI,GAAG,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,OAAO,GAAG,KAAK,CAAC,GAAG,MAAM,GAAG,OAAO;AACtJ,OAAO,MAAM;AACb,QAAQ,SAAS,GAAG,IAAI;AACxB,QAAQ,UAAU,GAAG,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,QAAQ;AAC3D;AACA,MAAM,MAAM,qBAAqB,GAAG,MAAM,GAAG,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM;AAC3E,MAAM,MAAM,oBAAoB,GAAG,KAAK,GAAG,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK;AACzE,MAAM,MAAM,uBAAuB,GAAG,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,EAAE,qBAAqB,CAAC;AAC/F,MAAM,MAAM,sBAAsB,GAAG,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,EAAE,oBAAoB,CAAC;AAC3F,MAAM,MAAM,OAAO,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK;AACjD,MAAM,IAAI,eAAe,GAAG,uBAAuB;AACnD,MAAM,IAAI,cAAc,GAAG,sBAAsB;AACjD,MAAM,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,KAAK,IAAI,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAC,EAAE;AAC3G,QAAQ,cAAc,GAAG,oBAAoB;AAC7C;AACA,MAAM,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,KAAK,IAAI,IAAI,sBAAsB,CAAC,OAAO,CAAC,CAAC,EAAE;AAC7G,QAAQ,eAAe,GAAG,qBAAqB;AAC/C;AACA,MAAM,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;AACjC,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1C,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;AAC3C,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC;AACzC,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;AAC5C,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,cAAc,GAAG,KAAK,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpH,SAAS,MAAM;AACf,UAAU,eAAe,GAAG,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtH;AACA;AACA,MAAM,MAAM,KAAK,CAAC;AAClB,QAAQ,GAAG,KAAK;AAChB,QAAQ,cAAc;AACtB,QAAQ;AACR,OAAO,CAAC;AACR,MAAM,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC5E,MAAM,IAAI,KAAK,KAAK,cAAc,CAAC,KAAK,IAAI,MAAM,KAAK,cAAc,CAAC,MAAM,EAAE;AAC9E,QAAQ,OAAO;AACf,UAAU,KAAK,EAAE;AACjB,YAAY,KAAK,EAAE;AACnB;AACA,SAAS;AACT;AACA,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,CAAC;;ACnhCD,SAAS,SAAS,GAAG;AACrB,EAAE,OAAO,OAAO,MAAM,KAAK,WAAW;AACtC;AACA,SAAS,WAAW,CAAC,IAAI,EAAE;AAC3B,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;AACpB,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,EAAE,EAAE,WAAW,EAAE;AAC9C;AACA;AACA;AACA;AACA,EAAE,OAAO,WAAW;AACpB;AACA,SAAS,SAAS,CAAC,IAAI,EAAE;AACzB,EAAE,IAAI,mBAAmB;AACzB,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI,GAAG,MAAM,GAAG,mBAAmB,CAAC,WAAW,KAAK,MAAM;AAClI;AACA,SAAS,kBAAkB,CAAC,IAAI,EAAE;AAClC,EAAE,IAAI,IAAI;AACV,EAAE,OAAO,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,QAAQ,KAAK,IAAI,GAAG,MAAM,GAAG,IAAI,CAAC,eAAe;AAChI;AACA,SAAS,MAAM,CAAC,KAAK,EAAE;AACvB,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE;AACpB,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,KAAK,YAAY,IAAI,IAAI,KAAK,YAAY,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI;AACxE;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE;AACpB,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,KAAK,YAAY,OAAO,IAAI,KAAK,YAAY,SAAS,CAAC,KAAK,CAAC,CAAC,OAAO;AAC9E;AACA,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE;AACpB,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,KAAK,YAAY,WAAW,IAAI,KAAK,YAAY,SAAS,CAAC,KAAK,CAAC,CAAC,WAAW;AACtF;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,OAAO,UAAU,KAAK,WAAW,EAAE;AACzD,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,KAAK,YAAY,UAAU,IAAI,KAAK,YAAY,SAAS,CAAC,KAAK,CAAC,CAAC,UAAU;AACpF;AACA,SAAS,iBAAiB,CAAC,OAAO,EAAE;AACpC,EAAE,MAAM;AACR,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI;AACJ,GAAG,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC/B,EAAE,OAAO,iCAAiC,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC9H;AACA,SAAS,cAAc,CAAC,OAAO,EAAE;AACjC,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC7D;AACA,SAAS,UAAU,CAAC,OAAO,EAAE;AAC7B,EAAE,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI;AACtD,IAAI,IAAI;AACR,MAAM,OAAO,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC;AACtC,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,OAAO,KAAK;AAClB;AACA,GAAG,CAAC;AACJ;AACA,SAAS,iBAAiB,CAAC,YAAY,EAAE;AACzC,EAAE,MAAM,MAAM,GAAG,QAAQ,EAAE;AAC3B,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,gBAAgB,CAAC,YAAY,CAAC,GAAG,YAAY;;AAErF;AACA;AACA,EAAE,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,MAAM,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,aAAa,GAAG,GAAG,CAAC,aAAa,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,CAAC,cAAc,GAAG,GAAG,CAAC,cAAc,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,KAAK,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;AACpiB;AACA,SAAS,kBAAkB,CAAC,OAAO,EAAE;AACrC,EAAE,IAAI,WAAW,GAAG,aAAa,CAAC,OAAO,CAAC;AAC1C,EAAE,OAAO,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE;AAC5E,IAAI,IAAI,iBAAiB,CAAC,WAAW,CAAC,EAAE;AACxC,MAAM,OAAO,WAAW;AACxB,KAAK,MAAM,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE;AACxC,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AAC5C;AACA,EAAE,OAAO,IAAI;AACb;AACA,SAAS,QAAQ,GAAG;AACpB,EAAE,IAAI,OAAO,GAAG,KAAK,WAAW,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,KAAK;AAC/D,EAAE,OAAO,GAAG,CAAC,QAAQ,CAAC,yBAAyB,EAAE,MAAM,CAAC;AACxD;AACA,SAAS,qBAAqB,CAAC,IAAI,EAAE;AACrC,EAAE,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAClE;AACA,SAAS,gBAAgB,CAAC,OAAO,EAAE;AACnC,EAAE,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC,gBAAgB,CAAC,OAAO,CAAC;AACrD;AACA,SAAS,aAAa,CAAC,OAAO,EAAE;AAChC,EAAE,IAAI,SAAS,CAAC,OAAO,CAAC,EAAE;AAC1B,IAAI,OAAO;AACX,MAAM,UAAU,EAAE,OAAO,CAAC,UAAU;AACpC,MAAM,SAAS,EAAE,OAAO,CAAC;AACzB,KAAK;AACL;AACA,EAAE,OAAO;AACT,IAAI,UAAU,EAAE,OAAO,CAAC,OAAO;AAC/B,IAAI,SAAS,EAAE,OAAO,CAAC;AACvB,GAAG;AACH;AACA,SAAS,aAAa,CAAC,IAAI,EAAE;AAC7B,EAAE,IAAI,WAAW,CAAC,IAAI,CAAC,KAAK,MAAM,EAAE;AACpC,IAAI,OAAO,IAAI;AACf;AACA,EAAE,MAAM,MAAM;AACd;AACA,EAAE,IAAI,CAAC,YAAY;AACnB;AACA,EAAE,IAAI,CAAC,UAAU;AACjB;AACA,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;AACjC;AACA,EAAE,kBAAkB,CAAC,IAAI,CAAC;AAC1B,EAAE,OAAO,YAAY,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM;AACpD;AACA,SAAS,0BAA0B,CAAC,IAAI,EAAE;AAC1C,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC;AACxC,EAAE,IAAI,qBAAqB,CAAC,UAAU,CAAC,EAAE;AACzC,IAAI,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;AACnE;AACA,EAAE,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,iBAAiB,CAAC,UAAU,CAAC,EAAE;AAClE,IAAI,OAAO,UAAU;AACrB;AACA,EAAE,OAAO,0BAA0B,CAAC,UAAU,CAAC;AAC/C;AACA,SAAS,oBAAoB,CAAC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE;AAC3D,EAAE,IAAI,oBAAoB;AAC1B,EAAE,IAAI,IAAI,KAAK,MAAM,EAAE;AACvB,IAAI,IAAI,GAAG,EAAE;AACb;AAIA,EAAE,MAAM,kBAAkB,GAAG,0BAA0B,CAAC,IAAI,CAAC;AAC7D,EAAE,MAAM,MAAM,GAAG,kBAAkB,MAAM,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI,GAAG,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC;AAClI,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,kBAAkB,CAAC;AAC3C,EAAE,IAAI,MAAM,EAAE;AACd,IAAyB,eAAe,CAAC,GAAG;AAC5C,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,cAAc,IAAI,EAAE,EAAE,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,kBAAkB,GAAG,EAAE,EAAyE,EAAE,CAAC;AACjM;AACA,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,kBAAkB,EAAE,EAAmB,CAAC,CAAC;AACvG;AACA,SAAS,eAAe,CAAC,GAAG,EAAE;AAC9B,EAAE,OAAO,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,YAAY,GAAG,IAAI;AAClF;;AClJA,SAAS,gBAAgB,CAAC,OAAO,EAAE;AACnC,EAAE,MAAM,GAAG,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACvC;AACA;AACA,EAAE,IAAI,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;AACxC,EAAE,IAAI,MAAM,GAAG,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AAC1C,EAAE,MAAM,SAAS,GAAG,aAAa,CAAC,OAAO,CAAC;AAC1C,EAAE,MAAM,WAAW,GAAG,SAAS,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK;AAC7D,EAAE,MAAM,YAAY,GAAG,SAAS,GAAG,OAAO,CAAC,YAAY,GAAG,MAAM;AAChE,EAAE,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,WAAW,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,YAAY;AACvF,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,KAAK,GAAG,WAAW;AACvB,IAAI,MAAM,GAAG,YAAY;AACzB;AACA,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,CAAC,EAAE;AACP,GAAG;AACH;;AAEA,SAAS,aAAa,CAAC,OAAO,EAAE;AAChC,EAAE,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,cAAc,GAAG,OAAO;AAC/D;;AAEA,SAAS,QAAQ,CAAC,OAAO,EAAE;AAC3B,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC;AAC3C,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAE;AAClC,IAAI,OAAO,YAAY,CAAC,CAAC,CAAC;AAC1B;AACA,EAAE,MAAM,IAAI,GAAG,UAAU,CAAC,qBAAqB,EAAE;AACjD,EAAE,MAAM;AACR,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,GAAG,gBAAgB,CAAC,UAAU,CAAC;AAClC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,KAAK;AACtD,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,IAAI,MAAM;;AAEzD;;AAEA,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AACjC,IAAI,CAAC,GAAG,CAAC;AACT;AACA,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;AACjC,IAAI,CAAC,GAAG,CAAC;AACT;AACA,EAAE,OAAO;AACT,IAAI,CAAC;AACL,IAAI;AACJ,GAAG;AACH;;AAEA,MAAM,SAAS,gBAAgB,YAAY,CAAC,CAAC,CAAC;AAC9C,SAAS,gBAAgB,CAAC,OAAO,EAAE;AACnC,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC;AAChC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE;AAC1C,IAAI,OAAO,SAAS;AACpB;AACA,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,GAAG,CAAC,cAAc,CAAC,UAAU;AACpC,IAAI,CAAC,EAAE,GAAG,CAAC,cAAc,CAAC;AAC1B,GAAG;AACH;AACA,SAAS,sBAAsB,CAAC,OAAO,EAAE,OAAO,EAAE,oBAAoB,EAAE;AACxE,EAAE,IAAI,OAAO,KAAK,MAAM,EAAE;AAC1B,IAAI,OAAO,GAAG,KAAK;AACnB;AACA,EAAE,IAAI,CAAC,oBAAoB,IAAI,OAAO,IAAI,oBAAoB,KAAK,SAAS,CAAC,OAAO,CAAC,EAAE;AACvF,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,OAAO;AAChB;;AAEA,SAAS,qBAAqB,CAAC,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,YAAY,EAAE;AACrF,EAAE,IAAI,YAAY,KAAK,MAAM,EAAE;AAC/B,IAAI,YAAY,GAAG,KAAK;AACxB;AACA,EAAE,IAAI,eAAe,KAAK,MAAM,EAAE;AAClC,IAAI,eAAe,GAAG,KAAK;AAC3B;AACA,EAAE,MAAM,UAAU,GAAG,OAAO,CAAC,qBAAqB,EAAE;AACpD,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC;AAC3C,EAAE,IAAI,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,IAAI,SAAS,CAAC,YAAY,CAAC,EAAE;AACnC,QAAQ,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC;AACtC;AACA,KAAK,MAAM;AACX,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,EAAE,MAAM,aAAa,GAAG,sBAAsB,CAAC,UAAU,EAAE,eAAe,EAAE,YAAY,CAAC,GAAG,gBAAgB,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AAC1I,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,GAAG,aAAa,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;AACvD,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,GAAG,aAAa,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;AACtD,EAAE,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AACxC,EAAE,IAAI,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AAC1C,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,MAAM,GAAG,GAAG,SAAS,CAAC,UAAU,CAAC;AACrC,IAAI,MAAM,SAAS,GAAG,YAAY,IAAI,SAAS,CAAC,YAAY,CAAC,GAAG,SAAS,CAAC,YAAY,CAAC,GAAG,YAAY;AACtG,IAAI,IAAI,UAAU,GAAG,GAAG;AACxB,IAAI,IAAI,aAAa,GAAG,eAAe,CAAC,UAAU,CAAC;AACnD,IAAI,OAAO,aAAa,IAAI,YAAY,IAAI,SAAS,KAAK,UAAU,EAAE;AACtE,MAAM,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC;AACjD,MAAM,MAAM,UAAU,GAAG,aAAa,CAAC,qBAAqB,EAAE;AAC9D,MAAM,MAAM,GAAG,GAAG,gBAAgB,CAAC,aAAa,CAAC;AACjD,MAAM,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,UAAU,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,WAAW,CAAC,CAAC;AAC7G,MAAM,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,SAAS,GAAG,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,WAAW,CAAC,CAAC;AACzG,MAAM,CAAC,IAAI,WAAW,CAAC,CAAC;AACxB,MAAM,CAAC,IAAI,WAAW,CAAC,CAAC;AACxB,MAAM,KAAK,IAAI,WAAW,CAAC,CAAC;AAC5B,MAAM,MAAM,IAAI,WAAW,CAAC,CAAC;AAC7B,MAAM,CAAC,IAAI,IAAI;AACf,MAAM,CAAC,IAAI,GAAG;AACd,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,CAAC;AAC3C,MAAM,aAAa,GAAG,eAAe,CAAC,UAAU,CAAC;AACjD;AACA;AACA,EAAE,OAAO,gBAAgB,CAAC;AAC1B,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,CAAC;AACL,IAAI;AACJ,GAAG,CAAC;AACJ;;AAEA;AACA;AACA,SAAS,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE;AAC5C,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC,UAAU;AACtD,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,qBAAqB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,GAAG,UAAU;AAC/E;AACA,EAAE,OAAO,IAAI,CAAC,IAAI,GAAG,UAAU;AAC/B;;AAEA,SAAS,aAAa,CAAC,eAAe,EAAE,MAAM,EAAE,gBAAgB,EAAE;AAClE,EAAE,IAAI,gBAAgB,KAAK,MAAM,EAAE;AACnC,IAAI,gBAAgB,GAAG,KAAK;AAC5B;AACA,EAAE,MAAM,QAAQ,GAAG,eAAe,CAAC,qBAAqB,EAAE;AAC1D,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,GAAG,MAAM,CAAC,UAAU,IAAI,gBAAgB,GAAG,CAAC;AACrE;AACA,EAAE,mBAAmB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;AACjD,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS;AAC3C,EAAE,OAAO;AACT,IAAI,CAAC;AACL,IAAI;AACJ,GAAG;AACH;;AAEA,SAAS,qDAAqD,CAAC,IAAI,EAAE;AACrE,EAAE,IAAI;AACN,IAAI,QAAQ;AACZ,IAAI,IAAI;AACR,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,OAAO,GAAG,QAAQ,KAAK,OAAO;AACtC,EAAE,MAAM,eAAe,GAAG,kBAAkB,CAAC,YAAY,CAAC;AAC1D,EAAE,MAAM,QAAQ,GAAG,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK;AACnE,EAAE,IAAI,YAAY,KAAK,eAAe,IAAI,QAAQ,IAAI,OAAO,EAAE;AAC/D,IAAI,OAAO,IAAI;AACf;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,UAAU,EAAE,CAAC;AACjB,IAAI,SAAS,EAAE;AACf,GAAG;AACH,EAAE,IAAI,KAAK,GAAG,YAAY,CAAC,CAAC,CAAC;AAC7B,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC;AACjC,EAAE,MAAM,uBAAuB,GAAG,aAAa,CAAC,YAAY,CAAC;AAC7D,EAAE,IAAI,uBAAuB,IAAI,CAAC,uBAAuB,IAAI,CAAC,OAAO,EAAE;AACvE,IAAI,IAAI,WAAW,CAAC,YAAY,CAAC,KAAK,MAAM,IAAI,iBAAiB,CAAC,eAAe,CAAC,EAAE;AACpF,MAAM,MAAM,GAAG,aAAa,CAAC,YAAY,CAAC;AAC1C;AACA,IAAI,IAAI,aAAa,CAAC,YAAY,CAAC,EAAE;AACrC,MAAM,MAAM,UAAU,GAAG,qBAAqB,CAAC,YAAY,CAAC;AAC5D,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC;AACpC,MAAM,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,YAAY,CAAC,UAAU;AACxD,MAAM,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,YAAY,CAAC,SAAS;AACvD;AACA;AACA,EAAE,MAAM,UAAU,GAAG,eAAe,IAAI,CAAC,uBAAuB,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,eAAe,EAAE,MAAM,EAAE,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AAC7I,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAC/B,IAAI,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC;AACjC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AAChF,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC;AAC9E,GAAG;AACH;;AAEA,SAAS,cAAc,CAAC,OAAO,EAAE;AACjC,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;AAC7C;;AAEA;AACA;AACA,SAAS,eAAe,CAAC,OAAO,EAAE;AAClC,EAAE,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC;AAC1C,EAAE,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC;AACvC,EAAE,MAAM,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI;AACzC,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC;AAC3F,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC;AAChG,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,GAAG,mBAAmB,CAAC,OAAO,CAAC;AAC3D,EAAE,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS;AAC7B,EAAE,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,EAAE;AAClD,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK;AACxD;AACA,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,CAAC;AACL,IAAI;AACJ,GAAG;AACH;;AAEA,SAAS,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE;AAC5C,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC;AAChC,EAAE,MAAM,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC;AAC1C,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,cAAc;AAC3C,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,WAAW;AAC9B,EAAE,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY;AAChC,EAAE,IAAI,CAAC,GAAG,CAAC;AACX,EAAE,IAAI,CAAC,GAAG,CAAC;AACX,EAAE,IAAI,cAAc,EAAE;AACtB,IAAI,KAAK,GAAG,cAAc,CAAC,KAAK;AAChC,IAAI,MAAM,GAAG,cAAc,CAAC,MAAM;AAClC,IAAI,MAAM,mBAAmB,GAAG,QAAQ,EAAE;AAC1C,IAAI,IAAI,CAAC,mBAAmB,IAAI,mBAAmB,IAAI,QAAQ,KAAK,OAAO,EAAE;AAC7E,MAAM,CAAC,GAAG,cAAc,CAAC,UAAU;AACnC,MAAM,CAAC,GAAG,cAAc,CAAC,SAAS;AAClC;AACA;AACA,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,CAAC;AACL,IAAI;AACJ,GAAG;AACH;;AAEA;AACA,SAAS,0BAA0B,CAAC,OAAO,EAAE,QAAQ,EAAE;AACvD,EAAE,MAAM,UAAU,GAAG,qBAAqB,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,KAAK,OAAO,CAAC;AAC/E,EAAE,MAAM,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,OAAO,CAAC,SAAS;AAChD,EAAE,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU;AACnD,EAAE,MAAM,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AAC5E,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC,CAAC;AAC7C,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,CAAC;AAC/C,EAAE,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC;AAC1B,EAAE,MAAM,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AACzB,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,MAAM;AACV,IAAI,CAAC;AACL,IAAI;AACJ,GAAG;AACH;AACA,SAAS,iCAAiC,CAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE;AAChF,EAAE,IAAI,IAAI;AACV,EAAE,IAAI,gBAAgB,KAAK,UAAU,EAAE;AACvC,IAAI,IAAI,GAAG,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC7C,GAAG,MAAM,IAAI,gBAAgB,KAAK,UAAU,EAAE;AAC9C,IAAI,IAAI,GAAG,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;AACvD,GAAG,MAAM,IAAI,SAAS,CAAC,gBAAgB,CAAC,EAAE;AAC1C,IAAI,IAAI,GAAG,0BAA0B,CAAC,gBAAgB,EAAE,QAAQ,CAAC;AACjE,GAAG,MAAM;AACT,IAAI,MAAM,aAAa,GAAG,gBAAgB,CAAC,OAAO,CAAC;AACnD,IAAI,IAAI,GAAG;AACX,MAAM,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;AAC7C,MAAM,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;AAC7C,MAAM,KAAK,EAAE,gBAAgB,CAAC,KAAK;AACnC,MAAM,MAAM,EAAE,gBAAgB,CAAC;AAC/B,KAAK;AACL;AACA,EAAE,OAAO,gBAAgB,CAAC,IAAI,CAAC;AAC/B;AACA,SAAS,wBAAwB,CAAC,OAAO,EAAE,QAAQ,EAAE;AACrD,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC;AAC3C,EAAE,IAAI,UAAU,KAAK,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,qBAAqB,CAAC,UAAU,CAAC,EAAE;AAC9F,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,gBAAgB,CAAC,UAAU,CAAC,CAAC,QAAQ,KAAK,OAAO,IAAI,wBAAwB,CAAC,UAAU,EAAE,QAAQ,CAAC;AAC5G;;AAEA;AACA;AACA;AACA,SAAS,2BAA2B,CAAC,OAAO,EAAE,KAAK,EAAE;AACrD,EAAE,MAAM,YAAY,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AACzC,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,OAAO,YAAY;AACvB;AACA,EAAE,IAAI,MAAM,GAAG,oBAAoB,CAAC,OAAO,EAAE,EAAS,CAAC,CAAC,MAAM,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,CAAC,IAAI,WAAW,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC;AACjH,EAAE,IAAI,mCAAmC,GAAG,IAAI;AAChD,EAAE,MAAM,cAAc,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,OAAO;AACvE,EAAE,IAAI,WAAW,GAAG,cAAc,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,OAAO;;AAErE;AACA,EAAE,OAAO,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE;AACxE,IAAI,MAAM,aAAa,GAAG,gBAAgB,CAAC,WAAW,CAAC;AACvD,IAAI,MAAM,uBAAuB,GAAG,iBAAiB,CAAC,WAAW,CAAC;AAClE,IAAI,IAAI,CAAC,uBAAuB,IAAI,aAAa,CAAC,QAAQ,KAAK,OAAO,EAAE;AACxE,MAAM,mCAAmC,GAAG,IAAI;AAChD;AACA,IAAI,MAAM,qBAAqB,GAAG,cAAc,GAAG,CAAC,uBAAuB,IAAI,CAAC,mCAAmC,GAAG,CAAC,uBAAuB,IAAI,aAAa,CAAC,QAAQ,KAAK,QAAQ,IAAI,CAAC,CAAC,mCAAmC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,mCAAmC,CAAC,QAAQ,CAAC,IAAI,iBAAiB,CAAC,WAAW,CAAC,IAAI,CAAC,uBAAuB,IAAI,wBAAwB,CAAC,OAAO,EAAE,WAAW,CAAC;AAC9Z,IAAI,IAAI,qBAAqB,EAAE;AAC/B;AACA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,QAAQ,KAAK,WAAW,CAAC;AAClE,KAAK,MAAM;AACX;AACA,MAAM,mCAAmC,GAAG,aAAa;AACzD;AACA,IAAI,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AAC5C;AACA,EAAE,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;AAC5B,EAAE,OAAO,MAAM;AACf;;AAEA;AACA;AACA,SAAS,eAAe,CAAC,IAAI,EAAE;AAC/B,EAAE,IAAI;AACN,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI;AACJ,GAAG,GAAG,IAAI;AACV,EAAE,MAAM,wBAAwB,GAAG,QAAQ,KAAK,mBAAmB,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,2BAA2B,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;AACpK,EAAE,MAAM,iBAAiB,GAAG,CAAC,GAAG,wBAAwB,EAAE,YAAY,CAAC;AACvE,EAAE,MAAM,qBAAqB,GAAG,iBAAiB,CAAC,CAAC,CAAC;AACpD,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,gBAAgB,KAAK;AAC/E,IAAI,MAAM,IAAI,GAAG,iCAAiC,CAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,CAAC;AACvF,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC;AAC5C,IAAI,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC;AAClD,IAAI,OAAO,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC;AACrD,IAAI,OAAO,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC;AAC/C,IAAI,OAAO,OAAO;AAClB,GAAG,EAAE,iCAAiC,CAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,CAAC,CAAC;AACjF,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI;AACjD,IAAI,MAAM,EAAE,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,GAAG;AAClD,IAAI,CAAC,EAAE,YAAY,CAAC,IAAI;AACxB,IAAI,CAAC,EAAE,YAAY,CAAC;AACpB,GAAG;AACH;;AAEA,SAAS,aAAa,CAAC,OAAO,EAAE;AAChC,EAAE,MAAM;AACR,IAAI,KAAK;AACT,IAAI;AACJ,GAAG,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAC/B,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI;AACJ,GAAG;AACH;;AAEA,SAAS,6BAA6B,CAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE;AACxE,EAAE,MAAM,uBAAuB,GAAG,aAAa,CAAC,YAAY,CAAC;AAC7D,EAAE,MAAM,eAAe,GAAG,kBAAkB,CAAC,YAAY,CAAC;AAC1D,EAAE,MAAM,OAAO,GAAG,QAAQ,KAAK,OAAO;AACtC,EAAE,MAAM,IAAI,GAAG,qBAAqB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC;AAC1E,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,UAAU,EAAE,CAAC;AACjB,IAAI,SAAS,EAAE;AACf,GAAG;AACH,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC;;AAEjC;AACA;AACA,EAAE,SAAS,yBAAyB,GAAG;AACvC,IAAI,OAAO,CAAC,CAAC,GAAG,mBAAmB,CAAC,eAAe,CAAC;AACpD;AACA,EAAE,IAAI,uBAAuB,IAAI,CAAC,uBAAuB,IAAI,CAAC,OAAO,EAAE;AACvE,IAAI,IAAI,WAAW,CAAC,YAAY,CAAC,KAAK,MAAM,IAAI,iBAAiB,CAAC,eAAe,CAAC,EAAE;AACpF,MAAM,MAAM,GAAG,aAAa,CAAC,YAAY,CAAC;AAC1C;AACA,IAAI,IAAI,uBAAuB,EAAE;AACjC,MAAM,MAAM,UAAU,GAAG,qBAAqB,CAAC,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,CAAC;AACzF,MAAM,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,YAAY,CAAC,UAAU;AACxD,MAAM,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,YAAY,CAAC,SAAS;AACvD,KAAK,MAAM,IAAI,eAAe,EAAE;AAChC,MAAM,yBAAyB,EAAE;AACjC;AACA;AACA,EAAE,IAAI,OAAO,IAAI,CAAC,uBAAuB,IAAI,eAAe,EAAE;AAC9D,IAAI,yBAAyB,EAAE;AAC/B;AACA,EAAE,MAAM,UAAU,GAAG,eAAe,IAAI,CAAC,uBAAuB,IAAI,CAAC,OAAO,GAAG,aAAa,CAAC,eAAe,EAAE,MAAM,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC;AACvI,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,UAAU,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AACpE,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AAClE,EAAE,OAAO;AACT,IAAI,CAAC;AACL,IAAI,CAAC;AACL,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;AACrB,IAAI,MAAM,EAAE,IAAI,CAAC;AACjB,GAAG;AACH;;AAEA,SAAS,kBAAkB,CAAC,OAAO,EAAE;AACrC,EAAE,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,QAAQ;AACxD;;AAEA,SAAS,mBAAmB,CAAC,OAAO,EAAE,QAAQ,EAAE;AAChD,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK,OAAO,EAAE;AACjF,IAAI,OAAO,IAAI;AACf;AACA,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,OAAO,QAAQ,CAAC,OAAO,CAAC;AAC5B;AACA,EAAE,IAAI,eAAe,GAAG,OAAO,CAAC,YAAY;;AAE5C;AACA;AACA;AACA;AACA,EAAE,IAAI,kBAAkB,CAAC,OAAO,CAAC,KAAK,eAAe,EAAE;AACvD,IAAI,eAAe,GAAG,eAAe,CAAC,aAAa,CAAC,IAAI;AACxD;AACA,EAAE,OAAO,eAAe;AACxB;;AAEA;AACA;AACA,SAAS,eAAe,CAAC,OAAO,EAAE,QAAQ,EAAE;AAC5C,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC;AAChC,EAAE,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;AAC3B,IAAI,OAAO,GAAG;AACd;AACA,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE;AAC/B,IAAI,IAAI,eAAe,GAAG,aAAa,CAAC,OAAO,CAAC;AAChD,IAAI,OAAO,eAAe,IAAI,CAAC,qBAAqB,CAAC,eAAe,CAAC,EAAE;AACvE,MAAM,IAAI,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,EAAE;AAC9E,QAAQ,OAAO,eAAe;AAC9B;AACA,MAAM,eAAe,GAAG,aAAa,CAAC,eAAe,CAAC;AACtD;AACA,IAAI,OAAO,GAAG;AACd;AACA,EAAE,IAAI,YAAY,GAAG,mBAAmB,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC3D,EAAE,OAAO,YAAY,IAAI,cAAc,CAAC,YAAY,CAAC,IAAI,kBAAkB,CAAC,YAAY,CAAC,EAAE;AAC3F,IAAI,YAAY,GAAG,mBAAmB,CAAC,YAAY,EAAE,QAAQ,CAAC;AAC9D;AACA,EAAE,IAAI,YAAY,IAAI,qBAAqB,CAAC,YAAY,CAAC,IAAI,kBAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,EAAE;AACnI,IAAI,OAAO,GAAG;AACd;AACA,EAAE,OAAO,YAAY,IAAI,kBAAkB,CAAC,OAAO,CAAC,IAAI,GAAG;AAC3D;;AAEA,MAAM,eAAe,GAAG,gBAAgB,IAAI,EAAE;AAC9C,EAAE,MAAM,iBAAiB,GAAG,IAAI,CAAC,eAAe,IAAI,eAAe;AACnE,EAAE,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa;AAC5C,EAAE,MAAM,kBAAkB,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC;AACjE,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,6BAA6B,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC;AACnH,IAAI,QAAQ,EAAE;AACd,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,CAAC,EAAE,CAAC;AACV,MAAM,KAAK,EAAE,kBAAkB,CAAC,KAAK;AACrC,MAAM,MAAM,EAAE,kBAAkB,CAAC;AACjC;AACA,GAAG;AACH,CAAC;;AAED,SAAS,KAAK,CAAC,OAAO,EAAE;AACxB,EAAE,OAAO,gBAAgB,CAAC,OAAO,CAAC,CAAC,SAAS,KAAK,KAAK;AACtD;;AAEA,MAAM,QAAQ,GAAG;AACjB,EAAE,qDAAqD;AACvD,EAAE,kBAAkB;AACpB,EAAE,eAAe;AACjB,EAAE,eAAe;AACjB,EAAE,eAAe;AACjB,EAAE,cAAc;AAChB,EAAE,aAAa;AACf,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE;AACF,CAAC;;AAuLD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,MAAM,GAAG,QAAQ;;AAUvB;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,GAAG,OAAO;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,GAAG,MAAM;;AAEnB;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,GAAG,MAAM;;AAEnB;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,GAAG,MAAM;;AAEnB;AACA;AACA;AACA;AACA;AACA,MAAM,KAAK,GAAG,OAAO;;AASrB;AACA;AACA;AACA,MAAM,UAAU,GAAG,YAAY;;AAE/B;AACA;AACA;AACA;AACA,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,KAAK;AAC1D;AACA;AACA;AACA,EAAE,MAAM,KAAK,GAAG,IAAI,GAAG,EAAE;AACzB,EAAE,MAAM,aAAa,GAAG;AACxB,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG;AACH,EAAE,MAAM,iBAAiB,GAAG;AAC5B,IAAI,GAAG,aAAa,CAAC,QAAQ;AAC7B,IAAI,EAAE,EAAE;AACR,GAAG;AACH,EAAE,OAAO,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE;AAChD,IAAI,GAAG,aAAa;AACpB,IAAI,QAAQ,EAAE;AACd,GAAG,CAAC;AACJ,CAAC;;ACzuBD,MAAM,WAAW,CAAC;AAClB,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;AACjC,EAAE,WAAW,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE;AACrD,IAAI,OAAO,CAAC,MAAM,IAAI,aAAa;AACnC,IAAI,IAAI,CAAC,KAAK,GAAG;AACjB,MAAM,KAAK,EAAE,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC;AAC5C,MAAM,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE,MAAM,IAAI;AAC7C,KAAK;AACL;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,KAAK;AACrB;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;AAC3B;AACA,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;AAC5B;AACA;AACA,SAAS,GAAG,CAAC,eAAe,EAAE;AAC9B,EAAE,OAAO,OAAO,eAAe,KAAK,UAAU,GAAG,eAAe,EAAE,GAAG,eAAe;AACpF;AACA,SAAS,MAAM,CAAC,OAAO,EAAE;AACzB,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,OAAO,CAAC;AAC7C,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,aAAa,CAAC,WAAW,IAAI,MAAM;AACzD,EAAE,OAAO,GAAG,CAAC,gBAAgB,IAAI,CAAC;AAClC;AACA,SAAS,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE;AACpC,EAAE,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC;AAC7B,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG;AACtC;AACA,SAAS,yBAAyB,CAAC,IAAI,EAAE;AACzC,EAAE,OAAO;AACT,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,qCAAqC,CAAC;AACxF,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,oCAAoC,CAAC;AACtF,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,qCAAqC,CAAC;AACxF,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,iCAAiC,CAAC;AACxE,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,kCAAkC;AACzE,GAAG;AACH;AACA,SAAS,WAAW,CAAC,OAAO,EAAE;AAC9B,EAAE,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI;AAC3B,EAAE,MAAM,gBAAgB,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;AAClD,EAAE,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,IAAI;AACxD,EAAE,MAAM,eAAe,GAAG,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,QAAQ;AAC5D,EAAE,MAAM,cAAc,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,UAAU;AAC5D,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS;AACrC,EAAE,IAAI,CAAC,GAAG,CAAC;AACX,EAAE,IAAI,CAAC,GAAG,CAAC;AACX,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC;AAC5B,EAAE,IAAI,QAAQ,GAAG,cAAc;AAC/B,EAAE,IAAI,SAAS,GAAG,eAAe;AACjC,EAAE,IAAI,cAAc,GAAG,EAAE;AACzB,EAAE,IAAI,YAAY,GAAG,KAAK;AAC1B,EAAE,MAAM,cAAc,GAAG,CAAC,MAAM;AAChC,IAAI,MAAM,aAAa,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AACrE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AAC3B,MAAM,OAAO,aAAa;AAC1B;AACA,IAAI,MAAM,IAAI,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;AAChD,IAAI,MAAM,IAAI,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC;AAChD,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,OAAO;AACb,QAAQ,GAAG,aAAa;AACxB,QAAQ,SAAS,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;AACpD,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,EAAE,UAAU,EAAE,WAAW;AACvE,OAAO;AACP;AACA,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC;AACvB,MAAM,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,EAAE;AACrB,KAAK;AACL,GAAG,GAAG;AACN,EAAE,SAAS,MAAM,GAAG;AACpB,IAAI,IAAI,SAAS,CAAC,OAAO,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,KAAK,IAAI,EAAE;AACjE,IAAI,eAAe,CAAC,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE;AACzD,MAAM,UAAU,EAAE,gBAAgB;AAClC,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,QAAQ,EAAE;AAChB,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK;AAC1B,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC;AACpB,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC;AACpB,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ;AAClC,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS;AACpC,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc;AAC9C,MAAM,YAAY,GAAG,IAAI;AACzB,KAAK,CAAC;AACN;AACA,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,SAAS;AACb,IAAI,IAAI,QAAQ,GAAG;AACnB,MAAM,OAAO,QAAQ;AACrB,KAAK;AACL,IAAI,IAAI,SAAS,GAAG;AACpB,MAAM,OAAO,SAAS;AACtB,KAAK;AACL,IAAI,IAAI,cAAc,GAAG;AACzB,MAAM,OAAO,cAAc;AAC3B,KAAK;AACL,IAAI,IAAI,YAAY,GAAG;AACvB,MAAM,OAAO,YAAY;AACzB,KAAK;AACL,IAAI,IAAI,cAAc,GAAG;AACzB,MAAM,OAAO,cAAc;AAC3B,KAAK;AACL,IAAI,IAAI,MAAM,GAAG;AACjB,MAAM,OAAO,MAAM;AACnB;AACA,GAAG;AACH;AACA,MAAM,aAAa,GAAG;AACtB,EAAE,GAAG,EAAE,QAAQ;AACf,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,IAAI,EAAE;AACR,CAAC;AACD,MAAM,iBAAiB,CAAC;AACxB,EAAE,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;AACxB,EAAE,gBAAgB,GAAG,GAAG,CAAC,IAAI,CAAC;AAC9B,EAAE,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC;AACzB,EAAE,WAAW,GAAG;AAChB;AACA;AACA,MAAM,oBAAoB,CAAC;AAC3B,EAAE,IAAI;AACN,EAAE,IAAI;AACN;AACA,EAAE,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;AACxB,EAAE,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC;AACxB,EAAE,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC;AACtB;AACA,EAAE,OAAO,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;AACxB,EAAE,iBAAiB,GAAG,OAAO,CAAC,MAAM;AACpC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;AAClF,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE;AACnC,GAAG,CAAC;AACJ,EAAE,uBAAuB,GAAG,MAAM;AAClC,EAAE,UAAU,GAAG,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,MAAM,CAAC;AACrE,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,CAAC;AAC1D,EAAE,YAAY,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,CAAC;AAC5D,EAAE,iBAAiB,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AAC1I,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC7J,EAAE,sBAAsB,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AACrE,EAAE,IAAI,qBAAqB,GAAG;AAC9B,IAAI,OAAO,IAAI,CAAC,sBAAsB,EAAE;AACxC;AACA,EAAE,IAAI,qBAAqB,CAAC,OAAO,EAAE;AACrC,IAAI,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;AAC/C;AACA,EAAE,sBAAsB,GAAG,OAAO,CAAC,OAAO;AAC1C,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO;AAC/C,IAAI,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;AAChD,IAAI,WAAW,EAAE,IAAI,CAAC;AACtB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,qBAAqB,GAAG;AAC9B,IAAI,OAAO,IAAI,CAAC,sBAAsB,EAAE;AACxC;AACA,EAAE,IAAI,qBAAqB,CAAC,OAAO,EAAE;AACrC,IAAI,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;AAC/C;AACA,EAAE,eAAe,GAAG,MAAM;AAC1B,EAAE,gBAAgB,GAAG,MAAM;AAC3B,EAAE,YAAY,GAAG,MAAM;AACvB,EAAE,aAAa,GAAG,MAAM;AACxB,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM;AAC9B,IAAI,MAAM,CAAC;AACX,MAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,EAAE;AAClE,MAAM,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;AAC3C,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI,KAAK,CAAC;AAC/C,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,SAAS,GAAG,UAAU,EAAE,GAAG,MAAM;AAC7E,MAAM,GAAG,IAAI,CAAC;AACd,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;AAChF,IAAI,IAAI,CAAC;AACT,MAAM,GAAG,IAAI,CAAC,qBAAqB;AACnC,MAAM,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,KAAK;AAC7D,QAAQ,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,KAAK,CAAC,SAAS;AAC5E,QAAQ,IAAI,CAAC,eAAe,GAAG,cAAc;AAC7C,QAAQ,IAAI,CAAC,gBAAgB,GAAG,eAAe;AAC/C,QAAQ,IAAI,CAAC,YAAY,GAAG,WAAW;AACvC,QAAQ,IAAI,CAAC,aAAa,GAAG,YAAY;AACzC;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,KAAK,CAAC;AACnC,MAAM,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO;AACpC,MAAM,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;AACtC,KAAK,CAAC;AACN,IAAI,eAAe,CAAC;AACpB,MAAM,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE;AACpC,MAAM,WAAW,EAAE,IAAI,CAAC,YAAY;AACpC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,IAAI,IAAI,CAAC;AAC/C,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,GAAG,IAAI,CAAC;AACd,KAAK;AACL,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACpB,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE;AAC7B;AACA,EAAE,IAAI,UAAU,CAAC,OAAO,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC;AACA,EAAE,QAAQ;AACV,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC5E,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE;AAC7B;AACA,EAAE,IAAI,UAAU,CAAC,OAAO,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC;AACA,EAAE,YAAY,GAAG,OAAO,CAAC,MAAM,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC9E,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;AAC9B;AACA,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACrC;AACA,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;AACrE,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;AACzB;AACA,EAAE,IAAI,MAAM,CAAC,OAAO,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AAChC;AACA,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;AACrE,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;AACzB;AACA,EAAE,IAAI,MAAM,CAAC,OAAO,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AAChC;AACA,EAAE,kBAAkB,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,KAAK,EAAE,YAAY,KAAK,CAAC,CAAC;AAC5F,EAAE,IAAI,iBAAiB,GAAG;AAC1B,IAAI,OAAO,IAAI,CAAC,kBAAkB,EAAE;AACpC;AACA,EAAE,IAAI,iBAAiB,CAAC,OAAO,EAAE;AACjC,IAAI,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC;AAC3C;AACA,EAAE,aAAa;AACf,EAAE,cAAc,GAAG,OAAO,CAAC,MAAM,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChE,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,cAAc,EAAE;AAChC;AACA,EAAE,IAAI,aAAa,CAAC,OAAO,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;AACvC;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO;AACjC,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO;AACnC,IAAI,oCAAoC,EAAE,EAAE;AAC5C,IAAI,KAAK,EAAE;AACX,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc;AACrC;AACA,MAAM,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,GAAG,qBAAqB;AAC5G,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,MAAM,EAAE,IAAI,CAAC,aAAa;AAChC,MAAM,kCAAkC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC;AACjJ,MAAM,iCAAiC,EAAE,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;AACpE,MAAM,kCAAkC,EAAE,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;AACtE,MAAM,8BAA8B,EAAE,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;AAC9D,MAAM,+BAA+B,EAAE,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;AAChE;AACA,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,IAAI;AAC/D,QAAQ,UAAU,EAAE,QAAQ;AAC5B,QAAQ,gBAAgB,EAAE;AAC1B,OAAO;AACP,MAAM,GAAG,IAAI,CAAC,iBAAiB;AAC/B,KAAK;AACL;AACA,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,WAAW,EAAE,IAAI,CAAC,UAAU;AAChC,IAAI,YAAY,EAAE,IAAI,CAAC,WAAW;AAClC,IAAI,KAAK,EAAE,aAAa,CAAC;AACzB,MAAM,GAAG,IAAI,CAAC,iBAAiB;AAC/B;AACA,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,WAAW,GAAG,OAAO,CAAC,OAAO;AAC/B,IAAI,QAAQ,EAAE,UAAU;AACxB,IAAI,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM;AACnD,IAAI,GAAG,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM;AAClD,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC;AAC3B,IAAI,kBAAkB,EAAE;AACxB,MAAM,GAAG,EAAE,EAAE;AACb,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AACtB,IAAI,SAAS,EAAE;AACf,MAAM,GAAG,EAAE,kBAAkB;AAC7B,MAAM,KAAK,EAAE,gDAAgD;AAC7D,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,IAAI,EAAE;AACZ,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AACtB,IAAI,UAAU,EAAE,IAAI,CAAC,iBAAiB,GAAG,QAAQ,GAAG;AACpD,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE;AAC7B;AACA,EAAE,IAAI,UAAU,CAAC,OAAO,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC;AACA,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;AAC3B,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO;AACpE;AACA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,YAAY,KAAK;AAC7D,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,YAAY;AACvD,KAAK,CAAC;AACN,IAAI,UAAU,CAAC;AACf,MAAM,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS;AAC7B,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU;AAC1B,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,UAAU,CAAC;AACf,MAAM,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AACtB,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU;AAC1B,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;AAChC,MAAM,QAAQ,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AAChD,MAAM,SAAS,EAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE;AAC/C,MAAM,UAAU,EAAE,MAAM,IAAI,CAAC,UAAU;AACvC,MAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;AACrC,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,WAAW,KAAK;AAC1D,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,MAAM,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,MAAM;AACtE,KAAK,CAAC;AACN;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE;AAC7B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO;AAC1B,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI;AAC5C,OAAO;AACP,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5C,KAAK,CAAC;AACN;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU;AAClC,IAAI,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,mBAAmB,CAAC;AAC1B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC;AACjB,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE;AAClD,MAAM,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AACzD,KAAK,MAAM;AACX,MAAM,UAAU,CAAC;AACjB,QAAQ,EAAE,EAAE,IAAI,CAAC,EAAE;AACnB,QAAQ,GAAG,EAAE,IAAI,CAAC,GAAG;AACrB,QAAQ,WAAW,EAAE,CAAC,IAAI,KAAK;AAC/B,UAAU,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI;AACzC;AACA,OAAO,CAAC;AACR;AACA;AACA;AACA,MAAM,mBAAmB,GAAG,IAAI,OAAO,CAAC,eAAe,CAAC;AACxD,MAAM,sBAAsB,GAAG,IAAI,OAAO,CAAC,kBAAkB,CAAC;AAC9D,SAAS,oBAAoB,GAAG;AAChC,EAAE,OAAO,mBAAmB,CAAC,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;AACzD;AACA,SAAS,uBAAuB,CAAC,KAAK,EAAE;AACxC,EAAE,OAAO,sBAAsB,CAAC,GAAG,CAAC,IAAI,oBAAoB,CAAC,KAAK,EAAE,mBAAmB,CAAC,GAAG,EAAE,CAAC,CAAC;AAC/F;AACA,SAAS,qBAAqB,CAAC,KAAK,EAAE;AACtC,EAAE,OAAO,IAAI,kBAAkB,CAAC,KAAK,EAAE,sBAAsB,CAAC,GAAG,EAAE,CAAC;AACpE;AACA,SAAS,sBAAsB,CAAC,KAAK,EAAE;AACvC,EAAE,OAAO,IAAI,mBAAmB,CAAC,KAAK,EAAE,mBAAmB,CAAC,GAAG,EAAE,CAAC;AAClE;AACA,SAAS,eAAe,CAAC,OAAO,EAAE;AAClC,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,OAAO;AACX,IAAI,EAAE,CAAC,IAAI,EAAE;AACb,MAAM,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,IAAI;AACvD,MAAM,MAAM,iBAAiB,GAAG,cAAc,CAAC,KAAK,EAAE,YAAY,KAAK,CAAC;AACxE,MAAM,MAAM,aAAa,GAAG,iBAAiB;AAC7C,MAAM,MAAM,UAAU,GAAG,aAAa,GAAG,CAAC,GAAG,OAAO,CAAC,UAAU;AAC/D,MAAM,MAAM,WAAW,GAAG,aAAa,GAAG,CAAC,GAAG,OAAO,CAAC,WAAW;AACjE,MAAM,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,4BAA4B,CAAC,SAAS,CAAC;AAC/E,MAAM,MAAM,YAAY,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,WAAW,CAAC;AACnF,MAAM,MAAM,YAAY,GAAG,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,UAAU,GAAG,CAAC;AAC1E,MAAM,MAAM,YAAY,GAAG,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,IAAI,WAAW,GAAG,CAAC;AAC3E,MAAM,IAAI,CAAC,GAAG,EAAE;AAChB,MAAM,IAAI,CAAC,GAAG,EAAE;AAChB,MAAM,IAAI,UAAU,KAAK,QAAQ,EAAE;AACnC,QAAQ,CAAC,GAAG,aAAa,GAAG,YAAY,GAAG,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAC9D,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;AAC/B,OAAO,MAAM,IAAI,UAAU,KAAK,KAAK,EAAE;AACvC,QAAQ,CAAC,GAAG,aAAa,GAAG,YAAY,GAAG,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAC9D,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC,EAAE,CAAC;AACtD,OAAO,MAAM,IAAI,UAAU,KAAK,OAAO,EAAE;AACzC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC;AAC/B,QAAQ,CAAC,GAAG,aAAa,GAAG,YAAY,GAAG,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAC9D,OAAO,MAAM,IAAI,UAAU,KAAK,MAAM,EAAE;AACxC,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,EAAE,CAAC;AACrD,QAAQ,CAAC,GAAG,aAAa,GAAG,YAAY,GAAG,CAAC,EAAE,YAAY,CAAC,EAAE,CAAC;AAC9D;AACA,MAAM,OAAO,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/B;AACA,GAAG;AACH;AACA,SAAS,4BAA4B,CAAC,SAAS,EAAE;AACjD,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,QAAQ,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC;AACvD,EAAE,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AACtB;AACA,SAAS,oBAAoB,CAAC,SAAS,EAAE;AACzC,EAAE,OAAO,4BAA4B,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACnD;AACA,SAAS,qBAAqB,CAAC,SAAS,EAAE;AAC1C,EAAE,OAAO,4BAA4B,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACnD;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC5B,EAAE,oBAAoB,EAAE;AACxB,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,qBAAqB,CAAC,SAAS,EAAE,OAAO,EAAE;AACnD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO;AAC3C,EAAE,sBAAsB,CAAC;AACzB,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,SAAS;AACvC,GAAG,CAAC;AACJ,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,sBAAsB,CAAC,SAAS,EAAE,OAAO,EAAE;AACpD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,OAAO;AACX,IAAI,IAAI,GAAG,QAAQ;AACnB,IAAI,UAAU,GAAG,CAAC;AAClB,IAAI,KAAK,GAAG,QAAQ;AACpB,IAAI,WAAW,GAAG,CAAC;AACnB,IAAI,EAAE;AACN,IAAI,YAAY,GAAG,CAAC;AACpB,IAAI,eAAe,GAAG,IAAI;AAC1B,IAAI,iBAAiB,GAAG,EAAE;AAC1B,IAAI,gBAAgB,GAAG,CAAC;AACxB,IAAI,gBAAgB,GAAG,KAAK;AAC5B,IAAI,QAAQ,GAAG,MAAM;AACrB,KAAK;AACL,IAAI,MAAM,GAAG,SAAS;AACtB,IAAI,sBAAsB,GAAG,WAAW;AACxC,IAAI,QAAQ,GAAG,OAAO;AACtB,IAAI,GAAG,GAAG,KAAK;AACf,IAAI,KAAK,GAAG,EAAE;AACd,IAAI,SAAS,GAAG,KAAK,EAAE;AACvB,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,uBAAuB,CAAC;AAC/C,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9B,IAAI,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,UAAU,CAAC;AAC1C,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,YAAY,CAAC;AAC9C,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,eAAe,CAAC;AACpD,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,iBAAiB,CAAC;AACxD,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,gBAAgB,CAAC;AACtD,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,gBAAgB,CAAC;AACtD,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,MAAM,CAAC;AAClC,IAAI,sBAAsB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,sBAAsB,CAAC;AAClE,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC;AACpC,IAAI,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC;AACxC,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,YAAY;AAC7C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,YAAY,CAAC,YAAY,EAAE,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,CAAC;AACjG,EAAE,OAAO,GAAG,SAAS,EAAE;AACvB,IAAI,KAAK,EAAE,YAAY,CAAC,KAAK;AAC7B,IAAI,YAAY,EAAE;AAClB,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,6BAA6B,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3D,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO;AAC3B,EAAE,OAAO,GAAG,SAAS,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;AACvD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI;AACN,IAAI,OAAO;AACX,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,6BAA6B,CAAC,SAAS,EAAE,EAAE,OAAO,EAAE,CAAC;AACzD,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,sBAAsB,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC;AACvF;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE;AAChD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,MAAM;AACV,IAAI,eAAe;AACnB,IAAI,qBAAqB;AACzB,IAAI,4BAA4B;AAChC,IAAI,EAAE;AACN,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,IAAI;AACR,IAAI,UAAU;AACd,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,MAAM;AACV,IAAI,gBAAgB;AACpB,IAAI,sBAAsB;AAC1B,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,uBAAuB,GAAG,OAAO;AACrC,IAAI,IAAI;AACR,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,YAAY,GAAG,MAAM,KAAK;AAC9B,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE;AACF,IAAI,IAAI,OAAO,GAAG,SAAS,UAAU,EAAE,EAAE,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,EAAE;AAC/E,MAAM,IAAI,SAAS,CAAC,UAAU,IAAI,OAAO,EAAE;AAC3C,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,WAAW,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,CAAC;AAClD,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE;AACxC,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC,QAAQ,WAAW,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,CAAC;AAClD,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC,MAAM;AACN,QAAQ,IAAI,UAAU,GAAG,SAAS,UAAU,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE,EAAE;AAC1E,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,eAAe;AAC3B,YAAY,qBAAqB;AACjC,YAAY,OAAO;AACnB,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc;AACd,gBAAgB,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE,EAAE;AACjF,kBAAkB,oBAAoB,CAAC,UAAU,EAAE;AACnD,oBAAoB,EAAE;AACtB,oBAAoB,4BAA4B;AAChD,oBAAoB,aAAa;AACjC,oBAAoB,WAAW;AAC/B,oBAAoB,OAAO;AAC3B,oBAAoB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9C,sBAAsB,MAAM,GAAG,UAAU,EAAE;AAC3C,wBAAwB,KAAK,EAAE,UAAU,CAAC,SAAS,EAAE,aAAa,EAAE,gBAAgB,EAAE,eAAe,EAAE,EAAE,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE,EAAE,CAAC;AAC5I,wBAAwB;AACxB,uBAAuB,CAAC;AACxB,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjD;AACA,mBAAmB,CAAC;AACpB,iBAAiB;AACjB,gBAAgB,iBAAiB,CAAC,UAAU,EAAE;AAC9C,kBAAkB,EAAE;AACpB,kBAAkB,iBAAiB;AACnC,kBAAkB,cAAc;AAChC,kBAAkB,uBAAuB;AACzC,kBAAkB,YAAY;AAC9B,kBAAkB,OAAO;AACzB,kBAAkB;AAClB,iBAAiB,CAAC;AAClB;AACA;AACA,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,WAAW,CAAC,UAAU,EAAE;AAChC,UAAU,EAAE;AACZ,UAAU,eAAe;AACzB,UAAU,gBAAgB;AAC1B,UAAU,IAAI;AACd,UAAU,SAAS,EAAE,OAAO,IAAI,SAAS;AACzC,UAAU,UAAU,EAAE,SAAS,CAAC,UAAU;AAC1C,UAAU;AACV,SAAS,CAAC;AACV;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,EAAE;AAC9B,MAAM,QAAQ;AACd,MAAM,EAAE;AACR,MAAM,IAAI;AACV,MAAM,UAAU;AAChB,MAAM,KAAK;AACX,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,gBAAgB;AACtB,MAAM,MAAM;AACZ,MAAM,gBAAgB;AACtB,MAAM,sBAAsB;AAC5B,MAAM,QAAQ;AACd,MAAM,GAAG;AACT,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,MAAM,YAAY;AAClB,MAAM,OAAO;AACb,MAAM,OAAO;AACb,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN;AACA,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI;AACN,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,eAAe;AACnB,IAAI,qBAAqB;AACzB,IAAI,4BAA4B;AAChC,IAAI,EAAE;AACN,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,IAAI;AACR,IAAI,UAAU;AACd,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,MAAM;AACV,IAAI,gBAAgB;AACpB,IAAI,sBAAsB;AAC1B,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,uBAAuB,GAAG,OAAO;AACrC,IAAI,IAAI;AACR,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,YAAY,GAAG,MAAM,KAAK;AAC9B,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE;AACF,IAAI,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE;AACxC,MAAM,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC;AAClD,QAAQ;AACR,UAAU,MAAM;AAChB,UAAU,eAAe;AACzB,UAAU,qBAAqB;AAC/B,UAAU,4BAA4B;AACtC,UAAU,EAAE;AACZ,UAAU,aAAa;AACvB,UAAU,WAAW;AACrB,UAAU,IAAI;AACd,UAAU,UAAU;AACpB,UAAU,KAAK;AACf,UAAU,WAAW;AACrB,UAAU,YAAY;AACtB,UAAU,eAAe;AACzB,UAAU,iBAAiB;AAC3B,UAAU,gBAAgB;AAC1B,UAAU,MAAM;AAChB,UAAU,gBAAgB;AAC1B,UAAU,sBAAsB;AAChC,UAAU,QAAQ;AAClB,UAAU,GAAG;AACb,UAAU,aAAa;AACvB,UAAU,SAAS;AACnB,UAAU,KAAK;AACf,UAAU,QAAQ;AAClB,UAAU,YAAY;AACtB,UAAU,QAAQ;AAClB,UAAU,OAAO,EAAE,OAAO;AAC1B,UAAU,iBAAiB;AAC3B,UAAU,gBAAgB;AAC1B,UAAU,eAAe;AACzB,UAAU,uBAAuB;AACjC,UAAU,IAAI;AACd,UAAU,SAAS;AACnB,UAAU,YAAY;AACtB,UAAU,cAAc;AACxB,UAAU,UAAU,EAAE;AACtB,SAAS;AACT,QAAQ;AACR,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,EAAE,YAAY,CAAC;AAC3C,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE;AACrB,MAAM,SAAS;AACf,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;AAC7C,KAAK,CAAC,CAAC;AACP;AACA;AACA,SAAS,wBAAwB,CAAC,SAAS,EAAE,OAAO,EAAE;AACtD,EAAE,IAAI;AACN,IAAI,MAAM;AACV,IAAI,eAAe;AACnB,IAAI,qBAAqB;AACzB,IAAI,4BAA4B;AAChC,IAAI,EAAE;AACN,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,IAAI;AACR,IAAI,UAAU;AACd,IAAI,KAAK;AACT,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,MAAM;AACV,IAAI,gBAAgB;AACpB,IAAI,sBAAsB;AAC1B,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,IAAI,aAAa;AACjB,IAAI,SAAS;AACb,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,iBAAiB;AACrB,IAAI,gBAAgB;AACpB,IAAI,eAAe;AACnB,IAAI,cAAc;AAClB,IAAI,uBAAuB,GAAG,OAAO;AACrC,IAAI,IAAI;AACR,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,YAAY,GAAG,MAAM,KAAK;AAC9B,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,kBAAkB,CAAC,SAAS,EAAE,YAAY,CAAC;AAC7C,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM,eAAe;AACrB,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAClC,MAAM,EAAE;AACR,MAAM,aAAa;AACnB,MAAM,WAAW;AACjB,MAAM,IAAI;AACV,MAAM,UAAU;AAChB,MAAM,KAAK;AACX,MAAM,WAAW;AACjB,MAAM,YAAY;AAClB,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,gBAAgB;AACtB,MAAM,MAAM;AACZ,MAAM,gBAAgB;AACtB,MAAM,sBAAsB;AAC5B,MAAM,QAAQ;AACd,MAAM,GAAG;AACT,MAAM,aAAa;AACnB,MAAM,SAAS;AACf,MAAM,KAAK;AACX,MAAM,QAAQ;AACd,MAAM,YAAY;AAClB,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,iBAAiB;AACvB,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,MAAM,uBAAuB;AAC7B,MAAM,IAAI;AACV,MAAM,SAAS;AACf,MAAM,YAAY;AAClB,MAAM;AACN,KAAK;AACL,IAAI,SAAS;AACb,IAAI,EAAE,UAAU,EAAE,IAAI;AACtB,GAAG,CAAC,CAAC;AACL;;;;", "x_google_ignoreList": [0, 1, 2, 3]}