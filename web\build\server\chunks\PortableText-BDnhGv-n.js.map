{"version": 3, "file": "PortableText-BDnhGv-n.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/PortableText.js"], "sourcesContent": ["import { Y as fallback, U as ensure_array_like, a4 as element, R as attr, V as escape_html, S as attr_class, T as clsx, N as bind_props, y as pop, w as push } from \"./index3.js\";\nimport { u as urlFor } from \"./sanityClient.js\";\nimport { h as html } from \"./html.js\";\nfunction PortableText($$payload, $$props) {\n  push();\n  let value = fallback($$props[\"value\"], () => [], true);\n  function getComponent(block) {\n    const style = block.style || \"normal\";\n    const listItem = block.listItem;\n    if (listItem) {\n      return \"li\";\n    }\n    switch (style) {\n      case \"h1\":\n        return \"h1\";\n      case \"h2\":\n        return \"h2\";\n      case \"h3\":\n        return \"h3\";\n      case \"h4\":\n        return \"h4\";\n      case \"blockquote\":\n        return \"blockquote\";\n      default:\n        return \"p\";\n    }\n  }\n  function groupBlocks(blocks) {\n    const result = [];\n    let currentList = null;\n    for (let i = 0; i < blocks.length; i++) {\n      const block = blocks[i];\n      if (block._type !== \"block\" || !block.listItem) {\n        if (currentList) {\n          result.push(currentList);\n          currentList = null;\n        }\n        result.push(block);\n        continue;\n      }\n      const listType = block.listItem === \"bullet\" ? \"ul\" : \"ol\";\n      if (!currentList || currentList.listType !== listType) {\n        if (currentList) {\n          result.push(currentList);\n        }\n        currentList = { _type: \"list\", listType, items: [block] };\n      } else {\n        currentList.items.push(block);\n      }\n    }\n    if (currentList) {\n      result.push(currentList);\n    }\n    return result;\n  }\n  function renderMarks(text, marks, markDefs = []) {\n    if (!marks || marks.length === 0) return text;\n    let result = text;\n    for (const mark of marks) {\n      const markDef = markDefs.find((def) => def._key === mark);\n      if (markDef) {\n        switch (markDef._type) {\n          case \"link\":\n            result = `<a href=\"${markDef.href}\" target=\"${markDef.blank ? \"_blank\" : \"_self\"}\" rel=\"${markDef.blank ? \"noopener noreferrer\" : \"\"}\" class=\"text-blue-600 hover:underline\">${result}</a>`;\n            break;\n          case \"internalLink\":\n            const slug = markDef.reference?.slug?.current;\n            if (slug) {\n              result = `<a href=\"/blog/${slug}\" class=\"text-blue-600 hover:underline\">${result}</a>`;\n            }\n            break;\n          case \"externalLink\":\n            result = `<a href=\"${markDef.url}\" target=\"_blank\" rel=\"noopener noreferrer\" class=\"text-blue-600 hover:underline\">${result}</a>`;\n            break;\n          default:\n            console.warn(`Unknown mark definition type: ${markDef._type}`);\n        }\n      } else {\n        switch (mark) {\n          case \"strong\":\n            result = `<strong>${result}</strong>`;\n            break;\n          case \"em\":\n            result = `<em>${result}</em>`;\n            break;\n          case \"underline\":\n            result = `<u>${result}</u>`;\n            break;\n          case \"strike-through\":\n            result = `<s>${result}</s>`;\n            break;\n          case \"code\":\n            result = `<code>${result}</code>`;\n            break;\n        }\n      }\n    }\n    return result;\n  }\n  if (value && value.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const groupedBlocks = groupBlocks(value);\n    const each_array = ensure_array_like(groupedBlocks);\n    $$payload.out += `<div class=\"portable-text svelte-1t92gsh\"><!--[-->`;\n    for (let $$index_3 = 0, $$length = each_array.length; $$index_3 < $$length; $$index_3++) {\n      let block = each_array[$$index_3];\n      if (block._type === \"block\") {\n        $$payload.out += \"<!--[-->\";\n        const Component = getComponent(block);\n        element(\n          $$payload,\n          Component,\n          () => {\n            $$payload.out += `${attr_class(clsx(block.style), \"svelte-1t92gsh\")}`;\n          },\n          () => {\n            const each_array_1 = ensure_array_like(block.children);\n            $$payload.out += `<!--[-->`;\n            for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {\n              let child = each_array_1[$$index];\n              if (child._type === \"span\") {\n                $$payload.out += \"<!--[-->\";\n                $$payload.out += `${html(renderMarks(child.text, child.marks, block.markDefs))}`;\n              } else {\n                $$payload.out += \"<!--[!-->\";\n              }\n              $$payload.out += `<!--]-->`;\n            }\n            $$payload.out += `<!--]-->`;\n          }\n        );\n      } else if (block._type === \"list\") {\n        $$payload.out += \"<!--[1-->\";\n        element($$payload, block.listType, void 0, () => {\n          const each_array_2 = ensure_array_like(block.items);\n          $$payload.out += `<!--[-->`;\n          for (let $$index_2 = 0, $$length2 = each_array_2.length; $$index_2 < $$length2; $$index_2++) {\n            let item = each_array_2[$$index_2];\n            const each_array_3 = ensure_array_like(item.children);\n            $$payload.out += `<li><!--[-->`;\n            for (let $$index_1 = 0, $$length3 = each_array_3.length; $$index_1 < $$length3; $$index_1++) {\n              let child = each_array_3[$$index_1];\n              if (child._type === \"span\") {\n                $$payload.out += \"<!--[-->\";\n                $$payload.out += `${html(renderMarks(child.text, child.marks, item.markDefs))}`;\n              } else {\n                $$payload.out += \"<!--[!-->\";\n              }\n              $$payload.out += `<!--]-->`;\n            }\n            $$payload.out += `<!--]--></li>`;\n          }\n          $$payload.out += `<!--]-->`;\n        });\n      } else if (block._type === \"image\") {\n        $$payload.out += \"<!--[2-->\";\n        $$payload.out += `<figure><img${attr(\"src\", urlFor(block))}${attr(\"alt\", block.alt || \"\")} loading=\"lazy\"/> `;\n        if (block.caption) {\n          $$payload.out += \"<!--[-->\";\n          $$payload.out += `<figcaption>${escape_html(block.caption)}</figcaption>`;\n        } else {\n          $$payload.out += \"<!--[!-->\";\n        }\n        $$payload.out += `<!--]--></figure>`;\n      } else if (block._type === \"code\") {\n        $$payload.out += \"<!--[3-->\";\n        $$payload.out += `<pre><code${attr_class(clsx(block.language || \"\"), \"svelte-1t92gsh\")}>${escape_html(block.code)}</code></pre>`;\n      } else {\n        $$payload.out += \"<!--[!-->\";\n      }\n      $$payload.out += `<!--]-->`;\n    }\n    $$payload.out += `<!--]--></div>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { value });\n  pop();\n}\nexport {\n  PortableText as P\n};\n"], "names": [], "mappings": ";;;;AAGA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AACxD,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE;AAC/B,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,QAAQ;AACzC,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ;AACnC,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,QAAQ,KAAK;AACjB,MAAM,KAAK,IAAI;AACf,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,IAAI;AACf,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,IAAI;AACf,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,IAAI;AACf,QAAQ,OAAO,IAAI;AACnB,MAAM,KAAK,YAAY;AACvB,QAAQ,OAAO,YAAY;AAC3B,MAAM;AACN,QAAQ,OAAO,GAAG;AAClB;AACA;AACA,EAAE,SAAS,WAAW,CAAC,MAAM,EAAE;AAC/B,IAAI,MAAM,MAAM,GAAG,EAAE;AACrB,IAAI,IAAI,WAAW,GAAG,IAAI;AAC1B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;AACtD,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;AAClC,UAAU,WAAW,GAAG,IAAI;AAC5B;AACA,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;AAC1B,QAAQ;AACR;AACA,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,KAAK,QAAQ,GAAG,IAAI,GAAG,IAAI;AAChE,MAAM,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAC7D,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;AAClC;AACA,QAAQ,WAAW,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,KAAK,CAAC,EAAE;AACjE,OAAO,MAAM;AACb,QAAQ,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;AACrC;AACA;AACA,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC;AAC9B;AACA,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,GAAG,EAAE,EAAE;AACnD,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;AACjD,IAAI,IAAI,MAAM,GAAG,IAAI;AACrB,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC;AAC/D,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,QAAQ,OAAO,CAAC,KAAK;AAC7B,UAAU,KAAK,MAAM;AACrB,YAAY,MAAM,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,GAAG,qBAAqB,GAAG,EAAE,CAAC,wCAAwC,EAAE,MAAM,CAAC,IAAI,CAAC;AACvM,YAAY;AACZ,UAAU,KAAK,cAAc;AAC7B,YAAY,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,EAAE,IAAI,EAAE,OAAO;AACzD,YAAY,IAAI,IAAI,EAAE;AACtB,cAAc,MAAM,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,wCAAwC,EAAE,MAAM,CAAC,IAAI,CAAC;AACpG;AACA,YAAY;AACZ,UAAU,KAAK,cAAc;AAC7B,YAAY,MAAM,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,kFAAkF,EAAE,MAAM,CAAC,IAAI,CAAC;AAC7I,YAAY;AACZ,UAAU;AACV,YAAY,OAAO,CAAC,IAAI,CAAC,CAAC,8BAA8B,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAC1E;AACA,OAAO,MAAM;AACb,QAAQ,QAAQ,IAAI;AACpB,UAAU,KAAK,QAAQ;AACvB,YAAY,MAAM,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC;AACjD,YAAY;AACZ,UAAU,KAAK,IAAI;AACnB,YAAY,MAAM,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC;AACzC,YAAY;AACZ,UAAU,KAAK,WAAW;AAC1B,YAAY,MAAM,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC;AACvC,YAAY;AACZ,UAAU,KAAK,gBAAgB;AAC/B,YAAY,MAAM,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC;AACvC,YAAY;AACZ,UAAU,KAAK,MAAM;AACrB,YAAY,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,CAAC;AAC7C,YAAY;AACZ;AACA;AACA;AACA,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,aAAa,GAAG,WAAW,CAAC,KAAK,CAAC;AAC5C,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,aAAa,CAAC;AACvD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,kDAAkD,CAAC;AACzE,IAAI,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,SAAS,GAAG,QAAQ,EAAE,SAAS,EAAE,EAAE;AAC7F,MAAM,IAAI,KAAK,GAAG,UAAU,CAAC,SAAS,CAAC;AACvC,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,EAAE;AACnC,QAAQ,SAAS,CAAC,GAAG,IAAI,UAAU;AACnC,QAAQ,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC;AAC7C,QAAQ,OAAO;AACf,UAAU,SAAS;AACnB,UAAU,SAAS;AACnB,UAAU,MAAM;AAChB,YAAY,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC;AACjF,WAAW;AACX,UAAU,MAAM;AAChB,YAAY,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC,QAAQ,CAAC;AAClE,YAAY,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvC,YAAY,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,OAAO,GAAG,SAAS,EAAE,OAAO,EAAE,EAAE;AACnG,cAAc,IAAI,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC;AAC/C,cAAc,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,EAAE;AAC1C,gBAAgB,SAAS,CAAC,GAAG,IAAI,UAAU;AAC3C,gBAAgB,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAChG,eAAe,MAAM;AACrB,gBAAgB,SAAS,CAAC,GAAG,IAAI,WAAW;AAC5C;AACA,cAAc,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACzC;AACA,YAAY,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACvC;AACA,SAAS;AACT,OAAO,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,EAAE;AACzC,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC,QAAQ,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM;AACzD,UAAU,MAAM,YAAY,GAAG,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC;AAC7D,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACrC,UAAU,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACvG,YAAY,IAAI,IAAI,GAAG,YAAY,CAAC,SAAS,CAAC;AAC9C,YAAY,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;AACjE,YAAY,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAC3C,YAAY,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS,EAAE,SAAS,EAAE,EAAE;AACzG,cAAc,IAAI,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC;AACjD,cAAc,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,EAAE;AAC1C,gBAAgB,SAAS,CAAC,GAAG,IAAI,UAAU;AAC3C,gBAAgB,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/F,eAAe,MAAM;AACrB,gBAAgB,SAAS,CAAC,GAAG,IAAI,WAAW;AAC5C;AACA,cAAc,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACzC;AACA,YAAY,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC5C;AACA,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACrC,SAAS,CAAC;AACV,OAAO,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,EAAE;AAC1C,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC,kBAAkB,CAAC;AACrH,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AAC3B,UAAU,SAAS,CAAC,GAAG,IAAI,UAAU;AACrC,UAAU,SAAS,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,aAAa,CAAC;AACnF,SAAS,MAAM;AACf,UAAU,SAAS,CAAC,GAAG,IAAI,WAAW;AACtC;AACA,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC5C,OAAO,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,MAAM,EAAE;AACzC,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC,QAAQ,SAAS,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC;AACxI,OAAO,MAAM;AACb,QAAQ,SAAS,CAAC,GAAG,IAAI,WAAW;AACpC;AACA,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACjC;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,CAAC;AAChC,EAAE,GAAG,EAAE;AACP;;;;"}