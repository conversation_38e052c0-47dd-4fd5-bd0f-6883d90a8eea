{"version": 3, "file": "clone-BRGVxGEr.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/clone.js"], "sourcesContent": ["import { I as is_array, a8 as get_prototype_of, a9 as object_prototype } from \"./index3.js\";\nconst empty = [];\nfunction snapshot(value, skip_warning = false) {\n  return clone(value, /* @__PURE__ */ new Map(), \"\", empty);\n}\nfunction clone(value, cloned, path, paths, original = null) {\n  if (typeof value === \"object\" && value !== null) {\n    var unwrapped = cloned.get(value);\n    if (unwrapped !== void 0) return unwrapped;\n    if (value instanceof Map) return (\n      /** @type {Snapshot<T>} */\n      new Map(value)\n    );\n    if (value instanceof Set) return (\n      /** @type {Snapshot<T>} */\n      new Set(value)\n    );\n    if (is_array(value)) {\n      var copy = (\n        /** @type {Snapshot<any>} */\n        Array(value.length)\n      );\n      cloned.set(value, copy);\n      if (original !== null) {\n        cloned.set(original, copy);\n      }\n      for (var i = 0; i < value.length; i += 1) {\n        var element = value[i];\n        if (i in value) {\n          copy[i] = clone(element, cloned, path, paths);\n        }\n      }\n      return copy;\n    }\n    if (get_prototype_of(value) === object_prototype) {\n      copy = {};\n      cloned.set(value, copy);\n      if (original !== null) {\n        cloned.set(original, copy);\n      }\n      for (var key in value) {\n        copy[key] = clone(value[key], cloned, path, paths);\n      }\n      return copy;\n    }\n    if (value instanceof Date) {\n      return (\n        /** @type {Snapshot<T>} */\n        structuredClone(value)\n      );\n    }\n    if (typeof /** @type {T & { toJSON?: any } } */\n    value.toJSON === \"function\") {\n      return clone(\n        /** @type {T & { toJSON(): any } } */\n        value.toJSON(),\n        cloned,\n        path,\n        paths,\n        // Associate the instance with the toJSON clone\n        value\n      );\n    }\n  }\n  if (value instanceof EventTarget) {\n    return (\n      /** @type {Snapshot<T>} */\n      value\n    );\n  }\n  try {\n    return (\n      /** @type {Snapshot<T>} */\n      structuredClone(value)\n    );\n  } catch (e) {\n    return (\n      /** @type {Snapshot<T>} */\n      value\n    );\n  }\n}\nexport {\n  snapshot as s\n};\n"], "names": [], "mappings": ";;AACA,MAAM,KAAK,GAAG,EAAE;AAChB,SAAS,QAAQ,CAAC,KAAK,EAAE,YAAY,GAAG,KAAK,EAAE;AAC/C,EAAE,OAAO,KAAK,CAAC,KAAK,kBAAkB,IAAI,GAAG,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC;AAC3D;AACA,SAAS,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,GAAG,IAAI,EAAE;AAC5D,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;AACnD,IAAI,IAAI,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;AACrC,IAAI,IAAI,SAAS,KAAK,MAAM,EAAE,OAAO,SAAS;AAC9C,IAAI,IAAI,KAAK,YAAY,GAAG,EAAE;AAC9B;AACA,MAAM,IAAI,GAAG,CAAC,KAAK;AACnB;AACA,IAAI,IAAI,KAAK,YAAY,GAAG,EAAE;AAC9B;AACA,MAAM,IAAI,GAAG,CAAC,KAAK;AACnB;AACA,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;AACzB,MAAM,IAAI,IAAI;AACd;AACA,QAAQ,KAAK,CAAC,KAAK,CAAC,MAAM;AAC1B,OAAO;AACP,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC;AAC7B,MAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;AAC7B,QAAQ,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;AAClC;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;AAChD,QAAQ,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;AAC9B,QAAQ,IAAI,CAAC,IAAI,KAAK,EAAE;AACxB,UAAU,IAAI,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;AACvD;AACA;AACA,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,IAAI,gBAAgB,CAAC,KAAK,CAAC,KAAK,gBAAgB,EAAE;AACtD,MAAM,IAAI,GAAG,EAAE;AACf,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC;AAC7B,MAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;AAC7B,QAAQ,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC;AAClC;AACA,MAAM,KAAK,IAAI,GAAG,IAAI,KAAK,EAAE;AAC7B,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC;AAC1D;AACA,MAAM,OAAO,IAAI;AACjB;AACA,IAAI,IAAI,KAAK,YAAY,IAAI,EAAE;AAC/B,MAAM;AACN;AACA,QAAQ,eAAe,CAAC,KAAK;AAC7B;AACA;AACA,IAAI,IAAI;AACR,IAAI,KAAK,CAAC,MAAM,KAAK,UAAU,EAAE;AACjC,MAAM,OAAO,KAAK;AAClB;AACA,QAAQ,KAAK,CAAC,MAAM,EAAE;AACtB,QAAQ,MAAM;AACd,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb;AACA,QAAQ;AACR,OAAO;AACP;AACA;AACA,EAAE,IAAI,KAAK,YAAY,WAAW,EAAE;AACpC,IAAI;AACJ;AACA,MAAM;AACN;AACA;AACA,EAAE,IAAI;AACN,IAAI;AACJ;AACA,MAAM,eAAe,CAAC,KAAK;AAC3B;AACA,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI;AACJ;AACA,MAAM;AACN;AACA;AACA;;;;"}