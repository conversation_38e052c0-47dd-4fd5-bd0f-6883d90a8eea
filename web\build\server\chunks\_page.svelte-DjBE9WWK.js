import { O as escape_html, M as ensure_array_like, N as attr, Q as bind_props } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import { F as File_text } from './file-text-HttY5S5h.js';
import { S as Scale, A as Accessibility } from './scale-CwxPsNiZ.js';
import { G as Globe } from './globe-B6sBOhFF.js';
import { D as Database } from './database-DTyOQm04.js';
import { C as Cookie } from './cookie-CTvRQAsw.js';
import { S as Shield } from './shield-BzJ8ZsQa.js';
import 'clsx';
import './false-CRHihH2U.js';
import './sanityClient-BQ6Z_2a-.js';
import '@sanity/client';
import './html-FW6Ia4bL.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  let data = $$props["data"];
  const { legalPage } = data;
  function getIconComponent(iconName) {
    switch (iconName) {
      case "Shield":
        return Shield;
      case "FileText":
        return File_text;
      case "Cookie":
        return Cookie;
      case "Accessibility":
        return Accessibility;
      case "Database":
        return Database;
      case "Globe":
        return Globe;
      case "Scale":
        return Scale;
      default:
        return File_text;
    }
  }
  SEO($$payload, {
    title: legalPage?.title ? `${legalPage.title} | Hirli` : "Legal Center | Hirli",
    description: legalPage?.description || "Access Hirli's legal documents, policies, and compliance information.",
    keywords: "legal, terms of service, privacy policy, cookie policy, accessibility, data security, GDPR, legal notices"
  });
  $$payload.out += `<!----> <div class="max-w-none"><h1 class="mb-4 text-2xl font-bold">${escape_html(legalPage?.title || "Legal Center")}</h1> `;
  if (legalPage?.content) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-6">`;
    PortableText($$payload, { value: legalPage.content });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="mb-6 text-gray-700">Welcome to Hirli's Legal Center. Here you'll find all our legal documents, policies, and
      compliance information. We're committed to transparency and protecting your rights while using
      our services.</p>`;
  }
  $$payload.out += `<!--]--> `;
  if (legalPage?.legalPages && legalPage.legalPages.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(legalPage.legalPages);
    $$payload.out += `<div class="not-prose mt-8 grid grid-cols-1 gap-6 md:grid-cols-2"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let page = each_array[$$index];
      $$payload.out += `<a${attr("href", `/legal/${page.slug}`)} class="bg-card hover:bg-muted/50 group flex flex-col rounded-xl border p-6 shadow-sm transition-colors"><div class="mb-4 flex items-center gap-3"><div class="bg-primary/10 text-primary flex h-10 w-10 items-center justify-center rounded-full"><!---->`;
      getIconComponent(page.icon)?.($$payload, { class: "h-5 w-5" });
      $$payload.out += `<!----></div> <h2 class="text-xl font-semibold">${escape_html(page.title)}</h2></div> <p class="text-muted-foreground">${escape_html(page.description)}</p></a>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="mt-8 rounded-lg border bg-amber-50 p-6"><p class="text-amber-800">No legal pages found in Sanity CMS. Please make sure you have created legal pages with the
        appropriate slugs.</p></div>`;
  }
  $$payload.out += `<!--]--> `;
  if (legalPage?.legalContact) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-12 rounded-lg border bg-gray-50 p-6"><p class="mb-4 text-gray-700">${escape_html(legalPage.legalContact.message || "If you have questions about our legal documents, please contact our legal team.")}</p> <a${attr("href", `mailto:${legalPage.legalContact.email || "<EMAIL>"}`)} class="text-primary font-medium hover:underline">${escape_html(legalPage.legalContact.email || "<EMAIL>")}</a></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, { data });
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DjBE9WWK.js.map
