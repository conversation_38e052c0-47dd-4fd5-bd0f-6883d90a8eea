{"version": 3, "file": "pricing-D13CEnfk.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/pricing.js"], "sourcesContent": ["import { w as writable } from \"./index2.js\";\nconst pricingModalStore = writable({\n  isOpen: false,\n  section: \"pro\",\n  currentPlanId: null,\n  onSelectPlan: null\n});\nfunction openPricingModal(options) {\n  const section = options.section || \"pro\";\n  const url = `/pricing?section=${section}`;\n  if (typeof window !== \"undefined\") {\n    window.location.href = url;\n  }\n  pricingModalStore.update((state) => ({\n    ...state,\n    isOpen: false,\n    // Don't actually open the modal\n    section,\n    currentPlanId: options.currentPlanId || null,\n    onSelectPlan: options.onSelectPlan || null\n  }));\n}\nfunction closePricingModal() {\n  pricingModalStore.update((state) => ({\n    ...state,\n    isOpen: false\n  }));\n}\nexport {\n  closePricingModal as c,\n  openPricingModal as o,\n  pricingModalStore as p\n};\n"], "names": [], "mappings": ";;AACK,MAAC,iBAAiB,GAAG,QAAQ,CAAC;AACnC,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,OAAO,EAAE,KAAK;AAChB,EAAE,aAAa,EAAE,IAAI;AACrB,EAAE,YAAY,EAAE;AAChB,CAAC;AACD,SAAS,gBAAgB,CAAC,OAAO,EAAE;AACnC,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,KAAK;AAC1C,EAAE,MAAM,GAAG,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;AAC3C,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AACrC,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,GAAG,GAAG;AAC9B;AACA,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,KAAK,MAAM;AACvC,IAAI,GAAG,KAAK;AACZ,IAAI,MAAM,EAAE,KAAK;AACjB;AACA,IAAI,OAAO;AACX,IAAI,aAAa,EAAE,OAAO,CAAC,aAAa,IAAI,IAAI;AAChD,IAAI,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI;AAC1C,GAAG,CAAC,CAAC;AACL;AACA,SAAS,iBAAiB,GAAG;AAC7B,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,KAAK,MAAM;AACvC,IAAI,GAAG,KAAK;AACZ,IAAI,MAAM,EAAE;AACZ,GAAG,CAAC,CAAC;AACL;;;;"}