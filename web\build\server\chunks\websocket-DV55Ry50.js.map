{"version": 3, "file": "websocket-DV55Ry50.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/websocket.js"], "sourcesContent": ["import { g as getRedisClient } from \"./redis.js\";\nasync function broadcastMessage(message) {\n  try {\n    if (!message.timestamp) {\n      message.timestamp = (/* @__PURE__ */ new Date()).toISOString();\n    }\n    const redis = await getRedisClient();\n    if (!redis) {\n      console.error(\"[WebSocket] Redis client not available for broadcasting\");\n      return;\n    }\n    await redis.publish(\"websocket::broadcast\", JSON.stringify(message));\n    console.log(`[WebSocket] Published message to Redis: ${message.type}`);\n  } catch (error) {\n    console.error(\"[WebSocket] Error broadcasting message:\", error);\n  }\n}\nexport {\n  broadcastMessage\n};\n"], "names": [], "mappings": ";;;AACA,eAAe,gBAAgB,CAAC,OAAO,EAAE;AACzC,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAC5B,MAAM,OAAO,CAAC,SAAS,GAAG,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AACpE;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,OAAO,CAAC,KAAK,CAAC,yDAAyD,CAAC;AAC9E,MAAM;AACN;AACA,IAAI,MAAM,KAAK,CAAC,OAAO,CAAC,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AACxE,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,wCAAwC,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAC1E,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC;AACnE;AACA;;;;"}