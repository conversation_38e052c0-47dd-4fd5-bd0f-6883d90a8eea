import 'clsx';
import { p as push, q as pop } from './index3-CqUPEnZw.js';
import './false-CRHihH2U.js';

function _page($$payload, $$props) {
  push();
  $$payload.out += `<div class="container svelte-1yqb9ci"><h1 class="svelte-1yqb9ci">Redirecting to Sanity Studio...</h1> <p class="svelte-1yqb9ci">If you are not redirected automatically, please click the button below:</p> <a href="/static/studio/index.html" class="button svelte-1yqb9ci">Open Sanity Studio</a></div>`;
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BXtwj-Zm.js.map
