{"version": 3, "file": "chevron-right-C2rn-JeO.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/chevron-right.js"], "sourcesContent": ["import { Q as spread_props, y as pop, w as push } from \"./index3.js\";\nimport { I as Icon } from \"./Icon2.js\";\nfunction Chevron_right($$payload, $$props) {\n  push();\n  let { $$slots, $$events, ...props } = $$props;\n  const iconNode = [[\"path\", { \"d\": \"m9 18 6-6-6-6\" }]];\n  Icon($$payload, spread_props([\n    { name: \"chevron-right\" },\n    props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        props.children?.($$payload2);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n  pop();\n}\nexport {\n  Chevron_right as C\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AAC/C,EAAE,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,eAAe,EAAE,CAAC,CAAC;AACvD,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE;AAC7B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACpC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL,EAAE,GAAG,EAAE;AACP;;;;"}