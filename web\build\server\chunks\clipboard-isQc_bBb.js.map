{"version": 3, "file": "clipboard-isQc_bBb.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/clipboard.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Clipboard($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\n      \"rect\",\n      {\n        \"width\": \"8\",\n        \"height\": \"4\",\n        \"x\": \"8\",\n        \"y\": \"2\",\n        \"rx\": \"1\",\n        \"ry\": \"1\"\n      }\n    ],\n    [\n      \"path\",\n      {\n        \"d\": \"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2\"\n      }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"clipboard\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Clipboard as C\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,OAAO,EAAE,GAAG;AACpB,QAAQ,QAAQ,EAAE,GAAG;AACrB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,GAAG,EAAE;AACb;AACA;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE;AACzB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}