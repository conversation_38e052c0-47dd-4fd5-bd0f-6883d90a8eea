{"version": 3, "file": "Icon-A4vzmk-O.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/Icon.js"], "sourcesContent": ["import { Z as sanitize_props, a2 as rest_props, Y as fallback, U as ensure_array_like, M as spread_attributes, T as clsx, a4 as element, a0 as slot, N as bind_props, y as pop, w as push } from \"./index3.js\";\n/**\n * @license lucide-svelte v0.486.0 - ISC\n *\n * ISC License\n * \n * Copyright (c) for portions of Lucide are held by <PERSON> 2013-2022 as part of Feather (MIT). All other copyright (c) for Lucide are held by Lucide Contributors 2022.\n * \n * Permission to use, copy, modify, and/or distribute this software for any\n * purpose with or without fee is hereby granted, provided that the above\n * copyright notice and this permission notice appear in all copies.\n * \n * THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n * \n */\nconst defaultAttributes = {\n  xmlns: \"http://www.w3.org/2000/svg\",\n  width: 24,\n  height: 24,\n  viewBox: \"0 0 24 24\",\n  fill: \"none\",\n  stroke: \"currentColor\",\n  \"stroke-width\": 2,\n  \"stroke-linecap\": \"round\",\n  \"stroke-linejoin\": \"round\"\n};\nfunction Icon($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const $$restProps = rest_props($$sanitized_props, [\n    \"name\",\n    \"color\",\n    \"size\",\n    \"strokeWidth\",\n    \"absoluteStrokeWidth\",\n    \"iconNode\"\n  ]);\n  push();\n  let name = fallback($$props[\"name\"], void 0);\n  let color = fallback($$props[\"color\"], \"currentColor\");\n  let size = fallback($$props[\"size\"], 24);\n  let strokeWidth = fallback($$props[\"strokeWidth\"], 2);\n  let absoluteStrokeWidth = fallback($$props[\"absoluteStrokeWidth\"], false);\n  let iconNode = fallback($$props[\"iconNode\"], () => [], true);\n  const mergeClasses = (...classes) => classes.filter((className, index, array) => {\n    return Boolean(className) && array.indexOf(className) === index;\n  }).join(\" \");\n  const each_array = ensure_array_like(iconNode);\n  $$payload.out += `<svg${spread_attributes(\n    {\n      ...defaultAttributes,\n      ...$$restProps,\n      width: size,\n      height: size,\n      stroke: color,\n      \"stroke-width\": absoluteStrokeWidth ? Number(strokeWidth) * 24 / Number(size) : strokeWidth,\n      class: clsx(mergeClasses(\"lucide-icon\", \"lucide\", name ? `lucide-${name}` : \"\", $$sanitized_props.class))\n    },\n    null,\n    void 0,\n    void 0,\n    3\n  )}><!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let [tag, attrs] = each_array[$$index];\n    element($$payload, tag, () => {\n      $$payload.out += `${spread_attributes({ ...attrs }, null, void 0, void 0, 3)}`;\n    });\n  }\n  $$payload.out += `<!--]--><!---->`;\n  slot($$payload, $$props, \"default\", {}, null);\n  $$payload.out += `<!----></svg>`;\n  bind_props($$props, {\n    name,\n    color,\n    size,\n    strokeWidth,\n    absoluteStrokeWidth,\n    iconNode\n  });\n  pop();\n}\nexport {\n  Icon as I,\n  defaultAttributes as d\n};\n"], "names": [], "mappings": ";;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACK,MAAC,iBAAiB,GAAG;AAC1B,EAAE,KAAK,EAAE,4BAA4B;AACrC,EAAE,KAAK,EAAE,EAAE;AACX,EAAE,MAAM,EAAE,EAAE;AACZ,EAAE,OAAO,EAAE,WAAW;AACtB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,MAAM,EAAE,cAAc;AACxB,EAAE,cAAc,EAAE,CAAC;AACnB,EAAE,gBAAgB,EAAE,OAAO;AAC3B,EAAE,iBAAiB,EAAE;AACrB;AACA,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,EAAE;AACpD,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,aAAa;AACjB,IAAI,qBAAqB;AACzB,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;AAC9C,EAAE,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,cAAc,CAAC;AACxD,EAAE,IAAI,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;AAC1C,EAAE,IAAI,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC;AACvD,EAAE,IAAI,mBAAmB,GAAG,QAAQ,CAAC,OAAO,CAAC,qBAAqB,CAAC,EAAE,KAAK,CAAC;AAC3E,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;AAC9D,EAAE,MAAM,YAAY,GAAG,CAAC,GAAG,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,KAAK,KAAK;AACnF,IAAI,OAAO,OAAO,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,KAAK;AACnE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AACd,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AAChD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,GAAG,iBAAiB;AAC1B,MAAM,GAAG,WAAW;AACpB,MAAM,KAAK,EAAE,IAAI;AACjB,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,cAAc,EAAE,mBAAmB,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,WAAW;AACjG,MAAM,KAAK,EAAE,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,iBAAiB,CAAC,KAAK,CAAC;AAC9G,KAAK;AACL,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,CAAC,SAAS,CAAC;AACd,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC;AAC1C,IAAI,OAAO,CAAC,SAAS,EAAE,GAAG,EAAE,MAAM;AAClC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,iBAAiB,CAAC,EAAE,GAAG,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;AACpF,KAAK,CAAC;AACN;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACpC,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AAC/C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,IAAI;AACR,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,WAAW;AACf,IAAI,mBAAmB;AACvB,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,GAAG,EAAE;AACP;;;;"}