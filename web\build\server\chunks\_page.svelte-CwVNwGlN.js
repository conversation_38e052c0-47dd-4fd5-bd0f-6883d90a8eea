import { M as ensure_array_like, N as attr, O as escape_html, Q as bind_props } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import { D as Download } from './download-CLn66Ope.js';
import 'clsx';
import './false-CRHihH2U.js';
import './sanityClient-BQ6Z_2a-.js';
import '@sanity/client';
import './html-FW6Ia4bL.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  let data = $$props["data"];
  const { pressImagesPage } = data;
  const defaultImages = [
    {
      title: "Hirli Logo",
      description: "Official Hirli logo in full color",
      image: "/assets/images/press/logo.png",
      downloadUrl: "/assets/images/press/logo.png"
    },
    {
      title: "<PERSON>rl<PERSON> Logo (Dark)",
      description: "Official Hirli logo for dark backgrounds",
      image: "/assets/images/press/logo-dark.png",
      downloadUrl: "/assets/images/press/logo-dark.png"
    },
    {
      title: "Hirli App Screenshot",
      description: "Screenshot of the Hirli application dashboard",
      image: "/assets/images/press/app-screenshot.png",
      downloadUrl: "/assets/images/press/app-screenshot.png"
    },
    {
      title: "Founder Photo",
      description: "Official photo of Hirli founder",
      image: "/assets/images/press/founder.jpg",
      downloadUrl: "/assets/images/press/founder.jpg"
    }
  ];
  const images = pressImagesPage?.images?.length > 0 ? pressImagesPage.images : defaultImages;
  const each_array = ensure_array_like(images);
  SEO($$payload, {
    title: pressImagesPage?.seo?.metaTitle || "Press Images | Hirli",
    description: pressImagesPage?.seo?.metaDescription || "Official Hirli brand assets, logos, and images for media use.",
    keywords: pressImagesPage?.seo?.keywords?.join(", ") || "Hirli logo, brand assets, press images, media kit"
  });
  $$payload.out += `<!----> <div><h2 class="mb-8 text-3xl font-semibold">Press Images</h2> `;
  if (pressImagesPage?.content) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-8">`;
    PortableText($$payload, { value: pressImagesPage.content });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="text-muted-foreground mb-6">Download official Hirli logos, product screenshots, and other brand assets for media use. All
      images are available in high resolution and can be used in accordance with our brand
      guidelines.</p>`;
  }
  $$payload.out += `<!--]--> <div class="mt-8 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"><!--[-->`;
  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
    let image = each_array[$$index];
    $$payload.out += `<div class="border-border bg-card text-card-foreground overflow-hidden rounded-lg border shadow-sm"><div class="bg-muted relative aspect-video overflow-hidden"><img${attr("src", image.image)}${attr("alt", image.title)} class="h-full w-full object-cover"/></div> <div class="p-4"><h3 class="mb-2 text-lg font-medium">${escape_html(image.title)}</h3> <p class="text-muted-foreground mb-4 text-sm">${escape_html(image.description)}</p> <a${attr("href", image.downloadUrl)} download="" class="text-primary inline-flex items-center gap-2 text-sm font-medium hover:underline">`;
    Download($$payload, { class: "h-4 w-4" });
    $$payload.out += `<!----> <span>Download</span></a></div></div>`;
  }
  $$payload.out += `<!--]--></div> <div class="mt-12 rounded-lg border bg-gray-50 p-6"><h3 class="mb-4 text-xl font-medium">Usage Guidelines</h3> <ul class="text-muted-foreground list-inside list-disc space-y-2"><li>Do not alter, distort, or modify the logos in any way</li> <li>Maintain adequate spacing around logos</li> <li>Do not use Hirli logos or images to imply partnership or endorsement without permission</li> <li>For questions about usage, please contact <a href="mailto:<EMAIL>" class="text-primary hover:underline"><EMAIL></a></li></ul></div></div>`;
  bind_props($$props, { data });
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CwVNwGlN.js.map
