{"version": 3, "file": "dynamic-registry-Cmy1Wm2Q.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/dynamic-registry.js"], "sourcesContent": ["import { b as browser } from \"./index4.js\";\nlet featuresCache = [];\nlet limitsCache = {};\nlet lastFetchTime = 0;\nconst CACHE_TTL = 60 * 1e3;\nasync function fetchFeatures() {\n  try {\n    const now = Date.now();\n    if (featuresCache.length > 0 && now - lastFetchTime < CACHE_TTL) {\n      return featuresCache;\n    }\n    if (!browser) {\n      console.log(\"Skipping features fetch on server side\");\n      return [];\n    }\n    const response = await fetch(\"/api/admin/features\", {\n      credentials: \"include\"\n    });\n    if (!response.ok) {\n      throw new Error(\"Failed to fetch features\");\n    }\n    const data = await response.json();\n    featuresCache = data.features ?? [];\n    lastFetchTime = now;\n    limitsCache = {};\n    for (const feature of featuresCache) {\n      if (feature.limits) {\n        for (const limit of feature.limits) {\n          limitsCache[limit.id] = limit;\n        }\n      }\n    }\n    return featuresCache;\n  } catch (error) {\n    console.error(\"Error fetching features:\", error);\n    return [];\n  }\n}\nconst FEATURES = [];\nconst FEATURE_LIMITS = {};\nfunction getFeatureById(id) {\n  return featuresCache.find((feature) => feature.id === id);\n}\nfunction getFeaturesByCategory(category) {\n  return featuresCache.filter((feature) => feature.category === category);\n}\nfunction getFeatureLimitById(id) {\n  return limitsCache[id];\n}\nfetchFeatures().then((features) => {\n  FEATURES.length = 0;\n  FEATURES.push(...features);\n  for (const key in limitsCache) {\n    FEATURE_LIMITS[key] = limitsCache[key];\n  }\n});\nexport {\n  FEATURES as F,\n  FEATURE_LIMITS as a,\n  getFeatureById as b,\n  getFeaturesByCategory as c,\n  getFeatureLimitById as g\n};\n"], "names": [], "mappings": ";;AACA,IAAI,aAAa,GAAG,EAAE;AACtB,IAAI,WAAW,GAAG,EAAE;AACpB,IAAI,aAAa,GAAG,CAAC;AACrB,MAAM,SAAS,GAAG,EAAE,GAAG,GAAG;AAC1B,eAAe,aAAa,GAAG;AAC/B,EAAE,IAAI;AACN,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE;AAC1B,IAAI,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,GAAG,aAAa,GAAG,SAAS,EAAE;AACrE,MAAM,OAAO,aAAa;AAC1B;AACA,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC;AAC3D,MAAM,OAAO,EAAE;AACf;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,qBAAqB,EAAE;AACxD,MAAM,WAAW,EAAE;AACnB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACtB,MAAM,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC;AACjD;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACtC,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE;AACvC,IAAI,aAAa,GAAG,GAAG;AACvB,IAAI,WAAW,GAAG,EAAE;AACpB,IAAI,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;AACzC,MAAM,IAAI,OAAO,CAAC,MAAM,EAAE;AAC1B,QAAQ,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC5C,UAAU,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK;AACvC;AACA;AACA;AACA,IAAI,OAAO,aAAa;AACxB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACpD,IAAI,OAAO,EAAE;AACb;AACA;AACK,MAAC,QAAQ,GAAG;AACZ,MAAC,cAAc,GAAG;AACvB,SAAS,cAAc,CAAC,EAAE,EAAE;AAC5B,EAAE,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AAC3D;AACA,SAAS,qBAAqB,CAAC,QAAQ,EAAE;AACzC,EAAE,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,KAAK,QAAQ,CAAC;AACzE;AACA,SAAS,mBAAmB,CAAC,EAAE,EAAE;AACjC,EAAE,OAAO,WAAW,CAAC,EAAE,CAAC;AACxB;AACA,aAAa,EAAE,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK;AACnC,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC;AACrB,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;AAC5B,EAAE,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE;AACjC,IAAI,cAAc,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC,GAAG,CAAC;AAC1C;AACA,CAAC,CAAC;;;;"}