{"version": 3, "file": "use-debounce.svelte-gxToHznJ.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/use-debounce.svelte.js"], "sourcesContent": ["import \"clsx\";\nfunction useDebounce(callback, wait = 250) {\n  let context = null;\n  function debounced(...args) {\n    if (context) {\n      if (context.timeout) {\n        clearTimeout(context.timeout);\n      }\n    } else {\n      let resolve;\n      let reject;\n      const promise = new Promise((res, rej) => {\n        resolve = res;\n        reject = rej;\n      });\n      context = {\n        timeout: null,\n        runner: null,\n        promise,\n        resolve,\n        reject\n      };\n    }\n    context.runner = async () => {\n      if (!context) return;\n      const ctx = context;\n      context = null;\n      try {\n        ctx.resolve(await callback.apply(this, args));\n      } catch (error) {\n        ctx.reject(error);\n      }\n    };\n    context.timeout = setTimeout(context.runner, typeof wait === \"function\" ? wait() : wait);\n    return context.promise;\n  }\n  debounced.cancel = async () => {\n    if (!context || context.timeout === null) {\n      await new Promise((resolve) => setTimeout(resolve, 0));\n      if (!context || context.timeout === null) return;\n    }\n    clearTimeout(context.timeout);\n    context.reject(\"Cancelled\");\n    context = null;\n  };\n  debounced.runScheduledNow = async () => {\n    if (!context || !context.timeout) {\n      await new Promise((resolve) => setTimeout(resolve, 0));\n      if (!context || !context.timeout) return;\n    }\n    clearTimeout(context.timeout);\n    context.timeout = null;\n    await context.runner?.();\n  };\n  Object.defineProperty(debounced, \"pending\", {\n    enumerable: true,\n    get() {\n      return !!context?.timeout;\n    }\n  });\n  return debounced;\n}\nexport {\n  useDebounce as u\n};\n"], "names": [], "mappings": ";;AACA,SAAS,WAAW,CAAC,QAAQ,EAAE,IAAI,GAAG,GAAG,EAAE;AAC3C,EAAE,IAAI,OAAO,GAAG,IAAI;AACpB,EAAE,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE;AAC9B,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC;AACrC;AACA,KAAK,MAAM;AACX,MAAM,IAAI,OAAO;AACjB,MAAM,IAAI,MAAM;AAChB,MAAM,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK;AAChD,QAAQ,OAAO,GAAG,GAAG;AACrB,QAAQ,MAAM,GAAG,GAAG;AACpB,OAAO,CAAC;AACR,MAAM,OAAO,GAAG;AAChB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,OAAO;AACf,QAAQ,OAAO;AACf,QAAQ;AACR,OAAO;AACP;AACA,IAAI,OAAO,CAAC,MAAM,GAAG,YAAY;AACjC,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,MAAM,MAAM,GAAG,GAAG,OAAO;AACzB,MAAM,OAAO,GAAG,IAAI;AACpB,MAAM,IAAI;AACV,QAAQ,GAAG,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACrD,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC;AACzB;AACA,KAAK;AACL,IAAI,OAAO,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,IAAI,KAAK,UAAU,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC;AAC5F,IAAI,OAAO,OAAO,CAAC,OAAO;AAC1B;AACA,EAAE,SAAS,CAAC,MAAM,GAAG,YAAY;AACjC,IAAI,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;AAC9C,MAAM,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC5D,MAAM,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,IAAI,EAAE;AAChD;AACA,IAAI,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;AAC/B,IAAI,OAAO,GAAG,IAAI;AAClB,GAAG;AACH,EAAE,SAAS,CAAC,eAAe,GAAG,YAAY;AAC1C,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AACtC,MAAM,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,KAAK,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC5D,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AACxC;AACA,IAAI,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC;AACjC,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI;AAC1B,IAAI,MAAM,OAAO,CAAC,MAAM,IAAI;AAC5B,GAAG;AACH,EAAE,MAAM,CAAC,cAAc,CAAC,SAAS,EAAE,SAAS,EAAE;AAC9C,IAAI,UAAU,EAAE,IAAI;AACpB,IAAI,GAAG,GAAG;AACV,MAAM,OAAO,CAAC,CAAC,OAAO,EAAE,OAAO;AAC/B;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,SAAS;AAClB;;;;"}