{"version": 3, "file": "7-6ZGpbMQh.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/legal/_layout.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/7.js"], "sourcesContent": ["import { c as client } from \"../../../chunks/client2.js\";\nconst load = async () => {\n  try {\n    const result = await client.fetch(`\n      {\n        \"legalPages\": *[_type == \"page\" && pageType == \"legal\" && defined(slug.current)] | order(title asc) {\n          title,\n          description,\n          \"slug\": slug.current\n        },\n        \"legalContact\": *[_type == \"page\" && pageType == \"legal\"][0].legalContact\n      }\n    `);\n    console.log(\"Legal pages found:\", result.legalPages?.length || 0);\n    if (result.legalPages?.length) {\n      console.log(\"First legal page:\", result.legalPages[0]);\n    }\n    return {\n      legalPages: result.legalPages || [],\n      legalContact: result.legalContact || {\n        email: \"<EMAIL>\",\n        message: \"If you have questions about our legal documents, please contact our legal team.\"\n      }\n    };\n  } catch (error) {\n    console.error(\"Error loading legal navigation:\", error);\n    return {\n      legalPages: [\n        { title: \"Privacy Policy\", slug: \"privacy\" },\n        { title: \"Terms of Service\", slug: \"terms\" },\n        { title: \"Cookie Policy\", slug: \"cookies\" },\n        { title: \"Accessibility\", slug: \"accessibility\" },\n        { title: \"Data Security\", slug: \"data-security\" },\n        { title: \"GDPR Compliance\", slug: \"gdpr\" },\n        { title: \"Legal Notices\", slug: \"legal-notices\" }\n      ],\n      legalContact: {\n        email: \"<EMAIL>\",\n        message: \"If you have questions about our legal documents, please contact our legal team.\"\n      }\n    };\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/legal/_layout.server.ts.js';\n\nexport const index = 7;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/legal/_layout.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/legal/+layout.server.ts\";\nexport const imports = [\"_app/immutable/nodes/7.M8oxj5Y_.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/Z9Zpt0fH.js\",\"_app/immutable/chunks/B3MJtjra.js\",\"_app/immutable/chunks/nZgk9enP.js\"];\nexport const stylesheets = [];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;AACA,MAAM,IAAI,GAAG,YAAY;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE;AACnC,MAAM,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5D;AACA,IAAI,OAAO;AACX,MAAM,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,EAAE;AACzC,MAAM,YAAY,EAAE,MAAM,CAAC,YAAY,IAAI;AAC3C,QAAQ,KAAK,EAAE,iBAAiB;AAChC,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,OAAO;AACX,MAAM,UAAU,EAAE;AAClB,QAAQ,EAAE,KAAK,EAAE,gBAAgB,EAAE,IAAI,EAAE,SAAS,EAAE;AACpD,QAAQ,EAAE,KAAK,EAAE,kBAAkB,EAAE,IAAI,EAAE,OAAO,EAAE;AACpD,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,SAAS,EAAE;AACnD,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,eAAe,EAAE;AACzD,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,eAAe,EAAE;AACzD,QAAQ,EAAE,KAAK,EAAE,iBAAiB,EAAE,IAAI,EAAE,MAAM,EAAE;AAClD,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,IAAI,EAAE,eAAe;AACvD,OAAO;AACP,MAAM,YAAY,EAAE;AACpB,QAAQ,KAAK,EAAE,iBAAiB;AAChC,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK;AACL;AACA,CAAC;;;;;;;ACxCW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,8BAA0C,CAAC,EAAE;AAExG,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,oCAAoC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACxiB,MAAC,WAAW,GAAG;AACf,MAAC,KAAK,GAAG;;;;"}