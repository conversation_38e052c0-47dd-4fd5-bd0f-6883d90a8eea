{"version": 3, "file": "_layout.svelte-C8gXDzvJ.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/legal/_layout.svelte.js"], "sourcesContent": ["import { U as ensure_array_like, S as attr_class, W as stringify, _ as store_get, R as attr, V as escape_html, a0 as slot, a1 as unsubscribe_stores, N as bind_props, y as pop, w as push } from \"../../../chunks/index3.js\";\nimport { p as page } from \"../../../chunks/stores.js\";\nfunction _layout($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let data = $$props[\"data\"];\n  const { legalPages, legalContact } = data;\n  const contactEmail = legalContact?.email || \"<EMAIL>\";\n  const contactMessage = legalContact?.message || \"If you have questions about our legal documents, please contact our legal team.\";\n  const each_array = ensure_array_like(legalPages);\n  $$payload.out += `<div class=\"max-w-8xl container mx-auto px-8 py-20\"><div class=\"mb-8\"><h1 class=\"mb-2 text-3xl font-bold\">Legal Documents</h1> <p class=\"text-gray-600\">Important information about your rights and our obligations</p></div> <div class=\"grid grid-cols-1 gap-8 md:grid-cols-4\"><div class=\"md:col-span-1\"><div class=\"rounded-lg border p-4 shadow-sm\"><nav class=\"space-y-1\"><a href=\"/legal\"${attr_class(`block rounded px-3 py-2 ${stringify(store_get($$store_subs ??= {}, \"$page\", page).url.pathname === \"/legal\" ? \"bg-blue-50 font-medium text-blue-600\" : \"text-gray-700 hover:bg-gray-50\")}`)}>Legal Center</a> <!--[-->`;\n  for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n    let legalPage = each_array[$$index];\n    $$payload.out += `<a${attr(\"href\", `/legal/${stringify(legalPage.slug)}`)}${attr_class(`block rounded px-3 py-2 ${stringify(store_get($$store_subs ??= {}, \"$page\", page).url.pathname === `/legal/${legalPage.slug}` ? \"bg-blue-50 font-medium text-blue-600\" : \"text-gray-700 hover:bg-gray-50\")}`)}>${escape_html(legalPage.title)}</a>`;\n  }\n  $$payload.out += `<!--]--></nav> <div class=\"mt-6 border-t pt-6\"><p class=\"mb-4 text-sm text-gray-600\">${escape_html(contactMessage)}</p> <a${attr(\"href\", `mailto:${stringify(contactEmail)}`)} class=\"text-sm font-medium text-blue-600 hover:underline\">${escape_html(contactEmail)}</a></div></div></div> <div class=\"md:col-span-3\"><div class=\"rounded-lg border p-8\"><!---->`;\n  slot($$payload, $$props, \"default\", {}, null);\n  $$payload.out += `<!----></div></div></div></div>`;\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,IAAI;AAC3C,EAAE,MAAM,YAAY,GAAG,YAAY,EAAE,KAAK,IAAI,iBAAiB;AAC/D,EAAE,MAAM,cAAc,GAAG,YAAY,EAAE,OAAO,IAAI,iFAAiF;AACnI,EAAE,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;AAClD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gYAAgY,EAAE,UAAU,CAAC,CAAC,wBAAwB,EAAE,SAAS,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,KAAK,QAAQ,GAAG,sCAAsC,GAAG,gCAAgC,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B,CAAC;AACznB,EAAE,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACrF,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC;AACvC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,wBAAwB,EAAE,SAAS,CAAC,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,sCAAsC,GAAG,gCAAgC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AAC/U;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,qFAAqF,EAAE,WAAW,CAAC,cAAc,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,2DAA2D,EAAE,WAAW,CAAC,YAAY,CAAC,CAAC,4FAA4F,CAAC;AACtX,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AAC/C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,+BAA+B,CAAC;AACpD,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}