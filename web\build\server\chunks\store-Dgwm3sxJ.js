const isBrowser = typeof window !== "undefined";
function noop() {
}
function writable(value) {
  const subscribers = /* @__PURE__ */ new Set();
  function set(new_value) {
    value = new_value;
    if (isBrowser) {
      subscribers.forEach((subscriber) => subscriber[0](value));
    }
  }
  function update(fn) {
    set(fn(value));
  }
  function subscribe(run, invalidate = noop) {
    const subscriber = [run, invalidate];
    subscribers.add(subscriber);
    if (isBrowser) {
      run(value);
    }
    return () => {
      subscribers.delete(subscriber);
    };
  }
  return { set, update, subscribe };
}

export { writable as w };
//# sourceMappingURL=store-Dgwm3sxJ.js.map
