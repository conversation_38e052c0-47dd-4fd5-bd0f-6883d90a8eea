// Cloudflare Worker to proxy R2 static assets with proper CORS and caching
// Deploy this to a custom domain like static.yourdomain.com

export default {
  async fetch(request, env, ctx) {
    const url = new URL(request.url);

    // Handle CORS preflight requests
    if (request.method === "OPTIONS") {
      return new Response(null, {
        status: 200,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, HEAD, OPTIONS",
          "Access-Control-Allow-Headers": "*",
          "Access-Control-Max-Age": "86400",
        },
      });
    }

    // Only handle GET and HEAD requests
    if (!["GET", "HEAD"].includes(request.method)) {
      return new Response("Method not allowed", { status: 405 });
    }

    // Extract the file path (remove leading slash)
    const filePath = url.pathname.substring(1);

    // If no file path, return 404
    if (!filePath) {
      return new Response("Not found", { status: 404 });
    }

    try {
      // Determine the object key based on path prefix
      let objectKey;

      if (filePath.startsWith("logos/")) {
        // Company logos: try both R2 accounts as fallbacks
        objectKey = filePath.replace("logos/", "");

        // Based on testing, files actually exist in the second account with logos/ prefix
        const primaryUrl = `https://pub-46a6f782171b440493a823a520764a72.r2.dev/logos/${objectKey}`;
        const fallbackUrl = `https://pub-7efc1bf67e7d23f5683e06d0227c883f.r2.dev/logos/${objectKey}`;

        console.log(`🔍 Trying logo: ${objectKey}`);
        console.log(`📍 Primary URL: ${primaryUrl}`);

        let sourceResponse = await fetch(primaryUrl, {
          headers: { "User-Agent": "Cloudflare-Worker-Proxy/1.0" },
        });

        if (!sourceResponse.ok) {
          console.log(
            `❌ Primary failed (${sourceResponse.status}), trying fallback: ${fallbackUrl}`
          );
          sourceResponse = await fetch(fallbackUrl, {
            headers: { "User-Agent": "Cloudflare-Worker-Proxy/1.0" },
          });

          if (sourceResponse.ok) {
            console.log(`✅ Fallback succeeded for: ${objectKey}`);
          }
        } else {
          console.log(`✅ Primary succeeded for: ${objectKey}`);
        }

        if (!sourceResponse.ok) {
          console.log(`❌ Both accounts failed for: ${objectKey}`);
          return new Response("File not found", { status: 404 });
        }

        // Create response headers with proper CORS and caching
        const headers = new Headers();
        headers.set(
          "Content-Type",
          sourceResponse.headers.get("content-type") ||
            getContentType(objectKey)
        );
        headers.set("Access-Control-Allow-Origin", "*");
        headers.set("Access-Control-Allow-Methods", "GET, HEAD");
        headers.set("Access-Control-Allow-Headers", "*");
        headers.set("Access-Control-Max-Age", "86400");
        headers.set("Cache-Control", "public, max-age=********, immutable");

        const etag = sourceResponse.headers.get("etag");
        if (etag) {
          headers.set("ETag", etag);
          const ifNoneMatch = request.headers.get("If-None-Match");
          if (ifNoneMatch && ifNoneMatch === etag) {
            return new Response(null, { status: 304, headers });
          }
        }

        headers.set("X-Content-Type-Options", "nosniff");
        headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

        return new Response(sourceResponse.body, { status: 200, headers });
      } else if (filePath.startsWith("resumes/")) {
        // Resumes: use local bucket (these should be in the current account)
        const object = await env.RESUMES_BUCKET.get(
          filePath.replace("resumes/", "")
        );
        if (!object) {
          return new Response("File not found", { status: 404 });
        }
        return createR2Response(
          object,
          filePath.replace("resumes/", ""),
          request
        );
      } else if (filePath.startsWith("user/")) {
        // User files: use local bucket
        const object = await env.USER_BUCKET.get(filePath.replace("user/", ""));
        if (!object) {
          return new Response("File not found", { status: 404 });
        }
        return createR2Response(object, filePath.replace("user/", ""), request);
      } else {
        // Default to company logos (try both accounts)
        objectKey = filePath;

        // Use same account priority as logos/ path - try with logos/ prefix first
        const primaryUrl = `https://pub-46a6f782171b440493a823a520764a72.r2.dev/logos/${objectKey}`;
        const fallbackUrl = `https://pub-46a6f782171b440493a823a520764a72.r2.dev/${objectKey}`;

        console.log(`🔍 Trying default logo: ${objectKey}`);
        console.log(`📍 Primary URL: ${primaryUrl}`);

        let sourceResponse = await fetch(primaryUrl, {
          headers: { "User-Agent": "Cloudflare-Worker-Proxy/1.0" },
        });

        if (!sourceResponse.ok) {
          console.log(
            `❌ Primary failed (${sourceResponse.status}), trying fallback: ${fallbackUrl}`
          );
          sourceResponse = await fetch(fallbackUrl, {
            headers: { "User-Agent": "Cloudflare-Worker-Proxy/1.0" },
          });

          if (sourceResponse.ok) {
            console.log(`✅ Fallback succeeded for: ${objectKey}`);
          }
        } else {
          console.log(`✅ Primary succeeded for: ${objectKey}`);
        }

        if (!sourceResponse.ok) {
          console.log(`❌ Both accounts failed for: ${objectKey}`);
          return new Response("File not found", { status: 404 });
        }

        const headers = new Headers();
        headers.set(
          "Content-Type",
          sourceResponse.headers.get("content-type") ||
            getContentType(objectKey)
        );
        headers.set("Access-Control-Allow-Origin", "*");
        headers.set("Access-Control-Allow-Methods", "GET, HEAD");
        headers.set("Access-Control-Allow-Headers", "*");
        headers.set("Access-Control-Max-Age", "86400");
        headers.set("Cache-Control", "public, max-age=********, immutable");

        const etag = sourceResponse.headers.get("etag");
        if (etag) {
          headers.set("ETag", etag);
          const ifNoneMatch = request.headers.get("If-None-Match");
          if (ifNoneMatch && ifNoneMatch === etag) {
            return new Response(null, { status: 304, headers });
          }
        }

        headers.set("X-Content-Type-Options", "nosniff");
        headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

        return new Response(sourceResponse.body, { status: 200, headers });
      }
    } catch (error) {
      console.error("Error serving static asset:", error);
      return new Response("Internal server error", { status: 500 });
    }
  },
};

// Helper function to create response from R2 object
function createR2Response(object, objectKey, request) {
  const contentType =
    object.httpMetadata?.contentType || getContentType(objectKey);

  const headers = new Headers();
  headers.set("Content-Type", contentType);
  headers.set("Access-Control-Allow-Origin", "*");
  headers.set("Access-Control-Allow-Methods", "GET, HEAD");
  headers.set("Access-Control-Allow-Headers", "*");
  headers.set("Access-Control-Max-Age", "86400");
  headers.set("Cache-Control", "public, max-age=********, immutable");
  headers.set("ETag", object.httpEtag);
  headers.set("X-Content-Type-Options", "nosniff");
  headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  // Handle conditional requests
  const ifNoneMatch = request.headers.get("If-None-Match");
  if (ifNoneMatch && ifNoneMatch === object.httpEtag) {
    return new Response(null, { status: 304, headers });
  }

  return new Response(object.body, { status: 200, headers });
}

// Helper function to determine content type from file extension
function getContentType(filename) {
  const ext = filename.split(".").pop()?.toLowerCase();

  const mimeTypes = {
    webp: "image/webp",
    png: "image/png",
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    gif: "image/gif",
    svg: "image/svg+xml",
    pdf: "application/pdf",
    doc: "application/msword",
    docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    txt: "text/plain",
    json: "application/json",
    css: "text/css",
    js: "application/javascript",
    html: "text/html",
  };

  return mimeTypes[ext] || "application/octet-stream";
}
