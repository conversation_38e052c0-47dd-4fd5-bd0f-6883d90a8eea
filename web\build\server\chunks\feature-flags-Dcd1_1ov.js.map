{"version": 3, "file": "feature-flags-Dcd1_1ov.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/feature-flags.js"], "sourcesContent": ["import { d as dev } from \"./index4.js\";\nconst FEATURE_FLAGS = {\n  // Core features\n  dashboard: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"Main dashboard access\"\n  },\n  profile: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"User profile management\"\n  },\n  // Automation features\n  automation: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"Job automation and application features\"\n  },\n  // Resume features\n  resume_scanner: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"Resume scanning and analysis\"\n  },\n  ats_optimization: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"ATS optimization features\"\n  },\n  // AI features\n  cover_letter_generator: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"AI-powered cover letter generation\"\n  },\n  ai_matching: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"AI job matching\"\n  },\n  // Analytics features\n  analytics: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"Job market analytics and insights\"\n  },\n  // Team features\n  team_collaboration: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"Team collaboration features\"\n  },\n  // Integration features\n  linkedin_integration: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"LinkedIn integration\"\n  },\n  // Communication features\n  email_support: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"Email support access\"\n  },\n  // Advanced features\n  api_access: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"API access for integrations\"\n  },\n  custom_branding: {\n    enabled: true,\n    bypassInDevelopment: true,\n    description: \"Custom branding options\"\n  }\n};\nconst ENVIRONMENT_CONFIG = {\n  // Disable all feature limits in development\n  DISABLE_ALL_LIMITS: typeof window !== \"undefined\" && localStorage.getItem(\"disable-feature-limits\") === \"true\",\n  // Disable specific features via environment (can be set via localStorage in browser)\n  DISABLED_FEATURES: typeof window !== \"undefined\" ? (localStorage.getItem(\"disabled-features\") || \"\").split(\",\").filter(Boolean) : [],\n  // Enable all features for testing\n  ENABLE_ALL_FEATURES: typeof window !== \"undefined\" && localStorage.getItem(\"enable-all-features\") === \"true\",\n  // Development mode bypass\n  DEVELOPMENT_BYPASS: dev\n};\nfunction isFeatureEnabled(featureId) {\n  if (ENVIRONMENT_CONFIG.DISABLED_FEATURES.includes(\"*\")) {\n    return false;\n  }\n  if (ENVIRONMENT_CONFIG.ENABLE_ALL_FEATURES) {\n    return true;\n  }\n  if (ENVIRONMENT_CONFIG.DISABLED_FEATURES.includes(featureId)) {\n    return false;\n  }\n  const featureConfig = FEATURE_FLAGS[featureId];\n  if (!featureConfig) {\n    console.warn(`Feature '${featureId}' not found in FEATURE_FLAGS`);\n    return false;\n  }\n  return featureConfig.enabled;\n}\nfunction shouldBypassLimits(featureId) {\n  if (ENVIRONMENT_CONFIG.DISABLE_ALL_LIMITS) {\n    return true;\n  }\n  const featureConfig = FEATURE_FLAGS[featureId];\n  if (featureConfig?.bypassInDevelopment && ENVIRONMENT_CONFIG.DEVELOPMENT_BYPASS) ;\n  return false;\n}\nfunction getEnabledFeatures() {\n  return Object.entries(FEATURE_FLAGS).filter(([featureId]) => isFeatureEnabled(featureId)).map(([featureId]) => featureId);\n}\nfunction getFeatureConfig(featureId) {\n  return FEATURE_FLAGS[featureId] || null;\n}\nfunction toggleFeature(featureId, enabled) {\n  if (FEATURE_FLAGS[featureId]) {\n    FEATURE_FLAGS[featureId].enabled = enabled;\n    console.log(`Feature '${featureId}' ${enabled ? \"enabled\" : \"disabled\"}`);\n  }\n}\nexport {\n  ENVIRONMENT_CONFIG,\n  FEATURE_FLAGS,\n  getEnabledFeatures,\n  getFeatureConfig,\n  isFeatureEnabled,\n  shouldBypassLimits,\n  toggleFeature\n};\n"], "names": [], "mappings": ";;;AACK,MAAC,aAAa,GAAG;AACtB;AACA,EAAE,SAAS,EAAE;AACb,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH;AACA,EAAE,UAAU,EAAE;AACd,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH;AACA,EAAE,cAAc,EAAE;AAClB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH;AACA,EAAE,sBAAsB,EAAE;AAC1B,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH;AACA,EAAE,SAAS,EAAE;AACb,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH;AACA,EAAE,kBAAkB,EAAE;AACtB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH;AACA,EAAE,oBAAoB,EAAE;AACxB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH;AACA,EAAE,aAAa,EAAE;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH;AACA,EAAE,UAAU,EAAE;AACd,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,mBAAmB,EAAE,IAAI;AAC7B,IAAI,WAAW,EAAE;AACjB;AACA;AACK,MAAC,kBAAkB,GAAG;AAC3B;AACA,EAAE,kBAAkB,EAAE,OAAO,MAAM,KAAK,WAAW,IAAI,YAAY,CAAC,OAAO,CAAC,wBAAwB,CAAC,KAAK,MAAM;AAChH;AACA,EAAE,iBAAiB,EAAE,OAAO,MAAM,KAAK,WAAW,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,EAAE;AACtI;AACA,EAAE,mBAAmB,EAAE,OAAO,MAAM,KAAK,WAAW,IAAI,YAAY,CAAC,OAAO,CAAC,qBAAqB,CAAC,KAAK,MAAM;AAC9G;AACA,EAAE,kBAAkB,EAAE;AACtB;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE;AACrC,EAAE,IAAI,kBAAkB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AAC1D,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,IAAI,kBAAkB,CAAC,mBAAmB,EAAE;AAC9C,IAAI,OAAO,IAAI;AACf;AACA,EAAE,IAAI,kBAAkB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;AAChE,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,MAAM,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC;AAChD,EAAE,IAAI,CAAC,aAAa,EAAE;AACtB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,4BAA4B,CAAC,CAAC;AACrE,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,aAAa,CAAC,OAAO;AAC9B;AACA,SAAS,kBAAkB,CAAC,SAAS,EAAE;AACvC,EAAE,IAAI,kBAAkB,CAAC,kBAAkB,EAAE;AAC7C,IAAI,OAAO,IAAI;AACf;AACA,EAAE,MAAM,aAAa,GAAG,aAAa,CAAC,SAAS,CAAC;AAChD,EAAE,IAAI,aAAa,EAAE,mBAAmB,IAAI,kBAAkB,CAAC,kBAAkB,EAAE;AACnF,EAAE,OAAO,KAAK;AACd;AACA,SAAS,kBAAkB,GAAG;AAC9B,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,gBAAgB,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC;AAC3H;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE;AACrC,EAAE,OAAO,aAAa,CAAC,SAAS,CAAC,IAAI,IAAI;AACzC;AACA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE;AAChC,IAAI,aAAa,CAAC,SAAS,CAAC,CAAC,OAAO,GAAG,OAAO;AAC9C,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,EAAE,OAAO,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC;AAC7E;AACA;;;;"}