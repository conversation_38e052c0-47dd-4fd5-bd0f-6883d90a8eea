import { generateRegistrationOptions, verifyRegistrationResponse, generateAuthenticationOptions, verifyAuthenticationResponse } from '@simplewebauthn/server';

const RP_NAME = "Hirli";
const isDev = process.env.NODE_ENV !== "production";
const RP_ID = isDev ? "localhost" : "hirli.co";
const EXPECTED_ORIGIN = isDev ? ["http://localhost:5173"] : ["https://hirli.co"];
async function generatePasskeyRegistrationOptions(userId, username, existingCredentials = []) {
  console.log("generatePasskeyRegistrationOptions called");
  console.log("userId:", userId);
  console.log("username:", username);
  console.log("existingCredentials:", existingCredentials);
  try {
    const options = {
      rpName: RP_NAME,
      rpID: RP_ID,
      userID: userId,
      userName: username,
      timeout: 6e4,
      attestationType: "none",
      // Skip excludeCredentials for now to simplify the process
      excludeCredentials: [],
      authenticatorSelection: {
        authenticatorAttachment: "platform",
        userVerification: "required",
        residentKey: "preferred",
        requireResidentKey: false
      },
      supportedAlgorithmIDs: [-7, -257]
      // ES256, RS256
    };
    console.log("Options prepared:", options);
    const result = generateRegistrationOptions(options);
    console.log("Registration options generated successfully");
    return result;
  } catch (error) {
    console.error("Error in generatePasskeyRegistrationOptions:", error);
    console.error("Error stack:", error.stack);
    throw error;
  }
}
async function verifyPasskeyRegistration(response, expectedChallenge) {
  console.log("verifyPasskeyRegistration called");
  console.log("Response:", JSON.stringify(response, null, 2));
  console.log("Expected challenge:", expectedChallenge);
  try {
    const verification = {
      response,
      expectedChallenge,
      expectedOrigin: EXPECTED_ORIGIN[0],
      expectedRPID: RP_ID
    };
    console.log("Verification options:", JSON.stringify(verification, null, 2));
    const verificationResult = await verifyRegistrationResponse(verification);
    console.log("Verification result:", JSON.stringify(verificationResult, null, 2));
    return {
      verified: verificationResult.verified,
      registrationInfo: verificationResult.registrationInfo
    };
  } catch (error) {
    console.error("Error verifying registration:", error);
    console.error("Error stack:", error.stack);
    return { verified: false, error: error.message };
  }
}
async function generatePasskeyAuthenticationOptions(existingPasskeys = []) {
  console.log(
    "generatePasskeyAuthenticationOptions called with",
    existingPasskeys.length,
    "passkeys"
  );
  try {
    const options = {
      rpID: RP_ID,
      timeout: 6e4,
      userVerification: "preferred"
      // Use 'preferred' for better compatibility
    };
    if (existingPasskeys.length > 0) {
      console.log("Including allowCredentials for", existingPasskeys.length, "passkeys");
      const normalizedPasskeys = existingPasskeys.map((passkey) => {
        return {
          id: passkey.id || passkey.credentialID || "unknown-id",
          credentialID: passkey.credentialID || passkey.id || "unknown-id",
          credentialPublicKey: passkey.credentialPublicKey || passkey.publicKey || "",
          transports: passkey.transports || []
        };
      });
      options.allowCredentials = normalizedPasskeys.map((passkey) => {
        const credId = passkey.credentialID || passkey.id;
        console.log("Processing credential ID:", credId);
        let idBuffer;
        try {
          idBuffer = Buffer.from(credId, "base64url");
        } catch (e) {
          console.log("Failed to decode as base64url, trying base64. Error:", e);
          idBuffer = Buffer.from(credId, "base64");
        }
        return {
          id: idBuffer,
          type: "public-key",
          transports: passkey.transports || ["internal"]
        };
      });
    } else {
      console.log("No passkeys provided, browser will show all available passkeys");
    }
    console.log("Authentication options:", {
      ...options,
      allowCredentials: options.allowCredentials ? `${options.allowCredentials.length} credentials` : "omitted"
    });
    const result = generateAuthenticationOptions(options);
    console.log("Generated authentication options");
    return result;
  } catch (error) {
    console.error("Error generating authentication options:", error);
    console.error("Error stack:", error.stack);
    throw error;
  }
}
async function verifyPasskeyAuthentication(response, expectedChallenge, credentialPublicKey, credentialCounter) {
  console.log("verifyPasskeyAuthentication called");
  console.log("Response:", JSON.stringify(response, null, 2));
  console.log("Expected challenge:", expectedChallenge);
  try {
    let credentialIDBuffer;
    try {
      credentialIDBuffer = Buffer.from(response.id, "base64url");
    } catch {
      console.log("Failed to decode credential ID as base64url, trying base64");
      credentialIDBuffer = Buffer.from(response.id.replace(/-/g, "+").replace(/_/g, "/"), "base64");
    }
    let credentialPublicKeyBuffer;
    try {
      credentialPublicKeyBuffer = Buffer.from(credentialPublicKey, "base64url");
    } catch {
      console.log("Failed to decode public key as base64url, trying base64");
      credentialPublicKeyBuffer = Buffer.from(
        credentialPublicKey.replace(/-/g, "+").replace(/_/g, "/"),
        "base64"
      );
    }
    const verification = {
      response,
      expectedChallenge,
      expectedOrigin: EXPECTED_ORIGIN[0],
      expectedRPID: RP_ID,
      authenticator: {
        credentialID: credentialIDBuffer,
        credentialPublicKey: credentialPublicKeyBuffer,
        counter: credentialCounter
      }
    };
    console.log("Verification options:", JSON.stringify(verification, null, 2));
    return await verifyAuthenticationResponse(verification);
  } catch (error) {
    console.error("Error verifying authentication:", error);
    return { verified: false, error: error.message };
  }
}

export { generatePasskeyAuthenticationOptions as a, verifyPasskeyAuthentication as b, generatePasskeyRegistrationOptions as g, verifyPasskeyRegistration as v };
//# sourceMappingURL=webauthn-8Tn9JPcG.js.map
