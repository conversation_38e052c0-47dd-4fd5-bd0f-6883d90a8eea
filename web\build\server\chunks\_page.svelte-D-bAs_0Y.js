import { p as push, Q as bind_props, q as pop, N as attr, O as escape_html, M as ensure_array_like } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { A as Accordion_root } from './accordion-trigger-DwieKZVA.js';
import { M as MaintenanceAccordion } from './MaintenanceAccordion-hfzOCBt1.js';
import { C as Chevron_left } from './chevron-left-Q7JcxjQ3.js';
import { C as Chevron_right } from './chevron-right2-CeLbJ90J.js';
import 'clsx';
import './false-CRHihH2U.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './chevron-down-xGjWLrZH.js';
import './Icon-A4vzmk-O.js';
import './badge-C9pSznab.js';
import './index-DjwFQdT_.js';
import './StatusBar-DynsEX84.js';
import './circle-alert-BcRZk-Zc.js';
import './circle-x-DFm9GVXv.js';
import './clock-BHOPwoCS.js';
import './triangle-alert-DOwM8mYc.js';
import './search-B0oHlTPS.js';
import './play-DKNYqs4c.js';
import './circle-check-big-DBrIQZ7A.js';
import './info-Ce09B-Yv.js';
import './message-square-D57Olt6y.js';

function HistoryComponent($$payload, $$props) {
  push();
  const {
    maintenance,
    currentMonth,
    currentYear,
    hasNextMonth,
    hasPrevMonth
  } = $$props;
  function formatMonth(month, year) {
    return new Intl.DateTimeFormat("en-US", { month: "long", year: "numeric" }).format(new Date(year, month - 1, 1));
  }
  $$payload.out += `<div class="flex flex-col gap-4"><div class="flex items-center justify-between px-4"><button class="flex items-center gap-1 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-800"${attr("disabled", !hasPrevMonth, true)} aria-label="Previous month">`;
  Chevron_left($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> <span>Previous</span></button> <h2 class="text-xl font-semibold">${escape_html(formatMonth(currentMonth, currentYear))}</h2> <button class="flex items-center gap-1 rounded-md p-2 hover:bg-gray-100 dark:hover:bg-gray-800"${attr("disabled", !hasNextMonth, true)} aria-label="Next month"><span>Next</span> `;
  Chevron_right($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----></button></div> <div class="px-4 pb-8">`;
  if (maintenance && maintenance.length > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    Accordion_root($$payload, {
      type: "multiple",
      class: "w-full space-y-4",
      children: ($$payload2) => {
        const each_array = ensure_array_like(maintenance);
        $$payload2.out += `<!--[-->`;
        for (let i = 0, $$length = each_array.length; i < $$length; i++) {
          let incident = each_array[i];
          MaintenanceAccordion($$payload2, { incident, index: i });
        }
        $$payload2.out += `<!--]-->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="mt-8 rounded-lg border p-8 text-center"><p class="text-muted-foreground">No notices or maintenance events for ${escape_html(formatMonth(currentMonth, currentYear))}</p></div>`;
  }
  $$payload.out += `<!--]--></div></div>`;
  pop();
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  SEO($$payload, {
    title: "System Status History | Hirli",
    description: "View the history of system status notices and maintenance events.",
    keywords: "system status, maintenance history, incident history, Hirli status"
  });
  $$payload.out += `<!----> <div class="flex flex-col gap-4"><div class="border-border flex items-center justify-between border-b p-6"><div class="flex flex-col"><h1 class="text-3xl font-bold">Notice History</h1> <p class="text-muted-foreground">Past system notices and maintenance events</p></div> <div><a href="/system-status" class="text-primary hover:underline">Back to Status Page</a></div></div> `;
  HistoryComponent($$payload, {
    maintenance: data.maintenance,
    currentMonth: data.currentMonth,
    currentYear: data.currentYear,
    hasNextMonth: data.hasNextMonth,
    hasPrevMonth: data.hasPrevMonth
  });
  $$payload.out += `<!----></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-D-bAs_0Y.js.map
