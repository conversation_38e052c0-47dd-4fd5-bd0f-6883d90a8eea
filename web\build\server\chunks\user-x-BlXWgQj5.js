import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function Mouse_pointer_click($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    ["path", { "d": "M14 4.1 12 6" }],
    ["path", { "d": "m5.1 8-2.9-.8" }],
    ["path", { "d": "m6 12-1.9 2" }],
    ["path", { "d": "M7.2 2.2 8 5.1" }],
    [
      "path",
      {
        "d": "M9.037 9.69a.498.498 0 0 1 .653-.653l11 4.5a.5.5 0 0 1-.074.949l-4.349 1.041a1 1 0 0 0-.74.739l-1.04 4.35a.5.5 0 0 1-.95.074z"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "mouse-pointer-click" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}
function User_x($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "path",
      {
        "d": "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"
      }
    ],
    ["circle", { "cx": "9", "cy": "7", "r": "4" }],
    [
      "line",
      {
        "x1": "17",
        "x2": "22",
        "y1": "8",
        "y2": "13"
      }
    ],
    [
      "line",
      {
        "x1": "22",
        "x2": "17",
        "y1": "8",
        "y2": "13"
      }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "user-x" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { Mouse_pointer_click as M, User_x as U };
//# sourceMappingURL=user-x-BlXWgQj5.js.map
