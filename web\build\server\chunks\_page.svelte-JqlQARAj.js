import { p as push, V as copy_payload, W as assign_payload, Q as bind_props, q as pop, O as escape_html } from './index3-CqUPEnZw.js';
import { a as toast } from './Toaster.svelte_svelte_type_style_lang-C29KBcns.js';
import 'clsx';
import { S as SEO } from './SEO-UItXytUy.js';
import { J as JobSearch, a as JobFeed } from './JobFeed-lICPS7Rb.js';
import { R as Root, D as Dialog_content } from './index7-BURUpWjT.js';
import { D as Dialog_header, a as Dialog_title, b as Dialog_description } from './dialog-description-CxPAHL_4.js';
import './false-CRHihH2U.js';
import './index2-Cut0V_vU.js';
import './input-DF0gPqYN.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index12-H6t3LX3-.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './mounted-BL5aWRUY.js';
import './box-auto-reset.svelte-BDripiF0.js';
import './check2-Bg6barQb.js';
import './Icon2-DkOdBr51.js';
import './scroll-lock-BkBz2nVp.js';
import './index-server-CezSOnuG.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './hidden-input-1eDzjGOB.js';
import './multi-combobox-BJ-pW9qf.js';
import './index14-C2WSwUih.js';
import './button-CrucCo1G.js';
import './index-DjwFQdT_.js';
import './dropdown-store-B4Dfz2ZI.js';
import './clone-BRGVxGEr.js';
import './check-WP_4Msti.js';
import './Icon-A4vzmk-O.js';
import './chevron-down-xGjWLrZH.js';
import './search-input-CbGkN9s9.js';
import './search-B0oHlTPS.js';
import './index4-HpJcNJHQ.js';
import './index10-F28UXWIO.js';
import './dialog-overlay-CspOQRJq.js';
import './x-DwZgpWRG.js';
import './dialog-description2-rfr-pd9k.js';
import './switch-CwRjBz3R.js';
import './select-value-nUrqCsCq.js';
import './select-group-Cxqg41Dj.js';
import './sliders-vertical-or4TQDCk.js';
import './sheet-footer-B80ycEhL.js';
import './badge-C9pSznab.js';
import './bookmark-DazMkrfp.js';
import './map-pin-BBU2RKZV.js';
import './dollar-sign-CXBwKToB.js';
import './briefcase-DBFF7i-g.js';
import './clock-BHOPwoCS.js';
import './share-2-ihgFYKw2.js';
import './flag-CSTMD-YC.js';
import './sparkles-E4-thk3U.js';
import './html-FW6Ia4bL.js';
import './scroll-area-Dn69zlyp.js';
import './use-debounce.svelte-gxToHznJ.js';
import './loader-circle-BG7dEt2I.js';

const requestCache = /* @__PURE__ */ new Map();
const responseCache = /* @__PURE__ */ new Map();
const CACHE_TIMEOUT = 5 * 60 * 1e3;
function createCacheKey(query, variables) {
  return `${query}:${JSON.stringify(variables)}`;
}
let requestCounter = 0;
async function graphqlRequest(query, variables = {}, options = {}) {
  const cacheKey = createCacheKey(query, variables);
  const operationMatch = query.match(/(?:query|mutation)\s+(\w+)/);
  const operationName = operationMatch ? operationMatch[1] : "UnnamedOperation";
  const requestId = ++requestCounter;
  console.log(`[GraphQL ${requestId}] Request: ${operationName}`, {
    cacheKey,
    variables,
    stack: new Error().stack
  });
  const isMutation = query.trim().startsWith("mutation");
  if (!isMutation && responseCache.has(cacheKey)) {
    const cachedResponse = responseCache.get(cacheKey);
    const now = Date.now();
    if (now - cachedResponse.timestamp < CACHE_TIMEOUT) {
      console.log(`[GraphQL ${requestId}] Cache hit for ${operationName}`);
      return { data: cachedResponse.data };
    } else {
      console.log(`[GraphQL ${requestId}] Cache expired for ${operationName}`);
      responseCache.delete(cacheKey);
    }
  }
  if (requestCache.has(cacheKey)) {
    console.log(`[GraphQL ${requestId}] In-flight request found for ${operationName}`);
    return requestCache.get(cacheKey);
  }
  const requestPromise = (async () => {
    console.log(`[GraphQL ${requestId}] Executing network request for ${operationName}`);
    try {
      const response = await fetch("/api/graphql", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...options.headers
        },
        body: JSON.stringify({
          query,
          variables
        }),
        ...options
      });
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`[GraphQL ${requestId}] Response not OK:`, response.status, errorText);
        throw new Error(`GraphQL request failed with status ${response.status}`);
      }
      const result = await response.json();
      if (result.errors) {
        console.error(`[GraphQL ${requestId}] Errors:`, result.errors);
      } else if (!isMutation && result.data) {
        console.log(`[GraphQL ${requestId}] Caching result for ${operationName}`);
        responseCache.set(cacheKey, {
          data: result.data,
          timestamp: Date.now()
        });
      }
      console.log(`[GraphQL ${requestId}] Request completed for ${operationName}`);
      return result;
    } catch (error) {
      console.error(`[GraphQL ${requestId}] Request error:`, error);
      return { errors: [{ message: error instanceof Error ? error.message : String(error) }] };
    } finally {
      requestCache.delete(cacheKey);
    }
  })();
  requestCache.set(cacheKey, requestPromise);
  return requestPromise;
}
const JOB_LISTINGS_QUERY = `
  query JobListings($filter: JobListingsFilterInput) {
    jobListings(filter: $filter) {
      jobs {
        id
        title
        company
        location
        url
        employmentType
        remoteType
        postedDate
        salary
        applyLink
        companyLogo
      }
      pagination {
        page
        limit
        totalCount
        totalPages
        hasMore
      }
    }
  }
`;
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let jobs = [];
  let isLoading = false;
  let isSearching = false;
  let currentPage = 1;
  let totalCount = data.totalJobCount || 0;
  let selectedJob = data.selectedJob || null;
  let searchParams = data.searchParams || {
    title: "",
    locations: [],
    locationType: [],
    experience: [],
    category: [],
    education: [],
    salary: "",
    state: "",
    country: "US"
  };
  let showAuthDialog = false;
  let authAction = "";
  let searchTimeRemaining = 0;
  let searchTimerInterval;
  async function loadJobs(params = {}, reset = false) {
    console.log("loadJobs called with params:", params);
    if (reset) {
      jobs = [];
      currentPage = 1;
    }
    isLoading = true;
    try {
      const filter = { page: currentPage, limit: 20 };
      if (params.title) filter.title = params.title;
      if (params.locations?.length) {
        try {
          const locationData = params.locations.map((loc) => {
            if (loc.includes("|")) {
              const [id, name, stateCode, country] = loc.split("|");
              return { id, name, stateCode, country };
            } else {
              console.log("Location not in expected format:", loc);
              return {
                id: loc,
                name: loc,
                stateCode: "",
                country: "US"
              };
            }
          });
          console.log("Parsed location data:", locationData);
          filter.locations = locationData.map((loc) => loc.id);
          if (locationData.length > 0) {
            filter.location = `${locationData[0].name}, ${locationData[0].stateCode}`;
          }
        } catch (error) {
          console.error("Error parsing locations:", error, params.locations);
        }
      } else if (params.location) {
        filter.location = params.location;
      }
      if (params.locationType?.length) filter.locationType = params.locationType;
      if (params.experience?.length) filter.experienceLevel = params.experience;
      if (params.salary) filter.salary = params.salary;
      if (params.state) filter.state = params.state;
      if (params.country) filter.country = params.country;
      if (params.collection) filter.collection = params.collection;
      if (params.companies?.length) {
        console.log("Adding companies to filter:", params.companies);
        filter.companies = params.companies;
      }
      const result = await graphqlRequest(JOB_LISTINGS_QUERY, { filter });
      if (result.errors) {
        console.error("GraphQL errors:", result.errors);
        throw new Error(`Failed to fetch jobs: ${result.errors[0].message}`);
      }
      const data2 = result.data?.jobListings;
      const newJobs = data2?.jobs || [];
      totalCount = data2?.pagination?.totalCount || 0;
      if (reset) {
        jobs = newJobs;
      } else {
        jobs = [...jobs, ...newJobs];
      }
      const url = new URL(window.location.href);
      const jobId = url.searchParams.get("jobId");
      if (jobId && !selectedJob) {
        const foundJob = jobs.find((job) => job.id === jobId);
        if (foundJob) {
          selectedJob = foundJob;
        }
      }
      return newJobs;
    } catch (error) {
      console.error("Error loading jobs:", error);
      toast.error(`Failed to load jobs: ${error instanceof Error ? error.message : "Unknown error"}`);
      return [];
    } finally {
      isLoading = false;
    }
  }
  async function handleSearch(params) {
    const hasFilters = params.title || params.locations && params.locations.length > 0 || params.locationType && params.locationType.length > 0 || params.experience && params.experience.length > 0 || params.salary;
    if (!hasFilters) {
      toast.warning("Please enter at least one search filter");
      return Promise.resolve([]);
    }
    if (!data.user) {
      if (searchTimeRemaining > 0) {
        toast.error(`Please wait ${searchTimeRemaining} seconds before searching again`);
        return Promise.resolve([]);
      }
      searchTimeRemaining = 60;
      startSearchTimer();
    }
    searchParams = params;
    isSearching = true;
    try {
      updateUrlFromParams(params);
      const newJobs = await loadJobs(params, true);
      if (newJobs.length === 0) {
        toast.info("No jobs found matching your criteria. Try broadening your search.");
      } else {
        toast.success(`Found ${newJobs.length} jobs matching your search`);
      }
      return newJobs;
    } catch (error) {
      console.error("Search error:", error);
      toast.error(`Search failed: ${error instanceof Error ? error.message : "Unknown error"}`);
      return [];
    } finally {
      isSearching = false;
    }
  }
  function startSearchTimer() {
    clearInterval(searchTimerInterval);
    searchTimerInterval = setInterval(
      () => {
        searchTimeRemaining--;
        if (searchTimeRemaining <= 0) {
          clearInterval(searchTimerInterval);
        }
      },
      1e3
    );
  }
  async function loadMore() {
    currentPage++;
    return await loadJobs(searchParams);
  }
  function handleSignInRequired(action) {
    authAction = action;
    showAuthDialog = true;
  }
  function handleApply(_job) {
    if (!data.user) {
      handleSignInRequired("apply");
      return;
    }
    toast.success("Application started", {
      description: "Tracking this application in your dashboard"
    });
  }
  async function handleSave(job) {
    if (!data.user) {
      handleSignInRequired("save");
      return;
    }
    try {
      const response = await fetch(`/api/jobs/${job.id}/save`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ notes: "" })
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Failed to save job");
      }
      toast.success("Job saved", { description: "Added to your saved jobs" });
      return true;
    } catch (error) {
      console.error("Error saving job:", error);
      toast.error("Failed to save job");
      return false;
    }
  }
  function handleJobSelect(job) {
    selectedJob = job;
  }
  function updateUrlFromParams(params) {
    console.log("Updating URL with params:", params);
    const url = new URL(window.location.href);
    const currentParams = new URLSearchParams(url.search);
    const newParams = new URLSearchParams();
    const searchParams2 = [
      "title",
      "locations",
      "locationType",
      "experience",
      "salary",
      "state",
      "country",
      "datePosted",
      "easyApply",
      "companies"
    ];
    for (const [key, value] of currentParams.entries()) {
      if (!searchParams2.includes(key)) {
        newParams.append(key, value);
      }
    }
    for (const key of searchParams2) {
      if (currentParams.has(key) && params[key] === void 0) {
        newParams.set(key, currentParams.get(key));
      }
    }
    if (params.title !== void 0) {
      if (params.title) {
        newParams.set("title", params.title);
      } else {
        newParams.delete("title");
      }
    }
    if (params.locations !== void 0) {
      if (params.locations?.length) {
        const locationValues = params.locations.map((loc) => {
          if (typeof loc === "string") {
            return loc;
          } else if (loc.id) {
            return loc.id;
          }
          return loc;
        });
        newParams.set("locations", locationValues.join(","));
      } else {
        newParams.delete("locations");
      }
    }
    if (params.locationType !== void 0) {
      if (params.locationType?.length) {
        newParams.set("locationType", params.locationType.join(","));
      } else {
        newParams.delete("locationType");
      }
    }
    if (params.experience !== void 0) {
      if (params.experience?.length) {
        newParams.set("experience", params.experience.join(","));
      } else {
        newParams.delete("experience");
      }
    }
    if (params.salary !== void 0) {
      if (params.salary) {
        newParams.set("salary", params.salary);
      } else {
        newParams.delete("salary");
      }
    }
    if (params.datePosted !== void 0) {
      if (params.datePosted) {
        newParams.set("datePosted", params.datePosted);
      } else {
        newParams.delete("datePosted");
      }
    }
    if (params.easyApply !== void 0) {
      if (params.easyApply === true) {
        newParams.set("easyApply", "true");
      } else {
        newParams.delete("easyApply");
      }
    }
    if (params.companies !== void 0) {
      if (params.companies?.length) {
        const companyValues = params.companies.map((company) => {
          if (typeof company === "string") {
            return company;
          } else if (company.id) {
            return company.id;
          }
          return company;
        });
        console.log("Formatted company values:", companyValues);
        newParams.set("companies", companyValues.join(","));
      } else {
        newParams.delete("companies");
      }
    }
    if (params.state !== void 0) {
      if (params.state) {
        newParams.set("state", params.state);
      } else {
        newParams.delete("state");
      }
    }
    if (params.country !== void 0) {
      if (params.country) {
        newParams.set("country", params.country);
      } else {
        newParams.delete("country");
      }
    }
    const newUrl = `${url.origin}${url.pathname}?${newParams.toString()}`;
    console.log("Updated URL:", newUrl);
    window.history.replaceState({}, "", newUrl);
  }
  if (searchParams && Object.values(searchParams).some((val) => val && (!Array.isArray(val) || val.length > 0))) {
    if (typeof window !== "undefined") {
      updateUrlFromParams(searchParams);
    }
  }
  let $$settled = true;
  let $$inner_payload;
  function $$render_inner($$payload2) {
    SEO($$payload2, {
      title: "Job Search | Hirli",
      description: "Search for jobs that match your skills and experience. Find remote, hybrid, and on-site opportunities across various industries.",
      keywords: "job search, job listings, career opportunities, remote jobs, job board"
    });
    $$payload2.out += `<!----> `;
    JobSearch($$payload2, {
      onSearch: handleSearch,
      isSearching,
      initialParams: searchParams,
      user: data.user
    });
    $$payload2.out += `<!----> `;
    JobFeed($$payload2, {
      jobs,
      isAuthenticated: !!data.user,
      isLoading,
      onLoadMore: loadMore,
      onApply: handleApply,
      onSave: handleSave,
      onSignInRequired: handleSignInRequired,
      selectedJob,
      onSelectJob: handleJobSelect,
      searchParams,
      totalJobCount: totalCount,
      onFilterChange: handleSearch
    });
    $$payload2.out += `<!----> `;
    if (!data.user && searchTimeRemaining > 0) {
      $$payload2.out += "<!--[-->";
      $$payload2.out += `<div class="mt-4 rounded-lg bg-yellow-50 p-4 text-center text-sm text-yellow-800"><p>You can search again in ${escape_html(searchTimeRemaining)} seconds. <a href="/auth/sign-in" class="font-medium text-blue-600 hover:underline">Sign in</a> for unlimited
      searches.</p></div>`;
    } else {
      $$payload2.out += "<!--[!-->";
    }
    $$payload2.out += `<!--]--> `;
    Root($$payload2, {
      get open() {
        return showAuthDialog;
      },
      set open($$value) {
        showAuthDialog = $$value;
        $$settled = false;
      },
      children: ($$payload3) => {
        Dialog_content($$payload3, {
          children: ($$payload4) => {
            Dialog_header($$payload4, {
              children: ($$payload5) => {
                Dialog_title($$payload5, {
                  children: ($$payload6) => {
                    $$payload6.out += `<!---->Sign in required`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!----> `;
                Dialog_description($$payload5, {
                  children: ($$payload6) => {
                    if (authAction === "apply") {
                      $$payload6.out += "<!--[-->";
                      $$payload6.out += `You need to sign in to apply for jobs and track your applications.`;
                    } else if (authAction === "save") {
                      $$payload6.out += "<!--[1-->";
                      $$payload6.out += `You need to sign in to save jobs to your profile.`;
                    } else {
                      $$payload6.out += "<!--[!-->";
                      $$payload6.out += `You need to sign in to access this feature.`;
                    }
                    $$payload6.out += `<!--]-->`;
                  },
                  $$slots: { default: true }
                });
                $$payload5.out += `<!---->`;
              },
              $$slots: { default: true }
            });
            $$payload4.out += `<!----> <div class="flex justify-end gap-4"><button class="border-input bg-background ring-offset-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Cancel</button> <button class="bg-primary text-primary-foreground ring-offset-background hover:bg-primary/90 focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Sign In</button> <button class="border-input bg-background ring-offset-background hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring inline-flex h-10 items-center justify-center rounded-md border px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">Sign Up</button></div>`;
          },
          $$slots: { default: true }
        });
      },
      $$slots: { default: true }
    });
    $$payload2.out += `<!---->`;
  }
  do {
    $$settled = true;
    $$inner_payload = copy_payload($$payload);
    $$render_inner($$inner_payload);
  } while (!$$settled);
  assign_payload($$payload, $$inner_payload);
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-JqlQARAj.js.map
