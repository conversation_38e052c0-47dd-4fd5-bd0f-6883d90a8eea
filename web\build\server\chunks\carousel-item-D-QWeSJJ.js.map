{"version": 3, "file": "carousel-item-D-QWeSJJ.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/carousel-item.js"], "sourcesContent": ["import { x as setContext, a6 as hasContext, a7 as getContext, M as spread_attributes, T as clsx, N as bind_props, y as pop, w as push } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nconst EMBLA_CAROUSEL_CONTEXT = Symbol(\"EMBLA_CAROUSEL_CONTEXT\");\nfunction setEmblaContext(config) {\n  setContext(EMBLA_CAROUSEL_CONTEXT, config);\n  return config;\n}\nfunction getEmblaContext(name = \"This component\") {\n  if (!hasContext(EMBLA_CAROUSEL_CONTEXT)) {\n    throw new Error(`${name} must be used within a <Carousel.Root> component`);\n  }\n  return getContext(EMBLA_CAROUSEL_CONTEXT);\n}\nfunction Carousel($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    opts = {},\n    plugins = [],\n    setApi = () => {\n    },\n    orientation = \"horizontal\",\n    class: className,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let carouselState = {\n    api: void 0,\n    scrollPrev,\n    scrollNext,\n    orientation,\n    canScrollNext: false,\n    canScrollPrev: false,\n    handleKeyDown,\n    options: opts,\n    plugins,\n    onInit,\n    scrollSnaps: [],\n    selectedIndex: 0,\n    scrollTo\n  };\n  setEmblaContext(carouselState);\n  function scrollPrev() {\n    carouselState.api?.scrollPrev();\n  }\n  function scrollNext() {\n    carouselState.api?.scrollNext();\n  }\n  function scrollTo(index, jump) {\n    carouselState.api?.scrollTo(index, jump);\n  }\n  function handleKeyDown(e) {\n    if (e.key === \"ArrowLeft\") {\n      e.preventDefault();\n      scrollPrev();\n    } else if (e.key === \"ArrowRight\") {\n      e.preventDefault();\n      scrollNext();\n    }\n  }\n  function onInit(event) {\n    carouselState.api = event.detail;\n    carouselState.scrollSnaps = carouselState.api.scrollSnapList();\n  }\n  $$payload.out += `<div${spread_attributes(\n    {\n      \"data-slot\": \"carousel\",\n      class: clsx(cn(\"relative\", className)),\n      role: \"region\",\n      \"aria-roledescription\": \"carousel\",\n      ...restProps\n    },\n    null\n  )}>`;\n  children?.($$payload);\n  $$payload.out += `<!----></div>`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Carousel_content($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const emblaCtx = getEmblaContext(\"<Carousel.Content/>\");\n  $$payload.out += `<div data-slot=\"carousel-content\" class=\"overflow-hidden\"><div${spread_attributes(\n    {\n      class: clsx(cn(\"flex\", emblaCtx.orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\", className)),\n      \"data-embla-container\": \"\",\n      ...restProps\n    },\n    null\n  )}>`;\n  children?.($$payload);\n  $$payload.out += `<!----></div></div>`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Carousel_item($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const emblaCtx = getEmblaContext(\"<Carousel.Item/>\");\n  $$payload.out += `<div${spread_attributes(\n    {\n      \"data-slot\": \"carousel-item\",\n      role: \"group\",\n      \"aria-roledescription\": \"slide\",\n      class: clsx(cn(\"min-w-0 shrink-0 grow-0 basis-full\", emblaCtx.orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\", className)),\n      \"data-embla-slide\": \"\",\n      ...restProps\n    },\n    null\n  )}>`;\n  children?.($$payload);\n  $$payload.out += `<!----></div>`;\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Carousel as C,\n  Carousel_content as a,\n  Carousel_item as b\n};\n"], "names": [], "mappings": ";;;AAEA,MAAM,sBAAsB,GAAG,MAAM,CAAC,wBAAwB,CAAC;AAC/D,SAAS,eAAe,CAAC,MAAM,EAAE;AACjC,EAAE,UAAU,CAAC,sBAAsB,EAAE,MAAM,CAAC;AAC5C,EAAE,OAAO,MAAM;AACf;AACA,SAAS,eAAe,CAAC,IAAI,GAAG,gBAAgB,EAAE;AAClD,EAAE,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE;AAC3C,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,gDAAgD,CAAC,CAAC;AAC9E;AACA,EAAE,OAAO,UAAU,CAAC,sBAAsB,CAAC;AAC3C;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,IAAI,GAAG,EAAE;AACb,IAAI,OAAO,GAAG,EAAE;AAChB,IAAI,MAAM,GAAG,MAAM;AACnB,KAAK;AACL,IAAI,WAAW,GAAG,YAAY;AAC9B,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,GAAG,EAAE,MAAM;AACf,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,aAAa,EAAE,KAAK;AACxB,IAAI,aAAa,EAAE,KAAK;AACxB,IAAI,aAAa;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,WAAW,EAAE,EAAE;AACnB,IAAI,aAAa,EAAE,CAAC;AACpB,IAAI;AACJ,GAAG;AACH,EAAE,eAAe,CAAC,aAAa,CAAC;AAChC,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,aAAa,CAAC,GAAG,EAAE,UAAU,EAAE;AACnC;AACA,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,aAAa,CAAC,GAAG,EAAE,UAAU,EAAE;AACnC;AACA,EAAE,SAAS,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE;AACjC,IAAI,aAAa,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC;AAC5C;AACA,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE;AAC5B,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,WAAW,EAAE;AAC/B,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,UAAU,EAAE;AAClB,KAAK,MAAM,IAAI,CAAC,CAAC,GAAG,KAAK,YAAY,EAAE;AACvC,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,UAAU,EAAE;AAClB;AACA;AACA,EAAE,SAAS,MAAM,CAAC,KAAK,EAAE;AACzB,IAAI,aAAa,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM;AACpC,IAAI,aAAa,CAAC,WAAW,GAAG,aAAa,CAAC,GAAG,CAAC,cAAc,EAAE;AAClE;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;AAC5C,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,sBAAsB,EAAE,UAAU;AACxC,MAAM,GAAG;AACT,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,QAAQ,GAAG,eAAe,CAAC,qBAAqB,CAAC;AACzD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8DAA8D,EAAE,iBAAiB;AACrG,IAAI;AACJ,MAAM,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,QAAQ,CAAC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,gBAAgB,EAAE,SAAS,CAAC,CAAC;AAC5G,MAAM,sBAAsB,EAAE,EAAE;AAChC,MAAM,GAAG;AACT,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,QAAQ,GAAG,eAAe,CAAC,kBAAkB,CAAC;AACtD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,WAAW,EAAE,eAAe;AAClC,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,sBAAsB,EAAE,OAAO;AACrC,MAAM,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,oCAAoC,EAAE,QAAQ,CAAC,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,MAAM,EAAE,SAAS,CAAC,CAAC;AAC/H,MAAM,kBAAkB,EAAE,EAAE;AAC5B,MAAM,GAAG;AACT,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAClC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}