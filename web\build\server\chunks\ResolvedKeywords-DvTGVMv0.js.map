{"version": 3, "file": "ResolvedKeywords-DvTGVMv0.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/ResolvedKeywords.js"], "sourcesContent": ["import \"clsx\";\nimport { y as pop, w as push } from \"./index3.js\";\nfunction ResolvedKeywords($$payload, $$props) {\n  push();\n  let { keywordIds, fallback = \"None specified\" } = $$props;\n  {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<span class=\"text-gray-400\">Loading...</span>`;\n  }\n  $$payload.out += `<!--]-->`;\n  pop();\n}\nexport {\n  ResolvedKeywords as R\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,GAAG,gBAAgB,EAAE,GAAG,OAAO;AAC3D,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,6CAA6C,CAAC;AACpE;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP;;;;"}