// web/src/lib/utils/r2DocumentUpload.ts
// Web-specific R2 document upload utility

import {
  S3Client,
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  CopyObjectCommand,
} from '@aws-sdk/client-s3';

// Simple logger for web environment
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`[WARN] ${message}`, ...args),
};

// R2 Configuration
const R2_CONFIG = {
  endpoint:
    process.env.R2_ENDPOINT || 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
  region: 'auto', // R2 uses 'auto' as region
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID ?? '',
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY ?? '',
  },
};

// Initialize R2 client
const r2Client = new S3Client(R2_CONFIG);

// Fixed bucket names for different purposes
export enum BucketType {
  COMPANY = 'company',
  RESUMES = 'resumes',
  USER = 'user',
  JOBS = 'jobs',
  ANALYTICS = 'analytics',
}

// Bucket name mapping
const BUCKET_NAMES = {
  [BucketType.COMPANY]: 'hirli-company-logos',
  [BucketType.RESUMES]: 'hirli-resume-files',
  [BucketType.USER]: 'hirli-user-images',
  [BucketType.JOBS]: 'hirli-job-assets',
  [BucketType.ANALYTICS]: 'hirli-analytics-data',
};

export type FileType = 'resumes' | 'userDocuments' | 'companyLogos';

export interface R2UploadResult {
  success: boolean;
  originalFileName: string;
  filename: string;
  filePath: string; // R2 file key
  publicPath: string; // R2 public URL
  fileSize: number;
  contentType: string;
  error?: string;
}

export interface UploadResult {
  success: boolean;
  fileKey?: string;
  publicUrl?: string;
  error?: string;
  fileSize?: number;
  contentType?: string;
  bucketName?: string;
}

/**
 * Get bucket name for a bucket type
 */
function getBucketName(bucketType: BucketType): string {
  return BUCKET_NAMES[bucketType];
}

/**
 * Generate a unique file key for R2 storage
 */
function generateFileKey(fileType: FileType, originalName: string, identifier?: string): string {
  const timestamp = Date.now();
  const randomSuffix = Math.random().toString(36).substring(2, 8);
  const extension = originalName.split('.').pop() || '';
  const baseName = originalName
    .replace(/\.[^/.]+$/, '')
    .replace(/[^a-zA-Z0-9]/g, '-')
    .toLowerCase();

  if (fileType === 'resumes' && identifier) {
    return `profile-${identifier}-resume-${timestamp}.${extension}`;
  }

  return `${baseName}-${timestamp}-${randomSuffix}.${extension}`;
}

/**
 * Get public URL for a file - Use worker URL for consistent access
 */
export function getPublicUrl(fileKey: string): string {
  const customDomain = process.env.R2_CUSTOM_DOMAIN;
  if (customDomain) {
    return `https://${customDomain}/${fileKey}`;
  }

  // Use Cloudflare Worker URL for consistent access without hardcoded account IDs
  const workerUrl =
    process.env.R2_WORKER_URL ||
    'https://hirli-static-assets.christopher-eugene-rodriguez.workers.dev';

  // Determine the path prefix based on file type
  let pathPrefix = '';
  if (fileKey.includes('resume')) {
    pathPrefix = 'resumes/';
  } else if (fileKey.includes('logo')) {
    pathPrefix = 'logos/';
  } else {
    pathPrefix = 'user/';
  }

  return `${workerUrl}/${pathPrefix}${fileKey}`;
}

/**
 * Upload a file to R2 storage
 */
export async function uploadFile(
  buffer: Buffer,
  originalName: string,
  contentType: string,
  fileType: FileType,
  identifier?: string
): Promise<UploadResult> {
  try {
    // Determine bucket type based on file type
    let bucketType: BucketType;
    switch (fileType) {
      case 'resumes':
        bucketType = BucketType.RESUMES;
        break;
      case 'companyLogos':
        bucketType = BucketType.COMPANY;
        break;
      case 'userDocuments':
      default:
        bucketType = BucketType.USER;
        break;
    }

    const bucketName = getBucketName(bucketType);
    const fileKey = generateFileKey(fileType, originalName, identifier);

    logger.info(`📤 Uploading to bucket: ${bucketName} with key: ${fileKey}`);

    // Prepare upload command
    const uploadCommand = new PutObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
      Body: buffer,
      ContentType: contentType,
      Metadata: {
        originalName,
        uploadedAt: new Date().toISOString(),
        fileType,
        bucketType,
        ...(identifier && { identifier }),
      },
    });

    // Upload to R2
    await r2Client.send(uploadCommand);

    const publicUrl = getPublicUrl(fileKey);

    logger.info(
      `✅ File uploaded successfully to ${bucketName}: ${fileKey} (${buffer.length} bytes)`
    );

    return {
      success: true,
      fileKey,
      publicUrl,
      fileSize: buffer.length,
      contentType,
      bucketName,
    };
  } catch (error) {
    logger.error(`❌ Failed to upload file ${originalName}:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown upload error',
    };
  }
}

/**
 * Document type to R2 file type mapping
 */
const DOCUMENT_TYPE_TO_R2_TYPE: Record<string, FileType> = {
  resume: 'resumes',
  'cover-letter': 'userDocuments',
  portfolio: 'userDocuments',
  transcript: 'userDocuments',
  certificate: 'userDocuments',
  default: 'userDocuments',
};

/**
 * Upload a document to Cloudflare R2
 */
export async function uploadDocumentToR2(
  file: File,
  documentType: string = 'default',
  identifier?: string
): Promise<R2UploadResult> {
  try {
    logger.info(`📤 Uploading document to R2: ${file.name} (type: ${documentType})`);

    // Validate file
    if (!file || file.size === 0) {
      throw new Error('No file provided or file is empty');
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      throw new Error(`File size ${file.size} exceeds maximum allowed size of ${maxSize} bytes`);
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];

    if (!allowedTypes.includes(file.type)) {
      throw new Error(
        `File type ${file.type} is not supported. Please upload a PDF or Word document.`
      );
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());

    // Get R2 file type
    const r2FileType = DOCUMENT_TYPE_TO_R2_TYPE[documentType] || DOCUMENT_TYPE_TO_R2_TYPE.default;

    // Upload to R2
    const uploadResult = await uploadFile(buffer, file.name, file.type, r2FileType, identifier);

    if (!uploadResult.success) {
      throw new Error(uploadResult.error || 'Upload failed');
    }

    logger.info(`✅ Document uploaded successfully to R2: ${uploadResult.publicUrl}`);

    return {
      success: true,
      originalFileName: file.name,
      filename: uploadResult.fileKey?.split('/').pop() || file.name,
      filePath: uploadResult.fileKey!, // R2 file key
      publicPath: uploadResult.publicUrl!, // R2 public URL
      fileSize: uploadResult.fileSize!,
      contentType: uploadResult.contentType!,
    };
  } catch (error) {
    logger.error(`❌ Failed to upload document to R2:`, error);

    return {
      success: false,
      originalFileName: file.name,
      filename: '',
      filePath: '',
      publicPath: '',
      fileSize: 0,
      contentType: file.type,
      error: error instanceof Error ? error.message : 'Unknown upload error',
    };
  }
}

/**
 * Download a document from R2
 */
export async function downloadDocumentFromR2(
  fileKey: string,
  bucketType?: 'resumes' | 'userDocuments'
): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  try {
    logger.info(`📥 Downloading document from R2: ${fileKey}`);

    // Determine bucket type
    const r2BucketType = bucketType === 'resumes' ? BucketType.RESUMES : BucketType.USER;
    const bucketName = getBucketName(r2BucketType);

    const downloadCommand = new GetObjectCommand({
      Bucket: bucketName,
      Key: fileKey,
    });

    const response = await r2Client.send(downloadCommand);

    if (!response.Body) {
      return {
        success: false,
        error: 'No file content received',
      };
    }

    // Convert stream to buffer
    const chunks: Uint8Array[] = [];
    const reader = response.Body.transformToWebStream().getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }

    const buffer = Buffer.concat(chunks);

    logger.info(`✅ Document downloaded successfully from R2: ${fileKey}`);

    return {
      success: true,
      buffer,
    };
  } catch (error) {
    logger.error(`❌ Failed to download document from R2:`, error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown download error',
    };
  }
}

/**
 * Check if a document exists in R2
 */
export async function checkDocumentExistsInR2(
  fileKey: string,
  bucketType?: 'resumes' | 'userDocuments'
): Promise<boolean> {
  try {
    const downloadResult = await downloadDocumentFromR2(fileKey, bucketType);
    return downloadResult.success;
  } catch {
    return false;
  }
}

/**
 * Get the public URL for a document in R2
 */
export function getDocumentPublicUrl(
  fileKey: string,
  bucketType: 'resumes' | 'userDocuments' = 'userDocuments'
): string {
  return getPublicUrl(fileKey);
}

/**
 * Update resume file key with actual resume ID (for proper database association)
 */
export async function updateResumeFileKey(
  oldFileKey: string,
  resumeId: string
): Promise<{ success: boolean; newFileKey?: string; newPublicUrl?: string; error?: string }> {
  try {
    logger.info(`🔄 Updating resume file key: ${oldFileKey} -> resume-${resumeId}`);

    const bucketName = getBucketName(BucketType.RESUMES);

    // Extract extension from old file key
    const extension = oldFileKey.split('.').pop() || 'pdf';
    const newFileKey = `profile-resume-${resumeId}.${extension}`;

    // Copy object to new key
    const copyCommand = new CopyObjectCommand({
      Bucket: bucketName,
      Key: newFileKey,
      CopySource: `${bucketName}/${oldFileKey}`,
      MetadataDirective: 'COPY',
    });

    await r2Client.send(copyCommand);

    // Delete old object
    const deleteCommand = new DeleteObjectCommand({
      Bucket: bucketName,
      Key: oldFileKey,
    });

    await r2Client.send(deleteCommand);

    const newPublicUrl = getPublicUrl(newFileKey);

    logger.info(`✅ Resume file key updated successfully: ${newFileKey}`);

    return {
      success: true,
      newFileKey,
      newPublicUrl,
    };
  } catch (error) {
    logger.error(`❌ Failed to update resume file key:`, error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Migrate a local file to R2 (utility function)
 */
export async function migrateLocalFileToR2(
  localFilePath: string,
  originalFileName: string,
  contentType: string,
  documentType: string,
  userId?: string
): Promise<R2UploadResult> {
  try {
    logger.info(`🔄 Migrating local file to R2: ${localFilePath}`);

    // Read the local file
    const fs = await import('fs/promises');
    const buffer = await fs.readFile(localFilePath);

    // Create a mock File object
    const file = new File([buffer], originalFileName, { type: contentType });

    // Upload to R2
    return await uploadDocumentToR2(file, documentType, userId);
  } catch (error) {
    logger.error(`❌ Failed to migrate local file to R2:`, error);

    return {
      success: false,
      originalFileName,
      filename: '',
      filePath: '',
      publicPath: '',
      fileSize: 0,
      contentType,
      error: error instanceof Error ? error.message : 'Migration failed',
    };
  }
}

export default uploadDocumentToR2;
