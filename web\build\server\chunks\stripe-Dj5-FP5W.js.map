{"version": 3, "file": "stripe-Dj5-FP5W.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/stripe.js"], "sourcesContent": ["import Stripe from \"stripe\";\nconst isProd = process.env.NODE_ENV === \"production\";\nconst stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || \"sk_live_placeholder\" : process.env.STRIPE_SECRET_KEY_TEST || \"sk_test_placeholder\";\nconst stripe = new Stripe(stripeSecret, {\n  apiVersion: \"2025-04-30.basil\"\n});\nasync function syncPlanProduct(plan) {\n  try {\n    const existingProducts = await stripe.products.list({\n      active: true,\n      expand: [\"data.default_price\"]\n    });\n    const existingProduct = existingProducts.data.find(\n      (product) => product.metadata.plan_id === plan.id\n    );\n    if (existingProduct) {\n      return await stripe.products.update(existingProduct.id, {\n        name: plan.name,\n        description: plan.description || void 0,\n        metadata: {\n          plan_id: plan.id,\n          section: plan.section\n        }\n      });\n    } else {\n      return await stripe.products.create({\n        name: plan.name,\n        description: plan.description || void 0,\n        metadata: {\n          plan_id: plan.id,\n          section: plan.section\n        }\n      });\n    }\n  } catch (error) {\n    console.error(\"Error syncing plan product:\", error);\n    throw new Error(`Failed to sync plan product: ${error.message}`);\n  }\n}\nasync function syncPlanPrices(plan, productId) {\n  try {\n    const existingPrices = await stripe.prices.list({\n      product: productId,\n      active: true\n    });\n    const existingMonthlyPrice = existingPrices.data.find(\n      (price) => price.recurring?.interval === \"month\"\n    );\n    const existingYearlyPrice = existingPrices.data.find(\n      (price) => price.recurring?.interval === \"year\"\n    );\n    let monthlyPrice;\n    if (existingMonthlyPrice && existingMonthlyPrice.unit_amount !== plan.monthlyPrice) {\n      await stripe.prices.update(existingMonthlyPrice.id, { active: false });\n      monthlyPrice = await stripe.prices.create({\n        product: productId,\n        unit_amount: plan.monthlyPrice,\n        currency: \"usd\",\n        recurring: { interval: \"month\" },\n        metadata: {\n          plan_id: plan.id\n        }\n      });\n    } else if (!existingMonthlyPrice) {\n      monthlyPrice = await stripe.prices.create({\n        product: productId,\n        unit_amount: plan.monthlyPrice,\n        currency: \"usd\",\n        recurring: { interval: \"month\" },\n        metadata: {\n          plan_id: plan.id\n        }\n      });\n    } else {\n      monthlyPrice = existingMonthlyPrice;\n    }\n    let yearlyPrice;\n    if (existingYearlyPrice && existingYearlyPrice.unit_amount !== plan.annualPrice) {\n      await stripe.prices.update(existingYearlyPrice.id, { active: false });\n      yearlyPrice = await stripe.prices.create({\n        product: productId,\n        unit_amount: plan.annualPrice,\n        currency: \"usd\",\n        recurring: { interval: \"year\" },\n        metadata: {\n          plan_id: plan.id\n        }\n      });\n    } else if (!existingYearlyPrice) {\n      yearlyPrice = await stripe.prices.create({\n        product: productId,\n        unit_amount: plan.annualPrice,\n        currency: \"usd\",\n        recurring: { interval: \"year\" },\n        metadata: {\n          plan_id: plan.id\n        }\n      });\n    } else {\n      yearlyPrice = existingYearlyPrice;\n    }\n    return {\n      monthlyPriceId: monthlyPrice.id,\n      yearlyPriceId: yearlyPrice.id\n    };\n  } catch (error) {\n    console.error(\"Error syncing plan prices:\", error);\n    throw new Error(`Failed to sync plan prices: ${error.message}`);\n  }\n}\nasync function syncPlanWithStripe(plan) {\n  try {\n    const product = await syncPlanProduct(plan);\n    const { monthlyPriceId, yearlyPriceId } = await syncPlanPrices(plan, product.id);\n    return {\n      ...plan,\n      stripePriceMonthlyId: monthlyPriceId,\n      stripePriceYearlyId: yearlyPriceId\n    };\n  } catch (error) {\n    console.error(\"Error syncing plan with Stripe:\", error);\n    throw new Error(`Failed to sync plan with Stripe: ${error.message}`);\n  }\n}\nexport {\n  syncPlanWithStripe as a,\n  stripe as s\n};\n"], "names": [], "mappings": ";;AACA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACpD,MAAM,YAAY,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,qBAAqB;AAClJ,MAAC,MAAM,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE;AACxC,EAAE,UAAU,EAAE;AACd,CAAC;AACD,eAAe,eAAe,CAAC,IAAI,EAAE;AACrC,EAAE,IAAI;AACN,IAAI,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;AACxD,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,MAAM,EAAE,CAAC,oBAAoB;AACnC,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI;AACtD,MAAM,CAAC,OAAO,KAAK,OAAO,CAAC,QAAQ,CAAC,OAAO,KAAK,IAAI,CAAC;AACrD,KAAK;AACL,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,OAAO,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,EAAE;AAC9D,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC;AAC/C,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE,IAAI,CAAC,EAAE;AAC1B,UAAU,OAAO,EAAE,IAAI,CAAC;AACxB;AACA,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,OAAO,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC1C,QAAQ,IAAI,EAAE,IAAI,CAAC,IAAI;AACvB,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC;AAC/C,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE,IAAI,CAAC,EAAE;AAC1B,UAAU,OAAO,EAAE,IAAI,CAAC;AACxB;AACA,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC;AACvD,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACpE;AACA;AACA,eAAe,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE;AAC/C,EAAE,IAAI;AACN,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;AACpD,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,MAAM,EAAE;AACd,KAAK,CAAC;AACN,IAAI,MAAM,oBAAoB,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI;AACzD,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,EAAE,QAAQ,KAAK;AAC/C,KAAK;AACL,IAAI,MAAM,mBAAmB,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI;AACxD,MAAM,CAAC,KAAK,KAAK,KAAK,CAAC,SAAS,EAAE,QAAQ,KAAK;AAC/C,KAAK;AACL,IAAI,IAAI,YAAY;AACpB,IAAI,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,WAAW,KAAK,IAAI,CAAC,YAAY,EAAE;AACxF,MAAM,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC5E,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAChD,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,WAAW,EAAE,IAAI,CAAC,YAAY;AACtC,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,SAAS,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;AACxC,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE,IAAI,CAAC;AACxB;AACA,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,CAAC,oBAAoB,EAAE;AACtC,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAChD,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,WAAW,EAAE,IAAI,CAAC,YAAY;AACtC,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,SAAS,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE;AACxC,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE,IAAI,CAAC;AACxB;AACA,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,YAAY,GAAG,oBAAoB;AACzC;AACA,IAAI,IAAI,WAAW;AACnB,IAAI,IAAI,mBAAmB,IAAI,mBAAmB,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE;AACrF,MAAM,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;AAC3E,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC/C,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW;AACrC,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,SAAS,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;AACvC,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE,IAAI,CAAC;AACxB;AACA,OAAO,CAAC;AACR,KAAK,MAAM,IAAI,CAAC,mBAAmB,EAAE;AACrC,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;AAC/C,QAAQ,OAAO,EAAE,SAAS;AAC1B,QAAQ,WAAW,EAAE,IAAI,CAAC,WAAW;AACrC,QAAQ,QAAQ,EAAE,KAAK;AACvB,QAAQ,SAAS,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE;AACvC,QAAQ,QAAQ,EAAE;AAClB,UAAU,OAAO,EAAE,IAAI,CAAC;AACxB;AACA,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,WAAW,GAAG,mBAAmB;AACvC;AACA,IAAI,OAAO;AACX,MAAM,cAAc,EAAE,YAAY,CAAC,EAAE;AACrC,MAAM,aAAa,EAAE,WAAW,CAAC;AACjC,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACtD,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACnE;AACA;AACA,eAAe,kBAAkB,CAAC,IAAI,EAAE;AACxC,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,IAAI,CAAC;AAC/C,IAAI,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,MAAM,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;AACpF,IAAI,OAAO;AACX,MAAM,GAAG,IAAI;AACb,MAAM,oBAAoB,EAAE,cAAc;AAC1C,MAAM,mBAAmB,EAAE;AAC3B,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC;AAC3D,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,iCAAiC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACxE;AACA;;;;"}