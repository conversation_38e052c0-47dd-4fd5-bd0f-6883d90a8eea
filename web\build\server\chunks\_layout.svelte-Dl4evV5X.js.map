{"version": 3, "file": "_layout.svelte-Dl4evV5X.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/_layout.svelte.js"], "sourcesContent": ["import { J as derived, M as spread_attributes, N as bind_props, y as pop, w as push, Q as spread_props, O as copy_payload, P as assign_payload, _ as store_get, U as ensure_array_like, S as attr_class, T as clsx, R as attr, V as escape_html, a0 as slot, a1 as unsubscribe_stores } from \"../../../../chunks/index3.js\";\nimport { g as getStores } from \"../../../../chunks/stores.js\";\nimport { o as onDestroy } from \"../../../../chunks/index-server.js\";\nimport \"../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport { w as watch, b as box } from \"../../../../chunks/watch.svelte.js\";\nimport \"style-to-object\";\nimport \"clsx\";\nimport { u as useRefById, m as mergeProps } from \"../../../../chunks/use-ref-by-id.svelte.js\";\nimport { a as afterTick } from \"../../../../chunks/after-tick.js\";\nimport { s as snapshot } from \"../../../../chunks/clone.js\";\nimport { C as Context } from \"../../../../chunks/context.js\";\nimport { c as cn } from \"../../../../chunks/utils.js\";\nimport { I as Icon } from \"../../../../chunks/Icon2.js\";\nimport { S as Settings_2, C as Chart_column_stacked } from \"../../../../chunks/settings-2.js\";\nimport { F as File_text } from \"../../../../chunks/file-text.js\";\nimport { A as Activity } from \"../../../../chunks/activity.js\";\nimport { B as Bot } from \"../../../../chunks/bot.js\";\nimport { U as User } from \"../../../../chunks/user.js\";\nimport { S as Shield } from \"../../../../chunks/shield.js\";\nimport { C as Credit_card } from \"../../../../chunks/credit-card.js\";\nimport { B as Bell } from \"../../../../chunks/bell.js\";\nimport { S as Share_2 } from \"../../../../chunks/share-2.js\";\nimport { U as Users } from \"../../../../chunks/users.js\";\nimport { L as Log_out } from \"../../../../chunks/log-out.js\";\nfunction addEventListener(target, event, handler, options) {\n  const events = Array.isArray(event) ? event : [event];\n  events.forEach((_event) => target.addEventListener(_event, handler, options));\n  return () => {\n    events.forEach((_event) => target.removeEventListener(_event, handler, options));\n  };\n}\nfunction calculateAriaValues({ layout, panesArray, pivotIndices }) {\n  let currentMinSize = 0;\n  let currentMaxSize = 100;\n  let totalMinSize = 0;\n  let totalMaxSize = 0;\n  const firstIndex = pivotIndices[0];\n  for (let i = 0; i < panesArray.length; i++) {\n    const constraints = panesArray[i].constraints;\n    const { maxSize = 100, minSize = 0 } = constraints;\n    if (i === firstIndex) {\n      currentMinSize = minSize;\n      currentMaxSize = maxSize;\n    } else {\n      totalMinSize += minSize;\n      totalMaxSize += maxSize;\n    }\n  }\n  const valueMax = Math.min(currentMaxSize, 100 - totalMinSize);\n  const valueMin = Math.max(currentMinSize, 100 - totalMaxSize);\n  const valueNow = layout[firstIndex];\n  return {\n    valueMax,\n    valueMin,\n    valueNow\n  };\n}\nfunction assert(expectedCondition, message = \"Assertion failed!\") {\n  if (!expectedCondition) {\n    console.error(message);\n    throw new Error(message);\n  }\n}\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst PRECISION = 10;\nfunction areNumbersAlmostEqual(actual, expected, fractionDigits = PRECISION) {\n  return compareNumbersWithTolerance(actual, expected, fractionDigits) === 0;\n}\nfunction compareNumbersWithTolerance(actual, expected, fractionDigits = PRECISION) {\n  const roundedActual = roundTo(actual, fractionDigits);\n  const roundedExpected = roundTo(expected, fractionDigits);\n  return Math.sign(roundedActual - roundedExpected);\n}\nfunction areArraysEqual(arrA, arrB) {\n  if (arrA.length !== arrB.length)\n    return false;\n  for (let index = 0; index < arrA.length; index++) {\n    if (arrA[index] !== arrB[index])\n      return false;\n  }\n  return true;\n}\nfunction roundTo(value, decimals) {\n  return Number.parseFloat(value.toFixed(decimals));\n}\nconst isBrowser = typeof document !== \"undefined\";\nfunction isHTMLElement(element2) {\n  return element2 instanceof HTMLElement;\n}\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\nfunction isTouchEvent(event) {\n  return event.type.startsWith(\"touch\");\n}\nfunction resizePane({ paneConstraints: paneConstraintsArray, paneIndex, initialSize }) {\n  const paneConstraints = paneConstraintsArray[paneIndex];\n  assert(paneConstraints != null, \"Pane constraints should not be null.\");\n  const { collapsedSize = 0, collapsible, maxSize = 100, minSize = 0 } = paneConstraints;\n  let newSize = initialSize;\n  if (compareNumbersWithTolerance(newSize, minSize) < 0) {\n    newSize = getAdjustedSizeForCollapsible(newSize, collapsible, collapsedSize, minSize);\n  }\n  newSize = Math.min(maxSize, newSize);\n  return Number.parseFloat(newSize.toFixed(PRECISION));\n}\nfunction getAdjustedSizeForCollapsible(size, collapsible, collapsedSize, minSize) {\n  if (!collapsible)\n    return minSize;\n  const halfwayPoint = (collapsedSize + minSize) / 2;\n  return compareNumbersWithTolerance(size, halfwayPoint) < 0 ? collapsedSize : minSize;\n}\nfunction noop() {\n}\nfunction updateResizeHandleAriaValues({ groupId, layout, panesArray }) {\n  const resizeHandleElements = getResizeHandleElementsForGroup(groupId);\n  for (let index = 0; index < panesArray.length - 1; index++) {\n    const { valueMax, valueMin, valueNow } = calculateAriaValues({\n      layout,\n      panesArray,\n      pivotIndices: [index, index + 1]\n    });\n    const resizeHandleEl = resizeHandleElements[index];\n    if (isHTMLElement(resizeHandleEl)) {\n      const paneData = panesArray[index];\n      resizeHandleEl.setAttribute(\"aria-controls\", paneData.opts.id.current);\n      resizeHandleEl.setAttribute(\"aria-valuemax\", `${Math.round(valueMax)}`);\n      resizeHandleEl.setAttribute(\"aria-valuemin\", `${Math.round(valueMin)}`);\n      resizeHandleEl.setAttribute(\"aria-valuenow\", valueNow != null ? `${Math.round(valueNow)}` : \"\");\n    }\n  }\n  return () => {\n    resizeHandleElements.forEach((resizeHandleElement) => {\n      resizeHandleElement.removeAttribute(\"aria-controls\");\n      resizeHandleElement.removeAttribute(\"aria-valuemax\");\n      resizeHandleElement.removeAttribute(\"aria-valuemin\");\n      resizeHandleElement.removeAttribute(\"aria-valuenow\");\n    });\n  };\n}\nfunction getResizeHandleElementsForGroup(groupId) {\n  if (!isBrowser)\n    return [];\n  return Array.from(document.querySelectorAll(`[data-pane-resizer-id][data-pane-group-id=\"${groupId}\"]`));\n}\nfunction getResizeHandleElementIndex(groupId, id) {\n  if (!isBrowser)\n    return null;\n  const handles = getResizeHandleElementsForGroup(groupId);\n  const index = handles.findIndex((handle) => handle.getAttribute(\"data-pane-resizer-id\") === id);\n  return index ?? null;\n}\nfunction getPivotIndices(groupId, dragHandleId) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\nfunction paneDataHelper(panesArray, pane, layout) {\n  const paneConstraintsArray = panesArray.map((paneData) => paneData.constraints);\n  const paneIndex = findPaneDataIndex(panesArray, pane);\n  const paneConstraints = paneConstraintsArray[paneIndex];\n  const isLastPane = paneIndex === panesArray.length - 1;\n  const pivotIndices = isLastPane ? [paneIndex - 1, paneIndex] : [paneIndex, paneIndex + 1];\n  const paneSize = layout[paneIndex];\n  return {\n    ...paneConstraints,\n    paneSize,\n    pivotIndices\n  };\n}\nfunction findPaneDataIndex(panesArray, pane) {\n  return panesArray.findIndex((prevPaneData) => prevPaneData.opts.id.current === pane.opts.id.current);\n}\nfunction callPaneCallbacks(panesArray, layout, paneIdToLastNotifiedSizeMap) {\n  for (let index = 0; index < layout.length; index++) {\n    const size = layout[index];\n    const paneData = panesArray[index];\n    assert(paneData);\n    const { collapsedSize = 0, collapsible } = paneData.constraints;\n    const lastNotifiedSize = paneIdToLastNotifiedSizeMap[paneData.opts.id.current];\n    if (!(lastNotifiedSize == null || size !== lastNotifiedSize))\n      continue;\n    paneIdToLastNotifiedSizeMap[paneData.opts.id.current] = size;\n    const { onCollapse, onExpand, onResize } = paneData.callbacks;\n    onResize?.(size, lastNotifiedSize);\n    if (collapsible && (onCollapse || onExpand)) {\n      if (onExpand && (lastNotifiedSize == null || lastNotifiedSize === collapsedSize) && size !== collapsedSize) {\n        onExpand();\n      }\n      if (onCollapse && (lastNotifiedSize == null || lastNotifiedSize !== collapsedSize) && size === collapsedSize) {\n        onCollapse();\n      }\n    }\n  }\n}\nfunction getUnsafeDefaultLayout({ panesArray }) {\n  const layout = Array(panesArray.length);\n  const paneConstraintsArray = panesArray.map((paneData) => paneData.constraints);\n  let numPanesWithSizes = 0;\n  let remainingSize = 100;\n  for (let index = 0; index < panesArray.length; index++) {\n    const paneConstraints = paneConstraintsArray[index];\n    assert(paneConstraints);\n    const { defaultSize } = paneConstraints;\n    if (defaultSize != null) {\n      numPanesWithSizes++;\n      layout[index] = defaultSize;\n      remainingSize -= defaultSize;\n    }\n  }\n  for (let index = 0; index < panesArray.length; index++) {\n    const paneConstraints = paneConstraintsArray[index];\n    assert(paneConstraints);\n    const { defaultSize } = paneConstraints;\n    if (defaultSize != null) {\n      continue;\n    }\n    const numRemainingPanes = panesArray.length - numPanesWithSizes;\n    const size = remainingSize / numRemainingPanes;\n    numPanesWithSizes++;\n    layout[index] = size;\n    remainingSize -= size;\n  }\n  return layout;\n}\nfunction validatePaneGroupLayout({ layout: prevLayout, paneConstraints }) {\n  const nextLayout = [...prevLayout];\n  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n  if (nextLayout.length !== paneConstraints.length) {\n    throw new Error(`Invalid ${paneConstraints.length} pane layout: ${nextLayout.map((size) => `${size}%`).join(\", \")}`);\n  } else if (!areNumbersAlmostEqual(nextLayoutTotalSize, 100)) {\n    for (let index = 0; index < paneConstraints.length; index++) {\n      const unsafeSize = nextLayout[index];\n      assert(unsafeSize != null);\n      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  let remainingSize = 0;\n  for (let index = 0; index < paneConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    assert(unsafeSize != null);\n    const safeSize = resizePane({\n      paneConstraints,\n      paneIndex: index,\n      initialSize: unsafeSize\n    });\n    if (unsafeSize !== safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  if (!areNumbersAlmostEqual(remainingSize, 0)) {\n    for (let index = 0; index < paneConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      assert(prevSize != null);\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePane({\n        paneConstraints,\n        paneIndex: index,\n        initialSize: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n        if (areNumbersAlmostEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\nfunction getPaneGroupElement(id) {\n  if (!isBrowser)\n    return null;\n  const element2 = document.querySelector(`[data-pane-group][data-pane-group-id=\"${id}\"]`);\n  if (element2) {\n    return element2;\n  }\n  return null;\n}\nfunction getResizeHandleElement(id) {\n  if (!isBrowser)\n    return null;\n  const element2 = document.querySelector(`[data-pane-resizer-id=\"${id}\"]`);\n  if (element2) {\n    return element2;\n  }\n  return null;\n}\nfunction getDragOffsetPercentage(e, dragHandleId, dir, initialDragState) {\n  const isHorizontal = dir === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId);\n  assert(handleElement);\n  const groupId = handleElement.getAttribute(\"data-pane-group-id\");\n  assert(groupId);\n  const { initialCursorPosition } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(dir, e);\n  const groupElement = getPaneGroupElement(groupId);\n  assert(groupElement);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\nfunction getDeltaPercentage(e, dragHandleId, dir, initialDragState, keyboardResizeBy) {\n  if (isKeyDown(e)) {\n    const isHorizontal = dir === \"horizontal\";\n    let delta = 0;\n    if (e.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeBy != null) {\n      delta = keyboardResizeBy;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (e.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    if (initialDragState == null)\n      return 0;\n    return getDragOffsetPercentage(e, dragHandleId, dir, initialDragState);\n  }\n}\nfunction getResizeEventCursorPosition(dir, e) {\n  const isHorizontal = dir === \"horizontal\";\n  if (isMouseEvent(e)) {\n    return isHorizontal ? e.clientX : e.clientY;\n  } else if (isTouchEvent(e)) {\n    const firstTouch = e.touches[0];\n    assert(firstTouch);\n    return isHorizontal ? firstTouch.screenX : firstTouch.screenY;\n  } else {\n    throw new Error(`Unsupported event type \"${e.type}\"`);\n  }\n}\nfunction getResizeHandlePaneIds(groupId, handleId, panesArray) {\n  const handle = getResizeHandleElement(handleId);\n  const handles = getResizeHandleElementsForGroup(groupId);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = panesArray[index]?.opts.id.current ?? null;\n  const idAfter = panesArray[index + 1]?.opts.id.current ?? null;\n  return [idBefore, idAfter];\n}\nlet count = 0;\nfunction useId(prefix = \"paneforge\") {\n  count++;\n  return `${prefix}-${count}`;\n}\nfunction adjustLayoutByDelta({ delta, layout: prevLayout, paneConstraints: paneConstraintsArray, pivotIndices, trigger }) {\n  if (areNumbersAlmostEqual(delta, 0))\n    return prevLayout;\n  const nextLayout = [...prevLayout];\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n  let deltaApplied = 0;\n  {\n    if (trigger === \"keyboard\") {\n      {\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const paneConstraints = paneConstraintsArray[index];\n        assert(paneConstraints);\n        if (paneConstraints.collapsible) {\n          const prevSize = prevLayout[index];\n          assert(prevSize != null);\n          const paneConstraints2 = paneConstraintsArray[index];\n          assert(paneConstraints2);\n          const { collapsedSize = 0, minSize = 0 } = paneConstraints2;\n          if (areNumbersAlmostEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize;\n            if (compareNumbersWithTolerance(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n            }\n          }\n        }\n      }\n      {\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        const paneConstraints = paneConstraintsArray[index];\n        assert(paneConstraints);\n        const { collapsible } = paneConstraints;\n        if (collapsible) {\n          const prevSize = prevLayout[index];\n          assert(prevSize != null);\n          const paneConstraints2 = paneConstraintsArray[index];\n          assert(paneConstraints2);\n          const { collapsedSize = 0, minSize = 0 } = paneConstraints2;\n          if (areNumbersAlmostEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize;\n            if (compareNumbersWithTolerance(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n            }\n          }\n        }\n      }\n    }\n  }\n  {\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    let maxAvailableDelta = 0;\n    while (true) {\n      const prevSize = prevLayout[index];\n      assert(prevSize != null);\n      const maxSafeSize = resizePane({\n        paneConstraints: paneConstraintsArray,\n        paneIndex: index,\n        initialSize: 100\n      });\n      const delta2 = maxSafeSize - prevSize;\n      maxAvailableDelta += delta2;\n      index += increment;\n      if (index < 0 || index >= paneConstraintsArray.length) {\n        break;\n      }\n    }\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n  }\n  {\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n    let index = pivotIndex;\n    while (index >= 0 && index < paneConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = prevLayout[index];\n      assert(prevSize != null);\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePane({\n        paneConstraints: paneConstraintsArray,\n        paneIndex: index,\n        initialSize: unsafeSize\n      });\n      if (!areNumbersAlmostEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toPrecision(3).localeCompare(Math.abs(delta).toPrecision(3), void 0, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  if (areNumbersAlmostEqual(deltaApplied, 0)) {\n    return prevLayout;\n  }\n  {\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    const prevSize = prevLayout[pivotIndex];\n    assert(prevSize != null);\n    const unsafeSize = prevSize + deltaApplied;\n    const safeSize = resizePane({\n      paneConstraints: paneConstraintsArray,\n      paneIndex: pivotIndex,\n      initialSize: unsafeSize\n    });\n    nextLayout[pivotIndex] = safeSize;\n    if (!areNumbersAlmostEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex2 = delta < 0 ? secondPivotIndex : firstPivotIndex;\n      let index = pivotIndex2;\n      while (index >= 0 && index < paneConstraintsArray.length) {\n        const prevSize2 = nextLayout[index];\n        assert(prevSize2 != null);\n        const unsafeSize2 = prevSize2 + deltaRemaining;\n        const safeSize2 = resizePane({\n          paneConstraints: paneConstraintsArray,\n          paneIndex: index,\n          initialSize: unsafeSize2\n        });\n        if (!areNumbersAlmostEqual(prevSize2, safeSize2)) {\n          deltaRemaining -= safeSize2 - prevSize2;\n          nextLayout[index] = safeSize2;\n        }\n        if (areNumbersAlmostEqual(deltaRemaining, 0))\n          break;\n        delta > 0 ? index-- : index++;\n      }\n    }\n  }\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  if (!areNumbersAlmostEqual(totalSize, 100))\n    return prevLayout;\n  return nextLayout;\n}\nlet currentState = null;\nlet element = null;\nfunction getCursorStyle(state) {\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"horizontal-max\":\n      return \"w-resize\";\n    case \"horizontal-min\":\n      return \"e-resize\";\n    case \"vertical\":\n      return \"ns-resize\";\n    case \"vertical-max\":\n      return \"n-resize\";\n    case \"vertical-min\":\n      return \"s-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (element === null)\n    return;\n  document.head.removeChild(element);\n  currentState = null;\n  element = null;\n}\nfunction setGlobalCursorStyle(state) {\n  if (currentState === state)\n    return;\n  currentState = state;\n  const style = getCursorStyle(state);\n  if (element === null) {\n    element = document.createElement(\"style\");\n    document.head.appendChild(element);\n  }\n  element.innerHTML = `*{cursor: ${style}!important;}`;\n}\nfunction computePaneFlexBoxStyle({ defaultSize, dragState, layout, panesArray, paneIndex, precision = 3 }) {\n  const size = layout[paneIndex];\n  let flexGrow;\n  if (size == null) {\n    flexGrow = defaultSize ?? \"1\";\n  } else if (panesArray.length === 1) {\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toPrecision(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, pane sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a pane during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : void 0\n  };\n}\nfunction initializeStorage(storageObject) {\n  try {\n    if (typeof localStorage === \"undefined\") {\n      throw new TypeError(\"localStorage is not supported in this environment\");\n    }\n    storageObject.getItem = (name) => localStorage.getItem(name);\n    storageObject.setItem = (name, value) => localStorage.setItem(name, value);\n  } catch (err) {\n    console.error(err);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {\n    };\n  }\n}\nfunction getPaneGroupKey(autoSaveId) {\n  return `paneforge:${autoSaveId}`;\n}\nfunction getPaneKey(panes) {\n  const sortedPaneIds = panes.map((pane) => {\n    return pane.opts.order.current ? `${pane.opts.order.current}:${JSON.stringify(pane.constraints)}` : JSON.stringify(pane.constraints);\n  }).sort().join(\",\");\n  return sortedPaneIds;\n}\nfunction loadSerializedPaneGroupState(autoSaveId, storage) {\n  try {\n    const paneGroupKey = getPaneGroupKey(autoSaveId);\n    const serialized = storage.getItem(paneGroupKey);\n    const parsed = JSON.parse(serialized || \"\");\n    if (typeof parsed === \"object\" && parsed !== null) {\n      return parsed;\n    }\n  } catch {\n  }\n  return null;\n}\nfunction loadPaneGroupState(autoSaveId, panesArray, storage) {\n  const state = loadSerializedPaneGroupState(autoSaveId, storage) || {};\n  const paneKey = getPaneKey(panesArray);\n  return state[paneKey] || null;\n}\nfunction savePaneGroupState(autoSaveId, panesArray, paneSizesBeforeCollapse, sizes, storage) {\n  const paneGroupKey = getPaneGroupKey(autoSaveId);\n  const paneKey = getPaneKey(panesArray);\n  const state = loadSerializedPaneGroupState(autoSaveId, storage) || {};\n  state[paneKey] = {\n    expandToSizes: Object.fromEntries(paneSizesBeforeCollapse.entries()),\n    layout: sizes\n  };\n  try {\n    storage.setItem(paneGroupKey, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\nconst debounceMap = {};\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  const callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\nfunction updateStorageValues({ autoSaveId, layout, storage, panesArray, paneSizeBeforeCollapse }) {\n  if (layout.length === 0 || layout.length !== panesArray.length)\n    return;\n  let debouncedSave = debounceMap[autoSaveId];\n  if (debouncedSave == null) {\n    debouncedSave = debounce(savePaneGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n    debounceMap[autoSaveId] = debouncedSave;\n  }\n  const clonedPanesArray = [...panesArray];\n  const clonedPaneSizesBeforeCollapse = new Map(paneSizeBeforeCollapse);\n  debouncedSave(autoSaveId, clonedPanesArray, clonedPaneSizesBeforeCollapse, layout, storage);\n}\nconst defaultStorage = {\n  getItem: (name) => {\n    initializeStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nclass PaneGroupState {\n  opts;\n  dragState = null;\n  layout = [];\n  panesArray = [];\n  panesArrayChanged = false;\n  paneIdToLastNotifiedSizeMap = {};\n  paneSizeBeforeCollapseMap = /* @__PURE__ */ new Map();\n  prevDelta = 0;\n  constructor(opts) {\n    this.opts = opts;\n    useRefById(opts);\n    watch(\n      [\n        () => this.opts.id.current,\n        () => this.layout,\n        () => this.panesArray\n      ],\n      () => {\n        return updateResizeHandleAriaValues({\n          groupId: this.opts.id.current,\n          layout: this.layout,\n          panesArray: this.panesArray\n        });\n      }\n    );\n    watch(\n      [\n        () => this.opts.autoSaveId.current,\n        () => this.layout,\n        () => this.opts.storage.current\n      ],\n      () => {\n        if (!this.opts.autoSaveId.current) return;\n        updateStorageValues({\n          autoSaveId: this.opts.autoSaveId.current,\n          layout: this.layout,\n          storage: this.opts.storage.current,\n          panesArray: this.panesArray,\n          paneSizeBeforeCollapse: this.paneSizeBeforeCollapseMap\n        });\n      }\n    );\n    watch(() => this.panesArrayChanged, () => {\n      if (!this.panesArrayChanged) return;\n      this.panesArrayChanged = false;\n      const prevLayout = this.layout;\n      let unsafeLayout = null;\n      if (this.opts.autoSaveId.current) {\n        const state = loadPaneGroupState(this.opts.autoSaveId.current, this.panesArray, this.opts.storage.current);\n        if (state) {\n          this.paneSizeBeforeCollapseMap = new Map(Object.entries(state.expandToSizes));\n          unsafeLayout = state.layout;\n        }\n      }\n      if (unsafeLayout == null) {\n        unsafeLayout = getUnsafeDefaultLayout({ panesArray: this.panesArray });\n      }\n      const nextLayout = validatePaneGroupLayout({\n        layout: unsafeLayout,\n        paneConstraints: this.panesArray.map((paneData) => paneData.constraints)\n      });\n      if (areArraysEqual(prevLayout, nextLayout)) return;\n      this.layout = nextLayout;\n      this.opts.onLayout.current?.(nextLayout);\n      callPaneCallbacks(this.panesArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n    });\n  }\n  setLayout = (newLayout) => {\n    this.layout = newLayout;\n  };\n  registerResizeHandle = (dragHandleId) => {\n    return (e) => {\n      e.preventDefault();\n      const direction = this.opts.direction.current;\n      const dragState = this.dragState;\n      const groupId = this.opts.id.current;\n      const keyboardResizeBy = this.opts.keyboardResizeBy.current;\n      const prevLayout = this.layout;\n      const paneDataArray = this.panesArray;\n      const { initialLayout } = dragState ?? {};\n      const pivotIndices = getPivotIndices(groupId, dragHandleId);\n      let delta = getDeltaPercentage(e, dragHandleId, direction, dragState, keyboardResizeBy);\n      if (delta === 0) return;\n      const isHorizontal = direction === \"horizontal\";\n      if (document.dir === \"rtl\" && isHorizontal) {\n        delta = -delta;\n      }\n      const paneConstraints = paneDataArray.map((paneData) => paneData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        layout: initialLayout ?? prevLayout,\n        paneConstraints,\n        pivotIndices,\n        trigger: isKeyDown(e) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !areArraysEqual(prevLayout, nextLayout);\n      if (isMouseEvent(e) || isTouchEvent(e)) {\n        const prevDelta = this.prevDelta;\n        if (prevDelta !== delta) {\n          this.prevDelta = delta;\n          if (!layoutChanged) {\n            if (isHorizontal) {\n              setGlobalCursorStyle(delta < 0 ? \"horizontal-min\" : \"horizontal-max\");\n            } else {\n              setGlobalCursorStyle(delta < 0 ? \"vertical-min\" : \"vertical-max\");\n            }\n          } else {\n            setGlobalCursorStyle(isHorizontal ? \"horizontal\" : \"vertical\");\n          }\n        }\n      }\n      if (layoutChanged) {\n        this.setLayout(nextLayout);\n        this.opts.onLayout.current?.(nextLayout);\n        callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n      }\n    };\n  };\n  resizePane = (paneState, unsafePaneSize) => {\n    const prevLayout = this.layout;\n    const panesArray = this.panesArray;\n    const paneConstraintsArr = panesArray.map((paneData) => paneData.constraints);\n    const { paneSize, pivotIndices } = paneDataHelper(panesArray, paneState, prevLayout);\n    assert(paneSize != null);\n    const isLastPane = findPaneDataIndex(panesArray, paneState) === panesArray.length - 1;\n    const delta = isLastPane ? paneSize - unsafePaneSize : unsafePaneSize - paneSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      layout: prevLayout,\n      paneConstraints: paneConstraintsArr,\n      pivotIndices,\n      trigger: \"imperative-api\"\n    });\n    if (areArraysEqual(prevLayout, nextLayout)) return;\n    this.setLayout(nextLayout);\n    this.opts.onLayout.current?.(nextLayout);\n    callPaneCallbacks(panesArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n  };\n  startDragging = (dragHandleId, e) => {\n    const direction = this.opts.direction.current;\n    const layout = this.layout;\n    const handleElement = getResizeHandleElement(dragHandleId);\n    assert(handleElement);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, e);\n    this.dragState = {\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    };\n  };\n  stopDragging = () => {\n    resetGlobalCursorStyle();\n    this.dragState = null;\n  };\n  isPaneCollapsed = (pane) => {\n    const paneDataArray = this.panesArray;\n    const layout = this.layout;\n    const { collapsedSize = 0, collapsible, paneSize } = paneDataHelper(paneDataArray, pane, layout);\n    return collapsible === true && paneSize === collapsedSize;\n  };\n  expandPane = (pane) => {\n    const prevLayout = this.layout;\n    const paneDataArray = this.panesArray;\n    if (!pane.constraints.collapsible) return;\n    const paneConstraintsArray = paneDataArray.map((paneData) => paneData.constraints);\n    const {\n      collapsedSize = 0,\n      paneSize,\n      minSize = 0,\n      pivotIndices\n    } = paneDataHelper(paneDataArray, pane, prevLayout);\n    if (paneSize !== collapsedSize) return;\n    const prevPaneSize = this.paneSizeBeforeCollapseMap.get(pane.opts.id.current);\n    const baseSize = prevPaneSize != null && prevPaneSize >= minSize ? prevPaneSize : minSize;\n    const isLastPane = findPaneDataIndex(paneDataArray, pane) === paneDataArray.length - 1;\n    const delta = isLastPane ? paneSize - baseSize : baseSize - paneSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      layout: prevLayout,\n      paneConstraints: paneConstraintsArray,\n      pivotIndices,\n      trigger: \"imperative-api\"\n    });\n    if (areArraysEqual(prevLayout, nextLayout)) return;\n    this.setLayout(nextLayout);\n    this.opts.onLayout.current?.(nextLayout);\n    callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n  };\n  collapsePane = (pane) => {\n    const prevLayout = this.layout;\n    const paneDataArray = this.panesArray;\n    if (!pane.constraints.collapsible) return;\n    const paneConstraintsArray = paneDataArray.map((paneData) => paneData.constraints);\n    const { collapsedSize = 0, paneSize, pivotIndices } = paneDataHelper(paneDataArray, pane, prevLayout);\n    assert(paneSize != null);\n    if (paneSize === collapsedSize) return;\n    this.paneSizeBeforeCollapseMap.set(pane.opts.id.current, paneSize);\n    const isLastPane = findPaneDataIndex(paneDataArray, pane) === paneDataArray.length - 1;\n    const delta = isLastPane ? paneSize - collapsedSize : collapsedSize - paneSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      layout: prevLayout,\n      paneConstraints: paneConstraintsArray,\n      pivotIndices,\n      trigger: \"imperative-api\"\n    });\n    if (areArraysEqual(prevLayout, nextLayout)) return;\n    this.layout = nextLayout;\n    this.opts.onLayout.current?.(nextLayout);\n    callPaneCallbacks(paneDataArray, nextLayout, this.paneIdToLastNotifiedSizeMap);\n  };\n  getPaneSize = (pane) => {\n    return paneDataHelper(this.panesArray, pane, this.layout).paneSize;\n  };\n  getPaneStyle = (pane, defaultSize) => {\n    const paneDataArray = this.panesArray;\n    const layout = this.layout;\n    const dragState = this.dragState;\n    const paneIndex = findPaneDataIndex(paneDataArray, pane);\n    return computePaneFlexBoxStyle({\n      defaultSize,\n      dragState,\n      layout,\n      panesArray: paneDataArray,\n      paneIndex\n    });\n  };\n  isPaneExpanded = (pane) => {\n    const { collapsedSize = 0, collapsible, paneSize } = paneDataHelper(this.panesArray, pane, this.layout);\n    return !collapsible || paneSize > collapsedSize;\n  };\n  registerPane = (pane) => {\n    const newPaneDataArray = [...this.panesArray, pane];\n    newPaneDataArray.sort((paneA, paneB) => {\n      const orderA = paneA.opts.order.current;\n      const orderB = paneB.opts.order.current;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n    this.panesArray = newPaneDataArray;\n    this.panesArrayChanged = true;\n    return () => {\n      const paneDataArray = [...this.panesArray];\n      const index = findPaneDataIndex(this.panesArray, pane);\n      if (index < 0) return;\n      paneDataArray.splice(index, 1);\n      this.panesArray = paneDataArray;\n      delete this.paneIdToLastNotifiedSizeMap[pane.opts.id.current];\n      this.panesArrayChanged = true;\n    };\n  };\n  #setResizeHandlerEventListeners = () => {\n    const groupId = this.opts.id.current;\n    const handles = getResizeHandleElementsForGroup(groupId);\n    const paneDataArray = this.panesArray;\n    const unsubHandlers = handles.map((handle) => {\n      const handleId = handle.getAttribute(\"data-pane-resizer-id\");\n      if (!handleId) return noop;\n      const [idBefore, idAfter] = getResizeHandlePaneIds(groupId, handleId, paneDataArray);\n      if (idBefore == null || idAfter == null) return noop;\n      const onKeydown = (e) => {\n        if (e.defaultPrevented || e.key !== \"Enter\") return;\n        e.preventDefault();\n        const paneDataArray2 = this.panesArray;\n        const index = paneDataArray2.findIndex((paneData2) => paneData2.opts.id.current === idBefore);\n        if (index < 0) return;\n        const paneData = paneDataArray2[index];\n        assert(paneData);\n        const layout = this.layout;\n        const size = layout[index];\n        const { collapsedSize = 0, collapsible, minSize = 0 } = paneData.constraints;\n        if (!(size != null && collapsible)) return;\n        const nextLayout = adjustLayoutByDelta({\n          delta: areNumbersAlmostEqual(size, collapsedSize) ? minSize - size : collapsedSize - size,\n          layout,\n          paneConstraints: paneDataArray2.map((paneData2) => paneData2.constraints),\n          pivotIndices: getPivotIndices(groupId, handleId),\n          trigger: \"keyboard\"\n        });\n        if (layout !== nextLayout) {\n          this.layout = nextLayout;\n        }\n      };\n      const unsubListener = addEventListener(handle, \"keydown\", onKeydown);\n      return () => {\n        unsubListener();\n      };\n    });\n    return () => {\n      for (const unsub of unsubHandlers) {\n        unsub();\n      }\n    };\n  };\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"data-pane-group\": \"\",\n    \"data-direction\": this.opts.direction.current,\n    \"data-pane-group-id\": this.opts.id.current,\n    style: {\n      display: \"flex\",\n      flexDirection: this.opts.direction.current === \"horizontal\" ? \"row\" : \"column\",\n      height: \"100%\",\n      overflow: \"hidden\",\n      width: \"100%\"\n    }\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst resizeKeys = [\n  \"ArrowDown\",\n  \"ArrowLeft\",\n  \"ArrowRight\",\n  \"ArrowUp\",\n  \"End\",\n  \"Home\"\n];\nclass PaneResizerState {\n  opts;\n  group;\n  #isDragging = derived(() => this.group.dragState?.dragHandleId === this.opts.id.current);\n  #isFocused = false;\n  resizeHandler = null;\n  constructor(opts, group) {\n    this.opts = opts;\n    this.group = group;\n    useRefById(opts);\n  }\n  #startDragging = (e) => {\n    e.preventDefault();\n    if (this.opts.disabled.current) return;\n    this.group.startDragging(this.opts.id.current, e);\n    this.opts.onDraggingChange.current(true);\n  };\n  #stopDraggingAndBlur = () => {\n    const node = this.opts.ref.current;\n    if (!node) return;\n    node.blur();\n    this.group.stopDragging();\n    this.opts.onDraggingChange.current(false);\n  };\n  #onkeydown = (e) => {\n    if (this.opts.disabled.current || !this.resizeHandler || e.defaultPrevented) return;\n    if (resizeKeys.includes(e.key)) {\n      e.preventDefault();\n      this.resizeHandler(e);\n      return;\n    }\n    if (e.key !== \"F6\") return;\n    e.preventDefault();\n    const handles = getResizeHandleElementsForGroup(this.group.opts.id.current);\n    const index = getResizeHandleElementIndex(this.group.opts.id.current, this.opts.id.current);\n    if (index === null) return;\n    let nextIndex = 0;\n    if (e.shiftKey) {\n      if (index > 0) {\n        nextIndex = index - 1;\n      } else {\n        nextIndex = handles.length - 1;\n      }\n    } else {\n      if (index + 1 < handles.length) {\n        nextIndex = index + 1;\n      } else {\n        nextIndex = 0;\n      }\n    }\n    const nextHandle = handles[nextIndex];\n    nextHandle.focus();\n  };\n  #onblur = () => {\n    this.#isFocused = false;\n  };\n  #onfocus = () => {\n    this.#isFocused = true;\n  };\n  #onmousedown = (e) => {\n    this.#startDragging(e);\n  };\n  #onmouseup = () => {\n    this.#stopDraggingAndBlur();\n  };\n  #ontouchcancel = () => {\n    this.#stopDraggingAndBlur();\n  };\n  #ontouchend = () => {\n    this.#stopDraggingAndBlur();\n  };\n  #ontouchstart = (e) => {\n    this.#startDragging(e);\n  };\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"separator\",\n    \"data-direction\": this.group.opts.direction.current,\n    \"data-pane-group-id\": this.group.opts.id.current,\n    \"data-active\": this.#isDragging() ? \"pointer\" : this.#isFocused ? \"keyboard\" : void 0,\n    \"data-enabled\": !this.opts.disabled.current,\n    \"data-pane-resizer-id\": this.opts.id.current,\n    \"data-pane-resizer\": \"\",\n    tabIndex: this.opts.tabIndex.current,\n    style: {\n      cursor: getCursorStyle(this.group.opts.direction.current),\n      touchAction: \"none\",\n      userSelect: \"none\",\n      \"-webkit-user-select\": \"none\",\n      \"-webkit-touch-callout\": \"none\"\n    },\n    onkeydown: this.#onkeydown,\n    onblur: this.#onblur,\n    onfocus: this.#onfocus,\n    onmousedown: this.#onmousedown,\n    onmouseup: this.#onmouseup,\n    ontouchcancel: this.#ontouchcancel,\n    ontouchend: this.#ontouchend,\n    ontouchstart: this.#ontouchstart\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass PaneState {\n  opts;\n  group;\n  #paneTransitionState = \"\";\n  #callbacks = derived(() => ({\n    onCollapse: this.opts.onCollapse.current,\n    onExpand: this.opts.onExpand.current,\n    onResize: this.opts.onResize.current\n  }));\n  get callbacks() {\n    return this.#callbacks();\n  }\n  set callbacks($$value) {\n    return this.#callbacks($$value);\n  }\n  #constraints = derived(() => ({\n    collapsedSize: this.opts.collapsedSize.current,\n    collapsible: this.opts.collapsible.current,\n    defaultSize: this.opts.defaultSize.current,\n    maxSize: this.opts.maxSize.current,\n    minSize: this.opts.minSize.current\n  }));\n  get constraints() {\n    return this.#constraints();\n  }\n  set constraints($$value) {\n    return this.#constraints($$value);\n  }\n  #handleTransition = (state) => {\n    this.#paneTransitionState = state;\n    afterTick(() => {\n      if (this.opts.ref.current) {\n        const element2 = this.opts.ref.current;\n        const computedStyle = getComputedStyle(element2);\n        const hasTransition = computedStyle.transitionDuration !== \"0s\";\n        if (!hasTransition) {\n          this.#paneTransitionState = \"\";\n          return;\n        }\n        const handleTransitionEnd = (event) => {\n          if (event.propertyName === \"flex-grow\") {\n            this.#paneTransitionState = \"\";\n            element2.removeEventListener(\"transitionend\", handleTransitionEnd);\n          }\n        };\n        element2.addEventListener(\"transitionend\", handleTransitionEnd);\n      } else {\n        this.#paneTransitionState = \"\";\n      }\n    });\n  };\n  pane = {\n    collapse: () => {\n      this.#handleTransition(\"collapsing\");\n      this.group.collapsePane(this);\n    },\n    expand: () => {\n      this.#handleTransition(\"expanding\");\n      this.group.expandPane(this);\n    },\n    getSize: () => this.group.getPaneSize(this),\n    isCollapsed: () => this.group.isPaneCollapsed(this),\n    isExpanded: () => this.group.isPaneExpanded(this),\n    resize: (size) => this.group.resizePane(this, size),\n    getId: () => this.opts.id.current\n  };\n  constructor(opts, group) {\n    this.opts = opts;\n    this.group = group;\n    useRefById(opts);\n    watch(() => snapshot(this.constraints), () => {\n      this.group.panesArrayChanged = true;\n    });\n  }\n  #isCollapsed = derived(() => this.group.isPaneCollapsed(this));\n  #paneState = derived(() => this.#paneTransitionState !== \"\" ? this.#paneTransitionState : this.#isCollapsed() ? \"collapsed\" : \"expanded\");\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    style: this.group.getPaneStyle(this, this.opts.defaultSize.current),\n    \"data-pane\": \"\",\n    \"data-pane-id\": this.opts.id.current,\n    \"data-pane-group-id\": this.group.opts.id.current,\n    \"data-collapsed\": this.#isCollapsed() ? \"\" : void 0,\n    \"data-expanded\": this.#isCollapsed() ? void 0 : \"\",\n    \"data-pane-state\": this.#paneState()\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst PaneGroupContext = new Context(\"PaneGroup\");\nfunction usePaneGroup(props) {\n  return PaneGroupContext.set(new PaneGroupState(props));\n}\nfunction usePaneResizer(props) {\n  return new PaneResizerState(props, PaneGroupContext.get());\n}\nfunction usePane(props) {\n  return new PaneState(props, PaneGroupContext.get());\n}\nfunction Pane_group($$payload, $$props) {\n  push();\n  let {\n    autoSaveId = null,\n    direction,\n    id = useId(),\n    keyboardResizeBy = null,\n    onLayoutChange = noop,\n    storage = defaultStorage,\n    ref = null,\n    child,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const paneGroupState = usePaneGroup({\n    id: box.with(() => id ?? useId()),\n    ref: box.with(() => ref, (v) => ref = v),\n    autoSaveId: box.with(() => autoSaveId),\n    direction: box.with(() => direction),\n    keyboardResizeBy: box.with(() => keyboardResizeBy),\n    onLayout: box.with(() => onLayoutChange),\n    storage: box.with(() => storage)\n  });\n  const getLayout = () => paneGroupState.layout;\n  const setLayout = paneGroupState.setLayout;\n  const getId = () => paneGroupState.opts.id.current;\n  const mergedProps = mergeProps(restProps, paneGroupState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref, getLayout, setLayout, getId });\n  pop();\n}\nfunction Pane($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    ref = null,\n    collapsedSize,\n    collapsible,\n    defaultSize,\n    maxSize,\n    minSize,\n    onCollapse = noop,\n    onExpand = noop,\n    onResize = noop,\n    order,\n    child,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const paneState = usePane({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v),\n    collapsedSize: box.with(() => collapsedSize),\n    collapsible: box.with(() => collapsible),\n    defaultSize: box.with(() => defaultSize),\n    maxSize: box.with(() => maxSize),\n    minSize: box.with(() => minSize),\n    onCollapse: box.with(() => onCollapse),\n    onExpand: box.with(() => onExpand),\n    onResize: box.with(() => onResize),\n    order: box.with(() => order)\n  });\n  const collapse = paneState.pane.collapse;\n  const expand = paneState.pane.expand;\n  const getSize = paneState.pane.getSize;\n  const isCollapsed = paneState.pane.isCollapsed;\n  const isExpanded = paneState.pane.isExpanded;\n  const resize = paneState.pane.resize;\n  const getId = paneState.pane.getId;\n  const mergedProps = mergeProps(restProps, paneState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, {\n    ref,\n    collapse,\n    expand,\n    getSize,\n    isCollapsed,\n    isExpanded,\n    resize,\n    getId\n  });\n  pop();\n}\nfunction Pane_resizer($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    ref = null,\n    disabled = false,\n    onDraggingChange = noop,\n    tabindex = 0,\n    child,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const resizerState = usePaneResizer({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v),\n    disabled: box.with(() => disabled),\n    onDraggingChange: box.with(() => onDraggingChange),\n    tabIndex: box.with(() => tabindex)\n  });\n  const mergedProps = mergeProps(restProps, resizerState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Grip_vertical($$payload, $$props) {\n  push();\n  let { $$slots, $$events, ...props } = $$props;\n  const iconNode = [\n    [\"circle\", { \"cx\": \"9\", \"cy\": \"12\", \"r\": \"1\" }],\n    [\"circle\", { \"cx\": \"9\", \"cy\": \"5\", \"r\": \"1\" }],\n    [\"circle\", { \"cx\": \"9\", \"cy\": \"19\", \"r\": \"1\" }],\n    [\n      \"circle\",\n      { \"cx\": \"15\", \"cy\": \"12\", \"r\": \"1\" }\n    ],\n    [\"circle\", { \"cx\": \"15\", \"cy\": \"5\", \"r\": \"1\" }],\n    [\n      \"circle\",\n      { \"cx\": \"15\", \"cy\": \"19\", \"r\": \"1\" }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"grip-vertical\" },\n    props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        props.children?.($$payload2);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n  pop();\n}\nfunction Resizable_handle($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    withHandle = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Pane_resizer($$payload2, spread_props([\n      {\n        \"data-slot\": \"resizable-handle\",\n        class: cn(\"bg-border focus-visible:ring-ring focus-visible:outline-hidden relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 data-[direction=vertical]:h-px data-[direction=vertical]:w-full data-[direction=vertical]:after:left-0 data-[direction=vertical]:after:h-1 data-[direction=vertical]:after:w-full data-[direction=vertical]:after:-translate-y-1/2 data-[direction=vertical]:after:translate-x-0 [&[data-direction=vertical]>div]:rotate-90\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        },\n        children: ($$payload3) => {\n          if (withHandle) {\n            $$payload3.out += \"<!--[-->\";\n            $$payload3.out += `<div class=\"bg-border rounded-xs z-10 flex h-4 w-3 items-center justify-center border\">`;\n            Grip_vertical($$payload3, { class: \"size-2.5\" });\n            $$payload3.out += `<!----></div>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]-->`;\n        },\n        $$slots: { default: true }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Resizable_pane_group($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    this: paneGroup = void 0,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  $$payload.out += `<!---->`;\n  Pane_group($$payload, spread_props([\n    {\n      \"data-slot\": \"resizable-pane-group\",\n      class: cn(\"flex h-full w-full data-[direction=vertical]:flex-col\", className)\n    },\n    restProps\n  ]));\n  $$payload.out += `<!---->`;\n  bind_props($$props, { ref, this: paneGroup });\n  pop();\n}\nfunction _layout($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let userData, hasTeamAccess, navItems;\n  const { page } = getStores();\n  const baseNavItems = [\n    {\n      href: \"/dashboard/settings\",\n      label: \"General\",\n      icon: Settings_2,\n      exact: true\n    },\n    {\n      href: \"/dashboard/settings/profile\",\n      label: \"Profile\",\n      icon: File_text\n    },\n    {\n      href: \"/dashboard/settings/usage\",\n      label: \"Usage\",\n      icon: Activity\n    },\n    {\n      href: \"/dashboard/settings/interview-coach\",\n      label: \"AI Coach\",\n      icon: Bot\n    },\n    {\n      href: \"/dashboard/settings/analysis\",\n      label: \"Analysis\",\n      icon: Chart_column_stacked\n    },\n    {\n      href: \"/dashboard/settings/account\",\n      label: \"Account\",\n      icon: User\n    },\n    {\n      href: \"/dashboard/settings/security\",\n      label: \"Security\",\n      icon: Shield\n    },\n    {\n      href: \"/dashboard/settings/billing\",\n      label: \"Billing\",\n      icon: Credit_card\n    },\n    {\n      href: \"/dashboard/settings/notifications\",\n      label: \"Notifications\",\n      icon: Bell\n    },\n    {\n      href: \"/dashboard/settings/referrals\",\n      label: \"Referrals\",\n      icon: Share_2\n    }\n  ];\n  const teamNavItem = {\n    href: \"/dashboard/settings/team\",\n    label: \"Team\",\n    icon: Users,\n    exact: false\n  };\n  let isCollapsed = false;\n  const layoutSettings = {\n    desktop: {\n      defaultLayout: [20, 80],\n      collapsedSize: 5,\n      minSize: 12,\n      maxSize: 14\n    }\n  };\n  let currentSettings = layoutSettings.desktop;\n  let defaultLayout = currentSettings.defaultLayout;\n  onDestroy(() => {\n  });\n  function isActive(path, exact = false) {\n    if (exact) {\n      return store_get($$store_subs ??= {}, \"$page\", page).url.pathname === path;\n    }\n    return store_get($$store_subs ??= {}, \"$page\", page).url.pathname === path || store_get($$store_subs ??= {}, \"$page\", page).url.pathname.startsWith(path + \"/\");\n  }\n  userData = store_get($$store_subs ??= {}, \"$page\", page).data.user;\n  hasTeamAccess = userData?.teamId || userData?.hasTeamFeature || false;\n  navItems = hasTeamAccess ? [...baseNavItems, teamNavItem] : baseNavItems;\n  $$payload.out += `<div class=\"flex h-[calc(100vh-65px)] w-full flex-col\"><main class=\"flex flex-1 flex-col\">`;\n  Resizable_pane_group($$payload, {\n    direction: \"horizontal\",\n    onLayoutChange: (sizes) => sizes && (defaultLayout = sizes),\n    class: \"h-full items-stretch\",\n    children: ($$payload2) => {\n      Pane($$payload2, {\n        defaultSize: defaultLayout[0],\n        collapsedSize: currentSettings.collapsedSize,\n        collapsible: true,\n        minSize: currentSettings.minSize,\n        maxSize: currentSettings.maxSize,\n        onCollapse: () => isCollapsed = true,\n        onExpand: () => isCollapsed = false,\n        children: ($$payload3) => {\n          const each_array = ensure_array_like(navItems);\n          $$payload3.out += `<div${attr_class(clsx(cn(\"bg-secondary/40 flex h-full flex-col\", isCollapsed ? \"p-4 pt-6\" : \"p-6\")))}><div class=\"flex flex-1 flex-col gap-4\"><!--[-->`;\n          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n            let { href, label, icon, exact } = each_array[$$index];\n            $$payload3.out += `<a${attr(\"href\", href)}${attr_class(clsx(cn(\"sm:text-md flex items-center text-sm font-light transition-colors xl:text-base\", isActive(href, exact) ? \"text-foreground font-semibold\" : \"text-foreground/60 hover:text-foreground\", isCollapsed ? \"mb-4 justify-center\" : \"gap-4\")))}>`;\n            if (icon) {\n              $$payload3.out += \"<!--[-->\";\n              $$payload3.out += `<!---->`;\n              icon?.($$payload3, { class: \"h-2 w-2 sm:h-3 sm:w-3 xl:h-4 xl:w-4\" });\n              $$payload3.out += `<!---->`;\n            } else {\n              $$payload3.out += \"<!--[!-->\";\n            }\n            $$payload3.out += `<!--]--> `;\n            if (!isCollapsed) {\n              $$payload3.out += \"<!--[-->\";\n              $$payload3.out += `<span>${escape_html(label)}</span>`;\n            } else {\n              $$payload3.out += \"<!--[!-->\";\n            }\n            $$payload3.out += `<!--]--></a>`;\n          }\n          $$payload3.out += `<!--]--> `;\n          {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--></div> <div${attr_class(clsx(cn(\"mt-auto\", isCollapsed ? \"flex justify-center\" : \"border-t pt-4\")))}><button${attr_class(clsx(cn(\"text-foreground/60 hover:text-foreground sm:text-md flex items-center text-sm font-light transition-colors xl:text-base\", isCollapsed ? \"flex justify-center p-2\" : \"\")))}><div${attr_class(clsx(cn(\"flex items-center\", isCollapsed ? \"\" : \"gap-4\")))}>`;\n          Log_out($$payload3, { class: \"h-2 w-2 sm:h-3 sm:w-3 xl:h-4 xl:w-4\" });\n          $$payload3.out += `<!----> `;\n          if (!isCollapsed) {\n            $$payload3.out += \"<!--[-->\";\n            $$payload3.out += `<span>Log out</span>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n          }\n          $$payload3.out += `<!--]--></div></button></div></div>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> `;\n      Resizable_handle($$payload2, { withHandle: true });\n      $$payload2.out += `<!----> `;\n      Pane($$payload2, {\n        defaultSize: defaultLayout[1],\n        minSize: 45,\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"show-scrollbar w-auto overflow-auto\"><!---->`;\n          slot($$payload3, $$props, \"default\", {}, null);\n          $$payload3.out += `<!----></div>`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></main></div>`;\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  pop();\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE;AAC3D,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC;AACvD,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC/E,EAAE,OAAO,MAAM;AACf,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACpF,GAAG;AACH;AACA,SAAS,mBAAmB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE;AACnE,EAAE,IAAI,cAAc,GAAG,CAAC;AACxB,EAAE,IAAI,cAAc,GAAG,GAAG;AAC1B,EAAE,IAAI,YAAY,GAAG,CAAC;AACtB,EAAE,IAAI,YAAY,GAAG,CAAC;AACtB,EAAE,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC;AACpC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC9C,IAAI,MAAM,WAAW,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,WAAW;AACjD,IAAI,MAAM,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,WAAW;AACtD,IAAI,IAAI,CAAC,KAAK,UAAU,EAAE;AAC1B,MAAM,cAAc,GAAG,OAAO;AAC9B,MAAM,cAAc,GAAG,OAAO;AAC9B,KAAK,MAAM;AACX,MAAM,YAAY,IAAI,OAAO;AAC7B,MAAM,YAAY,IAAI,OAAO;AAC7B;AACA;AACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,GAAG,YAAY,CAAC;AAC/D,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,GAAG,GAAG,YAAY,CAAC;AAC/D,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC;AACrC,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG;AACH;AACA,SAAS,MAAM,CAAC,iBAAiB,EAAE,OAAO,GAAG,mBAAmB,EAAE;AAClE,EAAE,IAAI,CAAC,iBAAiB,EAAE;AAC1B,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC;AAC1B,IAAI,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC;AAC5B;AACA;AACA,MAAM,+BAA+B,GAAG,GAAG;AAC3C,MAAM,SAAS,GAAG,EAAE;AACpB,SAAS,qBAAqB,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,GAAG,SAAS,EAAE;AAC7E,EAAE,OAAO,2BAA2B,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,CAAC,KAAK,CAAC;AAC5E;AACA,SAAS,2BAA2B,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,GAAG,SAAS,EAAE;AACnF,EAAE,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,EAAE,cAAc,CAAC;AACvD,EAAE,MAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC;AAC3D,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;AACnD;AACA,SAAS,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE;AACpC,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM;AACjC,IAAI,OAAO,KAAK;AAChB,EAAE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AACpD,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC;AACnC,MAAM,OAAO,KAAK;AAClB;AACA,EAAE,OAAO,IAAI;AACb;AACA,SAAS,OAAO,CAAC,KAAK,EAAE,QAAQ,EAAE;AAClC,EAAE,OAAO,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnD;AACA,MAAM,SAAS,GAAG,OAAO,QAAQ,KAAK,WAAW;AACjD,SAAS,aAAa,CAAC,QAAQ,EAAE;AACjC,EAAE,OAAO,QAAQ,YAAY,WAAW;AACxC;AACA,SAAS,SAAS,CAAC,KAAK,EAAE;AAC1B,EAAE,OAAO,KAAK,CAAC,IAAI,KAAK,SAAS;AACjC;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACvC;AACA,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACvC;AACA,SAAS,UAAU,CAAC,EAAE,eAAe,EAAE,oBAAoB,EAAE,SAAS,EAAE,WAAW,EAAE,EAAE;AACvF,EAAE,MAAM,eAAe,GAAG,oBAAoB,CAAC,SAAS,CAAC;AACzD,EAAE,MAAM,CAAC,eAAe,IAAI,IAAI,EAAE,sCAAsC,CAAC;AACzE,EAAE,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,WAAW,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,eAAe;AACxF,EAAE,IAAI,OAAO,GAAG,WAAW;AAC3B,EAAE,IAAI,2BAA2B,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;AACzD,IAAI,OAAO,GAAG,6BAA6B,CAAC,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,CAAC;AACzF;AACA,EAAE,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC;AACtC,EAAE,OAAO,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACtD;AACA,SAAS,6BAA6B,CAAC,IAAI,EAAE,WAAW,EAAE,aAAa,EAAE,OAAO,EAAE;AAClF,EAAE,IAAI,CAAC,WAAW;AAClB,IAAI,OAAO,OAAO;AAClB,EAAE,MAAM,YAAY,GAAG,CAAC,aAAa,GAAG,OAAO,IAAI,CAAC;AACpD,EAAE,OAAO,2BAA2B,CAAC,IAAI,EAAE,YAAY,CAAC,GAAG,CAAC,GAAG,aAAa,GAAG,OAAO;AACtF;AACA,SAAS,IAAI,GAAG;AAChB;AACA,SAAS,4BAA4B,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,EAAE;AACvE,EAAE,MAAM,oBAAoB,GAAG,+BAA+B,CAAC,OAAO,CAAC;AACvE,EAAE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE;AAC9D,IAAI,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,mBAAmB,CAAC;AACjE,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,YAAY,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC;AACrC,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC;AACtD,IAAI,IAAI,aAAa,CAAC,cAAc,CAAC,EAAE;AACvC,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;AACxC,MAAM,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AAC5E,MAAM,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7E,MAAM,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC7E,MAAM,cAAc,CAAC,YAAY,CAAC,eAAe,EAAE,QAAQ,IAAI,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AACrG;AACA;AACA,EAAE,OAAO,MAAM;AACf,IAAI,oBAAoB,CAAC,OAAO,CAAC,CAAC,mBAAmB,KAAK;AAC1D,MAAM,mBAAmB,CAAC,eAAe,CAAC,eAAe,CAAC;AAC1D,MAAM,mBAAmB,CAAC,eAAe,CAAC,eAAe,CAAC;AAC1D,MAAM,mBAAmB,CAAC,eAAe,CAAC,eAAe,CAAC;AAC1D,MAAM,mBAAmB,CAAC,eAAe,CAAC,eAAe,CAAC;AAC1D,KAAK,CAAC;AACN,GAAG;AACH;AACA,SAAS,+BAA+B,CAAC,OAAO,EAAE;AAClD,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,OAAO,EAAE;AACb,EAAE,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,2CAA2C,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;AACzG;AACA,SAAS,2BAA2B,CAAC,OAAO,EAAE,EAAE,EAAE;AAClD,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,OAAO,IAAI;AACf,EAAE,MAAM,OAAO,GAAG,+BAA+B,CAAC,OAAO,CAAC;AAC1D,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,YAAY,CAAC,sBAAsB,CAAC,KAAK,EAAE,CAAC;AACjG,EAAE,OAAO,KAAK,IAAI,IAAI;AACtB;AACA,SAAS,eAAe,CAAC,OAAO,EAAE,YAAY,EAAE;AAChD,EAAE,MAAM,KAAK,GAAG,2BAA2B,CAAC,OAAO,EAAE,YAAY,CAAC;AAClE,EAAE,OAAO,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;AACtD;AACA,SAAS,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE;AAClD,EAAE,MAAM,oBAAoB,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,CAAC;AACjF,EAAE,MAAM,SAAS,GAAG,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC;AACvD,EAAE,MAAM,eAAe,GAAG,oBAAoB,CAAC,SAAS,CAAC;AACzD,EAAE,MAAM,UAAU,GAAG,SAAS,KAAK,UAAU,CAAC,MAAM,GAAG,CAAC;AACxD,EAAE,MAAM,YAAY,GAAG,UAAU,GAAG,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,GAAG,CAAC,CAAC;AAC3F,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AACpC,EAAE,OAAO;AACT,IAAI,GAAG,eAAe;AACtB,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG;AACH;AACA,SAAS,iBAAiB,CAAC,UAAU,EAAE,IAAI,EAAE;AAC7C,EAAE,OAAO,UAAU,CAAC,SAAS,CAAC,CAAC,YAAY,KAAK,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AACtG;AACA,SAAS,iBAAiB,CAAC,UAAU,EAAE,MAAM,EAAE,2BAA2B,EAAE;AAC5E,EAAE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AACtD,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;AAC9B,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;AACtC,IAAI,MAAM,CAAC,QAAQ,CAAC;AACpB,IAAI,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,WAAW,EAAE,GAAG,QAAQ,CAAC,WAAW;AACnE,IAAI,MAAM,gBAAgB,GAAG,2BAA2B,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AAClF,IAAI,IAAI,EAAE,gBAAgB,IAAI,IAAI,IAAI,IAAI,KAAK,gBAAgB,CAAC;AAChE,MAAM;AACN,IAAI,2BAA2B,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,IAAI;AAChE,IAAI,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,QAAQ,CAAC,SAAS;AACjE,IAAI,QAAQ,GAAG,IAAI,EAAE,gBAAgB,CAAC;AACtC,IAAI,IAAI,WAAW,KAAK,UAAU,IAAI,QAAQ,CAAC,EAAE;AACjD,MAAM,IAAI,QAAQ,KAAK,gBAAgB,IAAI,IAAI,IAAI,gBAAgB,KAAK,aAAa,CAAC,IAAI,IAAI,KAAK,aAAa,EAAE;AAClH,QAAQ,QAAQ,EAAE;AAClB;AACA,MAAM,IAAI,UAAU,KAAK,gBAAgB,IAAI,IAAI,IAAI,gBAAgB,KAAK,aAAa,CAAC,IAAI,IAAI,KAAK,aAAa,EAAE;AACpH,QAAQ,UAAU,EAAE;AACpB;AACA;AACA;AACA;AACA,SAAS,sBAAsB,CAAC,EAAE,UAAU,EAAE,EAAE;AAChD,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;AACzC,EAAE,MAAM,oBAAoB,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,CAAC;AACjF,EAAE,IAAI,iBAAiB,GAAG,CAAC;AAC3B,EAAE,IAAI,aAAa,GAAG,GAAG;AACzB,EAAE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AAC1D,IAAI,MAAM,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC;AACvD,IAAI,MAAM,CAAC,eAAe,CAAC;AAC3B,IAAI,MAAM,EAAE,WAAW,EAAE,GAAG,eAAe;AAC3C,IAAI,IAAI,WAAW,IAAI,IAAI,EAAE;AAC7B,MAAM,iBAAiB,EAAE;AACzB,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,WAAW;AACjC,MAAM,aAAa,IAAI,WAAW;AAClC;AACA;AACA,EAAE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AAC1D,IAAI,MAAM,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC;AACvD,IAAI,MAAM,CAAC,eAAe,CAAC;AAC3B,IAAI,MAAM,EAAE,WAAW,EAAE,GAAG,eAAe;AAC3C,IAAI,IAAI,WAAW,IAAI,IAAI,EAAE;AAC7B,MAAM;AACN;AACA,IAAI,MAAM,iBAAiB,GAAG,UAAU,CAAC,MAAM,GAAG,iBAAiB;AACnE,IAAI,MAAM,IAAI,GAAG,aAAa,GAAG,iBAAiB;AAClD,IAAI,iBAAiB,EAAE;AACvB,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI;AACxB,IAAI,aAAa,IAAI,IAAI;AACzB;AACA,EAAE,OAAO,MAAM;AACf;AACA,SAAS,uBAAuB,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,EAAE;AAC1E,EAAE,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC;AACpC,EAAE,MAAM,mBAAmB,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO,KAAK,WAAW,GAAG,OAAO,EAAE,CAAC,CAAC;AACnG,EAAE,IAAI,UAAU,CAAC,MAAM,KAAK,eAAe,CAAC,MAAM,EAAE;AACpD,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,QAAQ,EAAE,eAAe,CAAC,MAAM,CAAC,cAAc,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxH,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,EAAE,GAAG,CAAC,EAAE;AAC/D,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AACjE,MAAM,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC;AAC1C,MAAM,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC;AAChC,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,mBAAmB,GAAG,UAAU;AAC7D,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG,QAAQ;AAClC;AACA;AACA,EAAE,IAAI,aAAa,GAAG,CAAC;AACvB,EAAE,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AAC/D,IAAI,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC;AACxC,IAAI,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC;AAC9B,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC;AAChC,MAAM,eAAe;AACrB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,WAAW,EAAE;AACnB,KAAK,CAAC;AACN,IAAI,IAAI,UAAU,KAAK,QAAQ,EAAE;AACjC,MAAM,aAAa,IAAI,UAAU,GAAG,QAAQ;AAC5C,MAAM,UAAU,CAAC,KAAK,CAAC,GAAG,QAAQ;AAClC;AACA;AACA,EAAE,IAAI,CAAC,qBAAqB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE;AAChD,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;AACjE,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;AACxC,MAAM,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AAC9B,MAAM,MAAM,UAAU,GAAG,QAAQ,GAAG,aAAa;AACjD,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC;AAClC,QAAQ,eAAe;AACvB,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACjC,QAAQ,aAAa,IAAI,QAAQ,GAAG,QAAQ;AAC5C,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG,QAAQ;AACpC,QAAQ,IAAI,qBAAqB,CAAC,aAAa,EAAE,CAAC,CAAC,EAAE;AACrD,UAAU;AACV;AACA;AACA;AACA;AACA,EAAE,OAAO,UAAU;AACnB;AACA,SAAS,mBAAmB,CAAC,EAAE,EAAE;AACjC,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,OAAO,IAAI;AACf,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,sCAAsC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;AAC1F,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,OAAO,QAAQ;AACnB;AACA,EAAE,OAAO,IAAI;AACb;AACA,SAAS,sBAAsB,CAAC,EAAE,EAAE;AACpC,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,OAAO,IAAI;AACf,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,uBAAuB,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3E,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,OAAO,QAAQ;AACnB;AACA,EAAE,OAAO,IAAI;AACb;AACA,SAAS,uBAAuB,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,gBAAgB,EAAE;AACzE,EAAE,MAAM,YAAY,GAAG,GAAG,KAAK,YAAY;AAC3C,EAAE,MAAM,aAAa,GAAG,sBAAsB,CAAC,YAAY,CAAC;AAC5D,EAAE,MAAM,CAAC,aAAa,CAAC;AACvB,EAAE,MAAM,OAAO,GAAG,aAAa,CAAC,YAAY,CAAC,oBAAoB,CAAC;AAClE,EAAE,MAAM,CAAC,OAAO,CAAC;AACjB,EAAE,MAAM,EAAE,qBAAqB,EAAE,GAAG,gBAAgB;AACpD,EAAE,MAAM,cAAc,GAAG,4BAA4B,CAAC,GAAG,EAAE,CAAC,CAAC;AAC7D,EAAE,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,CAAC,YAAY,CAAC;AACtB,EAAE,MAAM,SAAS,GAAG,YAAY,CAAC,qBAAqB,EAAE;AACxD,EAAE,MAAM,iBAAiB,GAAG,YAAY,GAAG,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,MAAM;AAC7E,EAAE,MAAM,YAAY,GAAG,cAAc,GAAG,qBAAqB;AAC7D,EAAE,MAAM,gBAAgB,GAAG,YAAY,GAAG,iBAAiB,GAAG,GAAG;AACjE,EAAE,OAAO,gBAAgB;AACzB;AACA,SAAS,kBAAkB,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,gBAAgB,EAAE,gBAAgB,EAAE;AACtF,EAAE,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;AACpB,IAAI,MAAM,YAAY,GAAG,GAAG,KAAK,YAAY;AAC7C,IAAI,IAAI,KAAK,GAAG,CAAC;AACjB,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE;AACpB,MAAM,KAAK,GAAG,GAAG;AACjB,KAAK,MAAM,IAAI,gBAAgB,IAAI,IAAI,EAAE;AACzC,MAAM,KAAK,GAAG,gBAAgB;AAC9B,KAAK,MAAM;AACX,MAAM,KAAK,GAAG,EAAE;AAChB;AACA,IAAI,IAAI,QAAQ,GAAG,CAAC;AACpB,IAAI,QAAQ,CAAC,CAAC,GAAG;AACjB,MAAM,KAAK,WAAW;AACtB,QAAQ,QAAQ,GAAG,YAAY,GAAG,CAAC,GAAG,KAAK;AAC3C,QAAQ;AACR,MAAM,KAAK,WAAW;AACtB,QAAQ,QAAQ,GAAG,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC;AAC5C,QAAQ;AACR,MAAM,KAAK,YAAY;AACvB,QAAQ,QAAQ,GAAG,YAAY,GAAG,KAAK,GAAG,CAAC;AAC3C,QAAQ;AACR,MAAM,KAAK,SAAS;AACpB,QAAQ,QAAQ,GAAG,YAAY,GAAG,CAAC,GAAG,CAAC,KAAK;AAC5C,QAAQ;AACR,MAAM,KAAK,KAAK;AAChB,QAAQ,QAAQ,GAAG,GAAG;AACtB,QAAQ;AACR,MAAM,KAAK,MAAM;AACjB,QAAQ,QAAQ,GAAG,IAAI;AACvB,QAAQ;AACR;AACA,IAAI,OAAO,QAAQ;AACnB,GAAG,MAAM;AACT,IAAI,IAAI,gBAAgB,IAAI,IAAI;AAChC,MAAM,OAAO,CAAC;AACd,IAAI,OAAO,uBAAuB,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,EAAE,gBAAgB,CAAC;AAC1E;AACA;AACA,SAAS,4BAA4B,CAAC,GAAG,EAAE,CAAC,EAAE;AAC9C,EAAE,MAAM,YAAY,GAAG,GAAG,KAAK,YAAY;AAC3C,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;AACvB,IAAI,OAAO,YAAY,GAAG,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,OAAO;AAC/C,GAAG,MAAM,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;AAC9B,IAAI,MAAM,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACnC,IAAI,MAAM,CAAC,UAAU,CAAC;AACtB,IAAI,OAAO,YAAY,GAAG,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO;AACjE,GAAG,MAAM;AACT,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,wBAAwB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzD;AACA;AACA,SAAS,sBAAsB,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC/D,EAAE,MAAM,MAAM,GAAG,sBAAsB,CAAC,QAAQ,CAAC;AACjD,EAAE,MAAM,OAAO,GAAG,+BAA+B,CAAC,OAAO,CAAC;AAC1D,EAAE,MAAM,KAAK,GAAG,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,EAAE;AACrD,EAAE,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,IAAI;AAC7D,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI,IAAI;AAChE,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC;AAC5B;AACA,IAAI,KAAK,GAAG,CAAC;AACb,SAAS,KAAK,CAAC,MAAM,GAAG,WAAW,EAAE;AACrC,EAAE,KAAK,EAAE;AACT,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAC7B;AACA,SAAS,mBAAmB,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,oBAAoB,EAAE,YAAY,EAAE,OAAO,EAAE,EAAE;AAC1H,EAAE,IAAI,qBAAqB,CAAC,KAAK,EAAE,CAAC,CAAC;AACrC,IAAI,OAAO,UAAU;AACrB,EAAE,MAAM,UAAU,GAAG,CAAC,GAAG,UAAU,CAAC;AACpC,EAAE,MAAM,CAAC,eAAe,EAAE,gBAAgB,CAAC,GAAG,YAAY;AAC1D,EAAE,IAAI,YAAY,GAAG,CAAC;AACtB,EAAE;AACF,IAAI,IAAI,OAAO,KAAK,UAAU,EAAE;AAChC,MAAM;AACN,QAAQ,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,gBAAgB,GAAG,eAAe;AACpE,QAAQ,MAAM,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAC3D,QAAQ,MAAM,CAAC,eAAe,CAAC;AAC/B,QAAQ,IAAI,eAAe,CAAC,WAAW,EAAE;AACzC,UAAU,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;AAC5C,UAAU,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AAClC,UAAU,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAC9D,UAAU,MAAM,CAAC,gBAAgB,CAAC;AAClC,UAAU,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,gBAAgB;AACrE,UAAU,IAAI,qBAAqB,CAAC,QAAQ,EAAE,aAAa,CAAC,EAAE;AAC9D,YAAY,MAAM,UAAU,GAAG,OAAO,GAAG,QAAQ;AACjD,YAAY,IAAI,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;AAC9E,cAAc,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;AAC7D;AACA;AACA;AACA;AACA,MAAM;AACN,QAAQ,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,eAAe,GAAG,gBAAgB;AACpE,QAAQ,MAAM,eAAe,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAC3D,QAAQ,MAAM,CAAC,eAAe,CAAC;AAC/B,QAAQ,MAAM,EAAE,WAAW,EAAE,GAAG,eAAe;AAC/C,QAAQ,IAAI,WAAW,EAAE;AACzB,UAAU,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;AAC5C,UAAU,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AAClC,UAAU,MAAM,gBAAgB,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAC9D,UAAU,MAAM,CAAC,gBAAgB,CAAC;AAClC,UAAU,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,gBAAgB;AACrE,UAAU,IAAI,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;AACxD,YAAY,MAAM,UAAU,GAAG,QAAQ,GAAG,aAAa;AACvD,YAAY,IAAI,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE;AAC9E,cAAc,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE;AACxC,IAAI,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,gBAAgB,GAAG,eAAe;AAC9D,IAAI,IAAI,iBAAiB,GAAG,CAAC;AAC7B,IAAI,OAAO,IAAI,EAAE;AACjB,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;AACxC,MAAM,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AAC9B,MAAM,MAAM,WAAW,GAAG,UAAU,CAAC;AACrC,QAAQ,eAAe,EAAE,oBAAoB;AAC7C,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,MAAM,MAAM,GAAG,WAAW,GAAG,QAAQ;AAC3C,MAAM,iBAAiB,IAAI,MAAM;AACjC,MAAM,KAAK,IAAI,SAAS;AACxB,MAAM,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,oBAAoB,CAAC,MAAM,EAAE;AAC7D,QAAQ;AACR;AACA;AACA,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAC9E,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,WAAW,GAAG,WAAW;AACrD;AACA,EAAE;AACF,IAAI,MAAM,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG,eAAe,GAAG,gBAAgB;AACrE,IAAI,IAAI,KAAK,GAAG,UAAU;AAC1B,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,oBAAoB,CAAC,MAAM,EAAE;AAC9D,MAAM,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC;AACrE,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC;AACxC,MAAM,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AAC9B,MAAM,MAAM,UAAU,GAAG,QAAQ,GAAG,cAAc;AAClD,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC;AAClC,QAAQ,eAAe,EAAE,oBAAoB;AAC7C,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;AACtD,QAAQ,YAAY,IAAI,QAAQ,GAAG,QAAQ;AAC3C,QAAQ,UAAU,CAAC,KAAK,CAAC,GAAG,QAAQ;AACpC,QAAQ,IAAI,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;AAC9F,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC,IAAI,CAAC,EAAE;AACjB,UAAU;AACV;AACA;AACA,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE;AACrB,QAAQ,KAAK,EAAE;AACf,OAAO,MAAM;AACb,QAAQ,KAAK,EAAE;AACf;AACA;AACA;AACA,EAAE,IAAI,qBAAqB,CAAC,YAAY,EAAE,CAAC,CAAC,EAAE;AAC9C,IAAI,OAAO,UAAU;AACrB;AACA,EAAE;AACF,IAAI,MAAM,UAAU,GAAG,KAAK,GAAG,CAAC,GAAG,gBAAgB,GAAG,eAAe;AACrE,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC;AAC3C,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AAC5B,IAAI,MAAM,UAAU,GAAG,QAAQ,GAAG,YAAY;AAC9C,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC;AAChC,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,WAAW,EAAE;AACnB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,UAAU,CAAC,GAAG,QAAQ;AACrC,IAAI,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE;AACtD,MAAM,IAAI,cAAc,GAAG,UAAU,GAAG,QAAQ;AAChD,MAAM,MAAM,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,gBAAgB,GAAG,eAAe;AACxE,MAAM,IAAI,KAAK,GAAG,WAAW;AAC7B,MAAM,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,oBAAoB,CAAC,MAAM,EAAE;AAChE,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;AAC3C,QAAQ,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC;AACjC,QAAQ,MAAM,WAAW,GAAG,SAAS,GAAG,cAAc;AACtD,QAAQ,MAAM,SAAS,GAAG,UAAU,CAAC;AACrC,UAAU,eAAe,EAAE,oBAAoB;AAC/C,UAAU,SAAS,EAAE,KAAK;AAC1B,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE;AAC1D,UAAU,cAAc,IAAI,SAAS,GAAG,SAAS;AACjD,UAAU,UAAU,CAAC,KAAK,CAAC,GAAG,SAAS;AACvC;AACA,QAAQ,IAAI,qBAAqB,CAAC,cAAc,EAAE,CAAC,CAAC;AACpD,UAAU;AACV,QAAQ,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE;AACrC;AACA;AACA;AACA,EAAE,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,IAAI,KAAK,IAAI,GAAG,KAAK,EAAE,CAAC,CAAC;AACvE,EAAE,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,GAAG,CAAC;AAC5C,IAAI,OAAO,UAAU;AACrB,EAAE,OAAO,UAAU;AACnB;AACA,IAAI,YAAY,GAAG,IAAI;AACvB,IAAI,OAAO,GAAG,IAAI;AAClB,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,QAAQ,KAAK;AACf,IAAI,KAAK,YAAY;AACrB,MAAM,OAAO,WAAW;AACxB,IAAI,KAAK,gBAAgB;AACzB,MAAM,OAAO,UAAU;AACvB,IAAI,KAAK,gBAAgB;AACzB,MAAM,OAAO,UAAU;AACvB,IAAI,KAAK,UAAU;AACnB,MAAM,OAAO,WAAW;AACxB,IAAI,KAAK,cAAc;AACvB,MAAM,OAAO,UAAU;AACvB,IAAI,KAAK,cAAc;AACvB,MAAM,OAAO,UAAU;AACvB;AACA;AACA,SAAS,sBAAsB,GAAG;AAClC,EAAE,IAAI,OAAO,KAAK,IAAI;AACtB,IAAI;AACJ,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC,EAAE,YAAY,GAAG,IAAI;AACrB,EAAE,OAAO,GAAG,IAAI;AAChB;AACA,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACrC,EAAE,IAAI,YAAY,KAAK,KAAK;AAC5B,IAAI;AACJ,EAAE,YAAY,GAAG,KAAK;AACtB,EAAE,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC;AACrC,EAAE,IAAI,OAAO,KAAK,IAAI,EAAE;AACxB,IAAI,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AAC7C,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,OAAO,CAAC,SAAS,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,YAAY,CAAC;AACtD;AACA,SAAS,uBAAuB,CAAC,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,GAAG,CAAC,EAAE,EAAE;AAC3G,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC;AAChC,EAAE,IAAI,QAAQ;AACd,EAAE,IAAI,IAAI,IAAI,IAAI,EAAE;AACpB,IAAI,QAAQ,GAAG,WAAW,IAAI,GAAG;AACjC,GAAG,MAAM,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AACtC,IAAI,QAAQ,GAAG,GAAG;AAClB,GAAG,MAAM;AACT,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;AAC1C;AACA,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,CAAC;AAChB,IAAI,QAAQ;AACZ,IAAI,UAAU,EAAE,CAAC;AACjB;AACA,IAAI,QAAQ,EAAE,QAAQ;AACtB;AACA;AACA,IAAI,aAAa,EAAE,SAAS,KAAK,IAAI,GAAG,MAAM,GAAG;AACjD,GAAG;AACH;AACA,SAAS,iBAAiB,CAAC,aAAa,EAAE;AAC1C,EAAE,IAAI;AACN,IAAI,IAAI,OAAO,YAAY,KAAK,WAAW,EAAE;AAC7C,MAAM,MAAM,IAAI,SAAS,CAAC,mDAAmD,CAAC;AAC9E;AACA,IAAI,aAAa,CAAC,OAAO,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC;AAChE,IAAI,aAAa,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AAC9E,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AACtB,IAAI,aAAa,CAAC,OAAO,GAAG,MAAM,IAAI;AACtC,IAAI,aAAa,CAAC,OAAO,GAAG,MAAM;AAClC,KAAK;AACL;AACA;AACA,SAAS,eAAe,CAAC,UAAU,EAAE;AACrC,EAAE,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAClC;AACA,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAC5C,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC;AACxI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC;AACrB,EAAE,OAAO,aAAa;AACtB;AACA,SAAS,4BAA4B,CAAC,UAAU,EAAE,OAAO,EAAE;AAC3D,EAAE,IAAI;AACN,IAAI,MAAM,YAAY,GAAG,eAAe,CAAC,UAAU,CAAC;AACpD,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC;AACpD,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,EAAE,CAAC;AAC/C,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,KAAK,IAAI,EAAE;AACvD,MAAM,OAAO,MAAM;AACnB;AACA,GAAG,CAAC,MAAM;AACV;AACA,EAAE,OAAO,IAAI;AACb;AACA,SAAS,kBAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE;AAC7D,EAAE,MAAM,KAAK,GAAG,4BAA4B,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,EAAE;AACvE,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC;AACxC,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,IAAI,IAAI;AAC/B;AACA,SAAS,kBAAkB,CAAC,UAAU,EAAE,UAAU,EAAE,uBAAuB,EAAE,KAAK,EAAE,OAAO,EAAE;AAC7F,EAAE,MAAM,YAAY,GAAG,eAAe,CAAC,UAAU,CAAC;AAClD,EAAE,MAAM,OAAO,GAAG,UAAU,CAAC,UAAU,CAAC;AACxC,EAAE,MAAM,KAAK,GAAG,4BAA4B,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,EAAE;AACvE,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG;AACnB,IAAI,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,uBAAuB,CAAC,OAAO,EAAE,CAAC;AACxE,IAAI,MAAM,EAAE;AACZ,GAAG;AACH,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACxD,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;AACxB;AACA;AACA,MAAM,WAAW,GAAG,EAAE;AACtB,SAAS,QAAQ,CAAC,QAAQ,EAAE,UAAU,GAAG,EAAE,EAAE;AAC7C,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,KAAK;AAChC,IAAI,IAAI,SAAS,KAAK,IAAI,EAAE;AAC5B,MAAM,YAAY,CAAC,SAAS,CAAC;AAC7B;AACA,IAAI,SAAS,GAAG,UAAU,CAAC,MAAM;AACjC,MAAM,QAAQ,CAAC,GAAG,IAAI,CAAC;AACvB,KAAK,EAAE,UAAU,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,QAAQ;AACjB;AACA,SAAS,mBAAmB,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,sBAAsB,EAAE,EAAE;AAClG,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,CAAC,MAAM;AAChE,IAAI;AACJ,EAAE,IAAI,aAAa,GAAG,WAAW,CAAC,UAAU,CAAC;AAC7C,EAAE,IAAI,aAAa,IAAI,IAAI,EAAE;AAC7B,IAAI,aAAa,GAAG,QAAQ,CAAC,kBAAkB,EAAE,+BAA+B,CAAC;AACjF,IAAI,WAAW,CAAC,UAAU,CAAC,GAAG,aAAa;AAC3C;AACA,EAAE,MAAM,gBAAgB,GAAG,CAAC,GAAG,UAAU,CAAC;AAC1C,EAAE,MAAM,6BAA6B,GAAG,IAAI,GAAG,CAAC,sBAAsB,CAAC;AACvE,EAAE,aAAa,CAAC,UAAU,EAAE,gBAAgB,EAAE,6BAA6B,EAAE,MAAM,EAAE,OAAO,CAAC;AAC7F;AACA,MAAM,cAAc,GAAG;AACvB,EAAE,OAAO,EAAE,CAAC,IAAI,KAAK;AACrB,IAAI,iBAAiB,CAAC,cAAc,CAAC;AACrC,IAAI,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC;AACvC,GAAG;AACH,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK;AAC5B,IAAI,iBAAiB,CAAC,cAAc,CAAC;AACrC,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC;AACvC;AACA,CAAC;AACD,MAAM,cAAc,CAAC;AACrB,EAAE,IAAI;AACN,EAAE,SAAS,GAAG,IAAI;AAClB,EAAE,MAAM,GAAG,EAAE;AACb,EAAE,UAAU,GAAG,EAAE;AACjB,EAAE,iBAAiB,GAAG,KAAK;AAC3B,EAAE,2BAA2B,GAAG,EAAE;AAClC,EAAE,yBAAyB,mBAAmB,IAAI,GAAG,EAAE;AACvD,EAAE,SAAS,GAAG,CAAC;AACf,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,KAAK;AACT,MAAM;AACN,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAClC,QAAQ,MAAM,IAAI,CAAC,MAAM;AACzB,QAAQ,MAAM,IAAI,CAAC;AACnB,OAAO;AACP,MAAM,MAAM;AACZ,QAAQ,OAAO,4BAA4B,CAAC;AAC5C,UAAU,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AACvC,UAAU,MAAM,EAAE,IAAI,CAAC,MAAM;AAC7B,UAAU,UAAU,EAAE,IAAI,CAAC;AAC3B,SAAS,CAAC;AACV;AACA,KAAK;AACL,IAAI,KAAK;AACT,MAAM;AACN,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO;AAC1C,QAAQ,MAAM,IAAI,CAAC,MAAM;AACzB,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAChC,OAAO;AACP,MAAM,MAAM;AACZ,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;AAC3C,QAAQ,mBAAmB,CAAC;AAC5B,UAAU,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO;AAClD,UAAU,MAAM,EAAE,IAAI,CAAC,MAAM;AAC7B,UAAU,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;AAC5C,UAAU,UAAU,EAAE,IAAI,CAAC,UAAU;AACrC,UAAU,sBAAsB,EAAE,IAAI,CAAC;AACvC,SAAS,CAAC;AACV;AACA,KAAK;AACL,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,MAAM;AAC9C,MAAM,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;AACnC,MAAM,IAAI,CAAC,iBAAiB,GAAG,KAAK;AACpC,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM;AACpC,MAAM,IAAI,YAAY,GAAG,IAAI;AAC7B,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;AACxC,QAAQ,MAAM,KAAK,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;AAClH,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,IAAI,CAAC,yBAAyB,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;AACvF,UAAU,YAAY,GAAG,KAAK,CAAC,MAAM;AACrC;AACA;AACA,MAAM,IAAI,YAAY,IAAI,IAAI,EAAE;AAChC,QAAQ,YAAY,GAAG,sBAAsB,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,EAAE,CAAC;AAC9E;AACA,MAAM,MAAM,UAAU,GAAG,uBAAuB,CAAC;AACjD,QAAQ,MAAM,EAAE,YAAY;AAC5B,QAAQ,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW;AAC/E,OAAO,CAAC;AACR,MAAM,IAAI,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;AAClD,MAAM,IAAI,CAAC,MAAM,GAAG,UAAU;AAC9B,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC;AAC9C,MAAM,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC;AACtF,KAAK,CAAC;AACN;AACA,EAAE,SAAS,GAAG,CAAC,SAAS,KAAK;AAC7B,IAAI,IAAI,CAAC,MAAM,GAAG,SAAS;AAC3B,GAAG;AACH,EAAE,oBAAoB,GAAG,CAAC,YAAY,KAAK;AAC3C,IAAI,OAAO,CAAC,CAAC,KAAK;AAClB,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO;AACnD,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;AACtC,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC1C,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO;AACjE,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM;AACpC,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU;AAC3C,MAAM,MAAM,EAAE,aAAa,EAAE,GAAG,SAAS,IAAI,EAAE;AAC/C,MAAM,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,EAAE,YAAY,CAAC;AACjE,MAAM,IAAI,KAAK,GAAG,kBAAkB,CAAC,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,CAAC;AAC7F,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE;AACvB,MAAM,MAAM,YAAY,GAAG,SAAS,KAAK,YAAY;AACrD,MAAM,IAAI,QAAQ,CAAC,GAAG,KAAK,KAAK,IAAI,YAAY,EAAE;AAClD,QAAQ,KAAK,GAAG,CAAC,KAAK;AACtB;AACA,MAAM,MAAM,eAAe,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,CAAC;AACnF,MAAM,MAAM,UAAU,GAAG,mBAAmB,CAAC;AAC7C,QAAQ,KAAK;AACb,QAAQ,MAAM,EAAE,aAAa,IAAI,UAAU;AAC3C,QAAQ,eAAe;AACvB,QAAQ,YAAY;AACpB,QAAQ,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU,GAAG;AAC7C,OAAO,CAAC;AACR,MAAM,MAAM,aAAa,GAAG,CAAC,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC;AACnE,MAAM,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE;AAC9C,QAAQ,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;AACxC,QAAQ,IAAI,SAAS,KAAK,KAAK,EAAE;AACjC,UAAU,IAAI,CAAC,SAAS,GAAG,KAAK;AAChC,UAAU,IAAI,CAAC,aAAa,EAAE;AAC9B,YAAY,IAAI,YAAY,EAAE;AAC9B,cAAc,oBAAoB,CAAC,KAAK,GAAG,CAAC,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;AACnF,aAAa,MAAM;AACnB,cAAc,oBAAoB,CAAC,KAAK,GAAG,CAAC,GAAG,cAAc,GAAG,cAAc,CAAC;AAC/E;AACA,WAAW,MAAM;AACjB,YAAY,oBAAoB,CAAC,YAAY,GAAG,YAAY,GAAG,UAAU,CAAC;AAC1E;AACA;AACA;AACA,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC;AAChD,QAAQ,iBAAiB,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC;AACtF;AACA,KAAK;AACL,GAAG;AACH,EAAE,UAAU,GAAG,CAAC,SAAS,EAAE,cAAc,KAAK;AAC9C,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM;AAClC,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU;AACtC,IAAI,MAAM,kBAAkB,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,CAAC;AACjF,IAAI,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,cAAc,CAAC,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;AACxF,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AAC5B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,EAAE,SAAS,CAAC,KAAK,UAAU,CAAC,MAAM,GAAG,CAAC;AACzF,IAAI,MAAM,KAAK,GAAG,UAAU,GAAG,QAAQ,GAAG,cAAc,GAAG,cAAc,GAAG,QAAQ;AACpF,IAAI,MAAM,UAAU,GAAG,mBAAmB,CAAC;AAC3C,MAAM,KAAK;AACX,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,YAAY;AAClB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,IAAI,IAAI,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;AAChD,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC;AAC5C,IAAI,iBAAiB,CAAC,UAAU,EAAE,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC;AAC/E,GAAG;AACH,EAAE,aAAa,GAAG,CAAC,YAAY,EAAE,CAAC,KAAK;AACvC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO;AACjD,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AAC9B,IAAI,MAAM,aAAa,GAAG,sBAAsB,CAAC,YAAY,CAAC;AAC9D,IAAI,MAAM,CAAC,aAAa,CAAC;AACzB,IAAI,MAAM,qBAAqB,GAAG,4BAA4B,CAAC,SAAS,EAAE,CAAC,CAAC;AAC5E,IAAI,IAAI,CAAC,SAAS,GAAG;AACrB,MAAM,YAAY;AAClB,MAAM,cAAc,EAAE,aAAa,CAAC,qBAAqB,EAAE;AAC3D,MAAM,qBAAqB;AAC3B,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,GAAG;AACH,EAAE,YAAY,GAAG,MAAM;AACvB,IAAI,sBAAsB,EAAE;AAC5B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI;AACzB,GAAG;AACH,EAAE,eAAe,GAAG,CAAC,IAAI,KAAK;AAC9B,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU;AACzC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AAC9B,IAAI,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC,aAAa,EAAE,IAAI,EAAE,MAAM,CAAC;AACpG,IAAI,OAAO,WAAW,KAAK,IAAI,IAAI,QAAQ,KAAK,aAAa;AAC7D,GAAG;AACH,EAAE,UAAU,GAAG,CAAC,IAAI,KAAK;AACzB,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM;AAClC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU;AACzC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;AACvC,IAAI,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,CAAC;AACtF,IAAI,MAAM;AACV,MAAM,aAAa,GAAG,CAAC;AACvB,MAAM,QAAQ;AACd,MAAM,OAAO,GAAG,CAAC;AACjB,MAAM;AACN,KAAK,GAAG,cAAc,CAAC,aAAa,EAAE,IAAI,EAAE,UAAU,CAAC;AACvD,IAAI,IAAI,QAAQ,KAAK,aAAa,EAAE;AACpC,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AACjF,IAAI,MAAM,QAAQ,GAAG,YAAY,IAAI,IAAI,IAAI,YAAY,IAAI,OAAO,GAAG,YAAY,GAAG,OAAO;AAC7F,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,GAAG,CAAC;AAC1F,IAAI,MAAM,KAAK,GAAG,UAAU,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;AACxE,IAAI,MAAM,UAAU,GAAG,mBAAmB,CAAC;AAC3C,MAAM,KAAK;AACX,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,YAAY;AAClB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,IAAI,IAAI,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;AAChD,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC;AAC5C,IAAI,iBAAiB,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC;AAClF,GAAG;AACH,EAAE,YAAY,GAAG,CAAC,IAAI,KAAK;AAC3B,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM;AAClC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU;AACzC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE;AACvC,IAAI,MAAM,oBAAoB,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,CAAC;AACtF,IAAI,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,cAAc,CAAC,aAAa,EAAE,IAAI,EAAE,UAAU,CAAC;AACzG,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC;AAC5B,IAAI,IAAI,QAAQ,KAAK,aAAa,EAAE;AACpC,IAAI,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;AACtE,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,GAAG,CAAC;AAC1F,IAAI,MAAM,KAAK,GAAG,UAAU,GAAG,QAAQ,GAAG,aAAa,GAAG,aAAa,GAAG,QAAQ;AAClF,IAAI,MAAM,UAAU,GAAG,mBAAmB,CAAC;AAC3C,MAAM,KAAK;AACX,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,eAAe,EAAE,oBAAoB;AAC3C,MAAM,YAAY;AAClB,MAAM,OAAO,EAAE;AACf,KAAK,CAAC;AACN,IAAI,IAAI,cAAc,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;AAChD,IAAI,IAAI,CAAC,MAAM,GAAG,UAAU;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC;AAC5C,IAAI,iBAAiB,CAAC,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC,2BAA2B,CAAC;AAClF,GAAG;AACH,EAAE,WAAW,GAAG,CAAC,IAAI,KAAK;AAC1B,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ;AACtE,GAAG;AACH,EAAE,YAAY,GAAG,CAAC,IAAI,EAAE,WAAW,KAAK;AACxC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU;AACzC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AAC9B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;AACpC,IAAI,MAAM,SAAS,GAAG,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC;AAC5D,IAAI,OAAO,uBAAuB,CAAC;AACnC,MAAM,WAAW;AACjB,MAAM,SAAS;AACf,MAAM,MAAM;AACZ,MAAM,UAAU,EAAE,aAAa;AAC/B,MAAM;AACN,KAAK,CAAC;AACN,GAAG;AACH,EAAE,cAAc,GAAG,CAAC,IAAI,KAAK;AAC7B,IAAI,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;AAC3G,IAAI,OAAO,CAAC,WAAW,IAAI,QAAQ,GAAG,aAAa;AACnD,GAAG;AACH,EAAE,YAAY,GAAG,CAAC,IAAI,KAAK;AAC3B,IAAI,MAAM,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;AACvD,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,KAAK,KAAK;AAC5C,MAAM,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAC7C,MAAM,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AAC7C,MAAM,IAAI,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,EAAE;AAC5C,QAAQ,OAAO,CAAC;AAChB,OAAO,MAAM,IAAI,MAAM,IAAI,IAAI,EAAE;AACjC,QAAQ,OAAO,EAAE;AACjB,OAAO,MAAM,IAAI,MAAM,IAAI,IAAI,EAAE;AACjC,QAAQ,OAAO,CAAC;AAChB,OAAO,MAAM;AACb,QAAQ,OAAO,MAAM,GAAG,MAAM;AAC9B;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,UAAU,GAAG,gBAAgB;AACtC,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI;AACjC,IAAI,OAAO,MAAM;AACjB,MAAM,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AAChD,MAAM,MAAM,KAAK,GAAG,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;AAC5D,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE;AACrB,MAAM,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AACpC,MAAM,IAAI,CAAC,UAAU,GAAG,aAAa;AACrC,MAAM,OAAO,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AACnE,MAAM,IAAI,CAAC,iBAAiB,GAAG,IAAI;AACnC,KAAK;AACL,GAAG;AACH,EAAE,+BAA+B,GAAG,MAAM;AAC1C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AACxC,IAAI,MAAM,OAAO,GAAG,+BAA+B,CAAC,OAAO,CAAC;AAC5D,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU;AACzC,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;AAClD,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,sBAAsB,CAAC;AAClE,MAAM,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI;AAChC,MAAM,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,sBAAsB,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;AAC1F,MAAM,IAAI,QAAQ,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,OAAO,IAAI;AAC1D,MAAM,MAAM,SAAS,GAAG,CAAC,CAAC,KAAK;AAC/B,QAAQ,IAAI,CAAC,CAAC,gBAAgB,IAAI,CAAC,CAAC,GAAG,KAAK,OAAO,EAAE;AACrD,QAAQ,CAAC,CAAC,cAAc,EAAE;AAC1B,QAAQ,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU;AAC9C,QAAQ,MAAM,KAAK,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,KAAK,QAAQ,CAAC;AACrG,QAAQ,IAAI,KAAK,GAAG,CAAC,EAAE;AACvB,QAAQ,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC;AAC9C,QAAQ,MAAM,CAAC,QAAQ,CAAC;AACxB,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AAClC,QAAQ,MAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC;AAClC,QAAQ,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,WAAW,EAAE,OAAO,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,WAAW;AACpF,QAAQ,IAAI,EAAE,IAAI,IAAI,IAAI,IAAI,WAAW,CAAC,EAAE;AAC5C,QAAQ,MAAM,UAAU,GAAG,mBAAmB,CAAC;AAC/C,UAAU,KAAK,EAAE,qBAAqB,CAAC,IAAI,EAAE,aAAa,CAAC,GAAG,OAAO,GAAG,IAAI,GAAG,aAAa,GAAG,IAAI;AACnG,UAAU,MAAM;AAChB,UAAU,eAAe,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,WAAW,CAAC;AACnF,UAAU,YAAY,EAAE,eAAe,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC1D,UAAU,OAAO,EAAE;AACnB,SAAS,CAAC;AACV,QAAQ,IAAI,MAAM,KAAK,UAAU,EAAE;AACnC,UAAU,IAAI,CAAC,MAAM,GAAG,UAAU;AAClC;AACA,OAAO;AACP,MAAM,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC;AAC1E,MAAM,OAAO,MAAM;AACnB,QAAQ,aAAa,EAAE;AACvB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO,MAAM;AACjB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;AACzC,QAAQ,KAAK,EAAE;AACf;AACA,KAAK;AACL,GAAG;AACH,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,iBAAiB,EAAE,EAAE;AACzB,IAAI,gBAAgB,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO;AACjD,IAAI,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC9C,IAAI,KAAK,EAAE;AACX,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,YAAY,GAAG,KAAK,GAAG,QAAQ;AACpF,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,KAAK,EAAE;AACb;AACA,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,UAAU,GAAG;AACnB,EAAE,WAAW;AACb,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,SAAS;AACX,EAAE,KAAK;AACP,EAAE;AACF,CAAC;AACD,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,KAAK,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AAC1F,EAAE,UAAU,GAAG,KAAK;AACpB,EAAE,aAAa,GAAG,IAAI;AACtB,EAAE,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AAC3B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK;AACtB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,cAAc,GAAG,CAAC,CAAC,KAAK;AAC1B,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC;AAC5C,GAAG;AACH,EAAE,oBAAoB,GAAG,MAAM;AAC/B,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AACtC,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;AAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7C,GAAG;AACH,EAAE,UAAU,GAAG,CAAC,CAAC,KAAK;AACtB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,CAAC,gBAAgB,EAAE;AACjF,IAAI,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;AACpC,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC;AAC3B,MAAM;AACN;AACA,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,IAAI,EAAE;AACxB,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,MAAM,OAAO,GAAG,+BAA+B,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AAC/E,IAAI,MAAM,KAAK,GAAG,2BAA2B,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC;AAC/F,IAAI,IAAI,KAAK,KAAK,IAAI,EAAE;AACxB,IAAI,IAAI,SAAS,GAAG,CAAC;AACrB,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE;AACpB,MAAM,IAAI,KAAK,GAAG,CAAC,EAAE;AACrB,QAAQ,SAAS,GAAG,KAAK,GAAG,CAAC;AAC7B,OAAO,MAAM;AACb,QAAQ,SAAS,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC;AACtC;AACA,KAAK,MAAM;AACX,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE;AACtC,QAAQ,SAAS,GAAG,KAAK,GAAG,CAAC;AAC7B,OAAO,MAAM;AACb,QAAQ,SAAS,GAAG,CAAC;AACrB;AACA;AACA,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC;AACzC,IAAI,UAAU,CAAC,KAAK,EAAE;AACtB,GAAG;AACH,EAAE,OAAO,GAAG,MAAM;AAClB,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK;AAC3B,GAAG;AACH,EAAE,QAAQ,GAAG,MAAM;AACnB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI;AAC1B,GAAG;AACH,EAAE,YAAY,GAAG,CAAC,CAAC,KAAK;AACxB,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;AAC1B,GAAG;AACH,EAAE,UAAU,GAAG,MAAM;AACrB,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC/B,GAAG;AACH,EAAE,cAAc,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC/B,GAAG;AACH,EAAE,WAAW,GAAG,MAAM;AACtB,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC/B,GAAG;AACH,EAAE,aAAa,GAAG,CAAC,CAAC,KAAK;AACzB,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC;AAC1B,GAAG;AACH,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO;AACvD,IAAI,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AACpD,IAAI,aAAa,EAAE,IAAI,CAAC,WAAW,EAAE,GAAG,SAAS,GAAG,IAAI,CAAC,UAAU,GAAG,UAAU,GAAG,MAAM;AACzF,IAAI,cAAc,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AAC/C,IAAI,sBAAsB,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAChD,IAAI,mBAAmB,EAAE,EAAE;AAC3B,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AACxC,IAAI,KAAK,EAAE;AACX,MAAM,MAAM,EAAE,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAC/D,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,UAAU,EAAE,MAAM;AACxB,MAAM,qBAAqB,EAAE,MAAM;AACnC,MAAM,uBAAuB,EAAE;AAC/B,KAAK;AACL,IAAI,SAAS,EAAE,IAAI,CAAC,UAAU;AAC9B,IAAI,MAAM,EAAE,IAAI,CAAC,OAAO;AACxB,IAAI,OAAO,EAAE,IAAI,CAAC,QAAQ;AAC1B,IAAI,WAAW,EAAE,IAAI,CAAC,YAAY;AAClC,IAAI,SAAS,EAAE,IAAI,CAAC,UAAU;AAC9B,IAAI,aAAa,EAAE,IAAI,CAAC,cAAc;AACtC,IAAI,UAAU,EAAE,IAAI,CAAC,WAAW;AAChC,IAAI,YAAY,EAAE,IAAI,CAAC;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,SAAS,CAAC;AAChB,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,oBAAoB,GAAG,EAAE;AAC3B,EAAE,UAAU,GAAG,OAAO,CAAC,OAAO;AAC9B,IAAI,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO;AAC5C,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AACxC,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACjC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,UAAU,EAAE;AAC5B;AACA,EAAE,IAAI,SAAS,CAAC,OAAO,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACnC;AACA,EAAE,YAAY,GAAG,OAAO,CAAC,OAAO;AAChC,IAAI,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO;AAClD,IAAI,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;AAC9C,IAAI,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;AAC9C,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;AACtC,IAAI,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAC/B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,YAAY,EAAE;AAC9B;AACA,EAAE,IAAI,WAAW,CAAC,OAAO,EAAE;AAC3B,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;AACrC;AACA,EAAE,iBAAiB,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,IAAI,CAAC,oBAAoB,GAAG,KAAK;AACrC,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AACjC,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC9C,QAAQ,MAAM,aAAa,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AACxD,QAAQ,MAAM,aAAa,GAAG,aAAa,CAAC,kBAAkB,KAAK,IAAI;AACvE,QAAQ,IAAI,CAAC,aAAa,EAAE;AAC5B,UAAU,IAAI,CAAC,oBAAoB,GAAG,EAAE;AACxC,UAAU;AACV;AACA,QAAQ,MAAM,mBAAmB,GAAG,CAAC,KAAK,KAAK;AAC/C,UAAU,IAAI,KAAK,CAAC,YAAY,KAAK,WAAW,EAAE;AAClD,YAAY,IAAI,CAAC,oBAAoB,GAAG,EAAE;AAC1C,YAAY,QAAQ,CAAC,mBAAmB,CAAC,eAAe,EAAE,mBAAmB,CAAC;AAC9E;AACA,SAAS;AACT,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,eAAe,EAAE,mBAAmB,CAAC;AACvE,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,oBAAoB,GAAG,EAAE;AACtC;AACA,KAAK,CAAC;AACN,GAAG;AACH,EAAE,IAAI,GAAG;AACT,IAAI,QAAQ,EAAE,MAAM;AACpB,MAAM,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC;AAC1C,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC;AACnC,KAAK;AACL,IAAI,MAAM,EAAE,MAAM;AAClB,MAAM,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;AACzC,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC;AAC/C,IAAI,WAAW,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC;AACvD,IAAI,UAAU,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC;AACrD,IAAI,MAAM,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC;AACvD,IAAI,KAAK,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AAC3B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK;AACtB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,MAAM;AAClD,MAAM,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,IAAI;AACzC,KAAK,CAAC;AACN;AACA,EAAE,YAAY,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAChE,EAAE,UAAU,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,oBAAoB,KAAK,EAAE,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,WAAW,GAAG,UAAU,CAAC;AAC3I,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACvE,IAAI,WAAW,EAAE,EAAE;AACnB,IAAI,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AACxC,IAAI,oBAAoB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AACpD,IAAI,gBAAgB,EAAE,IAAI,CAAC,YAAY,EAAE,GAAG,EAAE,GAAG,MAAM;AACvD,IAAI,eAAe,EAAE,IAAI,CAAC,YAAY,EAAE,GAAG,MAAM,GAAG,EAAE;AACtD,IAAI,iBAAiB,EAAE,IAAI,CAAC,UAAU;AACtC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,GAAG,IAAI,OAAO,CAAC,WAAW,CAAC;AACjD,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,OAAO,gBAAgB,CAAC,GAAG,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AACxD;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,gBAAgB,CAAC,GAAG,EAAE,CAAC;AAC5D;AACA,SAAS,OAAO,CAAC,KAAK,EAAE;AACxB,EAAE,OAAO,IAAI,SAAS,CAAC,KAAK,EAAE,gBAAgB,CAAC,GAAG,EAAE,CAAC;AACrD;AACA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,UAAU,GAAG,IAAI;AACrB,IAAI,SAAS;AACb,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,gBAAgB,GAAG,IAAI;AAC3B,IAAI,cAAc,GAAG,IAAI;AACzB,IAAI,OAAO,GAAG,cAAc;AAC5B,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,cAAc,GAAG,YAAY,CAAC;AACtC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK,EAAE,CAAC;AACrC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,UAAU,CAAC;AAC1C,IAAI,SAAS,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC;AACxC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,gBAAgB,CAAC;AACtD,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,CAAC;AAC5C,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO;AACnC,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,MAAM,cAAc,CAAC,MAAM;AAC/C,EAAE,MAAM,SAAS,GAAG,cAAc,CAAC,SAAS;AAC5C,EAAE,MAAM,KAAK,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AACpD,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,KAAK,CAAC;AACjE,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC3D,EAAE,GAAG,EAAE;AACP;AACA,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,aAAa;AACjB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,UAAU,GAAG,IAAI;AACrB,IAAI,QAAQ,GAAG,IAAI;AACnB,IAAI,QAAQ,GAAG,IAAI;AACnB,IAAI,KAAK;AACT,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC;AAC5B,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,aAAa,CAAC;AAChD,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC;AACpC,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC;AACpC,IAAI,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,UAAU,CAAC;AAC1C,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK;AAC/B,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ;AAC1C,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM;AACtC,EAAE,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO;AACxC,EAAE,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,WAAW;AAChD,EAAE,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,UAAU;AAC9C,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM;AACtC,EAAE,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK;AACpC,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,GAAG;AACP,IAAI,QAAQ;AACZ,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,MAAM;AACV,IAAI;AACJ,GAAG,CAAC;AACJ,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,gBAAgB,GAAG,IAAI;AAC3B,IAAI,QAAQ,GAAG,CAAC;AAChB,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,cAAc,CAAC;AACtC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,gBAAgB,CAAC;AACtD,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ;AACrC,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC;AAC/D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,GAAG,OAAO;AAC/C,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnD,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AAClD,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnD,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AACxC,KAAK;AACL,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnD,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AACxC;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE;AAC7B,IAAI,KAAK;AACT,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,KAAK,CAAC,QAAQ,GAAG,UAAU,CAAC;AACpC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL,EAAE,GAAG,EAAE;AACP;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC;AAC1C,MAAM;AACN,QAAQ,WAAW,EAAE,kBAAkB;AACvC,QAAQ,KAAK,EAAE,EAAE,CAAC,2iBAA2iB,EAAE,SAAS;AACxkB,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,IAAI,UAAU,EAAE;AAC1B,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,uFAAuF,CAAC;AACvH,YAAY,aAAa,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;AAC5D,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC7C,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE;AAClD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,IAAI,EAAE,SAAS,GAAG,MAAM;AAC5B,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC;AACrC,IAAI;AACJ,MAAM,WAAW,EAAE,sBAAsB;AACzC,MAAM,KAAK,EAAE,EAAE,CAAC,uDAAuD,EAAE,SAAS;AAClF,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC;AACL,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AAC/C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,QAAQ,EAAE,aAAa,EAAE,QAAQ;AACvC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,SAAS,EAAE;AAC9B,EAAE,MAAM,YAAY,GAAG;AACvB,IAAI;AACJ,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,KAAK,EAAE;AACb,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,6BAA6B;AACzC,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,2BAA2B;AACvC,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,qCAAqC;AACjD,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,8BAA8B;AAC1C,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,6BAA6B;AACzC,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,8BAA8B;AAC1C,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,6BAA6B;AACzC,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,mCAAmC;AAC/C,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,+BAA+B;AAC3C,MAAM,KAAK,EAAE,WAAW;AACxB,MAAM,IAAI,EAAE;AACZ;AACA,GAAG;AACH,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,IAAI,EAAE,0BAA0B;AACpC,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,KAAK,EAAE;AACX,GAAG;AACH,EAAE,IAAI,WAAW,GAAG,KAAK;AACzB,EAAE,MAAM,cAAc,GAAG;AACzB,IAAI,OAAO,EAAE;AACb,MAAM,aAAa,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;AAC7B,MAAM,aAAa,EAAE,CAAC;AACtB,MAAM,OAAO,EAAE,EAAE;AACjB,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,EAAE,IAAI,eAAe,GAAG,cAAc,CAAC,OAAO;AAC9C,EAAE,IAAI,aAAa,GAAG,eAAe,CAAC,aAAa;AACnD,EAAE,SAAS,CAAC,MAAM;AAClB,GAAG,CAAC;AACJ,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,EAAE;AACzC,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,KAAK,IAAI;AAChF;AACA,IAAI,OAAO,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,KAAK,IAAI,IAAI,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,GAAG,GAAG,CAAC;AACnK;AACA,EAAE,QAAQ,GAAG,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI;AACpE,EAAE,aAAa,GAAG,QAAQ,EAAE,MAAM,IAAI,QAAQ,EAAE,cAAc,IAAI,KAAK;AACvE,EAAE,QAAQ,GAAG,aAAa,GAAG,CAAC,GAAG,YAAY,EAAE,WAAW,CAAC,GAAG,YAAY;AAC1E,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0FAA0F,CAAC;AAC/G,EAAE,oBAAoB,CAAC,SAAS,EAAE;AAClC,IAAI,SAAS,EAAE,YAAY;AAC3B,IAAI,cAAc,EAAE,CAAC,KAAK,KAAK,KAAK,KAAK,aAAa,GAAG,KAAK,CAAC;AAC/D,IAAI,KAAK,EAAE,sBAAsB;AACjC,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAQ,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;AACrC,QAAQ,aAAa,EAAE,eAAe,CAAC,aAAa;AACpD,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,OAAO,EAAE,eAAe,CAAC,OAAO;AACxC,QAAQ,OAAO,EAAE,eAAe,CAAC,OAAO;AACxC,QAAQ,UAAU,EAAE,MAAM,WAAW,GAAG,IAAI;AAC5C,QAAQ,QAAQ,EAAE,MAAM,WAAW,GAAG,KAAK;AAC3C,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,MAAM,UAAU,GAAG,iBAAiB,CAAC,QAAQ,CAAC;AACxD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,sCAAsC,EAAE,WAAW,GAAG,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,iDAAiD,CAAC;AACpL,UAAU,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AAC7F,YAAY,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,UAAU,CAAC,OAAO,CAAC;AAClE,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,gFAAgF,EAAE,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,+BAA+B,GAAG,0CAA0C,EAAE,WAAW,GAAG,qBAAqB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtT,YAAY,IAAI,IAAI,EAAE;AACtB,cAAc,UAAU,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,cAAc,IAAI,GAAG,UAAU,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;AAClF,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa,MAAM;AACnB,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACzC,YAAY,IAAI,CAAC,WAAW,EAAE;AAC9B,cAAc,UAAU,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC;AACpE,aAAa,MAAM;AACnB,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AAC5C;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACvC,UAAU;AACV,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,SAAS,EAAE,WAAW,GAAG,qBAAqB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,yHAAyH,EAAE,WAAW,GAAG,yBAAyB,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,mBAAmB,EAAE,WAAW,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACha,UAAU,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE,CAAC;AAC/E,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,IAAI,CAAC,WAAW,EAAE;AAC5B,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACpD,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,mCAAmC,CAAC;AACjE,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM,gBAAgB,CAAC,UAAU,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AACxD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAQ,WAAW,EAAE,aAAa,CAAC,CAAC,CAAC;AACrC,QAAQ,OAAO,EAAE,EAAE;AACnB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,wDAAwD,CAAC;AACtF,UAAU,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACxD,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC3C,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACzC,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,GAAG,EAAE;AACP;;;;"}