import { Stripe } from './stripe.esm.node-BpZO3rKl.js';

const isProd = process.env.NODE_ENV === "production";
const stripeSecret = isProd ? process.env.STRIPE_SECRET_KEY_LIVE || "sk_live_placeholder" : process.env.STRIPE_SECRET_KEY_TEST || "sk_test_placeholder";
const stripe = new Stripe(stripeSecret, {
  apiVersion: "2025-04-30.basil"
});
async function syncPlanProduct(plan) {
  try {
    const existingProducts = await stripe.products.list({
      active: true,
      expand: ["data.default_price"]
    });
    const existingProduct = existingProducts.data.find(
      (product) => product.metadata.plan_id === plan.id
    );
    if (existingProduct) {
      return await stripe.products.update(existingProduct.id, {
        name: plan.name,
        description: plan.description || void 0,
        metadata: {
          plan_id: plan.id,
          section: plan.section
        }
      });
    } else {
      return await stripe.products.create({
        name: plan.name,
        description: plan.description || void 0,
        metadata: {
          plan_id: plan.id,
          section: plan.section
        }
      });
    }
  } catch (error) {
    console.error("Error syncing plan product:", error);
    throw new Error(`Failed to sync plan product: ${error.message}`);
  }
}
async function syncPlanPrices(plan, productId) {
  try {
    const existingPrices = await stripe.prices.list({
      product: productId,
      active: true
    });
    const existingMonthlyPrice = existingPrices.data.find(
      (price) => price.recurring?.interval === "month"
    );
    const existingYearlyPrice = existingPrices.data.find(
      (price) => price.recurring?.interval === "year"
    );
    let monthlyPrice;
    if (existingMonthlyPrice && existingMonthlyPrice.unit_amount !== plan.monthlyPrice) {
      await stripe.prices.update(existingMonthlyPrice.id, { active: false });
      monthlyPrice = await stripe.prices.create({
        product: productId,
        unit_amount: plan.monthlyPrice,
        currency: "usd",
        recurring: { interval: "month" },
        metadata: {
          plan_id: plan.id
        }
      });
    } else if (!existingMonthlyPrice) {
      monthlyPrice = await stripe.prices.create({
        product: productId,
        unit_amount: plan.monthlyPrice,
        currency: "usd",
        recurring: { interval: "month" },
        metadata: {
          plan_id: plan.id
        }
      });
    } else {
      monthlyPrice = existingMonthlyPrice;
    }
    let yearlyPrice;
    if (existingYearlyPrice && existingYearlyPrice.unit_amount !== plan.annualPrice) {
      await stripe.prices.update(existingYearlyPrice.id, { active: false });
      yearlyPrice = await stripe.prices.create({
        product: productId,
        unit_amount: plan.annualPrice,
        currency: "usd",
        recurring: { interval: "year" },
        metadata: {
          plan_id: plan.id
        }
      });
    } else if (!existingYearlyPrice) {
      yearlyPrice = await stripe.prices.create({
        product: productId,
        unit_amount: plan.annualPrice,
        currency: "usd",
        recurring: { interval: "year" },
        metadata: {
          plan_id: plan.id
        }
      });
    } else {
      yearlyPrice = existingYearlyPrice;
    }
    return {
      monthlyPriceId: monthlyPrice.id,
      yearlyPriceId: yearlyPrice.id
    };
  } catch (error) {
    console.error("Error syncing plan prices:", error);
    throw new Error(`Failed to sync plan prices: ${error.message}`);
  }
}
async function syncPlanWithStripe(plan) {
  try {
    const product = await syncPlanProduct(plan);
    const { monthlyPriceId, yearlyPriceId } = await syncPlanPrices(plan, product.id);
    return {
      ...plan,
      stripePriceMonthlyId: monthlyPriceId,
      stripePriceYearlyId: yearlyPriceId
    };
  } catch (error) {
    console.error("Error syncing plan with Stripe:", error);
    throw new Error(`Failed to sync plan with Stripe: ${error.message}`);
  }
}

export { stripe as a, syncPlanWithStripe as s };
//# sourceMappingURL=stripe-Dj5-FP5W.js.map
