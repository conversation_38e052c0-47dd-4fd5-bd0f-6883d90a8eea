import { r as redirect, f as fail } from './index-Ddp2AB5f.js';
import { p as prisma } from './prisma-Cit_HrSw.js';
import { c as calculateProfileCompletion } from './profileHelpers-m3Uw-RPd.js';
import { s as superValidate, z as zod } from './zod-DfpldWlD.js';
import { o as objectType, a as arrayType, s as stringType, b as booleanType, n as numberType } from './types-D78SXuvm.js';

const automationFormSchema = objectType({
  profileId: stringType().min(1, "Please select a profile"),
  keywords: arrayType(stringType()).min(0),
  locations: arrayType(stringType()).min(0),
  maxJobsToApply: numberType().min(1).max(50).default(10),
  minMatchScore: numberType().min(60).max(95).default(70),
  autoApplyEnabled: booleanType().default(false),
  salaryRange: arrayType(numberType()).length(2).default([50, 120]),
  experienceRange: arrayType(numberType()).length(2).default([2, 8]),
  jobTypes: arrayType(stringType()).default([]),
  remotePreference: stringType().default("any"),
  companySizePreference: arrayType(stringType()).default([]),
  excludeCompanies: arrayType(stringType()).default([]),
  preferredCompanies: arrayType(stringType()).default([])
}).refine((data) => data.keywords.length > 0 || data.locations.length > 0, {
  message: "Please select at least one keyword or location",
  path: ["keywords"]
}).refine((data) => data.salaryRange[0] <= data.salaryRange[1], {
  message: "Minimum salary cannot be greater than maximum salary",
  path: ["salaryRange"]
}).refine((data) => data.experienceRange[0] <= data.experienceRange[1], {
  message: "Minimum experience cannot be greater than maximum experience",
  path: ["experienceRange"]
});

const load = async ({ locals }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  locals.user = user;
  const form = await superValidate(zod(automationFormSchema));
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          team: {
            members: {
              some: { userId: user.id }
            }
          }
        }
      ]
    },
    include: {
      data: true,
      team: true,
      // Include documents of type resume associated with this profile
      documents: {
        where: {
          type: "resume"
        }
      }
    }
  });
  const automationRuns = await prisma.automationRun.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          profile: {
            team: {
              members: {
                some: { userId: user.id }
              }
            }
          }
        }
      ]
    },
    include: {
      profile: {
        include: {
          data: true,
          documents: {
            where: {
              type: "resume"
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: "desc"
    }
  });
  const documents = await prisma.document.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          profile: {
            team: {
              members: {
                some: { userId: user.id }
              }
            }
          }
        }
      ],
      type: "resume"
    },
    select: {
      id: true,
      label: true,
      fileName: true,
      createdAt: true
    }
  });
  const resumes = documents.map((doc) => ({
    id: doc.id,
    label: doc.label || doc.fileName || `Resume (${new Date(doc.createdAt).toLocaleDateString()})`
  }));
  const profilesWithCompletion = profiles.map((profile) => {
    let completionPercentage = 0;
    if (profile.data?.data) {
      let profileData = profile.data.data;
      if (typeof profileData === "string") {
        try {
          profileData = JSON.parse(profileData);
        } catch (e) {
          console.error("Error parsing profile data JSON:", e);
          profileData = {};
        }
      }
      completionPercentage = calculateProfileCompletion(profileData);
    }
    return {
      ...profile,
      completionPercentage
    };
  });
  const occupations = await prisma.occupation.findMany({
    select: {
      id: true,
      title: true,
      shortTitle: true,
      category: true
    },
    take: 500,
    orderBy: {
      title: "asc"
    }
  });
  const locations = await prisma.city.findMany({
    where: {
      state: {
        country: {
          isoCode: "US"
        }
      }
    },
    include: {
      state: true
    },
    orderBy: {
      name: "asc"
    },
    take: 100
  });
  const formattedLocations = locations.map((city) => ({
    id: city.id,
    name: city.name,
    state: {
      id: city.state.id,
      name: city.state.name,
      code: city.state.code || ""
    },
    country: "US"
  }));
  return {
    user,
    profiles: profilesWithCompletion,
    automationRuns,
    resumes,
    form,
    occupations,
    locations: formattedLocations
  };
};
const actions = {
  default: async ({ request, locals }) => {
    const user = locals.user;
    if (!user) {
      throw redirect(302, "/auth/sign-in");
    }
    const form = await superValidate(request, zod(automationFormSchema));
    if (!form.valid) {
      return fail(400, { form });
    }
    try {
      console.log("Creating automation run with data:", {
        userId: user.id,
        profileId: form.data.profileId,
        keywords: form.data.keywords,
        locations: form.data.locations,
        maxJobsToApply: form.data.maxJobsToApply,
        minMatchScore: form.data.minMatchScore,
        autoApplyEnabled: form.data.autoApplyEnabled,
        salaryRange: form.data.salaryRange,
        experienceRange: form.data.experienceRange
      });
      const automationRun = await prisma.automationRun.create({
        data: {
          userId: user.id,
          profileId: form.data.profileId,
          keywords: form.data.keywords.join(", "),
          location: form.data.locations.join(", "),
          maxJobsToApply: form.data.maxJobsToApply,
          minMatchScore: form.data.minMatchScore,
          autoApplyEnabled: form.data.autoApplyEnabled,
          salaryMin: form.data.salaryRange[0] * 1e3,
          salaryMax: form.data.salaryRange[1] * 1e3,
          experienceLevelMin: form.data.experienceRange[0],
          experienceLevelMax: form.data.experienceRange[1],
          jobTypes: form.data.jobTypes,
          remotePreference: form.data.remotePreference,
          companySizePreference: form.data.companySizePreference,
          excludeCompanies: form.data.excludeCompanies,
          preferredCompanies: form.data.preferredCompanies,
          specifications: {
            advancedFiltering: true,
            profileMatchingEnabled: true,
            intelligentScoring: true
          },
          status: "pending"
        }
      });
      console.log("Automation run created successfully:", automationRun.id);
      throw redirect(302, `/dashboard/automation/${automationRun.id}`);
    } catch (error) {
      console.error("Error creating automation run:", error);
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }
      if (error?.status === 302) {
        throw error;
      }
      return fail(500, {
        form,
        error: "Failed to create automation run: " + (error instanceof Error ? error.message : "Unknown error")
      });
    }
  }
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  actions: actions,
  load: load
});

const index = 27;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-fYREhXre.js')).default;
const server_id = "src/routes/dashboard/automation/+page.server.ts";
const imports = ["_app/immutable/nodes/27.D7b-pyfX.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/CTn0v-X8.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/DrGkVJ95.js","_app/immutable/chunks/D9yI7a4E.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/BnikQ10_.js","_app/immutable/chunks/BPr9JIwg.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/BwkAotBa.js","_app/immutable/chunks/C2MdR6K0.js","_app/immutable/chunks/hQ6uUXJy.js","_app/immutable/chunks/C4zOxlM4.js","_app/immutable/chunks/B8CsXmVA.js","_app/immutable/chunks/CQeqUgF6.js","_app/immutable/chunks/qwsZpUIl.js","_app/immutable/chunks/KVutzy_p.js","_app/immutable/chunks/D6Qh9vtB.js","_app/immutable/chunks/CZ8wIJN8.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/CDnvByek.js","_app/immutable/chunks/C2AK_5VT.js","_app/immutable/chunks/CwgkX8t9.js","_app/immutable/chunks/6BxQgNmX.js","_app/immutable/chunks/DZCYCPd3.js","_app/immutable/chunks/-SpbofVw.js","_app/immutable/chunks/BAIxhb6t.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/DvO_AOqy.js","_app/immutable/chunks/DDUgF6Ik.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/DuGukytH.js","_app/immutable/chunks/Cdn-N1RY.js","_app/immutable/chunks/BkJY4La4.js","_app/immutable/chunks/DETxXRrJ.js","_app/immutable/chunks/GwmmX_iF.js","_app/immutable/chunks/D50jIuLr.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/P6MDDUUJ.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/Ci8yIwIB.js","_app/immutable/chunks/3WmhYGjL.js","_app/immutable/chunks/Cf6rS4LV.js","_app/immutable/chunks/B6TiSgAN.js","_app/immutable/chunks/Dmwghw4a.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/DW5gea7N.js","_app/immutable/chunks/B5tu6DNS.js","_app/immutable/chunks/BNEH2jqx.js","_app/immutable/chunks/BMZasLyv.js","_app/immutable/chunks/iTBjRg9v.js","_app/immutable/chunks/YNp1uWxB.js","_app/immutable/chunks/DrHxToS6.js","_app/immutable/chunks/Bjxev4T5.js","_app/immutable/chunks/CTO_B1Jk.js","_app/immutable/chunks/DHNQRrgO.js","_app/immutable/chunks/B3MJtjra.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/B2lQHLf_.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/DR5zc253.js","_app/immutable/chunks/Dqigtbi1.js","_app/immutable/chunks/BG1dFdGG.js","_app/immutable/chunks/Z9Zpt0fH.js","_app/immutable/chunks/CrHU05dq.js","_app/immutable/chunks/C8B1VUaq.js","_app/immutable/chunks/I7hvcB12.js","_app/immutable/chunks/C88uNE8B.js","_app/immutable/chunks/B_6ivTD3.js","_app/immutable/chunks/DmZyh-PW.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/scroll-area.bHHIbcsu.css","_app/immutable/assets/index.CV-KWLNP.css"];
const fonts = [];

var _27 = /*#__PURE__*/Object.freeze({
  __proto__: null,
  component: component,
  fonts: fonts,
  imports: imports,
  index: index,
  server: _page_server_ts,
  server_id: server_id,
  stylesheets: stylesheets
});

export { _27 as _, automationFormSchema as a };
//# sourceMappingURL=27-BcBmGlBo.js.map
