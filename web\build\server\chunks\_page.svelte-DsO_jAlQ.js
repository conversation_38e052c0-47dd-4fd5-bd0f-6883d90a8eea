import { p as push, M as ensure_array_like, O as escape_html, N as attr, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import 'clsx';
import './false-CRHihH2U.js';
import './sanityClient-BQ6Z_2a-.js';
import '@sanity/client';
import './html-FW6Ia4bL.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const { newsCoveragePage, newsCoverage } = data;
  function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  }
  SEO($$payload, {
    title: newsCoveragePage?.seo?.metaTitle || "News Coverage | Hirli",
    description: newsCoveragePage?.seo?.metaDescription || "Latest news coverage about Hirli from various media outlets.",
    keywords: newsCoveragePage?.seo?.keywords?.join(", ") || "news coverage, media coverage, press, Hirli"
  });
  $$payload.out += `<!----> <div><h2 class="mb-8 text-3xl font-semibold">News Coverage</h2> `;
  if (newsCoveragePage?.content) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-8">`;
    PortableText($$payload, { value: newsCoveragePage.content });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="text-muted-foreground mb-6">Find the latest news coverage about Hirli from various media outlets.</p>`;
  }
  $$payload.out += `<!--]--> `;
  if (newsCoverage && newsCoverage.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(newsCoverage);
    $$payload.out += `<div class="space-y-6"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let coverage = each_array[$$index];
      $$payload.out += `<div class="border-b pb-6"><div class="flex flex-col md:flex-row md:items-center md:justify-between"><div><p class="text-muted-foreground mb-2 text-sm">${escape_html(formatDate(coverage.publishedAt))}</p> <h3 class="mb-2 text-xl font-medium">${escape_html(coverage.publicationName)}</h3> <p class="text-muted-foreground mb-4">${escape_html(coverage.title)}</p> `;
      if (coverage.excerpt) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="mb-4 text-sm text-gray-600">${escape_html(coverage.excerpt)}</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--></div> <div class="mt-4 md:ml-4 md:mt-0"><a${attr("href", coverage.externalUrl)} target="_blank" rel="noopener noreferrer" class="text-primary inline-flex items-center text-sm font-medium hover:underline">View Original Article `;
      External_link($$payload, { class: "ml-1 h-3 w-3" });
      $$payload.out += `<!----></a></div></div></div>`;
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="py-8 text-center"><p class="text-gray-500">No news coverage available at this time.</p></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DsO_jAlQ.js.map
