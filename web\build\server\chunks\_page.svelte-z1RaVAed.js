import { p as push, O as escape_html, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import 'clsx';
import './false-CRHihH2U.js';
import './sanityClient-BQ6Z_2a-.js';
import '@sanity/client';
import './html-FW6Ia4bL.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const { legalPage } = data;
  const formattedDate = legalPage?.updatedAt ? new Date(legalPage.updatedAt).toLocaleDateString("en-US", {
    month: "long",
    day: "numeric",
    year: "numeric"
  }) : null;
  SEO($$payload, {
    title: `${legalPage.title} | Hirli`,
    description: legalPage.description,
    keywords: `${legalPage.title.toLowerCase()}, legal, Hirli`
  });
  $$payload.out += `<!----> <div class="max-w-none"><h1 class="mb-4 text-2xl font-bold">${escape_html(legalPage.title)}</h1> `;
  if (formattedDate) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mb-6 text-gray-500">Last updated: ${escape_html(formattedDate)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (legalPage.content) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="prose max-w-none">`;
    PortableText($$payload, { value: legalPage.content });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="prose max-w-none"><p class="mb-6 rounded-lg border border-amber-200 bg-amber-50 p-4 text-amber-700">This content is not yet available in the CMS. Please add content for this page in Sanity.</p> <p>This is a placeholder for the ${escape_html(legalPage.title)} page. The actual content should be added in the
        Sanity CMS.</p> <p>To add content to this page:</p> <ol><li>Go to your Sanity Studio</li> <li>Find or create a page with the slug "${escape_html(legalPage.slug)}"</li> <li>Add your content using the rich text editor</li> <li>Publish the changes</li></ol></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-z1RaVAed.js.map
