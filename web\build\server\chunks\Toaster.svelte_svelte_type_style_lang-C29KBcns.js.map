{"version": 3, "file": "Toaster.svelte_svelte_type_style_lang-C29KBcns.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/Toaster.svelte_svelte_type_style_lang.js"], "sourcesContent": ["import { w as writable, g as get } from \"./index2.js\";\nfunction cn(...classes) {\n  return classes.filter(Boolean).join(\" \");\n}\nconst isBrowser = typeof document !== \"undefined\";\nfunction clientWritable(initialValue) {\n  const store = writable(initialValue);\n  function set(value) {\n    if (isBrowser) {\n      store.set(value);\n    }\n  }\n  function update(updater) {\n    if (isBrowser) {\n      store.update(updater);\n    }\n  }\n  return {\n    subscribe: store.subscribe,\n    set,\n    update\n  };\n}\nlet toastsCounter = 0;\nfunction createToastState() {\n  const toasts = clientWritable([]);\n  const heights = clientWritable([]);\n  function addToast(data) {\n    toasts.update((prev) => [data, ...prev]);\n  }\n  function create(data) {\n    const { message: message2, ...rest } = data;\n    const id = typeof data?.id === \"number\" || data.id && data.id?.length > 0 ? data.id : toastsCounter++;\n    const dismissable = data.dismissable === void 0 ? true : data.dismissable;\n    const type = data.type === void 0 ? \"default\" : data.type;\n    const $toasts = get(toasts);\n    const alreadyExists = $toasts.find((toast2) => {\n      return toast2.id === id;\n    });\n    if (alreadyExists) {\n      toasts.update((prev) => prev.map((toast2) => {\n        if (toast2.id === id) {\n          return {\n            ...toast2,\n            ...data,\n            id,\n            title: message2,\n            dismissable,\n            type,\n            updated: true\n          };\n        }\n        return {\n          ...toast2,\n          updated: false\n        };\n      }));\n    } else {\n      addToast({ ...rest, id, title: message2, dismissable, type });\n    }\n    return id;\n  }\n  function dismiss(id) {\n    if (id === void 0) {\n      toasts.update((prev) => prev.map((toast2) => ({ ...toast2, dismiss: true })));\n      return;\n    }\n    toasts.update((prev) => prev.map((toast2) => toast2.id === id ? { ...toast2, dismiss: true } : toast2));\n    return id;\n  }\n  function remove(id) {\n    if (id === void 0) {\n      toasts.set([]);\n      return;\n    }\n    toasts.update((prev) => prev.filter((toast2) => toast2.id !== id));\n    return id;\n  }\n  function message(message2, data) {\n    return create({ ...data, type: \"default\", message: message2 });\n  }\n  function error(message2, data) {\n    return create({ ...data, type: \"error\", message: message2 });\n  }\n  function success(message2, data) {\n    return create({ ...data, type: \"success\", message: message2 });\n  }\n  function info(message2, data) {\n    return create({ ...data, type: \"info\", message: message2 });\n  }\n  function warning(message2, data) {\n    return create({ ...data, type: \"warning\", message: message2 });\n  }\n  function loading(message2, data) {\n    return create({ ...data, type: \"loading\", message: message2 });\n  }\n  function promise(promise2, data) {\n    if (!data) {\n      return;\n    }\n    let id = void 0;\n    if (data.loading !== void 0) {\n      id = create({\n        ...data,\n        promise: promise2,\n        type: \"loading\",\n        message: data.loading\n      });\n    }\n    const p = promise2 instanceof Promise ? promise2 : promise2();\n    let shouldDismiss = id !== void 0;\n    p.then((response) => {\n      if (response && typeof response.ok === \"boolean\" && !response.ok) {\n        shouldDismiss = false;\n        const message2 = typeof data.error === \"function\" ? (\n          // @ts-expect-error: Incorrect response type\n          data.error(`HTTP error! status: ${response.status}`)\n        ) : data.error;\n        create({ id, type: \"error\", message: message2 });\n      } else if (data.success !== void 0) {\n        shouldDismiss = false;\n        const message2 = (\n          // @ts-expect-error: TODO: Better function checking\n          typeof data.success === \"function\" ? data.success(response) : data.success\n        );\n        create({ id, type: \"success\", message: message2 });\n      }\n    }).catch((error2) => {\n      if (data.error !== void 0) {\n        shouldDismiss = false;\n        const message2 = (\n          // @ts-expect-error: TODO: Better function checking\n          typeof data.error === \"function\" ? data.error(error2) : data.error\n        );\n        create({ id, type: \"error\", message: message2 });\n      }\n    }).finally(() => {\n      if (shouldDismiss) {\n        dismiss(id);\n        id = void 0;\n      }\n      data.finally?.();\n    });\n    return id;\n  }\n  function custom(component, data) {\n    const id = data?.id || toastsCounter++;\n    create({ component, id, ...data });\n    return id;\n  }\n  function removeHeight(id) {\n    heights.update((prev) => prev.filter((height) => height.toastId !== id));\n  }\n  function setHeight(data) {\n    const exists = get(heights).find((el) => el.toastId === data.toastId);\n    if (exists === void 0) {\n      heights.update((prev) => [data, ...prev]);\n      return;\n    }\n    heights.update((prev) => prev.map((el) => {\n      if (el.toastId === data.toastId) {\n        return data;\n      } else {\n        return el;\n      }\n    }));\n  }\n  function reset() {\n    toasts.set([]);\n    heights.set([]);\n  }\n  return {\n    // methods\n    create,\n    addToast,\n    dismiss,\n    remove,\n    message,\n    error,\n    success,\n    info,\n    warning,\n    loading,\n    promise,\n    custom,\n    removeHeight,\n    setHeight,\n    reset,\n    // stores\n    toasts,\n    heights\n  };\n}\nconst toastState = createToastState();\nfunction toastFunction(message, data) {\n  return toastState.create({\n    message,\n    ...data\n  });\n}\nconst basicToast = toastFunction;\nconst toast = Object.assign(basicToast, {\n  success: toastState.success,\n  info: toastState.info,\n  warning: toastState.warning,\n  error: toastState.error,\n  custom: toastState.custom,\n  message: toastState.message,\n  promise: toastState.promise,\n  dismiss: toastState.dismiss,\n  loading: toastState.loading\n});\nconst useEffect = (subscribe) => ({ subscribe });\nexport {\n  toast as a,\n  cn as c,\n  toastState as t,\n  useEffect as u\n};\n"], "names": [], "mappings": ";;AACA,SAAS,EAAE,CAAC,GAAG,OAAO,EAAE;AACxB,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAC1C;AACA,MAAM,SAAS,GAAG,OAAO,QAAQ,KAAK,WAAW;AACjD,SAAS,cAAc,CAAC,YAAY,EAAE;AACtC,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC;AACtC,EAAE,SAAS,GAAG,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AACtB;AACA;AACA,EAAE,SAAS,MAAM,CAAC,OAAO,EAAE;AAC3B,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC;AAC3B;AACA;AACA,EAAE,OAAO;AACT,IAAI,SAAS,EAAE,KAAK,CAAC,SAAS;AAC9B,IAAI,GAAG;AACP,IAAI;AACJ,GAAG;AACH;AACA,IAAI,aAAa,GAAG,CAAC;AACrB,SAAS,gBAAgB,GAAG;AAC5B,EAAE,MAAM,MAAM,GAAG,cAAc,CAAC,EAAE,CAAC;AACnC,EAAE,MAAM,OAAO,GAAG,cAAc,CAAC,EAAE,CAAC;AACpC,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE;AAC1B,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAC5C;AACA,EAAE,SAAS,MAAM,CAAC,IAAI,EAAE;AACxB,IAAI,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI;AAC/C,IAAI,MAAM,EAAE,GAAG,OAAO,IAAI,EAAE,EAAE,KAAK,QAAQ,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,EAAE,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,aAAa,EAAE;AACzG,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,KAAK,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,WAAW;AAC7E,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG,IAAI,CAAC,IAAI;AAC7D,IAAI,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC;AAC/B,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK;AACnD,MAAM,OAAO,MAAM,CAAC,EAAE,KAAK,EAAE;AAC7B,KAAK,CAAC;AACN,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;AACnD,QAAQ,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE;AAC9B,UAAU,OAAO;AACjB,YAAY,GAAG,MAAM;AACrB,YAAY,GAAG,IAAI;AACnB,YAAY,EAAE;AACd,YAAY,KAAK,EAAE,QAAQ;AAC3B,YAAY,WAAW;AACvB,YAAY,IAAI;AAChB,YAAY,OAAO,EAAE;AACrB,WAAW;AACX;AACA,QAAQ,OAAO;AACf,UAAU,GAAG,MAAM;AACnB,UAAU,OAAO,EAAE;AACnB,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;AACnE;AACA,IAAI,OAAO,EAAE;AACb;AACA,EAAE,SAAS,OAAO,CAAC,EAAE,EAAE;AACvB,IAAI,IAAI,EAAE,KAAK,MAAM,EAAE;AACvB,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,MAAM,EAAE,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AACnF,MAAM;AACN;AACA,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC;AAC3G,IAAI,OAAO,EAAE;AACb;AACA,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE;AACtB,IAAI,IAAI,EAAE,KAAK,MAAM,EAAE;AACvB,MAAM,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;AACpB,MAAM;AACN;AACA,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACtE,IAAI,OAAO,EAAE;AACb;AACA,EAAE,SAAS,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE;AACnC,IAAI,OAAO,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAClE;AACA,EAAE,SAAS,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE;AACjC,IAAI,OAAO,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAChE;AACA,EAAE,SAAS,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE;AACnC,IAAI,OAAO,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAClE;AACA,EAAE,SAAS,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE;AAChC,IAAI,OAAO,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAC/D;AACA,EAAE,SAAS,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE;AACnC,IAAI,OAAO,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAClE;AACA,EAAE,SAAS,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE;AACnC,IAAI,OAAO,MAAM,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAClE;AACA,EAAE,SAAS,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE;AACnC,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM;AACN;AACA,IAAI,IAAI,EAAE,GAAG,MAAM;AACnB,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,EAAE;AACjC,MAAM,EAAE,GAAG,MAAM,CAAC;AAClB,QAAQ,GAAG,IAAI;AACf,QAAQ,OAAO,EAAE,QAAQ;AACzB,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,OAAO,EAAE,IAAI,CAAC;AACtB,OAAO,CAAC;AACR;AACA,IAAI,MAAM,CAAC,GAAG,QAAQ,YAAY,OAAO,GAAG,QAAQ,GAAG,QAAQ,EAAE;AACjE,IAAI,IAAI,aAAa,GAAG,EAAE,KAAK,MAAM;AACrC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,QAAQ,KAAK;AACzB,MAAM,IAAI,QAAQ,IAAI,OAAO,QAAQ,CAAC,EAAE,KAAK,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE;AACxE,QAAQ,aAAa,GAAG,KAAK;AAC7B,QAAQ,MAAM,QAAQ,GAAG,OAAO,IAAI,CAAC,KAAK,KAAK,UAAU;AACzD;AACA,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC7D,YAAY,IAAI,CAAC,KAAK;AACtB,QAAQ,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AACxD,OAAO,MAAM,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM,EAAE;AAC1C,QAAQ,aAAa,GAAG,KAAK;AAC7B,QAAQ,MAAM,QAAQ;AACtB;AACA,UAAU,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC;AAC7E,SAAS;AACT,QAAQ,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AAC1D;AACA,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK;AACzB,MAAM,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;AACjC,QAAQ,aAAa,GAAG,KAAK;AAC7B,QAAQ,MAAM,QAAQ;AACtB;AACA,UAAU,OAAO,IAAI,CAAC,KAAK,KAAK,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AACvE,SAAS;AACT,QAAQ,MAAM,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;AACxD;AACA,KAAK,CAAC,CAAC,OAAO,CAAC,MAAM;AACrB,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,OAAO,CAAC,EAAE,CAAC;AACnB,QAAQ,EAAE,GAAG,MAAM;AACnB;AACA,MAAM,IAAI,CAAC,OAAO,IAAI;AACtB,KAAK,CAAC;AACN,IAAI,OAAO,EAAE;AACb;AACA,EAAE,SAAS,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE;AACnC,IAAI,MAAM,EAAE,GAAG,IAAI,EAAE,EAAE,IAAI,aAAa,EAAE;AAC1C,IAAI,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,GAAG,IAAI,EAAE,CAAC;AACtC,IAAI,OAAO,EAAE;AACb;AACA,EAAE,SAAS,YAAY,CAAC,EAAE,EAAE;AAC5B,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,OAAO,KAAK,EAAE,CAAC,CAAC;AAC5E;AACA,EAAE,SAAS,SAAS,CAAC,IAAI,EAAE;AAC3B,IAAI,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC;AACzE,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;AAC/C,MAAM;AACN;AACA,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK;AAC9C,MAAM,IAAI,EAAE,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;AACvC,QAAQ,OAAO,IAAI;AACnB,OAAO,MAAM;AACb,QAAQ,OAAO,EAAE;AACjB;AACA,KAAK,CAAC,CAAC;AACP;AACA,EAAE,SAAS,KAAK,GAAG;AACnB,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC;AAClB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;AACnB;AACA,EAAE,OAAO;AACT;AACA,IAAI,MAAM;AACV,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,OAAO;AACX,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,IAAI;AACR,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,MAAM;AACV,IAAI,YAAY;AAChB,IAAI,SAAS;AACb,IAAI,KAAK;AACT;AACA,IAAI,MAAM;AACV,IAAI;AACJ,GAAG;AACH;AACK,MAAC,UAAU,GAAG,gBAAgB;AACnC,SAAS,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE;AACtC,EAAE,OAAO,UAAU,CAAC,MAAM,CAAC;AAC3B,IAAI,OAAO;AACX,IAAI,GAAG;AACP,GAAG,CAAC;AACJ;AACA,MAAM,UAAU,GAAG,aAAa;AAC3B,MAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE;AACxC,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO;AAC7B,EAAE,IAAI,EAAE,UAAU,CAAC,IAAI;AACvB,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO;AAC7B,EAAE,KAAK,EAAE,UAAU,CAAC,KAAK;AACzB,EAAE,MAAM,EAAE,UAAU,CAAC,MAAM;AAC3B,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO;AAC7B,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO;AAC7B,EAAE,OAAO,EAAE,UAAU,CAAC,OAAO;AAC7B,EAAE,OAAO,EAAE,UAAU,CAAC;AACtB,CAAC;AACI,MAAC,SAAS,GAAG,CAAC,SAAS,MAAM,EAAE,SAAS,EAAE;;;;"}