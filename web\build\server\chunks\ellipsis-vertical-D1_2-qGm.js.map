{"version": 3, "file": "ellipsis-vertical-D1_2-qGm.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/ellipsis-vertical.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Ellipsis_vertical($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\n      \"circle\",\n      { \"cx\": \"12\", \"cy\": \"12\", \"r\": \"1\" }\n    ],\n    [\"circle\", { \"cx\": \"12\", \"cy\": \"5\", \"r\": \"1\" }],\n    [\n      \"circle\",\n      { \"cx\": \"12\", \"cy\": \"19\", \"r\": \"1\" }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"ellipsis-vertical\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Ellipsis_vertical as E\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AACxC,KAAK;AACL,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;AACnD,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;AACxC;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE;AACjC,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}