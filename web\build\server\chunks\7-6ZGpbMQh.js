import { c as client } from './client2-BLTPQNYX.js';
import '@sanity/client';

const load = async () => {
  try {
    const result = await client.fetch(`
      {
        "legalPages": *[_type == "page" && pageType == "legal" && defined(slug.current)] | order(title asc) {
          title,
          description,
          "slug": slug.current
        },
        "legalContact": *[_type == "page" && pageType == "legal"][0].legalContact
      }
    `);
    console.log("Legal pages found:", result.legalPages?.length || 0);
    if (result.legalPages?.length) {
      console.log("First legal page:", result.legalPages[0]);
    }
    return {
      legalPages: result.legalPages || [],
      legalContact: result.legalContact || {
        email: "<EMAIL>",
        message: "If you have questions about our legal documents, please contact our legal team."
      }
    };
  } catch (error) {
    console.error("Error loading legal navigation:", error);
    return {
      legalPages: [
        { title: "Privacy Policy", slug: "privacy" },
        { title: "Terms of Service", slug: "terms" },
        { title: "Cookie Policy", slug: "cookies" },
        { title: "Accessibility", slug: "accessibility" },
        { title: "Data Security", slug: "data-security" },
        { title: "GDPR Compliance", slug: "gdpr" },
        { title: "Legal Notices", slug: "legal-notices" }
      ],
      legalContact: {
        email: "<EMAIL>",
        message: "If you have questions about our legal documents, please contact our legal team."
      }
    };
  }
};

var _layout_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 7;
let component_cache;
const component = async () => component_cache ??= (await import('./_layout.svelte-C8gXDzvJ.js')).default;
const server_id = "src/routes/legal/+layout.server.ts";
const imports = ["_app/immutable/nodes/7.M8oxj5Y_.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/Z9Zpt0fH.js","_app/immutable/chunks/B3MJtjra.js","_app/immutable/chunks/nZgk9enP.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _layout_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=7-6ZGpbMQh.js.map
