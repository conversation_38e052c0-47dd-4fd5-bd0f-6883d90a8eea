{"version": 3, "file": "features-SWeUHekJ.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/features.js"], "sourcesContent": ["var FeatureCategory = /* @__PURE__ */ ((FeatureCategory2) => {\n  FeatureCategory2[\"Core\"] = \"core\";\n  FeatureCategory2[\"JobSearch\"] = \"job_search\";\n  FeatureCategory2[\"Resume\"] = \"resume\";\n  FeatureCategory2[\"Applications\"] = \"applications\";\n  FeatureCategory2[\"Analytics\"] = \"analytics\";\n  FeatureCategory2[\"Team\"] = \"team\";\n  FeatureCategory2[\"Integration\"] = \"integration\";\n  FeatureCategory2[\"Communication\"] = \"communication\";\n  FeatureCategory2[\"Automation\"] = \"automation\";\n  FeatureCategory2[\"Security\"] = \"security\";\n  FeatureCategory2[\"Customization\"] = \"customization\";\n  FeatureCategory2[\"Advanced\"] = \"advanced\";\n  return FeatureCategory2;\n})(FeatureCategory || {});\nvar LimitType = /* @__PURE__ */ ((LimitType2) => {\n  LimitType2[\"Monthly\"] = \"monthly\";\n  LimitType2[\"Total\"] = \"total\";\n  LimitType2[\"Concurrent\"] = \"concurrent\";\n  LimitType2[\"Unlimited\"] = \"unlimited\";\n  return LimitType2;\n})(LimitType || {});\nvar FeatureAccessLevel = /* @__PURE__ */ ((FeatureAccessLevel2) => {\n  FeatureAccessLevel2[\"NotIncluded\"] = \"not_included\";\n  FeatureAccessLevel2[\"Included\"] = \"included\";\n  FeatureAccessLevel2[\"Limited\"] = \"limited\";\n  FeatureAccessLevel2[\"Unlimited\"] = \"unlimited\";\n  return FeatureAccessLevel2;\n})(FeatureAccessLevel || {});\nexport {\n  FeatureAccessLevel as F,\n  LimitType as L,\n  FeatureCategory as a\n};\n"], "names": [], "mappings": "AAAG,IAAC,eAAe,mBAAmB,CAAC,CAAC,gBAAgB,KAAK;AAC7D,EAAE,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM;AACnC,EAAE,gBAAgB,CAAC,WAAW,CAAC,GAAG,YAAY;AAC9C,EAAE,gBAAgB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACvC,EAAE,gBAAgB,CAAC,cAAc,CAAC,GAAG,cAAc;AACnD,EAAE,gBAAgB,CAAC,WAAW,CAAC,GAAG,WAAW;AAC7C,EAAE,gBAAgB,CAAC,MAAM,CAAC,GAAG,MAAM;AACnC,EAAE,gBAAgB,CAAC,aAAa,CAAC,GAAG,aAAa;AACjD,EAAE,gBAAgB,CAAC,eAAe,CAAC,GAAG,eAAe;AACrD,EAAE,gBAAgB,CAAC,YAAY,CAAC,GAAG,YAAY;AAC/C,EAAE,gBAAgB,CAAC,UAAU,CAAC,GAAG,UAAU;AAC3C,EAAE,gBAAgB,CAAC,eAAe,CAAC,GAAG,eAAe;AACrD,EAAE,gBAAgB,CAAC,UAAU,CAAC,GAAG,UAAU;AAC3C,EAAE,OAAO,gBAAgB;AACzB,CAAC,EAAE,eAAe,IAAI,EAAE;AACrB,IAAC,SAAS,mBAAmB,CAAC,CAAC,UAAU,KAAK;AACjD,EAAE,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS;AACnC,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,OAAO;AAC/B,EAAE,UAAU,CAAC,YAAY,CAAC,GAAG,YAAY;AACzC,EAAE,UAAU,CAAC,WAAW,CAAC,GAAG,WAAW;AACvC,EAAE,OAAO,UAAU;AACnB,CAAC,EAAE,SAAS,IAAI,EAAE;AACf,IAAC,kBAAkB,mBAAmB,CAAC,CAAC,mBAAmB,KAAK;AACnE,EAAE,mBAAmB,CAAC,aAAa,CAAC,GAAG,cAAc;AACrD,EAAE,mBAAmB,CAAC,UAAU,CAAC,GAAG,UAAU;AAC9C,EAAE,mBAAmB,CAAC,SAAS,CAAC,GAAG,SAAS;AAC5C,EAAE,mBAAmB,CAAC,WAAW,CAAC,GAAG,WAAW;AAChD,EAAE,OAAO,mBAAmB;AAC5B,CAAC,EAAE,kBAAkB,IAAI,EAAE;;;;"}