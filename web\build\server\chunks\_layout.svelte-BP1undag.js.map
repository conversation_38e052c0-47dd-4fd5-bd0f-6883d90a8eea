{"version": 3, "file": "_layout.svelte-BP1undag.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/auth/_layout.svelte.js"], "sourcesContent": ["import { V as escape_html, a0 as slot, y as pop, w as push } from \"../../../chunks/index3.js\";\nimport \"../../../chunks/client.js\";\nfunction _layout($$payload, $$props) {\n  push();\n  const messages = [\n    {\n      text: \"I've submitted resumes automatically for countless hours, and I'm not stopping!\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"Just another batch of resumes sent while I sip my coffee.\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"Automating my way through job applications, one resume at a time!\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"Resumes flying off to potential employers faster than I can count.\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"I’ve sent 100s of resumes today, and it’s only 10 AM!\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"You blink, I submit another resume!\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"Job applications? Done. Resumes submitted automatically with precision!\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"I’m on a roll! Resumes are flowing like never before.\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"The auto-submit system never sleeps. Resumes are being sent constantly!\",\n      name: \"<PERSON>\"\n    },\n    {\n      text: \"Another day, another batch of resumes automatically submitted!\",\n      name: \"<PERSON>\"\n    }\n  ];\n  const randomMessage = messages[Math.floor(Math.random() * messages.length)];\n  $$payload.out += `<div class=\"flex min-h-screen flex-row items-center justify-center\"><div class=\"flex h-full w-full items-center justify-center\"><div class=\"bg-muted text-primary-foreground border-border relative hidden min-h-screen w-full flex-col border-r p-10 lg:flex\"><div class=\"absolute inset-0 bg-cover\" style=\"background-image:url(https://images.unsplash.com/photo-1590069261209-f8e9b8642343?ixlib=rb-4.0.3&amp;ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&amp;auto=format&amp;fit=crop&amp;w=1376&amp;q=80);\"></div> <div class=\"z-index-20 relative flex items-center gap-2\"><svg class=\"text-primary-foreground h-7 w-7\" fill=\"none\" height=\"32\" viewBox=\"0 0 256  256\" width=\"32\" xmlns=\"http://www.w3.org/2000/svg\"><rect fill=\"none\" height=\"224\" rx=\"32\" stroke=\"currentColor\" stroke-width=\"16\" width=\"224\" x=\"16\" y=\"16\"></rect><path d=\"M80 130 L110 160 L180 90\" fill=\"none\" stroke=\"currentColor\" stroke-dasharray=\"300\" stroke-dashoffset=\"300\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"24\"><animate attributeName=\"stroke-dashoffset\" begin=\"0.2s\" dur=\"0.6s\" fill=\"freeze\" from=\"300\" to=\"0\"></animate></path></svg> <a href=\"/\" class=\"font-inter text-foreground hover:text-foreground/80 text-2xl font-normal transition-colors duration-200\">hirli</a></div> <div class=\"relative z-20 mt-auto\"><blockquote class=\"space-y-2\"><p class=\"text-lg\">“${escape_html(randomMessage.text)}”</p> <footer class=\"text-sm\">${escape_html(randomMessage.name)}</footer></blockquote></div></div></div> <div class=\"flex w-2/3 flex-col items-center justify-center lg:p-8\"><!---->`;\n  slot($$payload, $$props, \"default\", {}, null);\n  $$payload.out += `<!----></div></div>`;\n  pop();\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;;;AAEA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,IAAI,EAAE,iFAAiF;AAC7F,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,2DAA2D;AACvE,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,mEAAmE;AAC/E,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,oEAAoE;AAChF,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,uDAAuD;AACnE,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,qCAAqC;AACjD,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,yEAAyE;AACrF,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,uDAAuD;AACnE,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,yEAAyE;AACrF,MAAM,IAAI,EAAE;AACZ,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,EAAE,gEAAgE;AAC5E,MAAM,IAAI,EAAE;AACZ;AACA,GAAG;AACH,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC7E,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,m0CAAm0C,EAAE,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,8BAA8B,EAAE,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,oHAAoH,CAAC;AAC9iD,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AAC/C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxC,EAAE,GAAG,EAAE;AACP;;;;"}