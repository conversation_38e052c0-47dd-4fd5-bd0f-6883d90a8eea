import { p as push, M as ensure_array_like, O as escape_html, N as attr, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import 'clsx';
import './false-CRHihH2U.js';
import './sanityClient-BQ6Z_2a-.js';
import '@sanity/client';
import './html-FW6Ia4bL.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const {
    pressReleasesPage,
    pressReleasesByYear,
    years
  } = data;
  const fallbackPressReleases = {
    "2023": [
      {
        title: "Hirli Raises $5M Series A to Revolutionize Job Applications",
        publishedAt: "2023-06-15",
        excerpt: "Funding will accelerate product development and market expansion for the AI-powered job application platform.",
        slug: { current: "series-a-funding" },
        location: "San Francisco, CA"
      },
      {
        title: "Hirli Launches AI Co-Pilot for Personalized Job Search Assistance",
        publishedAt: "2023-03-08",
        excerpt: "New AI-powered feature provides tailored guidance and support throughout the job search process.",
        slug: { current: "ai-copilot-launch" }
      }
    ],
    "2022": [
      {
        title: "Hirli Reaches Milestone of 100,000 Users",
        publishedAt: "2022-11-22",
        excerpt: "Platform celebrates helping 100,000 job seekers streamline their application process and find employment opportunities.",
        slug: { current: "100k-users" }
      }
    ]
  };
  const fallbackYears = ["2023", "2022"];
  Object.keys(pressReleasesByYear).length > 0 ? pressReleasesByYear : fallbackPressReleases;
  years.length > 0 ? years : fallbackYears;
  function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  }
  SEO($$payload, {
    title: pressReleasesPage?.seo?.metaTitle || "Press Releases | Hirli",
    description: pressReleasesPage?.seo?.metaDescription || "Official press releases from Hirli, the AI-powered job application platform.",
    keywords: pressReleasesPage?.seo?.keywords?.join(", ") || "Hirli press releases, company news, job application platform, AI technology"
  });
  $$payload.out += `<!----> <div><h2 class="mb-8 text-3xl font-semibold">Releases</h2> `;
  if (pressReleasesPage?.content) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-8">`;
    PortableText($$payload, { value: pressReleasesPage.content });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="space-y-16">`;
  if (Object.keys(pressReleasesByYear).length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(years);
    $$payload.out += `<!--[-->`;
    for (let $$index_1 = 0, $$length = each_array.length; $$index_1 < $$length; $$index_1++) {
      let year = each_array[$$index_1];
      const each_array_1 = ensure_array_like(pressReleasesByYear[year]);
      $$payload.out += `<div><h2 class="mb-8 text-2xl font-semibold">${escape_html(year)}</h2> <div class="space-y-6"><!--[-->`;
      for (let $$index = 0, $$length2 = each_array_1.length; $$index < $$length2; $$index++) {
        let release = each_array_1[$$index];
        $$payload.out += `<div class="border-b pb-6"><div class="mb-2 flex items-center gap-2">`;
        Calendar($$payload, { class: "h-4 w-4 text-gray-500" });
        $$payload.out += `<!----> <p class="text-muted-foreground text-sm">${escape_html(formatDate(release.publishedAt))}</p> `;
        if (release.location) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<span class="mx-1 text-gray-400">•</span> `;
          Map_pin($$payload, { class: "h-4 w-4 text-gray-500" });
          $$payload.out += `<!----> <p class="text-muted-foreground text-sm">${escape_html(release.location)}</p>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--></div> <h3 class="mb-2 text-xl font-medium">${escape_html(release.title)}</h3> `;
        if (release.subtitle) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<p class="mb-2 text-lg text-gray-700">${escape_html(release.subtitle)}</p>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--> `;
        if (release.excerpt) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<p class="text-muted-foreground mb-4">${escape_html(release.excerpt)}</p>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--> <div class="flex"><a${attr("href", `/press/releases/${release.slug.current}`)} class="text-primary inline-flex items-center text-sm font-medium hover:underline">Read More `;
        External_link($$payload, { class: "ml-1 h-3 w-3" });
        $$payload.out += `<!----></a></div></div>`;
      }
      $$payload.out += `<!--]--></div></div>`;
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
    const each_array_2 = ensure_array_like(fallbackYears);
    $$payload.out += `<!--[-->`;
    for (let $$index_3 = 0, $$length = each_array_2.length; $$index_3 < $$length; $$index_3++) {
      let year = each_array_2[$$index_3];
      const each_array_3 = ensure_array_like(fallbackPressReleases[year]);
      $$payload.out += `<div><h2 class="mb-8 text-2xl font-semibold">${escape_html(year)}</h2> <div class="space-y-6"><!--[-->`;
      for (let $$index_2 = 0, $$length2 = each_array_3.length; $$index_2 < $$length2; $$index_2++) {
        let release = each_array_3[$$index_2];
        $$payload.out += `<div class="border-b pb-6"><div class="mb-2 flex items-center gap-2">`;
        Calendar($$payload, { class: "h-4 w-4 text-gray-500" });
        $$payload.out += `<!----> <p class="text-muted-foreground text-sm">${escape_html(formatDate(release.publishedAt))}</p> `;
        if (release.location) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<span class="mx-1 text-gray-400">•</span> `;
          Map_pin($$payload, { class: "h-4 w-4 text-gray-500" });
          $$payload.out += `<!----> <p class="text-muted-foreground text-sm">${escape_html(release.location)}</p>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--></div> <h3 class="mb-2 text-xl font-medium">${escape_html(release.title)}</h3> `;
        if (release.excerpt) {
          $$payload.out += "<!--[-->";
          $$payload.out += `<p class="text-muted-foreground mb-4">${escape_html(release.excerpt)}</p>`;
        } else {
          $$payload.out += "<!--[!-->";
        }
        $$payload.out += `<!--]--> <div class="flex"><a${attr("href", `/press/releases/${release.slug.current}`)} class="text-primary inline-flex items-center text-sm font-medium hover:underline">Read More `;
        External_link($$payload, { class: "ml-1 h-3 w-3" });
        $$payload.out += `<!----></a></div></div>`;
      }
      $$payload.out += `<!--]--></div></div>`;
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-3NK76prC.js.map
