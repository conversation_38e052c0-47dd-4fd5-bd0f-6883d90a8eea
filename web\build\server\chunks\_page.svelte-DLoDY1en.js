import { p as push, P as stringify, O as escape_html, N as attr, Q as bind_props, q as pop, M as ensure_array_like } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { u as urlFor } from './client2-BLTPQNYX.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import { A as Arrow_left } from './arrow-left-DyZbJRhp.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import 'clsx';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import '@sanity/client';
import './sanityClient-BQ6Z_2a-.js';
import './html-FW6Ia4bL.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const resource = data.resource;
  const relatedResources = resource.relatedResources || [];
  SEO($$payload, {
    title: `${stringify(resource.title)} | Hirli Resources`,
    description: resource.description,
    keywords: resource.tags ? `career resources, job search, ${resource.tags.join(", ")}` : `career resources, job search, ${resource.title.toLowerCase()}, ${resource.resourceType || resource.type || "resource"}`
  });
  $$payload.out += `<!----> <div class="container mx-auto px-4 py-12"><div class="mb-8"><a href="/resources" class="text-primary inline-flex items-center text-sm hover:underline">`;
  Arrow_left($$payload, { class: "mr-1 h-4 w-4" });
  $$payload.out += `<!----> Back to Resources</a></div> <div class="grid gap-8 lg:grid-cols-3"><div class="lg:col-span-2"><div class="mb-8"><h1 class="mb-4 text-3xl font-bold">${escape_html(resource.title)}</h1> <p class="text-lg text-gray-600">${escape_html(resource.description)}</p></div> `;
  if (resource.mainImage) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-8 overflow-hidden rounded-lg"><img${attr("src", urlFor(resource.mainImage, { width: 800 }))}${attr("alt", resource.mainImage.alt || resource.title)} class="w-full"/></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (resource.content) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="prose prose-lg max-w-none">`;
    PortableText($$payload, { value: resource.content });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (resource.downloadUrl) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-8"><a${attr("href", resource.downloadUrl)} target="_blank" rel="noopener noreferrer" class="bg-primary hover:bg-primary/90 inline-flex items-center rounded-md px-4 py-2 text-white"><svg xmlns="http://www.w3.org/2000/svg" class="mr-2 h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg> Download Resource</a></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <div><div class="space-y-6">`;
  Card($$payload, {
    children: ($$payload2) => {
      Card_header($$payload2, {
        children: ($$payload3) => {
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Resource Details`;
            },
            $$slots: { default: true }
          });
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> `;
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<dl class="space-y-4"><div><dt class="text-sm font-medium text-gray-500">Category</dt> <dd class="text-gray-700">${escape_html(resource.category)}</dd></div> <div><dt class="text-sm font-medium text-gray-500">Type</dt> <dd class="capitalize text-gray-700">${escape_html(resource.resourceType || resource.type || "Resource")}</dd></div> `;
          if (resource.tags && resource.tags.length > 0) {
            $$payload3.out += "<!--[-->";
            const each_array = ensure_array_like(resource.tags);
            $$payload3.out += `<div><dt class="text-sm font-medium text-gray-500">Tags</dt> <dd class="text-gray-700"><div class="mt-1 flex flex-wrap gap-2"><!--[-->`;
            for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
              let tag = each_array[$$index];
              $$payload3.out += `<span class="bg-primary/10 text-primary rounded-full px-3 py-1 text-xs">${escape_html(tag)}</span>`;
            }
            $$payload3.out += `<!--]--></div></dd></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--> `;
          if (resource.publishedAt) {
            $$payload3.out += "<!--[-->";
            $$payload3.out += `<div><dt class="text-sm font-medium text-gray-500">Published</dt> <dd class="text-gray-700">${escape_html(new Date(resource.publishedAt).toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric"
            }))}</dd></div>`;
          } else {
            $$payload3.out += "<!--[!-->";
          }
          $$payload3.out += `<!--]--></dl>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  if (relatedResources.length > 0) {
    $$payload.out += "<!--[-->";
    Card($$payload, {
      children: ($$payload2) => {
        Card_header($$payload2, {
          children: ($$payload3) => {
            Card_title($$payload3, {
              children: ($$payload4) => {
                $$payload4.out += `<!---->Related Resources`;
              },
              $$slots: { default: true }
            });
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!----> `;
        Card_content($$payload2, {
          children: ($$payload3) => {
            const each_array_1 = ensure_array_like(relatedResources);
            $$payload3.out += `<div class="space-y-4"><!--[-->`;
            for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
              let relatedResource = each_array_1[$$index_1];
              $$payload3.out += `<div class="rounded-lg border p-4"><h3 class="mb-1 font-medium">${escape_html(relatedResource.title)}</h3> <p class="mb-2 text-sm text-gray-600">${escape_html(relatedResource.description.substring(0, 100))}...</p> <a${attr("href", `/resources/${relatedResource.slug.current}`)} class="text-primary inline-flex items-center text-sm font-medium hover:underline">View Resource `;
              Arrow_right($$payload3, { class: "ml-1 h-3 w-3" });
              $$payload3.out += `<!----></a></div>`;
            }
            $$payload3.out += `<!--]--></div>`;
          },
          $$slots: { default: true }
        });
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    });
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-DLoDY1en.js.map
