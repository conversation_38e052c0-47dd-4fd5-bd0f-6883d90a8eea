{"version": 3, "file": "_layout.svelte-xJYKruq5.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/_layout.svelte.js"], "sourcesContent": ["import { _ as store_get, S as attr_class, T as clsx, U as ensure_array_like, R as attr, V as escape_html, a0 as slot, a1 as unsubscribe_stores, N as bind_props, y as pop, w as push } from \"../../../chunks/index3.js\";\nimport { p as page } from \"../../../chunks/stores.js\";\nfunction _layout($$payload, $$props) {\n  push();\n  var $$store_subs;\n  let path, currentPath, pressPages;\n  let data = $$props[\"data\"];\n  path = store_get($$store_subs ??= {}, \"$page\", page).url.pathname;\n  currentPath = path.endsWith(\"/\") ? path.slice(0, -1) : path;\n  pressPages = data?.pressPages || [];\n  $$payload.out += `<div class=\"container mx-auto px-4 py-16\"><div><div class=\"mb-12 text-center\"><h1 class=\"mb-4 text-4xl font-bold\">Press &amp; Media</h1> <p class=\"text-muted-foreground mx-auto max-w-2xl text-lg\">Resources and information for journalists and media professionals covering Hirli.</p></div> <div class=\"border-border mb-12 border-b\"><div class=\"flex flex-wrap justify-center gap-2 pb-4\"><a href=\"/press\"${attr_class(clsx(currentPath === \"/press\" ? \"border-primary border-b-2 px-4 py-2 font-medium\" : \"text-muted-foreground hover:text-foreground px-4 py-2 font-medium\"))}>Press</a> <a href=\"/press/releases\"${attr_class(clsx(currentPath === \"/press/releases\" || currentPath.startsWith(\"/press/releases/\") ? \"border-primary border-b-2 px-4 py-2 font-medium\" : \"text-muted-foreground hover:text-foreground px-4 py-2 font-medium\"))}>Releases</a> <a href=\"/press/coverage\"${attr_class(clsx(currentPath === \"/press/coverage\" ? \"border-primary border-b-2 px-4 py-2 font-medium\" : \"text-muted-foreground hover:text-foreground px-4 py-2 font-medium\"))}>Coverage</a> `;\n  if (pressPages && pressPages.length > 0) {\n    $$payload.out += \"<!--[-->\";\n    const each_array = ensure_array_like(pressPages);\n    $$payload.out += `<!--[-->`;\n    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n      let pressPage = each_array[$$index];\n      $$payload.out += `<a${attr(\"href\", `/press/${pressPage.slug.current}`)}${attr_class(clsx(currentPath === `/press/${pressPage.slug.current}` ? \"border-primary border-b-2 px-4 py-2 font-medium\" : \"text-muted-foreground hover:text-foreground px-4 py-2 font-medium\"))}>${escape_html(pressPage.title)}</a>`;\n    }\n    $$payload.out += `<!--]-->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> <a href=\"/contact\"${attr_class(clsx(currentPath === \"/contact\" ? \"border-primary border-b-2 px-4 py-2 font-medium\" : \"text-muted-foreground hover:text-foreground px-4 py-2 font-medium\"))}>Contact</a></div></div> <!---->`;\n  slot($$payload, $$props, \"default\", {}, null);\n  $$payload.out += `<!----></div></div>`;\n  if ($$store_subs) unsubscribe_stores($$store_subs);\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _layout as default\n};\n"], "names": [], "mappings": ";;;;;AAEA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,IAAI,EAAE,WAAW,EAAE,UAAU;AACnC,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,IAAI,GAAG,SAAS,CAAC,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,GAAG,CAAC,QAAQ;AACnE,EAAE,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;AAC7D,EAAE,UAAU,GAAG,IAAI,EAAE,UAAU,IAAI,EAAE;AACrC,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gZAAgZ,EAAE,UAAU,CAAC,IAAI,CAAC,WAAW,KAAK,QAAQ,GAAG,iDAAiD,GAAG,mEAAmE,CAAC,CAAC,CAAC,oCAAoC,EAAE,UAAU,CAAC,IAAI,CAAC,WAAW,KAAK,iBAAiB,IAAI,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC,GAAG,iDAAiD,GAAG,mEAAmE,CAAC,CAAC,CAAC,uCAAuC,EAAE,UAAU,CAAC,IAAI,CAAC,WAAW,KAAK,iBAAiB,GAAG,iDAAiD,GAAG,mEAAmE,CAAC,CAAC,CAAC,cAAc,CAAC;AACnjC,EAAE,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AAC3C,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;AACpD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,IAAI,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACvF,MAAM,IAAI,SAAS,GAAG,UAAU,CAAC,OAAO,CAAC;AACzC,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,iDAAiD,GAAG,mEAAmE,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;AACnT;AACA,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,2BAA2B,EAAE,UAAU,CAAC,IAAI,CAAC,WAAW,KAAK,UAAU,GAAG,iDAAiD,GAAG,mEAAmE,CAAC,CAAC,CAAC,gCAAgC,CAAC;AACzP,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AAC/C,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxC,EAAE,IAAI,YAAY,EAAE,kBAAkB,CAAC,YAAY,CAAC;AACpD,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}