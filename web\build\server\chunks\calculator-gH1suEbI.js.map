{"version": 3, "file": "calculator-gH1suEbI.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/calculator.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Calculator($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\n      \"rect\",\n      {\n        \"width\": \"16\",\n        \"height\": \"20\",\n        \"x\": \"4\",\n        \"y\": \"2\",\n        \"rx\": \"2\"\n      }\n    ],\n    [\n      \"line\",\n      { \"x1\": \"8\", \"x2\": \"16\", \"y1\": \"6\", \"y2\": \"6\" }\n    ],\n    [\n      \"line\",\n      {\n        \"x1\": \"16\",\n        \"x2\": \"16\",\n        \"y1\": \"14\",\n        \"y2\": \"18\"\n      }\n    ],\n    [\"path\", { \"d\": \"M16 10h.01\" }],\n    [\"path\", { \"d\": \"M12 10h.01\" }],\n    [\"path\", { \"d\": \"M8 10h.01\" }],\n    [\"path\", { \"d\": \"M12 14h.01\" }],\n    [\"path\", { \"d\": \"M8 14h.01\" }],\n    [\"path\", { \"d\": \"M12 18h.01\" }],\n    [\"path\", { \"d\": \"M8 18h.01\" }]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"calculator\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Calculator as C\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,UAAU,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;AACnD,KAAK;AACL,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;AACnC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;AACnC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC;AAClC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;AACnC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC;AAClC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC;AACnC,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE;AACjC,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE;AAC1B,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}