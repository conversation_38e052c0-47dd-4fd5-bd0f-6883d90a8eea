{"version": 3, "file": "86-BTH3Ndhx.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/releases/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/86.js"], "sourcesContent": ["import { c as client } from \"../../../../chunks/client2.js\";\nconst load = async () => {\n  try {\n    const pressReleasesPage = await client.fetch(`\n      *[_type == \"page\" && slug.current == \"press/releases\"][0] {\n        title,\n        description,\n        content,\n        seo\n      }\n    `);\n    const pressReleases = await client.fetch(`\n      *[_type == \"post\" && postType == \"press\"] | order(publishedAt desc) {\n        _id,\n        title,\n        slug,\n        publishedAt,\n        subtitle,\n        location,\n        excerpt,\n        \"categories\": categories[]->\n      }\n    `);\n    const pressReleasesByYear = {};\n    pressReleases.forEach((release) => {\n      const year = new Date(release.publishedAt).getFullYear().toString();\n      if (!pressReleasesByYear[year]) {\n        pressReleasesByYear[year] = [];\n      }\n      pressReleasesByYear[year].push(release);\n    });\n    const years = Object.keys(pressReleasesByYear).sort((a, b) => parseInt(b) - parseInt(a));\n    return {\n      pressReleasesPage,\n      pressReleases,\n      pressReleasesByYear,\n      years\n    };\n  } catch (error) {\n    console.error(\"Error loading press releases data:\", error);\n    return {\n      pressReleasesPage: null,\n      pressReleases: [],\n      pressReleasesByYear: {},\n      years: []\n    };\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/press/releases/_page.server.ts.js';\n\nexport const index = 86;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/press/releases/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/press/releases/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/86.CuQECbjI.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/BMgaXnEE.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/DosGZj-c.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/zNKWipEG.js\",\"_app/immutable/chunks/CwgkX8t9.js\"];\nexport const stylesheets = [\"_app/immutable/assets/PortableText.DSHKgSkc.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;AACA,MAAM,IAAI,GAAG,YAAY;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC;AACN,IAAI,MAAM,aAAa,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC;AACN,IAAI,MAAM,mBAAmB,GAAG,EAAE;AAClC,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AACvC,MAAM,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;AACzE,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;AACtC,QAAQ,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE;AACtC;AACA,MAAM,mBAAmB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAC7C,KAAK,CAAC;AACN,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC5F,IAAI,OAAO;AACX,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM,mBAAmB;AACzB,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO;AACX,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,aAAa,EAAE,EAAE;AACvB,MAAM,mBAAmB,EAAE,EAAE;AAC7B,MAAM,KAAK,EAAE;AACb,KAAK;AACL;AACA,CAAC;;;;;;;AC7CW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAiD,CAAC,EAAE;AAE/G,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC72B,MAAC,WAAW,GAAG,CAAC,iDAAiD;AACjE,MAAC,KAAK,GAAG;;;;"}