{"version": 3, "file": "email-CMhyq3ly.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/email.js"], "sourcesContent": ["import { R as RedisConnection } from \"./redis.js\";\nimport { E as EmailTemplate } from \"./types.js\";\nconst EMAIL_QUEUE_KEY = \"email-queue\";\nasync function sendResetPasswordEmail(email, resetUrl, data) {\n  const job = {\n    type: \"reset_password_email\",\n    to: email,\n    subject: \"Reset Your Password\",\n    template: EmailTemplate.PASSWORD_RESET,\n    data: {\n      resetUrl,\n      firstName: data.firstName,\n      expiresInMinutes: data.expiresInMinutes,\n      token: data.token\n    }\n  };\n  await RedisConnection.xadd(EMAIL_QUEUE_KEY, \"*\", \"job\", JSON.stringify(job));\n  console.log(`Password reset email queued for ${email}`);\n  return true;\n}\nasync function sendPasswordChangedEmail(email, data) {\n  const job = {\n    type: \"password_changed_email\",\n    to: email,\n    subject: \"Your Password Has Been Changed\",\n    template: EmailTemplate.PASSWORD_CHANGED,\n    data: {\n      firstName: data.firstName,\n      loginUrl: data.loginUrl,\n      timestamp: (/* @__PURE__ */ new Date()).toISOString()\n    }\n  };\n  await RedisConnection.xadd(EMAIL_QUEUE_KEY, \"*\", \"job\", JSON.stringify(job));\n  console.log(`Password changed email queued for ${email}`);\n  return true;\n}\nexport {\n  sendPasswordChangedEmail as a,\n  sendResetPasswordEmail as s\n};\n"], "names": [], "mappings": ";;;AAEA,MAAM,eAAe,GAAG,aAAa;AACrC,eAAe,sBAAsB,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE;AAC7D,EAAE,MAAM,GAAG,GAAG;AACd,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,EAAE,EAAE,KAAK;AACb,IAAI,OAAO,EAAE,qBAAqB;AAClC,IAAI,QAAQ,EAAE,aAAa,CAAC,cAAc;AAC1C,IAAI,IAAI,EAAE;AACV,MAAM,QAAQ;AACd,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;AAC7C,MAAM,KAAK,EAAE,IAAI,CAAC;AAClB;AACA,GAAG;AACH,EAAE,MAAM,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC9E,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC,CAAC;AACzD,EAAE,OAAO,IAAI;AACb;AACA,eAAe,wBAAwB,CAAC,KAAK,EAAE,IAAI,EAAE;AACrD,EAAE,MAAM,GAAG,GAAG;AACd,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,EAAE,EAAE,KAAK;AACb,IAAI,OAAO,EAAE,gCAAgC;AAC7C,IAAI,QAAQ,EAAE,aAAa,CAAC,gBAAgB;AAC5C,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,SAAS,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW;AACzD;AACA,GAAG;AACH,EAAE,MAAM,eAAe,CAAC,IAAI,CAAC,eAAe,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAC9E,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3D,EAAE,OAAO,IAAI;AACb;;;;"}