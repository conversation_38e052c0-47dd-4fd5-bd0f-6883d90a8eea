{"version": 3, "file": "_page.svelte-0kz8PkYX.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/co-pilot/_page.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { B as <PERSON><PERSON> } from \"../../../chunks/button.js\";\nimport { S as SEO } from \"../../../chunks/SEO.js\";\nimport { B as Brain, C as Code } from \"../../../chunks/code.js\";\nimport { C as Circle_check_big } from \"../../../chunks/circle-check-big.js\";\nimport { M as Message_square } from \"../../../chunks/message-square.js\";\nimport { B as Bot } from \"../../../chunks/bot.js\";\nfunction _page($$payload) {\n  SEO($$payload, {\n    title: \"Hirl<PERSON> Co-Pilot - Your AI Job Search Assistant\",\n    description: \"Supercharge your job search with our AI-powered co-pilot. Get personalized advice, resume optimization, and interview preparation.\",\n    keywords: \"job search AI, career co-pilot, interview preparation, resume optimization, job search assistant\",\n    url: \"https://hirli.com/co-pilot\",\n    image: \"/assets/og-image-co-pilot.jpg\"\n  });\n  $$payload.out += `<!----> <section class=\"py-32 md:py-40\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto max-w-3xl\"><h1 class=\"mb-8 text-5xl font-light md:text-6xl lg:text-7xl\">Meet your job search <span class=\"text-purple-600\">co-pilot</span></h1> <p class=\"mb-12 text-xl text-gray-600\">Your AI-powered assistant that helps you navigate the job market, optimize your\n        applications, and prepare for interviews.</p> <div class=\"flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0\">`;\n  Button($$payload, {\n    class: \"bg-black px-8 py-4 text-lg text-white hover:bg-gray-800\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Try Co-Pilot Free`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----> `;\n  Button($$payload, {\n    variant: \"outline\",\n    class: \"border-gray-300 px-8 py-4 text-lg\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Watch Demo`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div></div></section> <section class=\"bg-gray-50 py-16\"><div class=\"container mx-auto px-4\"><img src=\"/images/co-pilot-interface.jpg\" alt=\"Co-Pilot Interface\" class=\"h-auto w-full shadow-lg\"/></div></section> <section class=\"py-24\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto max-w-5xl\"><div class=\"grid grid-cols-1 gap-16 md:grid-cols-3\"><div><div class=\"mb-4 text-5xl font-light text-black\">85%</div> <p class=\"text-xl text-gray-600\">More effective job search with AI-powered guidance</p></div> <div><div class=\"mb-4 text-5xl font-light text-black\">2x</div> <p class=\"text-xl text-gray-600\">More interview callbacks with optimized applications</p></div> <div><div class=\"mb-4 text-5xl font-light text-black\">24/7</div> <p class=\"text-xl text-gray-600\">Always available to answer your job search questions</p></div></div></div></div></section> <section class=\"border-t border-gray-100 bg-gray-50 py-24\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto mb-24 max-w-3xl text-center\"><h2 class=\"mb-6 text-4xl font-light\">How Co-Pilot Helps You</h2> <p class=\"text-xl text-gray-600\">Our AI assistant provides personalized guidance throughout your entire job search journey.</p></div> <div class=\"mx-auto mb-32 grid max-w-6xl grid-cols-1 items-center gap-16 md:grid-cols-2\"><div><div class=\"mb-6\">`;\n  Brain($$payload, { class: \"h-8 w-8 text-purple-600\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-6 text-3xl font-light\">Smart Job Matching</h3> <p class=\"mb-6 text-xl text-gray-600\">Co-Pilot analyzes your skills and experience to find the perfect job matches, saving you\n          hours of searching.</p> <ul class=\"space-y-4\"><li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">Skill-based matching</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">Personalized job recommendations</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">Opportunity scoring</span></li></ul></div> <div><img src=\"/images/co-pilot-matching.jpg\" alt=\"Smart Job Matching\" class=\"h-auto w-full shadow-lg\"/></div></div> <div class=\"mx-auto mb-32 grid max-w-6xl grid-cols-1 items-center gap-16 md:grid-cols-2\"><div class=\"order-2 md:order-1\"><img src=\"/images/co-pilot-interview.jpg\" alt=\"Interview Preparation\" class=\"h-auto w-full shadow-lg\"/></div> <div class=\"order-1 md:order-2\"><div class=\"mb-6\">`;\n  Message_square($$payload, { class: \"h-8 w-8 text-purple-600\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-6 text-3xl font-light\">Interview Preparation</h3> <p class=\"mb-6 text-xl text-gray-600\">Practice with AI-simulated interviews tailored to the specific job and company you're\n          applying to.</p> <ul class=\"space-y-4\"><li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">Company-specific questions</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">Real-time feedback</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">Answer improvement suggestions</span></li></ul></div></div> <div class=\"mx-auto grid max-w-6xl grid-cols-1 items-center gap-16 md:grid-cols-2\"><div><div class=\"mb-6\">`;\n  Code($$payload, { class: \"h-8 w-8 text-purple-600\" });\n  $$payload.out += `<!----></div> <h3 class=\"mb-6 text-3xl font-light\">Resume Optimization</h3> <p class=\"mb-6 text-xl text-gray-600\">Get AI-powered suggestions to tailor your resume for each job application and maximize\n          your chances.</p> <ul class=\"space-y-4\"><li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">Keyword optimization</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">Content improvement</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span class=\"text-gray-600\">ATS compatibility check</span></li></ul></div> <div><img src=\"/images/co-pilot-resume.jpg\" alt=\"Resume Optimization\" class=\"h-auto w-full shadow-lg\"/></div></div></div></section> <section class=\"py-24\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto mb-16 max-w-3xl text-center\"><h2 class=\"mb-6 text-4xl font-light\">See Co-Pilot in Action</h2> <p class=\"text-xl text-gray-600\">Experience how our AI assistant helps you navigate the job market, optimize your\n        applications, and prepare for interviews.</p></div> <div class=\"mx-auto grid max-w-5xl grid-cols-1 items-center gap-16 md:grid-cols-2\"><div><div class=\"mb-8 rounded-lg bg-gray-50 p-6\"><div class=\"mb-4 flex items-start\"><div class=\"mr-3 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-purple-100\">`;\n  Bot($$payload, { class: \"h-5 w-5 text-purple-600\" });\n  $$payload.out += `<!----></div> <div class=\"max-w-md rounded-lg bg-purple-50 p-4\"><p class=\"text-gray-700\">Based on your experience as a Frontend Developer, I've found 3 job matches with a\n                90%+ compatibility score. The highest match is at TechCorp, which aligns perfectly\n                with your React and TypeScript skills.</p></div></div> <div class=\"mb-4 flex items-start pl-16\"><div class=\"max-w-md rounded-lg bg-gray-100 p-4\"><p class=\"text-gray-700\">Can you tell me more about the TechCorp position?</p></div></div> <div class=\"flex items-start\"><div class=\"mr-3 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-purple-100\">`;\n  Bot($$payload, { class: \"h-5 w-5 text-purple-600\" });\n  $$payload.out += `<!----></div> <div class=\"max-w-md rounded-lg bg-purple-50 p-4\"><p class=\"text-gray-700\">The Senior Frontend Developer role at TechCorp offers $120-150K, remote work, and\n                focuses on their SaaS platform. They value your experience with React, TypeScript,\n                and state management. I suggest highlighting your work on scalable applications.</p></div></div></div> `;\n  Button($$payload, {\n    class: \"bg-black px-8 py-4 text-lg text-white hover:bg-gray-800\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Try Co-Pilot Free`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> <div><img src=\"/images/co-pilot-demo.jpg\" alt=\"Co-Pilot Demo\" class=\"h-auto w-full shadow-lg\"/></div></div></div></section> <section class=\"border-t border-gray-100 bg-gray-50 py-24\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto max-w-4xl text-center\"><div class=\"mb-8\"><img src=\"https://randomuser.me/api/portraits/women/32.jpg\" alt=\"User\" class=\"mx-auto h-20 w-20 rounded-full\"/></div> <blockquote class=\"mb-8 text-3xl font-light italic\">\"Co-Pilot transformed my job search. The interview practice helped me feel confident, and\n        the resume optimization got me more callbacks. I landed a job at my dream company within a\n        month!\"</blockquote> <div><p class=\"text-xl font-medium\">Jessica T.</p> <p class=\"text-gray-600\">UX Designer at Google</p></div></div></div></section> <section class=\"py-24\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto mb-20 max-w-3xl text-center\"><h2 class=\"mb-6 text-4xl font-light\">Simple, Transparent Pricing</h2> <p class=\"text-xl text-gray-600\">Choose the plan that fits your needs. All plans include our core Co-Pilot features.</p></div> <div class=\"mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2\"><div class=\"bg-white p-12\"><h3 class=\"mb-2 text-2xl font-light\">Basic</h3> <p class=\"mb-6 text-5xl font-light\">$0<span class=\"text-lg font-normal text-gray-500\">/month</span></p> <p class=\"mb-6 border-b border-gray-100 pb-6 text-gray-600\">Perfect for casual job seekers looking for basic assistance.</p> <ul class=\"mb-8 space-y-4\"><li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span>Limited job matching</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span>Basic resume tips</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span>5 interview practice questions</span></li></ul> `;\n  Button($$payload, {\n    variant: \"outline\",\n    class: \"w-full border-gray-300 p-4 text-lg font-medium\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Get Started`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> <div class=\"bg-white p-12\"><h3 class=\"mb-2 text-2xl font-light\">Pro</h3> <p class=\"mb-6 text-5xl font-light\">$19<span class=\"text-lg font-normal text-gray-500\">/month</span></p> <p class=\"mb-6 border-b border-gray-100 pb-6 text-gray-600\">For serious job seekers who want to maximize their chances.</p> <ul class=\"mb-8 space-y-4\"><li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span>Unlimited job matching</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span>Advanced resume optimization</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span>Unlimited interview practice</span></li> <li class=\"flex items-start\">`;\n  Circle_check_big($$payload, {\n    class: \"mr-3 mt-1 h-5 w-5 flex-shrink-0 text-purple-600\"\n  });\n  $$payload.out += `<!----> <span>Company-specific insights</span></li></ul> `;\n  Button($$payload, {\n    class: \"w-full bg-black p-4 text-lg font-medium text-white hover:bg-gray-800\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Start 7-Day Free Trial`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div></div></section> <section class=\"border-t border-gray-100 bg-gray-50 py-24\"><div class=\"container mx-auto px-4\"><div class=\"mx-auto max-w-3xl\"><h2 class=\"mb-16 text-center text-4xl font-light\">Frequently Asked Questions</h2> <div class=\"space-y-12\"><div><h3 class=\"mb-4 text-2xl font-light\">How does Co-Pilot work?</h3> <p class=\"text-lg text-gray-600\">Co-Pilot uses advanced AI to analyze your resume, job preferences, and the current job\n            market. It provides personalized recommendations, helps you optimize your applications,\n            and prepares you for interviews with company-specific insights.</p></div> <div><h3 class=\"mb-4 text-2xl font-light\">Is my data secure?</h3> <p class=\"text-lg text-gray-600\">Yes, we take data security very seriously. All your personal information is encrypted\n            and stored securely. We never share your data with third parties without your explicit\n            consent.</p></div> <div><h3 class=\"mb-4 text-2xl font-light\">How accurate are the job matches?</h3> <p class=\"text-lg text-gray-600\">Our AI continuously learns and improves its matching algorithms. Based on user feedback,\n            our job matches have a 90% relevance rate, meaning 9 out of 10 recommended jobs are\n            highly relevant to the user's skills and preferences.</p></div> <div><h3 class=\"mb-4 text-2xl font-light\">Can I cancel my subscription anytime?</h3> <p class=\"text-lg text-gray-600\">Yes, you can cancel your subscription at any time. If you cancel, you'll continue to\n            have access until the end of your billing period.</p></div></div></div></div></section> <section class=\"bg-black py-24 text-white\"><div class=\"container mx-auto px-4 text-center\"><h2 class=\"mb-8 text-4xl font-light\">Ready to Transform Your Job Search?</h2> <p class=\"mx-auto mb-12 max-w-3xl text-xl text-white/80\">Join thousands of job seekers who have used Co-Pilot to land their dream jobs faster and with\n      less stress.</p> <div class=\"flex flex-col justify-center gap-4 sm:flex-row\">`;\n  Button($$payload, {\n    class: \"bg-white px-10 py-5 text-lg font-medium text-black hover:bg-gray-100\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Get Started Free`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----> `;\n  Button($$payload, {\n    variant: \"outline\",\n    class: \"border-white px-10 py-5 text-lg font-medium text-white hover:bg-white/10\",\n    children: ($$payload2) => {\n      $$payload2.out += `<!---->Schedule Demo`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div> <p class=\"mt-8 text-white/60\">No credit card required. Start your 7-day free trial today.</p></div></section>`;\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAOA,SAAS,KAAK,CAAC,SAAS,EAAE;AAC1B,EAAE,GAAG,CAAC,SAAS,EAAE;AACjB,IAAI,KAAK,EAAE,+CAA+C;AAC1D,IAAI,WAAW,EAAE,oIAAoI;AACrJ,IAAI,QAAQ,EAAE,kGAAkG;AAChH,IAAI,GAAG,EAAE,4BAA4B;AACrC,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,iIAAiI,CAAC;AAClI,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,yDAAyD;AACpE,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC;AAClD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,KAAK,EAAE,mCAAmC;AAC9C,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8yCAA8yC,CAAC;AACn0C,EAAE,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;AACxD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,qFAAqF,CAAC;AACtF,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,kGAAkG,CAAC;AACvH,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8GAA8G,CAAC;AACnI,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,6dAA6d,CAAC;AAClf,EAAE,cAAc,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;AACjE,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,8EAA8E,CAAC;AAC/E,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wGAAwG,CAAC;AAC7H,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,gGAAgG,CAAC;AACrH,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0MAA0M,CAAC;AAC/N,EAAE,IAAI,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;AACvD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,+EAA+E,CAAC;AAChF,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,kGAAkG,CAAC;AACvH,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iGAAiG,CAAC;AACtH,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB,yUAAyU,CAAC;AAC1U,EAAE,GAAG,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;AACtD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB;AACA,gYAAgY,CAAC;AACjY,EAAE,GAAG,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE,CAAC;AACtD,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB;AACA,uHAAuH,CAAC;AACxH,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,yDAAyD;AACpE,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,wBAAwB,CAAC;AAClD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB;AACA,63BAA63B,CAAC;AAC93B,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,4EAA4E,CAAC;AACjG,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yEAAyE,CAAC;AAC9F,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8DAA8D,CAAC;AACnF,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,KAAK,EAAE,gDAAgD;AAC3D,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAC5C,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oXAAoX,CAAC;AACzY,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,8EAA8E,CAAC;AACnG,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oFAAoF,CAAC;AACzG,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,oFAAoF,CAAC;AACzG,EAAE,gBAAgB,CAAC,SAAS,EAAE;AAC9B,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yDAAyD,CAAC;AAC9E,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,sEAAsE;AACjF,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,6BAA6B,CAAC;AACvD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mFAAmF,CAAC;AACpF,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,KAAK,EAAE,sEAAsE;AACjF,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACjD,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,KAAK,EAAE,0EAA0E;AACrF,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC9C,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,2HAA2H,CAAC;AAChJ;;;;"}