// Simple R2 connection test with different SSL configurations
import { S3Client, ListBucketsCommand } from '@aws-sdk/client-s3';
import https from 'https';

// Test different configurations
const configs = [
  {
    name: 'Default Config',
    config: {
      endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
      region: 'auto',
      credentials: {
        accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
        secretAccessKey: '****************************************************************',
      },
    }
  },
  {
    name: 'With Custom Agent',
    config: {
      endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
      region: 'auto',
      credentials: {
        accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
        secretAccessKey: '****************************************************************',
      },
      requestHandler: {
        httpsAgent: new https.Agent({
          rejectUnauthorized: false,
          secureProtocol: 'TLSv1_2_method'
        })
      }
    }
  },
  {
    name: 'Alternative Endpoint Format',
    config: {
      endpoint: 'https://46a6f782171b440493a823a520764a72.r2.cloudflarestorage.com',
      region: 'weur',
      credentials: {
        accessKeyId: 'c3a71f217fdf3a056efaefab3a17afc5',
        secretAccessKey: '****************************************************************',
      },
      forcePathStyle: true,
    }
  }
];

async function testConfigs() {
  console.log('🧪 Testing different R2 configurations...\n');

  for (const { name, config } of configs) {
    console.log(`📋 Testing: ${name}`);
    
    try {
      const client = new S3Client(config);
      const command = new ListBucketsCommand({});
      
      console.log('   🔄 Attempting connection...');
      const response = await client.send(command);
      
      console.log(`   ✅ Success! Found ${response.Buckets?.length || 0} buckets`);
      if (response.Buckets && response.Buckets.length > 0) {
        console.log('   📦 Buckets:', response.Buckets.map(b => b.Name).join(', '));
      }
      
      // If this config works, we can stop here
      console.log(`   🎉 ${name} works! Using this configuration.\n`);
      break;
      
    } catch (error) {
      console.log(`   ❌ Failed: ${error.message}`);
      console.log(`   🔍 Error code: ${error.code || 'Unknown'}`);
      console.log('');
    }
  }
}

testConfigs();
