{"version": 3, "file": "avatar-fallback-B2wWy5ce.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/avatar-fallback.js"], "sourcesContent": ["import { J as derived, w as push, M as spread_attributes, N as bind_props, y as pop, O as copy_payload, P as assign_payload, Q as spread_props } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { u as useId } from \"./use-id.js\";\nimport \"clsx\";\nimport { C as Context } from \"./context.js\";\nconst AVATAR_ROOT_ATTR = \"data-avatar-root\";\nconst AVATAR_IMAGE_ATTR = \"data-avatar-image\";\nconst AVATAR_FALLBACK_ATTR = \"data-avatar-fallback\";\nclass AvatarRootState {\n  opts;\n  constructor(opts) {\n    this.opts = opts;\n    this.loadImage = this.loadImage.bind(this);\n    useRefById(opts);\n  }\n  loadImage(src, crossorigin, referrerPolicy) {\n    if (this.opts.loadingStatus.current === \"loaded\") return;\n    let imageTimerId;\n    const image = new Image();\n    image.src = src;\n    if (crossorigin !== void 0) image.crossOrigin = crossorigin;\n    if (referrerPolicy) image.referrerPolicy = referrerPolicy;\n    this.opts.loadingStatus.current = \"loading\";\n    image.onload = () => {\n      imageTimerId = window.setTimeout(\n        () => {\n          this.opts.loadingStatus.current = \"loaded\";\n        },\n        this.opts.delayMs.current\n      );\n    };\n    image.onerror = () => {\n      this.opts.loadingStatus.current = \"error\";\n    };\n    return () => {\n      window.clearTimeout(imageTimerId);\n    };\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    [AVATAR_ROOT_ATTR]: \"\",\n    \"data-status\": this.opts.loadingStatus.current\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass AvatarImageState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    style: {\n      display: this.root.opts.loadingStatus.current === \"loaded\" ? \"block\" : \"none\"\n    },\n    \"data-status\": this.root.opts.loadingStatus.current,\n    [AVATAR_IMAGE_ATTR]: \"\",\n    src: this.opts.src.current,\n    crossorigin: this.opts.crossOrigin.current,\n    referrerpolicy: this.opts.referrerPolicy.current\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass AvatarFallbackState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById(opts);\n  }\n  #style = derived(() => this.root.opts.loadingStatus.current === \"loaded\" ? { display: \"none\" } : void 0);\n  get style() {\n    return this.#style();\n  }\n  set style($$value) {\n    return this.#style($$value);\n  }\n  #props = derived(() => ({\n    style: this.style,\n    \"data-status\": this.root.opts.loadingStatus.current,\n    [AVATAR_FALLBACK_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst AvatarRootContext = new Context(\"Avatar.Root\");\nfunction useAvatarRoot(props) {\n  return AvatarRootContext.set(new AvatarRootState(props));\n}\nfunction useAvatarImage(props) {\n  return new AvatarImageState(props, AvatarRootContext.get());\n}\nfunction useAvatarFallback(props) {\n  return new AvatarFallbackState(props, AvatarRootContext.get());\n}\nfunction Avatar$1($$payload, $$props) {\n  push();\n  let {\n    delayMs = 0,\n    loadingStatus = \"loading\",\n    onLoadingStatusChange,\n    child,\n    children,\n    id = useId(),\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const rootState = useAvatarRoot({\n    delayMs: box.with(() => delayMs),\n    loadingStatus: box.with(() => loadingStatus, (v) => {\n      if (loadingStatus !== v) {\n        loadingStatus = v;\n        onLoadingStatusChange?.(v);\n      }\n    }),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, rootState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { loadingStatus, ref });\n  pop();\n}\nfunction Avatar_image$1($$payload, $$props) {\n  push();\n  let {\n    src,\n    child,\n    id = useId(),\n    ref = null,\n    crossorigin = void 0,\n    referrerpolicy = void 0,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const imageState = useAvatarImage({\n    src: box.with(() => src),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v),\n    crossOrigin: box.with(() => crossorigin),\n    referrerPolicy: box.with(() => referrerpolicy)\n  });\n  const mergedProps = mergeProps(restProps, imageState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<img${spread_attributes({ ...mergedProps, src }, null)} onload=\"this.__e=event\" onerror=\"this.__e=event\"/>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Avatar_fallback$1($$payload, $$props) {\n  push();\n  let {\n    children,\n    child,\n    id = useId(),\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const fallbackState = useAvatarFallback({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, fallbackState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<span${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></span>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Avatar($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Avatar$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"avatar\",\n        class: cn(\"relative flex size-8 shrink-0 overflow-hidden rounded-full\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Avatar_image($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Avatar_image$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"avatar-image\",\n        class: cn(\"aspect-square size-full\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Avatar_fallback($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Avatar_fallback$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"avatar-fallback\",\n        class: cn(\"bg-muted flex size-full items-center justify-center rounded-full\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Avatar as A,\n  Avatar_image as a,\n  Avatar_fallback as b\n};\n"], "names": [], "mappings": ";;;;;;;;AAQA,MAAM,gBAAgB,GAAG,kBAAkB;AAC3C,MAAM,iBAAiB,GAAG,mBAAmB;AAC7C,MAAM,oBAAoB,GAAG,sBAAsB;AACnD,MAAM,eAAe,CAAC;AACtB,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,SAAS,CAAC,GAAG,EAAE,WAAW,EAAE,cAAc,EAAE;AAC9C,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,QAAQ,EAAE;AACtD,IAAI,IAAI,YAAY;AACpB,IAAI,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE;AAC7B,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG;AACnB,IAAI,IAAI,WAAW,KAAK,MAAM,EAAE,KAAK,CAAC,WAAW,GAAG,WAAW;AAC/D,IAAI,IAAI,cAAc,EAAE,KAAK,CAAC,cAAc,GAAG,cAAc;AAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,SAAS;AAC/C,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM;AACzB,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU;AACtC,QAAQ,MAAM;AACd,UAAU,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,QAAQ;AACpD,SAAS;AACT,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAC1B,OAAO;AACP,KAAK;AACL,IAAI,KAAK,CAAC,OAAO,GAAG,MAAM;AAC1B,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,GAAG,OAAO;AAC/C,KAAK;AACL,IAAI,OAAO,MAAM;AACjB,MAAM,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC;AACvC,KAAK;AACL;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,CAAC,gBAAgB,GAAG,EAAE;AAC1B,IAAI,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC;AAC3C,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,KAAK,EAAE;AACX,MAAM,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,QAAQ,GAAG,OAAO,GAAG;AAC7E,KAAK;AACL,IAAI,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO;AACvD,IAAI,CAAC,iBAAiB,GAAG,EAAE;AAC3B,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO;AAC9B,IAAI,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO;AAC9C,IAAI,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;AAC7C,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,mBAAmB,CAAC;AAC1B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,QAAQ,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;AAC1G,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,KAAK,EAAE,IAAI,CAAC,KAAK;AACrB,IAAI,aAAa,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO;AACvD,IAAI,CAAC,oBAAoB,GAAG;AAC5B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,iBAAiB,GAAG,IAAI,OAAO,CAAC,aAAa,CAAC;AACpD,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,EAAE,OAAO,iBAAiB,CAAC,GAAG,CAAC,IAAI,eAAe,CAAC,KAAK,CAAC,CAAC;AAC1D;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAC7D;AACA,SAAS,iBAAiB,CAAC,KAAK,EAAE;AAClC,EAAE,OAAO,IAAI,mBAAmB,CAAC,KAAK,EAAE,iBAAiB,CAAC,GAAG,EAAE,CAAC;AAChE;AACA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,OAAO,GAAG,CAAC;AACf,IAAI,aAAa,GAAG,SAAS;AAC7B,IAAI,qBAAqB;AACzB,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,aAAa,CAAC;AAClC,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC;AACpC,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,aAAa,EAAE,CAAC,CAAC,KAAK;AACxD,MAAM,IAAI,aAAa,KAAK,CAAC,EAAE;AAC/B,QAAQ,aAAa,GAAG,CAAC;AACzB,QAAQ,qBAAqB,GAAG,CAAC,CAAC;AAClC;AACA,KAAK,CAAC;AACN,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC;AAC7C,EAAE,GAAG,EAAE;AACP;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG;AACP,IAAI,KAAK;AACT,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,WAAW,GAAG,MAAM;AACxB,IAAI,cAAc,GAAG,MAAM;AAC3B,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,UAAU,GAAG,cAAc,CAAC;AACpC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC;AAC5B,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc;AACjD,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,UAAU,CAAC,KAAK,CAAC;AAC7D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,GAAG,EAAE,EAAE,IAAI,CAAC,CAAC,mDAAmD,CAAC;AACjI;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,aAAa,GAAG,iBAAiB,CAAC;AAC1C,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,aAAa,CAAC,KAAK,CAAC;AAChE,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACrC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC;AACtC,MAAM;AACN,QAAQ,WAAW,EAAE,QAAQ;AAC7B,QAAQ,KAAK,EAAE,EAAE,CAAC,4DAA4D,EAAE,SAAS;AACzF,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC;AAC5C,MAAM;AACN,QAAQ,WAAW,EAAE,cAAc;AACnC,QAAQ,KAAK,EAAE,EAAE,CAAC,yBAAyB,EAAE,SAAS;AACtD,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,iBAAiB,CAAC,UAAU,EAAE,YAAY,CAAC;AAC/C,MAAM;AACN,QAAQ,WAAW,EAAE,iBAAiB;AACtC,QAAQ,KAAK,EAAE,EAAE,CAAC,kEAAkE,EAAE,SAAS;AAC/F,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}