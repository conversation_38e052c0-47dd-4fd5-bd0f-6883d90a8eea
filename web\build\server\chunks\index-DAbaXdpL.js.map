{"version": 3, "file": "index-DAbaXdpL.js", "sources": ["../../../node_modules/inline-style-parser/index.js", "../../../node_modules/style-to-object/cjs/index.js", "../../../node_modules/style-to-object/esm/index.mjs"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", "\"use strict\";\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.default = StyleToObject;\nvar inline_style_parser_1 = __importDefault(require(\"inline-style-parser\"));\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nfunction StyleToObject(style, iterator) {\n    var styleObject = null;\n    if (!style || typeof style !== 'string') {\n        return styleObject;\n    }\n    var declarations = (0, inline_style_parser_1.default)(style);\n    var hasIterator = typeof iterator === 'function';\n    declarations.forEach(function (declaration) {\n        if (declaration.type !== 'declaration') {\n            return;\n        }\n        var property = declaration.property, value = declaration.value;\n        if (hasIterator) {\n            iterator(property, value, declaration);\n        }\n        else if (value) {\n            styleObject = styleObject || {};\n            styleObject[property] = value;\n        }\n    });\n    return styleObject;\n}\n//# sourceMappingURL=index.js.map", "import StyleToObject from '../cjs/index.js';\n\n// ensure compatibility with rollup umd build\nexport default StyleToObject.default || StyleToObject;\n"], "names": ["this", "require$$0"], "mappings": ";;;;;;;;;;AAAA;AACA;CACA,IAAI,aAAa,GAAG,iCAAiC;;CAErD,IAAI,aAAa,GAAG,KAAK;CACzB,IAAI,gBAAgB,GAAG,MAAM;;AAE7B;CACA,IAAI,cAAc,GAAG,wCAAwC;CAC7D,IAAI,WAAW,GAAG,OAAO;CACzB,IAAI,WAAW,GAAG,sDAAsD;CACxE,IAAI,eAAe,GAAG,SAAS;;AAE/B;CACA,IAAI,UAAU,GAAG,YAAY;;AAE7B;CACA,IAAI,OAAO,GAAG,IAAI;CAClB,IAAI,aAAa,GAAG,GAAG;CACvB,IAAI,QAAQ,GAAG,GAAG;CAClB,IAAI,YAAY,GAAG,EAAE;;AAErB;CACA,IAAI,YAAY,GAAG,SAAS;CAC5B,IAAI,gBAAgB,GAAG,aAAa;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAA,iBAAc,GAAG,UAAU,KAAK,EAAE,OAAO,EAAE;AAC3C,GAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjC,KAAI,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC;AAC1D;;AAEA,GAAE,IAAI,CAAC,KAAK,EAAE,OAAO,EAAE;;AAEvB,GAAE,OAAO,GAAG,OAAO,IAAI,EAAE;;AAEzB;AACA;AACA;GACE,IAAI,MAAM,GAAG,CAAC;GACd,IAAI,MAAM,GAAG,CAAC;;AAEhB;AACA;AACA;AACA;AACA;AACA,GAAE,SAAS,cAAc,CAAC,GAAG,EAAE;KAC3B,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC;AACxC,KAAI,IAAI,KAAK,EAAE,MAAM,IAAI,KAAK,CAAC,MAAM;KACjC,IAAI,CAAC,GAAG,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC,KAAI,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,MAAM;AACtD;;AAEA;AACA;AACA;AACA;AACA;GACE,SAAS,QAAQ,GAAG;KAClB,IAAI,KAAK,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;KAC5C,OAAO,UAAU,IAAI,EAAE;OACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC;AACzC,OAAM,UAAU,EAAE;AAClB,OAAM,OAAO,IAAI;MACZ;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;AAC3B,KAAI,IAAI,CAAC,KAAK,GAAG,KAAK;AACtB,KAAI,IAAI,CAAC,GAAG,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAC/C,KAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;AAChC;;AAEA;AACA;AACA;AACA,GAAE,QAAQ,CAAC,SAAS,CAAC,OAAO,GAAG,KAAK;;AAIpC;AACA;AACA;AACA;AACA;AACA;AACA,GAAE,SAAS,KAAK,CAAC,GAAG,EAAE;AACtB,KAAI,IAAI,GAAG,GAAG,IAAI,KAAK;AACvB,OAAM,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI,GAAG;MACvD;AACL,KAAI,GAAG,CAAC,MAAM,GAAG,GAAG;AACpB,KAAI,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM;AACjC,KAAI,GAAG,CAAC,IAAI,GAAG,MAAM;AACrB,KAAI,GAAG,CAAC,MAAM,GAAG,MAAM;AACvB,KAAI,GAAG,CAAC,MAAM,GAAG,KAAK;;AAEtB,KAAI,IAAI,OAAO,CAAC,MAAM,EAAE,CAEnB,MAAM;AACX,OAAM,MAAM,GAAG;AACf;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAE,SAAS,KAAK,CAAC,EAAE,EAAE;KACjB,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;KACtB,IAAI,CAAC,CAAC,EAAE;AACZ,KAAI,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;KACd,cAAc,CAAC,GAAG,CAAC;KACnB,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC;AACnC,KAAI,OAAO,CAAC;AACZ;;AAEA;AACA;AACA;GACE,SAAS,UAAU,GAAG;KACpB,KAAK,CAAC,gBAAgB,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,GAAE,SAAS,QAAQ,CAAC,KAAK,EAAE;AAC3B,KAAI,IAAI,CAAC;AACT,KAAI,KAAK,GAAG,KAAK,IAAI,EAAE;AACvB,KAAI,QAAQ,CAAC,GAAG,OAAO,EAAE,GAAG;AAC5B,OAAM,IAAI,CAAC,KAAK,KAAK,EAAE;AACvB,SAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AACrB;AACA;AACA,KAAI,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;GACE,SAAS,OAAO,GAAG;AACrB,KAAI,IAAI,GAAG,GAAG,QAAQ,EAAE;AACxB,KAAI,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;;KAErE,IAAI,CAAC,GAAG,CAAC;KACT;AACJ,OAAM,YAAY,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AACrC,QAAO,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;OACpE;AACN,OAAM,EAAE,CAAC;AACT;KACI,CAAC,IAAI,CAAC;;KAEN,IAAI,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;AAC9C,OAAM,OAAO,KAAK,CAAC,wBAAwB,CAAC;AAC5C;;AAEA,KAAI,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;KAC/B,MAAM,IAAI,CAAC;KACX,cAAc,CAAC,GAAG,CAAC;AACvB,KAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;KACtB,MAAM,IAAI,CAAC;;KAEX,OAAO,GAAG,CAAC;OACT,IAAI,EAAE,YAAY;AACxB,OAAM,OAAO,EAAE;AACf,MAAK,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;GACE,SAAS,WAAW,GAAG;AACzB,KAAI,IAAI,GAAG,GAAG,QAAQ,EAAE;;AAExB;AACA,KAAI,IAAI,IAAI,GAAG,KAAK,CAAC,cAAc,CAAC;KAChC,IAAI,CAAC,IAAI,EAAE;AACf,KAAI,OAAO,EAAE;;AAEb;KACI,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,OAAO,KAAK,CAAC,sBAAsB,CAAC;;AAEjE;AACA,KAAI,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC;;AAEhC,KAAI,IAAI,GAAG,GAAG,GAAG,CAAC;OACZ,IAAI,EAAE,gBAAgB;AAC5B,OAAM,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;AAClE,OAAM,KAAK,EAAE;AACb,WAAU,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,YAAY,CAAC;WAChD;AACV,MAAK,CAAC;;AAEN;KACI,KAAK,CAAC,eAAe,CAAC;;AAE1B,KAAI,OAAO,GAAG;AACd;;AAEA;AACA;AACA;AACA;AACA;GACE,SAAS,YAAY,GAAG;KACtB,IAAI,KAAK,GAAG,EAAE;;KAEd,QAAQ,CAAC,KAAK,CAAC;;AAEnB;AACA,KAAI,IAAI,IAAI;AACZ,KAAI,QAAQ,IAAI,GAAG,WAAW,EAAE,GAAG;AACnC,OAAM,IAAI,IAAI,KAAK,KAAK,EAAE;AAC1B,SAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;SAChB,QAAQ,CAAC,KAAK,CAAC;AACvB;AACA;;AAEA,KAAI,OAAO,KAAK;AAChB;;AAEA,GAAE,UAAU,EAAE;GACZ,OAAO,YAAY,EAAE;EACtB;;AAED;AACA;AACA;AACA;AACA;AACA;CACA,SAAS,IAAI,CAAC,GAAG,EAAE;AACnB,GAAE,OAAO,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,CAAC,GAAG,YAAY;AACnE;;;;;;;;;CCnQA,IAAI,eAAe,GAAG,CAACA,GAAI,IAAIA,GAAI,CAAC,eAAe,KAAK,UAAU,GAAG,EAAE;AACvE,KAAI,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE;EAC5D;AACD,CAAA,MAAM,CAAC,cAAc,CAAC,GAAO,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;AAC7D,CAAA,GAAA,CAAA,OAAe,GAAG,aAAa;AAC/B,CAAA,IAAI,qBAAqB,GAAG,eAAe,CAACC,0BAA8B,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAA,SAAS,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE;KACpC,IAAI,WAAW,GAAG,IAAI;KACtB,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7C,SAAQ,OAAO,WAAW;AAC1B;KACI,IAAI,YAAY,GAAG,IAAI,qBAAqB,CAAC,OAAO,EAAE,KAAK,CAAC;AAChE,KAAI,IAAI,WAAW,GAAG,OAAO,QAAQ,KAAK,UAAU;AACpD,KAAI,YAAY,CAAC,OAAO,CAAC,UAAU,WAAW,EAAE;AAChD,SAAQ,IAAI,WAAW,CAAC,IAAI,KAAK,aAAa,EAAE;aACpC;AACZ;SACQ,IAAI,QAAQ,GAAG,WAAW,CAAC,QAAQ,EAAE,KAAK,GAAG,WAAW,CAAC,KAAK;SAC9D,IAAI,WAAW,EAAE;AACzB,aAAY,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC;AAClD;cACa,IAAI,KAAK,EAAE;AACxB,aAAY,WAAW,GAAG,WAAW,IAAI,EAAE;AAC3C,aAAY,WAAW,CAAC,QAAQ,CAAC,GAAG,KAAK;AACzC;AACA,MAAK,CAAC;AACN,KAAI,OAAO,WAAW;AACtB;AACA;;;;;;;ACzCA;AACA,YAAe,aAAa,CAAC,OAAO,IAAI,aAAa;;;;", "x_google_ignoreList": [0, 1, 2]}