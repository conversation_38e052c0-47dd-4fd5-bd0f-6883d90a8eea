{"version": 3, "file": "accordion-trigger-DwieKZVA.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/accordion-trigger.js"], "sourcesContent": ["import { J as derived, w as push, M as spread_attributes, N as bind_props, y as pop, O as copy_payload, P as assign_payload, Q as spread_props } from \"./index3.js\";\nimport { w as watch, b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { u as useId } from \"./use-id.js\";\nimport { n as noop } from \"./noop.js\";\nimport { c as cn } from \"./utils.js\";\nimport { P as Presence_layer } from \"./presence-layer.js\";\nimport \"clsx\";\nimport { a as afterTick } from \"./after-tick.js\";\nimport { C as Context } from \"./context.js\";\nimport { S as SPACE, i as ENTER, e as getDataDisabled, g as getDataOrientation, d as getDataOpenClosed, k as getAriaDisabled, c as getAriaExpanded } from \"./kbd-constants.js\";\nimport { u as useRovingFocus } from \"./use-roving-focus.svelte.js\";\nimport { C as Chevron_down } from \"./chevron-down.js\";\nconst ACCORDION_ROOT_ATTR = \"data-accordion-root\";\nconst ACCORDION_TRIGGER_ATTR = \"data-accordion-trigger\";\nconst ACCORDION_CONTENT_ATTR = \"data-accordion-content\";\nconst ACCORDION_ITEM_ATTR = \"data-accordion-item\";\nconst ACCORDION_HEADER_ATTR = \"data-accordion-header\";\nclass AccordionBaseState {\n  opts;\n  rovingFocusGroup;\n  constructor(opts) {\n    this.opts = opts;\n    useRefById(this.opts);\n    this.rovingFocusGroup = useRovingFocus({\n      rootNodeId: this.opts.id,\n      candidateAttr: ACCORDION_TRIGGER_ATTR,\n      loop: this.opts.loop,\n      orientation: this.opts.orientation\n    });\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"data-orientation\": getDataOrientation(this.opts.orientation.current),\n    \"data-disabled\": getDataDisabled(this.opts.disabled.current),\n    [ACCORDION_ROOT_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass AccordionSingleState extends AccordionBaseState {\n  opts;\n  isMulti = false;\n  constructor(opts) {\n    super(opts);\n    this.opts = opts;\n    this.includesItem = this.includesItem.bind(this);\n    this.toggleItem = this.toggleItem.bind(this);\n  }\n  includesItem(item) {\n    return this.opts.value.current === item;\n  }\n  toggleItem(item) {\n    this.opts.value.current = this.includesItem(item) ? \"\" : item;\n  }\n}\nclass AccordionMultiState extends AccordionBaseState {\n  #value;\n  isMulti = true;\n  constructor(props) {\n    super(props);\n    this.#value = props.value;\n    this.includesItem = this.includesItem.bind(this);\n    this.toggleItem = this.toggleItem.bind(this);\n  }\n  includesItem(item) {\n    return this.#value.current.includes(item);\n  }\n  toggleItem(item) {\n    if (this.includesItem(item)) {\n      this.#value.current = this.#value.current.filter((v) => v !== item);\n    } else {\n      this.#value.current = [...this.#value.current, item];\n    }\n  }\n}\nclass AccordionItemState {\n  opts;\n  root;\n  #isActive = derived(() => this.root.includesItem(this.opts.value.current));\n  get isActive() {\n    return this.#isActive();\n  }\n  set isActive($$value) {\n    return this.#isActive($$value);\n  }\n  #isDisabled = derived(() => this.opts.disabled.current || this.root.opts.disabled.current);\n  get isDisabled() {\n    return this.#isDisabled();\n  }\n  set isDisabled($$value) {\n    return this.#isDisabled($$value);\n  }\n  constructor(opts) {\n    this.opts = opts;\n    this.root = opts.rootState;\n    this.updateValue = this.updateValue.bind(this);\n    useRefById({ ...opts, deps: () => this.isActive });\n  }\n  updateValue() {\n    this.root.toggleItem(this.opts.value.current);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"data-state\": getDataOpenClosed(this.isActive),\n    \"data-disabled\": getDataDisabled(this.isDisabled),\n    \"data-orientation\": getDataOrientation(this.root.opts.orientation.current),\n    [ACCORDION_ITEM_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass AccordionTriggerState {\n  opts;\n  itemState;\n  #root;\n  #isDisabled = derived(() => this.opts.disabled.current || this.itemState.opts.disabled.current || this.#root.opts.disabled.current);\n  constructor(opts, itemState) {\n    this.opts = opts;\n    this.itemState = itemState;\n    this.#root = itemState.root;\n    this.onkeydown = this.onkeydown.bind(this);\n    this.onclick = this.onclick.bind(this);\n    useRefById(opts);\n  }\n  onclick(e) {\n    if (this.#isDisabled()) return;\n    if (e.button !== 0) return e.preventDefault();\n    this.itemState.updateValue();\n  }\n  onkeydown(e) {\n    if (this.#isDisabled()) return;\n    if (e.key === SPACE || e.key === ENTER) {\n      e.preventDefault();\n      this.itemState.updateValue();\n      return;\n    }\n    this.#root.rovingFocusGroup.handleKeydown(this.opts.ref.current, e);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    disabled: this.#isDisabled(),\n    \"aria-expanded\": getAriaExpanded(this.itemState.isActive),\n    \"aria-disabled\": getAriaDisabled(this.#isDisabled()),\n    \"data-disabled\": getDataDisabled(this.#isDisabled()),\n    \"data-state\": getDataOpenClosed(this.itemState.isActive),\n    \"data-orientation\": getDataOrientation(this.#root.opts.orientation.current),\n    [ACCORDION_TRIGGER_ATTR]: \"\",\n    tabindex: 0,\n    //\n    onclick: this.onclick,\n    onkeydown: this.onkeydown\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass AccordionContentState {\n  opts;\n  item;\n  #originalStyles = void 0;\n  #isMountAnimationPrevented = false;\n  #width = 0;\n  #height = 0;\n  #present = derived(() => this.opts.forceMount.current || this.item.isActive);\n  get present() {\n    return this.#present();\n  }\n  set present($$value) {\n    return this.#present($$value);\n  }\n  constructor(opts, item) {\n    this.opts = opts;\n    this.item = item;\n    this.#isMountAnimationPrevented = this.item.isActive;\n    useRefById(opts);\n    watch(\n      [\n        () => this.present,\n        () => this.opts.ref.current\n      ],\n      ([_, node]) => {\n        if (!node) return;\n        afterTick(() => {\n          if (!this.opts.ref.current) return;\n          this.#originalStyles = this.#originalStyles || {\n            transitionDuration: node.style.transitionDuration,\n            animationName: node.style.animationName\n          };\n          node.style.transitionDuration = \"0s\";\n          node.style.animationName = \"none\";\n          const rect = node.getBoundingClientRect();\n          this.#height = rect.height;\n          this.#width = rect.width;\n          if (!this.#isMountAnimationPrevented) {\n            const { animationName, transitionDuration } = this.#originalStyles;\n            node.style.transitionDuration = transitionDuration;\n            node.style.animationName = animationName;\n          }\n        });\n      }\n    );\n  }\n  #snippetProps = derived(() => ({ open: this.item.isActive }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"data-state\": getDataOpenClosed(this.item.isActive),\n    \"data-disabled\": getDataDisabled(this.item.isDisabled),\n    \"data-orientation\": getDataOrientation(this.item.root.opts.orientation.current),\n    [ACCORDION_CONTENT_ATTR]: \"\",\n    style: {\n      \"--bits-accordion-content-height\": `${this.#height}px`,\n      \"--bits-accordion-content-width\": `${this.#width}px`\n    }\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass AccordionHeaderState {\n  opts;\n  item;\n  constructor(opts, item) {\n    this.opts = opts;\n    this.item = item;\n    useRefById(opts);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    role: \"heading\",\n    \"aria-level\": this.opts.level.current,\n    \"data-heading-level\": this.opts.level.current,\n    \"data-state\": getDataOpenClosed(this.item.isActive),\n    \"data-orientation\": getDataOrientation(this.item.root.opts.orientation.current),\n    [ACCORDION_HEADER_ATTR]: \"\"\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nconst AccordionRootContext = new Context(\"Accordion.Root\");\nconst AccordionItemContext = new Context(\"Accordion.Item\");\nfunction useAccordionRoot(props) {\n  const { type, ...rest } = props;\n  const rootState = type === \"single\" ? new AccordionSingleState(rest) : new AccordionMultiState(rest);\n  return AccordionRootContext.set(rootState);\n}\nfunction useAccordionItem(props) {\n  const rootState = AccordionRootContext.get();\n  return AccordionItemContext.set(new AccordionItemState({ ...props, rootState }));\n}\nfunction useAccordionTrigger(props) {\n  return new AccordionTriggerState(props, AccordionItemContext.get());\n}\nfunction useAccordionContent(props) {\n  return new AccordionContentState(props, AccordionItemContext.get());\n}\nfunction useAccordionHeader(props) {\n  return new AccordionHeaderState(props, AccordionItemContext.get());\n}\nfunction Accordion($$payload, $$props) {\n  push();\n  let {\n    disabled = false,\n    children,\n    child,\n    type,\n    value = void 0,\n    ref = null,\n    id = useId(),\n    onValueChange = noop,\n    loop = true,\n    orientation = \"vertical\",\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  function handleDefaultValue() {\n    if (value !== void 0) return;\n    value = type === \"single\" ? \"\" : [];\n  }\n  handleDefaultValue();\n  watch.pre(() => value, () => {\n    handleDefaultValue();\n  });\n  const rootState = useAccordionRoot({\n    type,\n    value: box.with(() => value, (v) => {\n      value = v;\n      onValueChange(v);\n    }),\n    id: box.with(() => id),\n    disabled: box.with(() => disabled),\n    loop: box.with(() => loop),\n    orientation: box.with(() => orientation),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, rootState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { value, ref });\n  pop();\n}\nfunction Accordion_item$1($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    disabled = false,\n    value = useId(),\n    children,\n    child,\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const itemState = useAccordionItem({\n    value: box.with(() => value),\n    disabled: box.with(() => disabled),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, itemState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Accordion_header($$payload, $$props) {\n  push();\n  let {\n    id = useId(),\n    level = 2,\n    children,\n    child,\n    ref = null,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const headerState = useAccordionHeader({\n    id: box.with(() => id),\n    level: box.with(() => level),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, headerState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></div>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Accordion_trigger$1($$payload, $$props) {\n  push();\n  let {\n    disabled = false,\n    ref = null,\n    id = useId(),\n    children,\n    child,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const triggerState = useAccordionTrigger({\n    disabled: box.with(() => disabled),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  const mergedProps = mergeProps(restProps, triggerState.props);\n  if (child) {\n    $$payload.out += \"<!--[-->\";\n    child($$payload, { props: mergedProps });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<button${spread_attributes({ type: \"button\", ...mergedProps }, null)}>`;\n    children?.($$payload);\n    $$payload.out += `<!----></button>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Accordion_content$1($$payload, $$props) {\n  push();\n  let {\n    child,\n    ref = null,\n    id = useId(),\n    forceMount = false,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const contentState = useAccordionContent({\n    forceMount: box.with(() => forceMount),\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v)\n  });\n  {\n    let presence = function($$payload2, { present }) {\n      const mergedProps = mergeProps(restProps, contentState.props, {\n        hidden: forceMount ? void 0 : !present.current\n      });\n      if (child) {\n        $$payload2.out += \"<!--[-->\";\n        child($$payload2, {\n          props: mergedProps,\n          ...contentState.snippetProps\n        });\n        $$payload2.out += `<!---->`;\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n        $$payload2.out += `<div${spread_attributes({ ...mergedProps }, null)}>`;\n        children?.($$payload2);\n        $$payload2.out += `<!----></div>`;\n      }\n      $$payload2.out += `<!--]-->`;\n    };\n    Presence_layer($$payload, {\n      forceMount: true,\n      present: contentState.present,\n      id,\n      presence\n    });\n  }\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Accordion_root($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    value = void 0,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Accordion($$payload2, spread_props([\n      { \"data-slot\": \"accordion\" },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        },\n        get value() {\n          return value;\n        },\n        set value($$value) {\n          value = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref, value });\n  pop();\n}\nfunction Accordion_content($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Accordion_content$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"accordion-content\",\n        class: cn(\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        },\n        children: ($$payload3) => {\n          $$payload3.out += `<div class=\"p-4\">`;\n          children?.($$payload3);\n          $$payload3.out += `<!----></div>`;\n        },\n        $$slots: { default: true }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Accordion_item($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Accordion_item$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"accordion-item\",\n        class: cn(\"border-b last:border-b-0\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Accordion_trigger($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    level = 3,\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Accordion_header($$payload2, {\n      level,\n      class: \"flex\",\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->`;\n        Accordion_trigger$1($$payload3, spread_props([\n          {\n            \"data-slot\": \"accordion-trigger\",\n            class: cn(\"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-row items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium outline-none transition-all hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\", className)\n          },\n          restProps,\n          {\n            get ref() {\n              return ref;\n            },\n            set ref($$value) {\n              ref = $$value;\n              $$settled = false;\n            },\n            children: ($$payload4) => {\n              children?.($$payload4);\n              $$payload4.out += `<!----> `;\n              Chevron_down($$payload4, {\n                class: \"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\"\n              });\n              $$payload4.out += `<!---->`;\n            },\n            $$slots: { default: true }\n          }\n        ]));\n        $$payload3.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Accordion_root as A,\n  Accordion_item as a,\n  Accordion_trigger as b,\n  Accordion_content as c\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAcA,MAAM,mBAAmB,GAAG,qBAAqB;AACjD,MAAM,sBAAsB,GAAG,wBAAwB;AACvD,MAAM,sBAAsB,GAAG,wBAAwB;AACvD,MAAM,mBAAmB,GAAG,qBAAqB;AACjD,MAAM,qBAAqB,GAAG,uBAAuB;AACrD,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI;AACN,EAAE,gBAAgB;AAClB,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC;AAC3C,MAAM,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;AAC9B,MAAM,aAAa,EAAE,sBAAsB;AAC3C,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI;AAC1B,MAAM,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC;AAC7B,KAAK,CAAC;AACN;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACzE,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAChE,IAAI,CAAC,mBAAmB,GAAG;AAC3B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,oBAAoB,SAAS,kBAAkB,CAAC;AACtD,EAAE,IAAI;AACN,EAAE,OAAO,GAAG,KAAK;AACjB,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,KAAK,CAAC,IAAI,CAAC;AACf,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AACpD,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAChD;AACA,EAAE,YAAY,CAAC,IAAI,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;AAC3C;AACA,EAAE,UAAU,CAAC,IAAI,EAAE;AACnB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI;AACjE;AACA;AACA,MAAM,mBAAmB,SAAS,kBAAkB,CAAC;AACrD,EAAE,MAAM;AACR,EAAE,OAAO,GAAG,IAAI;AAChB,EAAE,WAAW,CAAC,KAAK,EAAE;AACrB,IAAI,KAAK,CAAC,KAAK,CAAC;AAChB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK;AAC7B,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;AACpD,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AAChD;AACA,EAAE,YAAY,CAAC,IAAI,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;AAC7C;AACA,EAAE,UAAU,CAAC,IAAI,EAAE;AACnB,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AACjC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC;AACzE,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC;AAC1D;AACA;AACA;AACA,MAAM,kBAAkB,CAAC;AACzB,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,SAAS,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAC5E,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,SAAS,EAAE;AAC3B;AACA,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;AAClC;AACA,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AAC5F,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,WAAW,EAAE;AAC7B;AACA,EAAE,IAAI,UAAU,CAAC,OAAO,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACpC;AACA,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS;AAC9B,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AAClD,IAAI,UAAU,CAAC,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;AACtD;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACjD;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;AAClD,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;AACrD,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC9E,IAAI,CAAC,mBAAmB,GAAG;AAC3B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,qBAAqB,CAAC;AAC5B,EAAE,IAAI;AACN,EAAE,SAAS;AACX,EAAE,KAAK;AACP,EAAE,WAAW,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACrI,EAAE,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE;AAC/B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,IAAI;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AAC5B,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,cAAc,EAAE;AACjD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAChC;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;AAC5B,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,EAAE;AAC5C,MAAM,CAAC,CAAC,cAAc,EAAE;AACxB,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAClC,MAAM;AACN;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;AACvE;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;AAChC,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC7D,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AACxD,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AACxD,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC5D,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AAC/E,IAAI,CAAC,sBAAsB,GAAG,EAAE;AAChC,IAAI,QAAQ,EAAE,CAAC;AACf;AACA,IAAI,OAAO,EAAE,IAAI,CAAC,OAAO;AACzB,IAAI,SAAS,EAAE,IAAI,CAAC;AACpB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,qBAAqB,CAAC;AAC5B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,eAAe,GAAG,MAAM;AAC1B,EAAE,0BAA0B,GAAG,KAAK;AACpC,EAAE,MAAM,GAAG,CAAC;AACZ,EAAE,OAAO,GAAG,CAAC;AACb,EAAE,QAAQ,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9E,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,QAAQ,EAAE;AAC1B;AACA,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC;AACA,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ;AACxD,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB,IAAI,KAAK;AACT,MAAM;AACN,QAAQ,MAAM,IAAI,CAAC,OAAO;AAC1B,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AAC5B,OAAO;AACP,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK;AACrB,QAAQ,IAAI,CAAC,IAAI,EAAE;AACnB,QAAQ,SAAS,CAAC,MAAM;AACxB,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AACtC,UAAU,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI;AACzD,YAAY,kBAAkB,EAAE,IAAI,CAAC,KAAK,CAAC,kBAAkB;AAC7D,YAAY,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC;AACtC,WAAW;AACX,UAAU,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,IAAI;AAC9C,UAAU,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,MAAM;AAC3C,UAAU,MAAM,IAAI,GAAG,IAAI,CAAC,qBAAqB,EAAE;AACnD,UAAU,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM;AACpC,UAAU,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK;AAClC,UAAU,IAAI,CAAC,IAAI,CAAC,0BAA0B,EAAE;AAChD,YAAY,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC,eAAe;AAC9E,YAAY,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,kBAAkB;AAC9D,YAAY,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa;AACpD;AACA,SAAS,CAAC;AACV;AACA,KAAK;AACL;AACA,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC/D,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACvD,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;AAC1D,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACnF,IAAI,CAAC,sBAAsB,GAAG,EAAE;AAChC,IAAI,KAAK,EAAE;AACX,MAAM,iCAAiC,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;AAC5D,MAAM,gCAAgC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;AACzD;AACA,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,oBAAoB,CAAC;AAC3B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC,IAAI,CAAC;AACpB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACzC,IAAI,oBAAoB,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO;AACjD,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;AACvD,IAAI,kBAAkB,EAAE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;AACnF,IAAI,CAAC,qBAAqB,GAAG;AAC7B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,oBAAoB,GAAG,IAAI,OAAO,CAAC,gBAAgB,CAAC;AAC1D,MAAM,oBAAoB,GAAG,IAAI,OAAO,CAAC,gBAAgB,CAAC;AAC1D,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK;AACjC,EAAE,MAAM,SAAS,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC;AACtG,EAAE,OAAO,oBAAoB,CAAC,GAAG,CAAC,SAAS,CAAC;AAC5C;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE;AACjC,EAAE,MAAM,SAAS,GAAG,oBAAoB,CAAC,GAAG,EAAE;AAC9C,EAAE,OAAO,oBAAoB,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,EAAE,GAAG,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AAClF;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAE;AACpC,EAAE,OAAO,IAAI,qBAAqB,CAAC,KAAK,EAAE,oBAAoB,CAAC,GAAG,EAAE,CAAC;AACrE;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAE;AACpC,EAAE,OAAO,IAAI,qBAAqB,CAAC,KAAK,EAAE,oBAAoB,CAAC,GAAG,EAAE,CAAC;AACrE;AACA,SAAS,kBAAkB,CAAC,KAAK,EAAE;AACnC,EAAE,OAAO,IAAI,oBAAoB,CAAC,KAAK,EAAE,oBAAoB,CAAC,GAAG,EAAE,CAAC;AACpE;AACA,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,IAAI;AACR,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,IAAI,GAAG,IAAI;AACf,IAAI,WAAW,GAAG,UAAU;AAC5B,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,kBAAkB,GAAG;AAChC,IAAI,IAAI,KAAK,KAAK,MAAM,EAAE;AAC1B,IAAI,KAAK,GAAG,IAAI,KAAK,QAAQ,GAAG,EAAE,GAAG,EAAE;AACvC;AACA,EAAE,kBAAkB,EAAE;AACtB,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,KAAK,EAAE,MAAM;AAC/B,IAAI,kBAAkB,EAAE;AACxB,GAAG,CAAC;AACJ,EAAE,MAAM,SAAS,GAAG,gBAAgB,CAAC;AACrC,IAAI,IAAI;AACR,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,KAAK;AACxC,MAAM,KAAK,GAAG,CAAC;AACf,MAAM,aAAa,CAAC,CAAC,CAAC;AACtB,KAAK,CAAC;AACN,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9B,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,KAAK,GAAG,KAAK,EAAE;AACnB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,SAAS,GAAG,gBAAgB,CAAC;AACrC,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC,KAAK,CAAC;AAC5D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,WAAW,GAAG,kBAAkB,CAAC;AACzC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC;AAChC,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC;AAC9D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1E,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACpC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAE;AACjD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,mBAAmB,CAAC;AAC3C,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC;AACtC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC;AAC/D,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AAC5C,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7F,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACvC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAE;AACjD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,mBAAmB,CAAC;AAC3C,IAAI,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,UAAU,CAAC;AAC1C,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC;AAC3C,GAAG,CAAC;AACJ,EAAE;AACF,IAAI,IAAI,QAAQ,GAAG,SAAS,UAAU,EAAE,EAAE,OAAO,EAAE,EAAE;AACrD,MAAM,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,EAAE;AACpE,QAAQ,MAAM,EAAE,UAAU,GAAG,MAAM,GAAG,CAAC,OAAO,CAAC;AAC/C,OAAO,CAAC;AACR,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,KAAK,CAAC,UAAU,EAAE;AAC1B,UAAU,KAAK,EAAE,WAAW;AAC5B,UAAU,GAAG,YAAY,CAAC;AAC1B,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/E,QAAQ,QAAQ,GAAG,UAAU,CAAC;AAC9B,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,KAAK;AACL,IAAI,cAAc,CAAC,SAAS,EAAE;AAC9B,MAAM,UAAU,EAAE,IAAI;AACtB,MAAM,OAAO,EAAE,YAAY,CAAC,OAAO;AACnC,MAAM,EAAE;AACR,MAAM;AACN,KAAK,CAAC;AACN;AACA,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,SAAS,CAAC,UAAU,EAAE,YAAY,CAAC;AACvC,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE;AAClC,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B,SAAS;AACT,QAAQ,IAAI,KAAK,GAAG;AACpB,UAAU,OAAO,KAAK;AACtB,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE;AAC3B,UAAU,KAAK,GAAG,OAAO;AACzB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,mBAAmB,CAAC,UAAU,EAAE,YAAY,CAAC;AACjD,MAAM;AACN,QAAQ,WAAW,EAAE,mBAAmB;AACxC,QAAQ,KAAK,EAAE,EAAE,CAAC,2GAA2G,EAAE,SAAS;AACxI,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC/C,UAAU,QAAQ,GAAG,UAAU,CAAC;AAChC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AAC3C,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC;AAC9C,MAAM;AACN,QAAQ,WAAW,EAAE,gBAAgB;AACrC,QAAQ,KAAK,EAAE,EAAE,CAAC,0BAA0B,EAAE,SAAS;AACvD,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,gBAAgB,CAAC,UAAU,EAAE;AACjC,MAAM,KAAK;AACX,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,mBAAmB,CAAC,UAAU,EAAE,YAAY,CAAC;AACrD,UAAU;AACV,YAAY,WAAW,EAAE,mBAAmB;AAC5C,YAAY,KAAK,EAAE,EAAE,CAAC,8SAA8S,EAAE,SAAS;AAC/U,WAAW;AACX,UAAU,SAAS;AACnB,UAAU;AACV,YAAY,IAAI,GAAG,GAAG;AACtB,cAAc,OAAO,GAAG;AACxB,aAAa;AACb,YAAY,IAAI,GAAG,CAAC,OAAO,EAAE;AAC7B,cAAc,GAAG,GAAG,OAAO;AAC3B,cAAc,SAAS,GAAG,KAAK;AAC/B,aAAa;AACb,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,QAAQ,GAAG,UAAU,CAAC;AACpC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1C,cAAc,YAAY,CAAC,UAAU,EAAE;AACvC,gBAAgB,KAAK,EAAE;AACvB,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC;AACA,SAAS,CAAC,CAAC;AACX,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}