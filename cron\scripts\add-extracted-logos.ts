// Add extracted logos directly without R2 upload (bypass SSL issue)
import { PrismaClient } from "@prisma/client";
import { logger } from "../utils/logger";
import { chromium } from "playwright";

// Simple delay function
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

const prisma = new PrismaClient();

/**
 * Test if a logo URL is accessible
 */
async function testLogoUrl(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: "HEAD" });
    return response.ok;
  } catch (error) {
    return false;
  }
}

/**
 * Extract company logo from website
 */
async function extractLogoFromWebsite(website: string): Promise<string | null> {
  const browser = await chromium.launch({ headless: true });
  const context = await browser.newContext({
    userAgent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
  });
  const page = await context.newPage();

  try {
    logger.info(`🎨 Extracting logo from: ${website}`);

    await page.goto(website, {
      waitUntil: "domcontentloaded",
      timeout: 15000,
    });

    // Wait for page to load
    await delay(2000);

    // Try multiple strategies to find the logo
    const logoUrl = await page.evaluate((siteUrl) => {
      const url = new URL(siteUrl);
      const baseUrl = `${url.protocol}//${url.hostname}`;

      // Strategy 1: Look for common logo selectors
      const logoSelectors = [
        'img[alt*="logo" i]',
        'img[class*="logo" i]',
        'img[id*="logo" i]',
        ".logo img",
        "#logo img",
        "header img",
        ".header img",
        ".navbar img",
        ".nav img",
        '[class*="brand"] img',
        '[class*="header"] img:first-of-type',
      ];

      for (const selector of logoSelectors) {
        const img = document.querySelector(selector) as HTMLImageElement;
        if (img && img.src) {
          // Make sure it's not a tiny icon or placeholder
          if (img.width >= 50 || img.height >= 30) {
            return img.src.startsWith("http")
              ? img.src
              : new URL(img.src, baseUrl).href;
          }
        }
      }

      // Strategy 2: Look for favicon as fallback
      const favicon = document.querySelector(
        'link[rel*="icon"]'
      ) as HTMLLinkElement;
      if (favicon && favicon.href) {
        return favicon.href.startsWith("http")
          ? favicon.href
          : new URL(favicon.href, baseUrl).href;
      }

      return null;
    }, website);

    return logoUrl;
  } catch (error) {
    logger.warn(`Failed to extract logo from ${website}: ${error.message}`);
    return null;
  } finally {
    await browser.close();
  }
}

async function addExtractedLogos() {
  logger.info("🎨 Adding extracted logos directly (bypassing R2)");

  try {
    // Get companies without logos that have websites
    const companies = await prisma.company.findMany({
      where: {
        logoUrl: null,
        website: { not: null },
      },
      select: {
        id: true,
        name: true,
        website: true,
        domain: true,
      },
      take: 50, // Process 50 companies
    });

    logger.info(`📊 Found ${companies.length} companies that need logos`);

    if (companies.length === 0) {
      logger.info("✅ No companies need logos");
      return;
    }

    let logosAdded = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const company of companies) {
      try {
        logger.info(`🔍 Processing: ${company.name}`);

        if (!company.website) {
          logger.info(`   ⏭️ No website for ${company.name}`);
          skippedCount++;
          continue;
        }

        // Extract logo from website
        logger.info(`   🌐 Extracting logo from: ${company.website}`);
        const logoUrl = await extractLogoFromWebsite(company.website);

        if (!logoUrl) {
          logger.info(`   ❌ No logo found for ${company.name}`);
          skippedCount++;
          continue;
        }

        // Test if logo URL is accessible
        logger.info(`   🔗 Testing logo URL: ${logoUrl}`);
        const isAccessible = await testLogoUrl(logoUrl);

        if (!isAccessible) {
          logger.info(`   ❌ Logo URL not accessible: ${logoUrl}`);
          skippedCount++;
          continue;
        }

        // Save logo URL directly to database
        await prisma.company.update({
          where: { id: company.id },
          data: {
            logoUrl: logoUrl,
            // Also update domain if not set
            domain: company.domain || extractDomain(company.website),
          },
        });

        logger.info(`   ✅ Added extracted logo: ${logoUrl}`);
        logosAdded++;

        // Small delay to be respectful to websites
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        logger.error(`❌ Error processing ${company.name}:`, error);
        errorCount++;
      }
    }

    // Summary
    logger.info("\n🎉 Extracted logo processing completed!");
    logger.info(`   ✅ Logos added: ${logosAdded}`);
    logger.info(`   ⏭️ Skipped: ${skippedCount}`);
    logger.info(`   ❌ Errors: ${errorCount}`);
    logger.info(`   📊 Total processed: ${companies.length}`);

    if (logosAdded > 0) {
      logger.info(
        `\n📈 Success rate: ${((logosAdded / companies.length) * 100).toFixed(1)}%`
      );
    }

    // Show some examples of companies that got logos
    if (logosAdded > 0) {
      logger.info(`\n✅ Companies with new extracted logos:`);
      const companiesWithLogos = await prisma.company.findMany({
        where: {
          logoUrl: {
            not: null,
            not: { contains: "logo.clearbit.com" }, // Exclude Clearbit logos
          },
        },
        select: { name: true, logoUrl: true },
        take: 10,
        orderBy: { updatedAt: "desc" },
      });

      companiesWithLogos.forEach((company) => {
        logger.info(`   - ${company.name}: ${company.logoUrl}`);
      });
    }
  } catch (error) {
    logger.error("❌ Error adding extracted logos:", error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Extract domain from website URL
 */
function extractDomain(website: string): string | null {
  try {
    const url = new URL(
      website.startsWith("http") ? website : `https://${website}`
    );
    return url.hostname.replace(/^www\./, "");
  } catch (error) {
    return null;
  }
}

// Run the script
addExtractedLogos()
  .then(() => {
    logger.info("✅ Extracted logo script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    logger.error("❌ Extracted logo script failed:", error);
    process.exit(1);
  });

export { addExtractedLogos };
