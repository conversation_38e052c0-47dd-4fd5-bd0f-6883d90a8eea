{"version": 3, "file": "job-usage-0k4FwyQA.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/job-usage.js"], "sourcesContent": ["import { hasReachedLimit, trackFeatureUsage } from \"./feature-usage.js\";\nasync function trackJobSearch(userId) {\n  return trackFeatureUsage(userId, \"job_search\", \"job_searches_per_month\", 1);\n}\nasync function trackSavedJob(userId) {\n  return trackFeatureUsage(userId, \"job_save\", \"saved_jobs\", 1);\n}\nasync function canPerformJobSearch(userId) {\n  return !await hasReachedLimit(userId, \"job_search\", \"job_searches_per_month\");\n}\nasync function canSaveJob(userId) {\n  return !await hasReachedLimit(userId, \"job_save\", \"saved_jobs\");\n}\nexport {\n  canPerformJobSearch,\n  canSaveJob,\n  trackJobSearch,\n  trackSavedJob\n};\n"], "names": [], "mappings": ";;;;;;;;AACA,eAAe,cAAc,CAAC,MAAM,EAAE;AACtC,EAAE,OAAO,iBAAiB,CAAC,MAAM,EAAE,YAAY,EAAE,wBAAwB,EAAE,CAAC,CAAC;AAC7E;AACA,eAAe,aAAa,CAAC,MAAM,EAAE;AACrC,EAAE,OAAO,iBAAiB,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,CAAC,CAAC;AAC/D;AACA,eAAe,mBAAmB,CAAC,MAAM,EAAE;AAC3C,EAAE,OAAO,CAAC,MAAM,eAAe,CAAC,MAAM,EAAE,YAAY,EAAE,wBAAwB,CAAC;AAC/E;AACA,eAAe,UAAU,CAAC,MAAM,EAAE;AAClC,EAAE,OAAO,CAAC,MAAM,eAAe,CAAC,MAAM,EAAE,UAAU,EAAE,YAAY,CAAC;AACjE;;;;"}