import { p as push, P as stringify, O as escape_html, M as ensure_array_like, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { H as HelpSearch } from './HelpSearch-8uDSfRza.js';
import { H as HelpSidebar } from './HelpSidebar-DjmYKcdY.js';
import { H as HelpArticleCard } from './HelpArticleCard-CzuyIqVY.js';
import { A as Arrow_left } from './arrow-left-DyZbJRhp.js';
import { S as Search } from './search-B0oHlTPS.js';
import 'clsx';
import './false-CRHihH2U.js';
import './client-dNyMPa8V.js';
import './search-input-CbGkN9s9.js';
import './index-server-CezSOnuG.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index14-C2WSwUih.js';
import './scroll-lock-BkBz2nVp.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './is-mzPc4wSG.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './noop-n4I-x7yK.js';
import './kbd-constants-Ch6RKbNZ.js';
import './context-oepKpCf5.js';
import './use-id-CcFpwo20.js';
import './popper-layer-force-mount-GhIXXB9T.js';
import './presence-layer-B0FVaAYL.js';
import './scroll-area-Dn69zlyp.js';
import './use-debounce.svelte-gxToHznJ.js';
import './arrow-right-8SE89OuT.js';
import './Icon-A4vzmk-O.js';
import './accordion-trigger-DwieKZVA.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './chevron-down-xGjWLrZH.js';
import './house-CyPv7nOm.js';
import './file-text-HttY5S5h.js';
import './credit-card-8KNeZIt3.js';
import './shield-BzJ8ZsQa.js';
import './circle-help-Bsq6Onfx.js';
import './card-D-TLkt4h.js';
import './card-description-CMuO6f9m.js';
import './card-footer-Bs6oLfVt.js';
import './card-header-BSbSWnCH.js';
import './card-title-DNJv4RN2.js';
import './badge-C9pSznab.js';
import './index-DjwFQdT_.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  SEO($$payload, {
    title: "Search Results | Help Center",
    description: `Search results for '${stringify(data.query)}' in the Help Center.`,
    keywords: `help center, search, ${stringify(data.query)}, support, guides, tutorials`
  });
  $$payload.out += `<!----> <div class="container mx-auto px-4 py-12"><div class="grid gap-8 lg:grid-cols-4"><div class="lg:col-span-1">`;
  HelpSidebar($$payload, { categories: data.categories });
  $$payload.out += `<!----></div> <div class="lg:col-span-3"><div class="mb-6"><a href="/help" class="text-primary inline-flex items-center text-sm hover:underline">`;
  Arrow_left($$payload, { class: "mr-1 h-4 w-4" });
  $$payload.out += `<!----> Back to Help Center</a></div> <div class="mb-8 flex items-center gap-3"><div class="bg-primary/10 text-primary rounded-full p-3">`;
  Search($$payload, { class: "h-6 w-6" });
  $$payload.out += `<!----></div> <h1 class="text-3xl font-bold">Search Results</h1></div> <div class="mb-8">`;
  HelpSearch($$payload, { className: "w-full" });
  $$payload.out += `<!----></div> `;
  if (data.query) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-6"><p class="text-muted-foreground">${escape_html(data.resultCount)} result${escape_html(data.resultCount !== 1 ? "s" : "")} for "${escape_html(data.query)}"</p></div> `;
    if (data.searchResults.length > 0) {
      $$payload.out += "<!--[-->";
      const each_array = ensure_array_like(data.searchResults);
      $$payload.out += `<div class="grid gap-6 md:grid-cols-2 xl:grid-cols-3"><!--[-->`;
      for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
        let article = each_array[$$index];
        HelpArticleCard($$payload, { article });
      }
      $$payload.out += `<!--]--></div>`;
    } else {
      $$payload.out += "<!--[!-->";
      $$payload.out += `<div class="bg-muted rounded-lg border p-8 text-center"><p class="text-muted-foreground mb-2">No results found for "${escape_html(data.query)}".</p> <p>Try different keywords or browse categories.</p></div>`;
    }
    $$payload.out += `<!--]-->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="bg-muted rounded-lg border p-8 text-center"><p class="text-muted-foreground mb-2">Enter a search term to find help articles.</p> <p>Or browse categories in the sidebar.</p></div>`;
  }
  $$payload.out += `<!--]--></div></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-CmyhXzfv.js.map
