{"version": 3, "sources": ["../../inline-style-parser/index.js", "../../style-to-object/src/index.ts", "../../mode-watcher/node_modules/runed/dist/internal/configurable-globals.js", "../../mode-watcher/node_modules/runed/dist/internal/utils/dom.js", "../../mode-watcher/node_modules/runed/dist/utilities/active-element/active-element.svelte.js", "../../mode-watcher/node_modules/runed/dist/utilities/watch/watch.svelte.js", "../../mode-watcher/node_modules/runed/dist/utilities/persisted-state/persisted-state.svelte.js", "../../mode-watcher/node_modules/runed/dist/utilities/resource/resource.svelte.js", "../../mode-watcher/dist/utils.js", "../../svelte-toolbelt/dist/utils/is.js", "../../svelte-toolbelt/dist/box/box.svelte.js", "../../style-to-object/esm/index.mjs", "../../svelte-toolbelt/dist/utils/style-to-css.js", "../../svelte-toolbelt/dist/utils/style.js", "../../runed/dist/internal/configurable-globals.js", "../../runed/dist/internal/utils/dom.js", "../../runed/dist/utilities/active-element/active-element.svelte.js", "../../runed/dist/utilities/watch/watch.svelte.js", "../../mode-watcher/dist/storage-keys.svelte.js", "../../mode-watcher/dist/modes.js", "../../mode-watcher/dist/mode-states.svelte.js", "../../mode-watcher/dist/theme-state.svelte.js", "../../mode-watcher/dist/without-transition.js", "../../mode-watcher/dist/states.svelte.js", "../../mode-watcher/dist/mode.js", "../../mode-watcher/dist/components/mode-watcher-lite.svelte", "../../mode-watcher/dist/components/mode-watcher-full.svelte", "../../mode-watcher/dist/components/mode-watcher.svelte"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", "import type { Declaration } from 'inline-style-parser';\nimport parse from 'inline-style-parser';\n\nexport { Declaration };\n\ninterface StyleObject {\n  [name: string]: string;\n}\n\ntype Iterator = (\n  property: string,\n  value: string,\n  declaration: Declaration,\n) => void;\n\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nexport default function StyleToObject(\n  style: string,\n  iterator?: Iterator,\n): StyleObject | null {\n  let styleObject: StyleObject | null = null;\n\n  if (!style || typeof style !== 'string') {\n    return styleObject;\n  }\n\n  const declarations = parse(style);\n  const hasIterator = typeof iterator === 'function';\n\n  declarations.forEach((declaration) => {\n    if (declaration.type !== 'declaration') {\n      return;\n    }\n\n    const { property, value } = declaration;\n\n    if (hasIterator) {\n      iterator(property, value, declaration);\n    } else if (value) {\n      styleObject = styleObject || {};\n      styleObject[property] = value;\n    }\n  });\n\n  return styleObject;\n}\n", "import { BROWSER } from \"esm-env\";\nexport const defaultWindow = BROWSER && typeof window !== \"undefined\" ? window : undefined;\nexport const defaultDocument = BROWSER && typeof window !== \"undefined\" ? window.document : undefined;\nexport const defaultNavigator = BROWSER && typeof window !== \"undefined\" ? window.navigator : undefined;\nexport const defaultLocation = BROWSER && typeof window !== \"undefined\" ? window.location : undefined;\n", "import { defaultDocument } from \"../configurable-globals.js\";\n/**\n * <PERSON>les getting the active element in a document or shadow root.\n * If the active element is within a shadow root, it will traverse the shadow root\n * to find the active element.\n * If not, it will return the active element in the document.\n *\n * @param document A document or shadow root to get the active element from.\n * @returns The active element in the document or shadow root.\n */\nexport function getActiveElement(document) {\n    let activeElement = document.activeElement;\n    while (activeElement?.shadowRoot) {\n        const node = activeElement.shadowRoot.activeElement;\n        if (node === activeElement)\n            break;\n        else\n            activeElement = node;\n    }\n    return activeElement;\n}\n/**\n * Returns the owner document of a given element.\n *\n * @param node The element to get the owner document from.\n * @returns\n */\nexport function getOwnerDocument(node, fallback = defaultDocument) {\n    return node?.ownerDocument ?? fallback;\n}\n/**\n * Checks if an element is or is contained by another element.\n *\n * @param node The element to check if it or its descendants contain the target element.\n * @param target The element to check if it is contained by the node.\n * @returns\n */\nexport function isOrContainsTarget(node, target) {\n    return node === target || node.contains(target);\n}\n", "import { defaultWindow, } from \"../../internal/configurable-globals.js\";\nimport { getActiveElement } from \"../../internal/utils/dom.js\";\nimport { on } from \"svelte/events\";\nimport { createSubscriber } from \"svelte/reactivity\";\nexport class ActiveElement {\n    #document;\n    #subscribe;\n    constructor(options = {}) {\n        const { window = defaultWindow, document = window?.document } = options;\n        if (window === undefined)\n            return;\n        this.#document = document;\n        this.#subscribe = createSubscriber((update) => {\n            const cleanupFocusIn = on(window, \"focusin\", update);\n            const cleanupFocusOut = on(window, \"focusout\", update);\n            return () => {\n                cleanupFocusIn();\n                cleanupFocusOut();\n            };\n        });\n    }\n    get current() {\n        this.#subscribe?.();\n        if (!this.#document)\n            return null;\n        return getActiveElement(this.#document);\n    }\n}\n/**\n * An object holding a reactive value that is equal to `document.activeElement`.\n * It automatically listens for changes, keeping the reference up to date.\n *\n * If you wish to use a custom document or shadowRoot, you should use\n * [useActiveElement](https://runed.dev/docs/utilities/active-element) instead.\n *\n * @see {@link https://runed.dev/docs/utilities/active-element}\n */\nexport const activeElement = new ActiveElement();\n", "import { untrack } from \"svelte\";\nfunction runEffect(flush, effect) {\n    switch (flush) {\n        case \"post\":\n            $effect(effect);\n            break;\n        case \"pre\":\n            $effect.pre(effect);\n            break;\n    }\n}\nfunction runWatcher(sources, flush, effect, options = {}) {\n    const { lazy = false } = options;\n    // Run the effect immediately if `lazy` is `false`.\n    let active = !lazy;\n    // On the first run, if the dependencies are an array, pass an empty array\n    // to the previous value instead of `undefined` to allow destructuring.\n    //\n    // watch(() => [a, b], ([a, b], [prevA, prevB]) => { ... });\n    let previousValues = Array.isArray(sources)\n        ? []\n        : undefined;\n    runEffect(flush, () => {\n        const values = Array.isArray(sources) ? sources.map((source) => source()) : sources();\n        if (!active) {\n            active = true;\n            previousValues = values;\n            return;\n        }\n        const cleanup = untrack(() => effect(values, previousValues));\n        previousValues = values;\n        return cleanup;\n    });\n}\nfunction runWatcherOnce(sources, flush, effect) {\n    const cleanupRoot = $effect.root(() => {\n        let stop = false;\n        runWatcher(sources, flush, (values, previousValues) => {\n            if (stop) {\n                cleanupRoot();\n                return;\n            }\n            // Since `lazy` is `true`, `previousValues` is always defined.\n            const cleanup = effect(values, previousValues);\n            stop = true;\n            return cleanup;\n        }, \n        // Running the effect immediately just once makes no sense at all.\n        // That's just `onMount` with extra steps.\n        { lazy: true });\n    });\n    $effect(() => {\n        return cleanupRoot;\n    });\n}\nexport function watch(sources, effect, options) {\n    runWatcher(sources, \"post\", effect, options);\n}\nfunction watchPre(sources, effect, options) {\n    runWatcher(sources, \"pre\", effect, options);\n}\nwatch.pre = watchPre;\nexport function watchOnce(source, effect) {\n    runWatcherOnce(source, \"post\", effect);\n}\nfunction watchOncePre(source, effect) {\n    runWatcherOnce(source, \"pre\", effect);\n}\nwatchOnce.pre = watchOncePre;\n", "import { defaultWindow } from \"../../internal/configurable-globals.js\";\nimport { on } from \"svelte/events\";\nimport { createSubscriber } from \"svelte/reactivity\";\nfunction getStorage(storageType, window) {\n    switch (storageType) {\n        case \"local\":\n            return window.localStorage;\n        case \"session\":\n            return window.sessionStorage;\n    }\n}\n/**\n * Creates reactive state that is persisted and synchronized across browser sessions and tabs using Web Storage.\n * @param key The unique key used to store the state in the storage.\n * @param initialValue The initial value of the state if not already present in the storage.\n * @param options Configuration options including storage type, serializer for complex data types, and whether to sync state changes across tabs.\n *\n * @see {@link https://runed.dev/docs/utilities/persisted-state}\n */\nexport class PersistedState {\n    #current;\n    #key;\n    #serializer;\n    #storage;\n    #subscribe;\n    #version = $state(0);\n    constructor(key, initialValue, options = {}) {\n        const { storage: storageType = \"local\", serializer = { serialize: JSON.stringify, deserialize: JSON.parse }, syncTabs = true, window = defaultWindow, } = options;\n        this.#current = initialValue;\n        this.#key = key;\n        this.#serializer = serializer;\n        if (window === undefined)\n            return;\n        const storage = getStorage(storageType, window);\n        this.#storage = storage;\n        const existingValue = storage.getItem(key);\n        if (existingValue !== null) {\n            this.#current = this.#deserialize(existingValue);\n        }\n        else {\n            this.#serialize(initialValue);\n        }\n        if (syncTabs && storageType === \"local\") {\n            this.#subscribe = createSubscriber(() => {\n                return on(window, \"storage\", this.#handleStorageEvent);\n            });\n        }\n    }\n    get current() {\n        this.#subscribe?.();\n        this.#version;\n        const root = this.#deserialize(this.#storage?.getItem(this.#key)) ?? this.#current;\n        const proxies = new WeakMap();\n        const proxy = (value) => {\n            if (value === null || value?.constructor.name === \"Date\" || typeof value !== \"object\") {\n                return value;\n            }\n            let p = proxies.get(value);\n            if (!p) {\n                p = new Proxy(value, {\n                    get: (target, property) => {\n                        this.#version;\n                        return proxy(Reflect.get(target, property));\n                    },\n                    set: (target, property, value) => {\n                        this.#version += 1;\n                        Reflect.set(target, property, value);\n                        this.#serialize(root);\n                        return true;\n                    },\n                });\n                proxies.set(value, p);\n            }\n            return p;\n        };\n        return proxy(root);\n    }\n    set current(newValue) {\n        this.#serialize(newValue);\n        this.#version += 1;\n    }\n    #handleStorageEvent = (event) => {\n        if (event.key !== this.#key || event.newValue === null)\n            return;\n        this.#current = this.#deserialize(event.newValue);\n        this.#version += 1;\n    };\n    #deserialize(value) {\n        try {\n            return this.#serializer.deserialize(value);\n        }\n        catch (error) {\n            console.error(`Error when parsing \"${value}\" from persisted store \"${this.#key}\"`, error);\n            return;\n        }\n    }\n    #serialize(value) {\n        try {\n            if (value != undefined) {\n                this.#storage?.setItem(this.#key, this.#serializer.serialize(value));\n            }\n        }\n        catch (error) {\n            console.error(`Error when writing value from persisted store \"${this.#key}\" to ${this.#storage}`, error);\n        }\n    }\n}\n", "import { watch } from \"../watch/index.js\";\n// Helper functions for debounce and throttle\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction debounce(fn, delay) {\n    let timeoutId;\n    let lastResolve = null;\n    return (...args) => {\n        return new Promise((resolve) => {\n            if (lastResolve) {\n                lastResolve(undefined);\n            }\n            lastResolve = resolve;\n            clearTimeout(timeoutId);\n            timeoutId = setTimeout(async () => {\n                const result = await fn(...args);\n                if (lastResolve) {\n                    lastResolve(result);\n                    lastResolve = null;\n                }\n            }, delay);\n        });\n    };\n}\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction throttle(fn, delay) {\n    let lastRun = 0;\n    let lastPromise = null;\n    return (...args) => {\n        const now = Date.now();\n        if (lastRun && now - lastRun < delay) {\n            return lastPromise ?? Promise.resolve(undefined);\n        }\n        lastRun = now;\n        lastPromise = fn(...args);\n        return lastPromise;\n    };\n}\nfunction runResource(source, fetcher, options = {}, effectFn) {\n    const { lazy = false, once = false, initialValue, debounce: debounceTime, throttle: throttleTime, } = options;\n    // Create state\n    let current = $state(initialValue);\n    let loading = $state(false);\n    let error = $state(undefined);\n    let cleanupFns = $state([]);\n    // Helper function to run cleanup functions\n    const runCleanup = () => {\n        cleanupFns.forEach((fn) => fn());\n        cleanupFns = [];\n    };\n    // Helper function to register cleanup\n    const onCleanup = (fn) => {\n        cleanupFns = [...cleanupFns, fn];\n    };\n    // Create the base fetcher function\n    const baseFetcher = async (value, previousValue, refetching = false) => {\n        try {\n            loading = true;\n            error = undefined;\n            runCleanup();\n            // Create new AbortController for this fetch\n            const controller = new AbortController();\n            onCleanup(() => controller.abort());\n            // eslint-disable-next-line @typescript-eslint/no-explicit-any\n            const result = await fetcher(value, previousValue, {\n                data: current,\n                refetching,\n                onCleanup,\n                signal: controller.signal,\n            });\n            current = result;\n            return result;\n        }\n        catch (e) {\n            if (!(e instanceof DOMException && e.name === \"AbortError\")) {\n                error = e;\n            }\n            return undefined;\n        }\n        finally {\n            loading = false;\n        }\n    };\n    // Apply debounce or throttle if specified\n    const runFetcher = debounceTime\n        ? debounce(baseFetcher, debounceTime)\n        : throttleTime\n            ? throttle(baseFetcher, throttleTime)\n            : baseFetcher;\n    // Setup effect\n    const sources = Array.isArray(source) ? source : [source];\n    let prevValues;\n    effectFn((values, previousValues) => {\n        // Skip if once and already ran\n        if (once && prevValues) {\n            return;\n        }\n        // Skip if values haven't changed\n        if (prevValues && JSON.stringify(values) === JSON.stringify(prevValues)) {\n            return;\n        }\n        prevValues = values;\n        runFetcher(Array.isArray(source) ? values : values[0], Array.isArray(source) ? previousValues : previousValues?.[0]);\n    }, { lazy });\n    return {\n        get current() {\n            return current;\n        },\n        get loading() {\n            return loading;\n        },\n        get error() {\n            return error;\n        },\n        mutate: (value) => {\n            current = value;\n        },\n        refetch: (info) => {\n            const values = sources.map((s) => s());\n            return runFetcher(Array.isArray(source) ? values : values[0], Array.isArray(source) ? values : values[0], info ?? true);\n        },\n    };\n}\n// Implementation\nexport function resource(source, fetcher, options) {\n    return runResource(source, fetcher, options, (fn, options) => {\n        const sources = Array.isArray(source) ? source : [source];\n        const getters = () => sources.map((s) => s());\n        watch(getters, (values, previousValues) => {\n            fn(values, previousValues ?? []);\n        }, options);\n    });\n}\n// Implementation\nexport function resourcePre(source, fetcher, options) {\n    return runResource(source, fetcher, options, (fn, options) => {\n        const sources = Array.isArray(source) ? source : [source];\n        const getter = () => sources.map((s) => s());\n        watch.pre(getter, (values, previousValues) => {\n            fn(values, previousValues ?? []);\n        }, options);\n    });\n}\nresource.pre = resourcePre;\n", "/**\n * Sanitizes an array of classnames by removing any empty strings.\n */\nexport function sanitizeClassNames(classNames) {\n    return classNames.filter((className) => className.length > 0);\n}\nexport const noopStorage = {\n    getItem: (_key) => null,\n    setItem: (_key, _value) => { },\n};\nexport const isBrowser = typeof document !== \"undefined\";\n", "export function isFunction(value) {\n    return typeof value === \"function\";\n}\nexport function isObject(value) {\n    return value !== null && typeof value === \"object\";\n}\nconst CLASS_VALUE_PRIMITIVE_TYPES = [\"string\", \"number\", \"bigint\", \"boolean\"];\nexport function isClassValue(value) {\n    // handle primitive types\n    if (value === null || value === undefined)\n        return true;\n    if (CLASS_VALUE_PRIMITIVE_TYPES.includes(typeof value))\n        return true;\n    // handle arrays (ClassArray)\n    if (Array.isArray(value))\n        return value.every((item) => isClassValue(item));\n    // handle objects (ClassDictionary)\n    if (typeof value === \"object\") {\n        // ensure it's a plain object and not some other object type\n        if (Object.getPrototypeOf(value) !== Object.prototype)\n            return false;\n        return true;\n    }\n    return false;\n}\nconst ELEMENT_NODE = 1;\nconst DOCUMENT_NODE = 9;\nconst DOCUMENT_FRAGMENT_NODE = 11;\nexport function isHTMLElement(v) {\n    return isObject(v) && v.nodeType === ELEMENT_NODE && typeof v.nodeName === \"string\";\n}\nexport function isDocument(v) {\n    return isObject(v) && v.nodeType === DOCUMENT_NODE;\n}\nexport function isWindow(v) {\n    return isObject(v) && v === v.window;\n}\nexport function getNodeName(node) {\n    if (isHTMLElement(node))\n        return node.localName || \"\";\n    return \"#document\";\n}\nexport function isRootElement(node) {\n    return [\"html\", \"body\", \"#document\"].includes(getNodeName(node));\n}\nexport function isNode(v) {\n    return isObject(v) && v.nodeType !== undefined;\n}\nexport function isShadowRoot(v) {\n    return isNode(v) && v.nodeType === DOCUMENT_FRAGMENT_NODE && \"host\" in v;\n}\n", "import { isFunction, isObject } from \"../utils/is.js\";\nconst BoxSymbol = Symbol(\"box\");\nconst isWritableSymbol = Symbol(\"is-writable\");\n/**\n * @returns Whether the value is a Box\n *\n * @see {@link https://runed.dev/docs/functions/box}\n */\nfunction isBox(value) {\n    return isObject(value) && BoxSymbol in value;\n}\n/**\n * @returns Whether the value is a WritableBox\n *\n * @see {@link https://runed.dev/docs/functions/box}\n */\nfunction isWritableBox(value) {\n    return box.isBox(value) && isWritableSymbol in value;\n}\nexport function box(initialValue) {\n    let current = $state(initialValue);\n    return {\n        [BoxSymbol]: true,\n        [isWritableSymbol]: true,\n        get current() {\n            return current;\n        },\n        set current(v) {\n            current = v;\n        }\n    };\n}\nfunction boxWith(getter, setter) {\n    const derived = $derived.by(getter);\n    if (setter) {\n        return {\n            [BoxSymbol]: true,\n            [isWritableSymbol]: true,\n            get current() {\n                return derived;\n            },\n            set current(v) {\n                setter(v);\n            }\n        };\n    }\n    return {\n        [BoxSymbol]: true,\n        get current() {\n            return getter();\n        }\n    };\n}\nfunction boxFrom(value) {\n    if (box.isBox(value))\n        return value;\n    if (isFunction(value))\n        return box.with(value);\n    return box(value);\n}\n/**\n * Function that gets an object of boxes, and returns an object of reactive values\n *\n * @example\n * const count = box(0)\n * const flat = box.flatten({ count, double: box.with(() => count.current) })\n * // type of flat is { count: number, readonly double: number }\n *\n * @see {@link https://runed.dev/docs/functions/box}\n */\nfunction boxFlatten(boxes) {\n    return Object.entries(boxes).reduce((acc, [key, b]) => {\n        if (!box.isBox(b)) {\n            return Object.assign(acc, { [key]: b });\n        }\n        if (box.isWritableBox(b)) {\n            Object.defineProperty(acc, key, {\n                get() {\n                    return b.current;\n                },\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                set(v) {\n                    b.current = v;\n                }\n            });\n        }\n        else {\n            Object.defineProperty(acc, key, {\n                get() {\n                    return b.current;\n                }\n            });\n        }\n        return acc;\n    }, {});\n}\n/**\n * Function that converts a box to a readonly box.\n *\n * @example\n * const count = box(0) // WritableBox<number>\n * const countReadonly = box.readonly(count) // ReadableBox<number>\n *\n * @see {@link https://runed.dev/docs/functions/box}\n */\nfunction toReadonlyBox(b) {\n    if (!box.isWritableBox(b))\n        return b;\n    return {\n        [BoxSymbol]: true,\n        get current() {\n            return b.current;\n        }\n    };\n}\nbox.from = boxFrom;\nbox.with = boxWith;\nbox.flatten = boxFlatten;\nbox.readonly = toReadonlyBox;\nbox.isBox = isBox;\nbox.isWritableBox = isWritableBox;\n", "import StyleToObject from '../cjs/index.js';\n\n// ensure compatibility with rollup umd build\nexport default StyleToObject.default || StyleToObject;\n", "function createParser(matcher, replacer) {\n    const regex = RegExp(matcher, \"g\");\n    return (str) => {\n        // throw an error if not a string\n        if (typeof str !== \"string\") {\n            throw new TypeError(`expected an argument of type string, but got ${typeof str}`);\n        }\n        // if no match between string and matcher\n        if (!str.match(regex))\n            return str;\n        // executes the replacer function for each match\n        return str.replace(regex, replacer);\n    };\n}\nconst camelToKebab = createParser(/[A-Z]/, (match) => `-${match.toLowerCase()}`);\nexport function styleToCSS(styleObj) {\n    if (!styleObj || typeof styleObj !== \"object\" || Array.isArray(styleObj)) {\n        throw new TypeError(`expected an argument of type object, but got ${typeof styleObj}`);\n    }\n    return Object.keys(styleObj)\n        .map((property) => `${camelToKebab(property)}: ${styleObj[property]};`)\n        .join(\"\\n\");\n}\n", "import { styleToCSS } from \"./style-to-css.js\";\nexport function styleToString(style = {}) {\n    return styleToCSS(style).replace(\"\\n\", \" \");\n}\nexport const srOnlyStyles = {\n    position: \"absolute\",\n    width: \"1px\",\n    height: \"1px\",\n    padding: \"0\",\n    margin: \"-1px\",\n    overflow: \"hidden\",\n    clip: \"rect(0, 0, 0, 0)\",\n    whiteSpace: \"nowrap\",\n    borderWidth: \"0\",\n    transform: \"translateX(-100%)\"\n};\nexport const srOnlyStylesString = styleToString(srOnlyStyles);\n", "import { BROWSER } from \"esm-env\";\nexport const defaultWindow = BROWSER && typeof window !== \"undefined\" ? window : undefined;\nexport const defaultDocument = BROWSER && typeof window !== \"undefined\" ? window.document : undefined;\nexport const defaultNavigator = BROWSER && typeof window !== \"undefined\" ? window.navigator : undefined;\nexport const defaultLocation = BROWSER && typeof window !== \"undefined\" ? window.location : undefined;\n", "import { defaultDocument } from \"../configurable-globals.js\";\n/**\n * <PERSON>les getting the active element in a document or shadow root.\n * If the active element is within a shadow root, it will traverse the shadow root\n * to find the active element.\n * If not, it will return the active element in the document.\n *\n * @param document A document or shadow root to get the active element from.\n * @returns The active element in the document or shadow root.\n */\nexport function getActiveElement(document) {\n    let activeElement = document.activeElement;\n    while (activeElement?.shadowRoot) {\n        const node = activeElement.shadowRoot.activeElement;\n        if (node === activeElement)\n            break;\n        else\n            activeElement = node;\n    }\n    return activeElement;\n}\n/**\n * Returns the owner document of a given element.\n *\n * @param node The element to get the owner document from.\n * @returns\n */\nexport function getOwnerDocument(node, fallback = defaultDocument) {\n    return node?.ownerDocument ?? fallback;\n}\n/**\n * Checks if an element is or is contained by another element.\n *\n * @param node The element to check if it or its descendants contain the target element.\n * @param target The element to check if it is contained by the node.\n * @returns\n */\nexport function isOrContainsTarget(node, target) {\n    return node === target || node.contains(target);\n}\n", "import { defaultWindow, } from \"../../internal/configurable-globals.js\";\nimport { getActiveElement } from \"../../internal/utils/dom.js\";\nimport { on } from \"svelte/events\";\nimport { createSubscriber } from \"svelte/reactivity\";\nexport class ActiveElement {\n    #document;\n    #subscribe;\n    constructor(options = {}) {\n        const { window = defaultWindow, document = window?.document } = options;\n        if (window === undefined)\n            return;\n        this.#document = document;\n        this.#subscribe = createSubscriber((update) => {\n            const cleanupFocusIn = on(window, \"focusin\", update);\n            const cleanupFocusOut = on(window, \"focusout\", update);\n            return () => {\n                cleanupFocusIn();\n                cleanupFocusOut();\n            };\n        });\n    }\n    get current() {\n        this.#subscribe?.();\n        if (!this.#document)\n            return null;\n        return getActiveElement(this.#document);\n    }\n}\n/**\n * An object holding a reactive value that is equal to `document.activeElement`.\n * It automatically listens for changes, keeping the reference up to date.\n *\n * If you wish to use a custom document or shadowRoot, you should use\n * [useActiveElement](https://runed.dev/docs/utilities/active-element) instead.\n *\n * @see {@link https://runed.dev/docs/utilities/active-element}\n */\nexport const activeElement = new ActiveElement();\n", "import { untrack } from \"svelte\";\nfunction runEffect(flush, effect) {\n    switch (flush) {\n        case \"post\":\n            $effect(effect);\n            break;\n        case \"pre\":\n            $effect.pre(effect);\n            break;\n    }\n}\nfunction runWatcher(sources, flush, effect, options = {}) {\n    const { lazy = false } = options;\n    // Run the effect immediately if `lazy` is `false`.\n    let active = !lazy;\n    // On the first run, if the dependencies are an array, pass an empty array\n    // to the previous value instead of `undefined` to allow destructuring.\n    //\n    // watch(() => [a, b], ([a, b], [prevA, prevB]) => { ... });\n    let previousValues = Array.isArray(sources)\n        ? []\n        : undefined;\n    runEffect(flush, () => {\n        const values = Array.isArray(sources) ? sources.map((source) => source()) : sources();\n        if (!active) {\n            active = true;\n            previousValues = values;\n            return;\n        }\n        const cleanup = untrack(() => effect(values, previousValues));\n        previousValues = values;\n        return cleanup;\n    });\n}\nfunction runWatcherOnce(sources, flush, effect) {\n    const cleanupRoot = $effect.root(() => {\n        let stop = false;\n        runWatcher(sources, flush, (values, previousValues) => {\n            if (stop) {\n                cleanupRoot();\n                return;\n            }\n            // Since `lazy` is `true`, `previousValues` is always defined.\n            const cleanup = effect(values, previousValues);\n            stop = true;\n            return cleanup;\n        }, \n        // Running the effect immediately just once makes no sense at all.\n        // That's just `onMount` with extra steps.\n        { lazy: true });\n    });\n    $effect(() => {\n        return cleanupRoot;\n    });\n}\nexport function watch(sources, effect, options) {\n    runWatcher(sources, \"post\", effect, options);\n}\nfunction watchPre(sources, effect, options) {\n    runWatcher(sources, \"pre\", effect, options);\n}\nwatch.pre = watchPre;\nexport function watchOnce(source, effect) {\n    runWatcherOnce(source, \"post\", effect);\n}\nfunction watchOncePre(source, effect) {\n    runWatcherOnce(source, \"pre\", effect);\n}\nwatchOnce.pre = watchOncePre;\n", "import { box } from \"svelte-toolbelt\";\n/**\n * The key used to store the `mode` in localStorage.\n */\nexport const modeStorageKey = box(\"mode-watcher-mode\");\n/**\n * The key used to store the `theme` in localStorage.\n */\nexport const themeStorageKey = box(\"mode-watcher-theme\");\n", "/**\n * the modes that are supported, used for validation & type\n * derivation\n */\nexport const modes = [\"dark\", \"light\", \"system\"];\nexport function isValidMode(value) {\n    if (typeof value !== \"string\")\n        return false;\n    return modes.includes(value);\n}\n", "import { PersistedState, watch } from \"runed\";\nimport { isBrowser, noopStorage } from \"./utils.js\";\nimport { modeStorageKey } from \"./storage-keys.svelte.js\";\nimport { isValidMode } from \"./modes.js\";\nimport { MediaQuery } from \"svelte/reactivity\";\nexport class UserPrefersMode {\n    #defaultValue = \"system\";\n    #storage = isBrowser ? localStorage : noopStorage;\n    #initialValue = this.#storage.getItem(modeStorageKey.current);\n    #value = isValidMode(this.#initialValue) ? this.#initialValue : this.#defaultValue;\n    #persisted = $state(this.#makePersisted());\n    #makePersisted(value = this.#value) {\n        return new PersistedState(modeStorageKey.current, value, {\n            serializer: {\n                serialize: (v) => v,\n                deserialize: (v) => {\n                    if (isValidMode(v))\n                        return v;\n                    return this.#defaultValue;\n                },\n            },\n        });\n    }\n    constructor() {\n        $effect.root(() => {\n            return watch.pre(() => modeStorageKey.current, (_, prevStorageKey) => {\n                const currModeValue = this.#persisted.current;\n                this.#persisted = this.#makePersisted(currModeValue);\n                if (prevStorageKey) {\n                    localStorage.removeItem(prevStorageKey);\n                }\n            });\n        });\n    }\n    get current() {\n        return this.#persisted.current;\n    }\n    set current(newValue) {\n        this.#persisted.current = newValue;\n    }\n}\nexport class SystemPrefersMode {\n    #defaultValue = undefined;\n    #track = true;\n    #current = $state(this.#defaultValue);\n    #mediaQueryState = typeof window !== \"undefined\" && typeof window.matchMedia === \"function\"\n        ? new MediaQuery(\"prefers-color-scheme: light\")\n        : { current: false };\n    query() {\n        if (!isBrowser)\n            return;\n        this.#current = this.#mediaQueryState.current ? \"light\" : \"dark\";\n    }\n    tracking(active) {\n        this.#track = active;\n    }\n    constructor() {\n        $effect.root(() => {\n            $effect.pre(() => {\n                if (!this.#track)\n                    return;\n                this.query();\n            });\n        });\n        this.query = this.query.bind(this);\n        this.tracking = this.tracking.bind(this);\n    }\n    get current() {\n        return this.#current;\n    }\n}\n/**\n * Writable state that represents the user's preferred mode\n * (`\"dark\"`, `\"light\"` or `\"system\"`)\n */\nexport const userPrefersMode = new UserPrefersMode();\n/**\n * Readable store that represents the system's preferred mode (`\"dark\"`, `\"light\"` or `undefined`)\n */\nexport const systemPrefersMode = new SystemPrefersMode();\n", "import { PersistedState, watch } from \"runed\";\nimport { themeStorageKey } from \"./storage-keys.svelte.js\";\nimport { isBrowser, noopStorage } from \"./utils.js\";\nclass CustomTheme {\n    #storage = isBrowser ? localStorage : noopStorage;\n    #initialValue = this.#storage.getItem(themeStorageKey.current);\n    #value = this.#initialValue === null || this.#initialValue === undefined ? \"\" : this.#initialValue;\n    #persisted = $state(this.#makePersisted());\n    #makePersisted(value = this.#value) {\n        return new PersistedState(themeStorageKey.current, value, {\n            serializer: {\n                serialize: (v) => {\n                    if (typeof v !== \"string\")\n                        return \"\";\n                    return v;\n                },\n                deserialize: (v) => v,\n            },\n        });\n    }\n    constructor() {\n        $effect.root(() => {\n            return watch.pre(() => themeStorageKey.current, (_, prevStorageKey) => {\n                const currModeValue = this.#persisted.current;\n                this.#persisted = this.#makePersisted(currModeValue);\n                if (prevStorageKey) {\n                    localStorage.removeItem(prevStorageKey);\n                }\n            });\n        });\n    }\n    /**\n     * The current theme.\n     * @returns The current theme.\n     */\n    get current() {\n        return this.#persisted.current;\n    }\n    /**\n     * Set the current theme.\n     * @param newValue The new theme to set.\n     */\n    set current(newValue) {\n        this.#persisted.current = newValue;\n    }\n}\n/**\n * A custom theme to apply and persist to the root `html` element.\n */\nexport const customTheme = new CustomTheme();\n", "// Original Source: https://reemus.dev/article/disable-css-transition-color-scheme-change#heading-ultimate-solution-for-changing-color-scheme-without-transitions\nlet timeoutAction;\nlet timeoutEnable;\n/**\n * Whether this is the first time the function has been\n * called, which will be true for the initial load, where\n * we shouldn't need to disable any transitions, as there\n * is nothing to transition from.\n */\nlet hasLoaded = false;\n// Perform a task without any css transitions\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function withoutTransition(action) {\n    if (typeof document === \"undefined\")\n        return;\n    if (!hasLoaded) {\n        hasLoaded = true;\n        action();\n        return;\n    }\n    // Clear fallback timeouts\n    clearTimeout(timeoutAction);\n    clearTimeout(timeoutEnable);\n    // Create style element to disable transitions\n    const style = document.createElement(\"style\");\n    const css = document.createTextNode(`* {\n     -webkit-transition: none !important;\n     -moz-transition: none !important;\n     -o-transition: none !important;\n     -ms-transition: none !important;\n     transition: none !important;\n  }`);\n    style.appendChild(css);\n    // Functions to insert and remove style element\n    const disable = () => document.head.appendChild(style);\n    const enable = () => document.head.removeChild(style);\n    // Best method, getComputedStyle forces browser to repaint\n    if (typeof window.getComputedStyle !== \"undefined\") {\n        disable();\n        action();\n        window.getComputedStyle(style).opacity;\n        enable();\n        return;\n    }\n    // Better method, requestAnimationFrame processes function before next repaint\n    if (typeof window.requestAnimationFrame !== \"undefined\") {\n        disable();\n        action();\n        window.requestAnimationFrame(enable);\n        return;\n    }\n    // Fallback\n    disable();\n    timeoutAction = window.setTimeout(() => {\n        action();\n        timeoutEnable = window.setTimeout(enable, 120);\n    }, 120);\n}\n", "import { box } from \"svelte-toolbelt\";\nimport { isBrowser, sanitizeClassNames } from \"./utils.js\";\nimport { withoutTransition } from \"./without-transition.js\";\nimport { systemPrefersMode, userPrefersMode } from \"./mode-states.svelte.js\";\nimport { customTheme } from \"./theme-state.svelte.js\";\n/**\n * Theme colors for light and dark modes.\n */\nexport const themeColors = box(undefined);\n/**\n * Whether to disable transitions when changing the mode.\n */\nexport const disableTransitions = box(true);\n/**\n * The classnames to add to the root `html` element when the mode is dark.\n */\nexport const darkClassNames = box([]);\n/**\n * The classnames to add to the root `html` element when the mode is light.\n */\nexport const lightClassNames = box([]);\nfunction createDerivedMode() {\n    const current = $derived.by(() => {\n        if (!isBrowser)\n            return undefined;\n        const derivedMode = userPrefersMode.current === \"system\"\n            ? systemPrefersMode.current\n            : userPrefersMode.current;\n        const sanitizedDarkClassNames = sanitizeClassNames(darkClassNames.current);\n        const sanitizedLightClassNames = sanitizeClassNames(lightClassNames.current);\n        function update() {\n            const htmlEl = document.documentElement;\n            const themeColorEl = document.querySelector('meta[name=\"theme-color\"]');\n            if (derivedMode === \"light\") {\n                if (sanitizedDarkClassNames.length)\n                    htmlEl.classList.remove(...sanitizedDarkClassNames);\n                if (sanitizedLightClassNames.length)\n                    htmlEl.classList.add(...sanitizedLightClassNames);\n                htmlEl.style.colorScheme = \"light\";\n                if (themeColorEl && themeColors.current) {\n                    themeColorEl.setAttribute(\"content\", themeColors.current.light);\n                }\n            }\n            else {\n                if (sanitizedLightClassNames.length)\n                    htmlEl.classList.remove(...sanitizedLightClassNames);\n                if (sanitizedDarkClassNames.length)\n                    htmlEl.classList.add(...sanitizedDarkClassNames);\n                htmlEl.style.colorScheme = \"dark\";\n                if (themeColorEl && themeColors.current) {\n                    themeColorEl.setAttribute(\"content\", themeColors.current.dark);\n                }\n            }\n        }\n        if (disableTransitions.current) {\n            withoutTransition(update);\n        }\n        else {\n            update();\n        }\n        return derivedMode;\n    });\n    return {\n        get current() {\n            return current;\n        },\n    };\n}\nfunction createDerivedTheme() {\n    const current = $derived.by(() => {\n        customTheme.current;\n        if (!isBrowser)\n            return undefined;\n        function update() {\n            const htmlEl = document.documentElement;\n            htmlEl.setAttribute(\"data-theme\", customTheme.current);\n        }\n        if (disableTransitions.current) {\n            withoutTransition(update);\n        }\n        else {\n            update();\n        }\n        return customTheme.current;\n    });\n    return {\n        get current() {\n            return current;\n        },\n    };\n}\n/**\n * Derived store that represents the current mode (`\"dark\"`, `\"light\"` or `undefined`)\n */\nexport const derivedMode = createDerivedMode();\n/**\n * Derived store that represents the current custom theme\n */\nexport const derivedTheme = createDerivedTheme();\nexport { derivedMode as mode, derivedTheme as theme };\n", "import { userPrefersMode } from \"./mode-states.svelte.js\";\nimport { customTheme } from \"./theme-state.svelte.js\";\nimport { derivedMode } from \"./states.svelte.js\";\n/** Toggle between light and dark mode */\nexport function toggleMode() {\n    userPrefersMode.current = derivedMode.current === \"dark\" ? \"light\" : \"dark\";\n}\n/** Set the mode to light or dark */\nexport function setMode(mode) {\n    userPrefersMode.current = mode;\n}\n/** Reset the mode to operating system preference */\nexport function resetMode() {\n    userPrefersMode.current = \"system\";\n}\n/** Set the theme to a custom value */\nexport function setTheme(newTheme) {\n    customTheme.current = newTheme;\n}\nexport function defineConfig(config) {\n    return config;\n}\n/** Used to set the mode on initial page load to prevent FOUC */\nexport function setInitialMode({ defaultMode = \"system\", themeColors, darkClassNames = [\"dark\"], lightClassNames = [], defaultTheme = \"\", modeStorageKey = \"mode-watcher-mode\", themeStorageKey = \"mode-watcher-theme\", }) {\n    const rootEl = document.documentElement;\n    const mode = localStorage.getItem(modeStorageKey) ?? defaultMode;\n    const theme = localStorage.getItem(themeStorageKey) ?? defaultTheme;\n    const light = mode === \"light\" ||\n        (mode === \"system\" && window.matchMedia(\"(prefers-color-scheme: light)\").matches);\n    if (light) {\n        if (darkClassNames.length)\n            rootEl.classList.remove(...darkClassNames.filter(Boolean));\n        if (lightClassNames.length)\n            rootEl.classList.add(...lightClassNames.filter(Boolean));\n    }\n    else {\n        if (lightClassNames.length)\n            rootEl.classList.remove(...lightClassNames.filter(Boolean));\n        if (darkClassNames.length)\n            rootEl.classList.add(...darkClassNames.filter(Boolean));\n    }\n    rootEl.style.colorScheme = light ? \"light\" : \"dark\";\n    if (themeColors) {\n        const themeMetaEl = document.querySelector('meta[name=\"theme-color\"]');\n        if (themeMetaEl) {\n            themeMetaEl.setAttribute(\"content\", mode === \"light\" ? themeColors.light : themeColors.dark);\n        }\n    }\n    if (theme) {\n        rootEl.setAttribute(\"data-theme\", theme);\n        localStorage.setItem(themeStorageKey, theme);\n    }\n    localStorage.setItem(modeStorageKey, mode);\n}\n/**\n * A type-safe way to generate the source expression used to set the initial mode and avoid FOUC.\n *\n * @deprecated Use `createInitialModeExpression` instead.\n */\nexport function generateSetInitialModeExpression(config = {}) {\n    return `(${setInitialMode.toString()})(${JSON.stringify(config)});`;\n}\n/**\n * A type-safe way to generate the source expression used to set the initial mode and avoid FOUC.\n */\nexport const createInitialModeExpression = generateSetInitialModeExpression;\n", null, null, null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAEA,QAAI,gBAAgB;AAEpB,QAAI,gBAAgB;AACpB,QAAI,mBAAmB;AAGvB,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AAGtB,QAAI,aAAa;AAGjB,QAAI,UAAU;AACd,QAAI,gBAAgB;AACpB,QAAI,WAAW;AACf,QAAI,eAAe;AAGnB,QAAI,eAAe;AACnB,QAAI,mBAAmB;AASvB,WAAO,UAAU,SAAU,OAAO,SAAS;AACzC,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,CAAC,MAAO,QAAO,CAAC;AAEpB,gBAAU,WAAW,CAAC;AAKtB,UAAI,SAAS;AACb,UAAI,SAAS;AAOb,eAAS,eAAe,KAAK;AAC3B,YAAI,QAAQ,IAAI,MAAM,aAAa;AACnC,YAAI,MAAO,WAAU,MAAM;AAC3B,YAAI,IAAI,IAAI,YAAY,OAAO;AAC/B,iBAAS,CAAC,IAAI,IAAI,SAAS,IAAI,SAAS,IAAI;AAAA,MAC9C;AAOA,eAAS,WAAW;AAClB,YAAI,QAAQ,EAAE,MAAM,QAAQ,OAAe;AAC3C,eAAO,SAAU,MAAM;AACrB,eAAK,WAAW,IAAI,SAAS,KAAK;AAClC,qBAAW;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAUA,eAAS,SAAS,OAAO;AACvB,aAAK,QAAQ;AACb,aAAK,MAAM,EAAE,MAAM,QAAQ,OAAe;AAC1C,aAAK,SAAS,QAAQ;AAAA,MACxB;AAKA,eAAS,UAAU,UAAU;AAE7B,UAAI,aAAa,CAAC;AAQlB,eAAS,MAAM,KAAK;AAClB,YAAI,MAAM,IAAI;AAAA,UACZ,QAAQ,SAAS,MAAM,SAAS,MAAM,SAAS,OAAO;AAAA,QACxD;AACA,YAAI,SAAS;AACb,YAAI,WAAW,QAAQ;AACvB,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,SAAS;AAEb,YAAI,QAAQ,QAAQ;AAClB,qBAAW,KAAK,GAAG;AAAA,QACrB,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAQA,eAAS,MAAM,IAAI;AACjB,YAAI,IAAI,GAAG,KAAK,KAAK;AACrB,YAAI,CAAC,EAAG;AACR,YAAI,MAAM,EAAE,CAAC;AACb,uBAAe,GAAG;AAClB,gBAAQ,MAAM,MAAM,IAAI,MAAM;AAC9B,eAAO;AAAA,MACT;AAKA,eAAS,aAAa;AACpB,cAAM,gBAAgB;AAAA,MACxB;AAQA,eAAS,SAAS,OAAO;AACvB,YAAI;AACJ,gBAAQ,SAAS,CAAC;AAClB,eAAQ,IAAIA,SAAQ,GAAI;AACtB,cAAI,MAAM,OAAO;AACf,kBAAM,KAAK,CAAC;AAAA,UACd;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAQA,eAASA,WAAU;AACjB,YAAI,MAAM,SAAS;AACnB,YAAI,iBAAiB,MAAM,OAAO,CAAC,KAAK,YAAY,MAAM,OAAO,CAAC,EAAG;AAErE,YAAI,IAAI;AACR,eACE,gBAAgB,MAAM,OAAO,CAAC,MAC7B,YAAY,MAAM,OAAO,CAAC,KAAK,iBAAiB,MAAM,OAAO,IAAI,CAAC,IACnE;AACA,YAAE;AAAA,QACJ;AACA,aAAK;AAEL,YAAI,iBAAiB,MAAM,OAAO,IAAI,CAAC,GAAG;AACxC,iBAAO,MAAM,wBAAwB;AAAA,QACvC;AAEA,YAAI,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC;AAC9B,kBAAU;AACV,uBAAe,GAAG;AAClB,gBAAQ,MAAM,MAAM,CAAC;AACrB,kBAAU;AAEV,eAAO,IAAI;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAQA,eAAS,cAAc;AACrB,YAAI,MAAM,SAAS;AAGnB,YAAIC,QAAO,MAAM,cAAc;AAC/B,YAAI,CAACA,MAAM;AACX,QAAAD,SAAQ;AAGR,YAAI,CAAC,MAAM,WAAW,EAAG,QAAO,MAAM,sBAAsB;AAG5D,YAAI,MAAM,MAAM,WAAW;AAE3B,YAAI,MAAM,IAAI;AAAA,UACZ,MAAM;AAAA,UACN,UAAU,KAAKC,MAAK,CAAC,EAAE,QAAQ,eAAe,YAAY,CAAC;AAAA,UAC3D,OAAO,MACH,KAAK,IAAI,CAAC,EAAE,QAAQ,eAAe,YAAY,CAAC,IAChD;AAAA,QACN,CAAC;AAGD,cAAM,eAAe;AAErB,eAAO;AAAA,MACT;AAOA,eAAS,eAAe;AACtB,YAAI,QAAQ,CAAC;AAEb,iBAAS,KAAK;AAGd,YAAI;AACJ,eAAQ,OAAO,YAAY,GAAI;AAC7B,cAAI,SAAS,OAAO;AAClB,kBAAM,KAAK,IAAI;AACf,qBAAS,KAAK;AAAA,UAChB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,iBAAW;AACX,aAAO,aAAa;AAAA,IACtB;AAQA,aAAS,KAAK,KAAK;AACjB,aAAO,MAAM,IAAI,QAAQ,YAAY,YAAY,IAAI;AAAA,IACvD;AAAA;AAAA;;;;;;;;;;ACvOA,YAAA,UAAAC;AA5BA,QAAA,wBAAA,gBAAA,6BAAA;AA4BA,aAAwBA,eACtB,OACA,UAAmB;AAEnB,UAAI,cAAkC;AAEtC,UAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,eAAO;MACT;AAEA,UAAM,gBAAe,GAAA,sBAAA,SAAM,KAAK;AAChC,UAAM,cAAc,OAAO,aAAa;AAExC,mBAAa,QAAQ,SAAC,aAAW;AAC/B,YAAI,YAAY,SAAS,eAAe;AACtC;QACF;AAEQ,YAAA,WAAoB,YAAW,UAArB,QAAU,YAAW;AAEvC,YAAI,aAAa;AACf,mBAAS,UAAU,OAAO,WAAW;QACvC,WAAW,OAAO;AAChB,wBAAc,eAAe,CAAA;AAC7B,sBAAY,QAAQ,IAAI;QAC1B;MACF,CAAC;AAED,aAAO;IACT;;;;;ACzDO,IAAM,gBAAgB,gBAAW,OAAO,WAAW,cAAc,SAAS;AAC1E,IAAM,kBAAkB,gBAAW,OAAO,WAAW,cAAc,OAAO,WAAW;AACrF,IAAM,mBAAmB,gBAAW,OAAO,WAAW,cAAc,OAAO,YAAY;AACvF,IAAM,kBAAkB,gBAAW,OAAO,WAAW,cAAc,OAAO,WAAW;;;ACMrF,SAAS,iBAAiBC,WAAU;AACvC,MAAIC,iBAAgBD,UAAS;AAC7B,SAAOC,kBAAA,gBAAAA,eAAe,YAAY;AAC9B,UAAM,OAAOA,eAAc,WAAW;AACtC,QAAI,SAASA;AACT;AAAA;AAEA,MAAAA,iBAAgB;AAAA,EACxB;AACA,SAAOA;AACX;;;;IChBa,sBAAc;EAGvB,YAAY,UAAO,CAAA,GAAO;;;;MACd,QAAAC,UAAS;MAAe,UAAAC,YAAWD,WAAA,gBAAAA,QAAQ;QAAa;sBAC5DA,SAAW,MAAS,EAAA;AAExB,uBAAI,WAAaC;AACjB,uBAAI,YAAc,iBAAgB,CAAE,WAAW;YACrC,iBAAiB,GAAGD,SAAQ,WAAW,MAAM;YAC7C,kBAAkB,GAAGA,SAAQ,YAAY,MAAM;mBACxC;AACT,uBAAc;AACd,wBAAe;MACnB;IACJ,CAAC;EACL;MACI,UAAU;;AACV,6BAAI,gBAAJ;SACK,mBAAI,WAAU,QACR;WACJ,iBAAiB,mBAAI,UAAU;EAC1C;AACJ;;;IAUa,gBAAa,IAAO,cAAa;;;SCpCrC,UAAU,OAAO,QAAQ;UACtB,OAAK;SACJ;AACD,MAAA,YAAQ,MAAM;;SAEb;AACD,MAAA,gBAAY,MAAM;;;AAG9B;SACS,WAAW,SAAS,OAAO,QAAQ,UAAO,CAAA,GAAO;UAC9C,OAAO,MAAK,IAAK;MAErB,SAAM,CAAI;MAKV,iBAAiB,MAAM,QAAQ,OAAO,IAAA,CAAA,IAEpC;AACN,YAAU,OAAK,MAAQ;UACb,SAAS,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAG,CAAE,WAAW,OAAM,CAAA,IAAM,QAAO;SAC9E,QAAQ;AACT,eAAS;AACT,uBAAiB;;IAErB;UACM,UAAU,QAAO,MAAO,OAAO,QAAQ,cAAc,CAAA;AAC3D,qBAAiB;WACV;EACX,CAAC;AACL;SACS,eAAe,SAAS,OAAO,QAAQ;QACtC,cAAW,YAAA,MAAsB;QAC/B,OAAO;AACX;MAAW;MAAS;OAAQ,QAAQ,mBAAmB;YAC/C,MAAM;AACN,sBAAW;;QAEf;cAEM,UAAU,OAAO,QAAQ,cAAc;AAC7C,eAAO;eACA;MACX;;;QAGE,MAAM,KAAI;;EAChB,CAAC;AACD,EAAA,YAAO,MAAO;WACH;EACX,CAAC;AACL;SACgB,MAAM,SAAS,QAAQ,SAAS;AAC5C,aAAW,SAAS,QAAQ,QAAQ,OAAO;AAC/C;SACS,SAAS,SAAS,QAAQ,SAAS;AACxC,aAAW,SAAS,OAAO,QAAQ,OAAO;AAC9C;AACA,MAAM,MAAM;SACI,UAAU,QAAQ,QAAQ;AACtC,iBAAe,QAAQ,QAAQ,MAAM;AACzC;SACS,aAAa,QAAQ,QAAQ;AAClC,iBAAe,QAAQ,OAAO,MAAM;AACxC;AACA,UAAU,MAAM;;;SCjEP,WAAW,aAAaE,SAAQ;UAC7B,aAAW;SACV;aACMA,QAAO;SACb;aACMA,QAAO;;AAE1B;;IASa,uBAAe;EAOxB,YAAY,KAAK,cAAc,UAAO,CAAA,GAAO;;;;;;;iCADrC,MAAU,CAAC;4CAwDA,CAAI,UAAU;wBACzB,MAAM,KAAQ,mBAAI,OAAK,KAAA,KAAA,cAAI,MAAM,UAAa,IAAI,EAAA;AAEtD,yBAAI,UAAY,sBAAI,2CAAJ,WAAkB,MAAM;UACxC,mBAAI,WAAS,IAAb,mBAAI,SAAS,IAAI,CAAC;IACtB;;MA3DY,SAAS,cAAc;MAAS,aAAU;QAAK,WAAW,KAAK;QAAW,aAAa,KAAK;;MAAS,WAAW;MAAM,QAAAA,UAAS;QAAmB;AAC1J,uBAAI,UAAY;AAChB,uBAAI,MAAQ;AACZ,uBAAI,aAAe;sBACfA,SAAW,MAAS,EAAA;UAElB,UAAU,WAAW,aAAaA,OAAM;AAC9C,uBAAI,UAAY;UACV,gBAAgB,QAAQ,QAAQ,GAAG;sBACrC,eAAkB,MAAI,KAAA,GAAE;AACxB,yBAAI,UAAY,sBAAI,2CAAJ,WAAkB;IACtC,OACK;AACD,4BAAI,yCAAJ,WAAgB;IACpB;QACI,YAAQ,cAAI,aAAgB,OAAO,GAAE;AACrC,yBAAIC,aAAc,iBAAgB,MAAO;eAC9B,GAAGD,SAAQ,WAAW,mBAAI,oBAAoB;MACzD,CAAC;IACL;EACJ;MACI,UAAU;;AACV,6BAAIC,iBAAJ;QACA,mBAAI,SAAS;UACP,OAAO,sBAAI,2CAAJ,YAAkB,wBAAI,cAAJ,mBAAe,QAAQ,mBAAI,WAAW,mBAAI;UACnE,UAAO,oBAAO,QAAO;UACrBC,SAAK,CAAI,UAAU;wBACjB,OAAU,IAAI,KAAA,cAAI,+BAAO,YAAY,MAAS,MAAM,KAAA,cAAA,OAAW,OAAU,UAAQ,KAAA,GAAE;eAC5E;MACX;UACI,IAAI,QAAQ,IAAI,KAAK;WACpB,GAAG;AACJ,YAAC,IAAO,MAAM,OAAK;UACf,KAAG,CAAG,QAAQ,aAAa;gBACvB,mBAAI,SAAS;mBACNA,OAAM,QAAQ,IAAI,QAAQ,QAAQ,CAAA;UAC7C;UACA,KAAG,CAAG,QAAQ,UAAUC,WAAU;gBAC9B,mBAAI,WAAS,IAAb,mBAAI,SAAS,IAAI,CAAC;AAClB,oBAAQ,IAAI,QAAQ,UAAUA,MAAK;AACnC,kCAAI,yCAAJ,WAAgB;mBACT;UACX;;AAEJ,gBAAQ,IAAI,OAAO,CAAC;MACxB;aACO;IACX;WACOD,OAAM,IAAI;EACrB;MACI,QAAQ,UAAU;AAClB,0BAAI,yCAAJ,WAAgB;QAChB,mBAAI,WAAS,IAAb,mBAAI,SAAS,IAAI,CAAC;EACtB;AA0BJ;;;;;;;;;iBAnBgB,SAAC,OAAO;MACZ;WACO,mBAAI,aAAa,YAAY,KAAK;EAC7C,SACO,OAAO;AACV,YAAQ,MAAK,GAAA,sBAAA,SAAA,uBAAwB,KAAK,2BAA2B,mBAAI,KAAK,KAAK,KAAK,CAAA;;EAE5F;AACJ;eACU,SAAC,OAAO;;MACV;eACI,OAAS,QAAS,KAAA,GAAE;AACpB,+BAAI,cAAJ,mBAAe,QAAQ,mBAAI,OAAO,mBAAI,aAAa,UAAU,KAAK;IACtE;EACJ,SACO,OAAO;AACV,YAAQ,MAAK,GAAA,sBAAA,SAAA,kDAAmD,mBAAI,KAAK,QAAQ,mBAAI,SAAS,IAAI,KAAK,CAAA;EAC3G;AACJ;;;SCtGK,SAAS,IAAI,OAAO;MACrB;MACA,cAAc;aACP,SAAS;eACL,QAAO,CAAE,YAAY;UACxB,aAAa;AACb,oBAAY,MAAS;MACzB;AACA,oBAAc;AACd,mBAAa,SAAS;AACtB,kBAAY;oBAAuB;gBACzB,SAAM,MAAS,GAAE,GAAI,IAAI;cAC3B,aAAa;AACb,wBAAY,MAAM;AAClB,0BAAc;UAClB;QACJ;QAAG;;IACP,CAAC;EACL;AACJ;SAES,SAAS,IAAI,OAAO;MACrB,UAAU;MACV,cAAc;aACP,SAAS;UACV,MAAM,KAAK,IAAG;QAChB,WAAW,MAAM,UAAU,OAAO;aAC3B,eAAe,QAAQ,QAAQ,MAAS;IACnD;AACA,cAAU;AACV,kBAAc,GAAE,GAAI,IAAI;WACjB;EACX;AACJ;SACS,YAAY,QAAQ,SAAS,UAAO,CAAA,GAAO,UAAU;;IAClD,OAAO;IAAO,OAAO;IAAO;IAAc,UAAU;IAAc,UAAU;MAAkB;MAElG,UAAO,MAAA,MAAU,YAAY,CAAA;MAC7B,UAAO,MAAU,KAAK;MACtB,QAAK,MAAU,MAAS;MACxB,aAAU,MAAA,MAAA,CAAA,CAAA,CAAA;QAER,aAAU,MAAS;QACrB,UAAU,EAAC,QAAO,CAAE,OAAO,GAAE,CAAA;QAC7B,YAAU,CAAA,GAAA,IAAA;EACd;QAEM,YAAS,CAAI,OAAO;QACtB,YAAU,CAAA,GAAA,IAAO,UAAU,GAAE,EAAE,GAAA,IAAA;EACnC;QAEM,cAAW,OAAU,OAAO,eAAe,aAAa,UAAU;QAChE;UACA,SAAU,IAAI;UACd,OAAQ,MAAS;AACjB,iBAAU;YAEJ,aAAU,IAAO,gBAAe;AACtC,gBAAS,MAAO,WAAW,MAAK,CAAA;YAE1B,SAAM,MAAS,QAAQ,OAAO,eAAa;QAC7C,MAAI,IAAE,OAAO;QACb;QACA;QACA,QAAQ,WAAW;;UAEvB,SAAU,QAAM,IAAA;aACT;IACX,SACO,GAAG;YACA,aAAa,gBAAY,cAAI,EAAE,MAAS,YAAY,IAAG;YACzD,OAAQ,GAAC,IAAA;MACb;aACO;IACX,UAAC;UAEG,SAAU,KAAK;IACnB;EACJ;QAEM,aAAa,eACb,SAAS,aAAa,YAAY,IAClC,eACI,SAAS,aAAa,YAAY,IAClC;QAEJ,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAM,CAAI,MAAM;MACpD;AACJ;KAAU,QAAQ,mBAAmB;UAE7B,QAAQ,YAAY;;MAExB;UAEI,cAAU,cAAI,KAAK,UAAU,MAAM,GAAM,KAAK,UAAU,UAAU,CAAA,GAAG;;MAEzE;AACA,mBAAa;AACb,iBAAW,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,QAAQ,MAAM,IAAI,iBAAiB,iDAAiB,EAAC;IACtH;MAAK,KAAI;;;QAED,UAAU;iBACH,OAAO;IAClB;QACI,UAAU;iBACH,OAAO;IAClB;QACI,QAAQ;iBACD,KAAK;IAChB;IACA,QAAM,CAAG,UAAU;UACf,SAAU,OAAK,IAAA;IACnB;IACA,SAAO,CAAG,SAAS;YACT,SAAS,QAAQ,IAAG,CAAE,MAAM,EAAC,CAAA;aAC5B,WAAW,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,QAAQ,MAAM,IAAI,SAAS,OAAO,CAAC,GAAG,QAAQ,IAAI;IAC1H;;AAER;SAEgB,SAAS,QAAQ,SAAS,SAAS;SACxC,YAAY,QAAQ,SAAS,SAAO,CAAG,IAAIE,aAAY;UACpD,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAM,CAAI,MAAM;UAClD,UAAO,MAAS,QAAQ,IAAG,CAAE,MAAM,EAAC,CAAA;AAC1C;MAAM;OAAU,QAAQ,mBAAmB;AACvC,WAAG,QAAQ,kBAAc,CAAA,CAAA;MAC7B;MAAGA;;EACP,CAAC;AACL;SAEgB,YAAY,QAAQ,SAAS,SAAS;SAC3C,YAAY,QAAQ,SAAS,SAAO,CAAG,IAAIA,aAAY;UACpD,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAM,CAAI,MAAM;UAClD,SAAM,MAAS,QAAQ,IAAG,CAAE,MAAM,EAAC,CAAA;AACzC,UAAM;MAAI;OAAS,QAAQ,mBAAmB;AAC1C,WAAG,QAAQ,kBAAc,CAAA,CAAA;MAC7B;MAAGA;;EACP,CAAC;AACL;AACA,SAAS,MAAM;;;AC3IR,SAAS,mBAAmB,YAAY;AAC3C,SAAO,WAAW,OAAO,CAAC,cAAc,UAAU,SAAS,CAAC;AAChE;AACO,IAAM,cAAc;AAAA,EACvB,SAAS,CAACC,UAAS;AAAA,EACnB,SAAS,CAACA,OAAMC,YAAW;AAAA,EAAE;AACjC;AACO,IAAM,YAAY,OAAO,aAAa;;;ACVtC,SAASC,YAAW,OAAO;AAC9B,SAAO,OAAO,UAAU;AAC5B;AACO,SAAS,SAAS,OAAO;AAC5B,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC9C;;;ICJM,YAAY,OAAO,KAAK;IACxB,mBAAmB,OAAO,aAAa;SAMpC,MAAM,OAAO;SACX,SAAS,KAAK,KAAK,aAAa;AAC3C;SAMS,cAAc,OAAO;SACnB,IAAI,MAAM,KAAK,KAAK,oBAAoB;AACnD;SACgB,IAAI,cAAc;MAC1B,UAAO,MAAA,MAAU,YAAY,CAAA;;KAE5B,SAAS,GAAG;KACZ,gBAAgB,GAAG;QAChB,UAAU;iBACH,OAAO;IAClB;QACI,QAAQ,GAAG;UACX,SAAU,GAAC,IAAA;IACf;;AAER;SACS,QAAQ,QAAQ,QAAQ;QACvB,UAAO,aAAe,MAAM;MAC9B,QAAQ;;OAEH,SAAS,GAAG;OACZ,gBAAgB,GAAG;UAChB,UAAU;mBACH,OAAO;MAClB;UACI,QAAQ,GAAG;AACX,eAAO,CAAC;MACZ;;EAER;;KAEK,SAAS,GAAG;QACT,UAAU;aACH,OAAM;IACjB;;AAER;SACS,QAAQ,OAAO;MAChB,IAAI,MAAM,KAAK,EAAA,QACR;MACPC,YAAW,KAAK,EAAA,QACT,IAAI,KAAK,KAAK;SAClB,IAAI,KAAK;AACpB;SAWS,WAAW,OAAO;SAChB,OAAO,QAAQ,KAAK,EAAE;KAAQ,KAAG,CAAG,KAAK,CAAC,MAAM;WAC9C,IAAI,MAAM,CAAC,GAAG;eACR,OAAO,OAAO,KAAG,EAAA,CAAK,GAAG,GAAG,EAAC,CAAA;MACxC;UACI,IAAI,cAAc,CAAC,GAAG;AACtB,eAAO,eAAe,KAAK,KAAG;UAC1B,MAAM;mBACK,EAAE;UACb;UAEA,IAAI,GAAG;AACH,cAAE,UAAU;UAChB;;MAER,OACK;AACD,eAAO,eAAe,KAAK,KAAG;UAC1B,MAAM;mBACK,EAAE;UACb;;MAER;aACO;IACX;;;AACJ;SAUS,cAAc,GAAG;OACjB,IAAI,cAAc,CAAC,EAAA,QACb;;KAEN,SAAS,GAAG;QACT,UAAU;aACH,EAAE;IACb;;AAER;AACA,IAAI,OAAO;AACX,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,gBAAgB;;;ACxHpB,iBAA0B;AAG1B,IAAO,cAAQ,WAAAC,QAAc,WAAW,WAAAA;;;ACHxC,SAAS,aAAa,SAAS,UAAU;AACrC,QAAM,QAAQ,OAAO,SAAS,GAAG;AACjC,SAAO,CAAC,QAAQ;AAEZ,QAAI,OAAO,QAAQ,UAAU;AACzB,YAAM,IAAI,UAAU,gDAAgD,OAAO,GAAG,EAAE;AAAA,IACpF;AAEA,QAAI,CAAC,IAAI,MAAM,KAAK;AAChB,aAAO;AAEX,WAAO,IAAI,QAAQ,OAAO,QAAQ;AAAA,EACtC;AACJ;AACA,IAAM,eAAe,aAAa,SAAS,CAAC,UAAU,IAAI,MAAM,YAAY,CAAC,EAAE;AACxE,SAAS,WAAW,UAAU;AACjC,MAAI,CAAC,YAAY,OAAO,aAAa,YAAY,MAAM,QAAQ,QAAQ,GAAG;AACtE,UAAM,IAAI,UAAU,gDAAgD,OAAO,QAAQ,EAAE;AAAA,EACzF;AACA,SAAO,OAAO,KAAK,QAAQ,EACtB,IAAI,CAAC,aAAa,GAAG,aAAa,QAAQ,CAAC,KAAK,SAAS,QAAQ,CAAC,GAAG,EACrE,KAAK,IAAI;AAClB;;;ACrBO,SAAS,cAAc,QAAQ,CAAC,GAAG;AACtC,SAAO,WAAW,KAAK,EAAE,QAAQ,MAAM,GAAG;AAC9C;AACO,IAAM,eAAe;AAAA,EACxB,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,WAAW;AACf;AACO,IAAM,qBAAqB,cAAc,YAAY;;;ACfrD,IAAMC,iBAAgB,gBAAW,OAAO,WAAW,cAAc,SAAS;AAC1E,IAAMC,mBAAkB,gBAAW,OAAO,WAAW,cAAc,OAAO,WAAW;AACrF,IAAMC,oBAAmB,gBAAW,OAAO,WAAW,cAAc,OAAO,YAAY;AACvF,IAAMC,mBAAkB,gBAAW,OAAO,WAAW,cAAc,OAAO,WAAW;;;ACMrF,SAASC,kBAAiBC,WAAU;AACvC,MAAIC,iBAAgBD,UAAS;AAC7B,SAAOC,kBAAA,gBAAAA,eAAe,YAAY;AAC9B,UAAM,OAAOA,eAAc,WAAW;AACtC,QAAI,SAASA;AACT;AAAA;AAEA,MAAAA,iBAAgB;AAAA,EACxB;AACA,SAAOA;AACX;;;;IChBaC,uBAAc;EAGvB,YAAY,UAAO,CAAA,GAAO;;;;MACd,QAAAC,UAASC;MAAe,UAAAC,YAAWF,WAAA,gBAAAA,QAAQ;QAAa;sBAC5DA,SAAW,MAAS,EAAA;AAExB,uBAAIG,YAAaD;AACjB,uBAAIE,aAAc,iBAAgB,CAAE,WAAW;YACrC,iBAAiB,GAAGJ,SAAQ,WAAW,MAAM;YAC7C,kBAAkB,GAAGA,SAAQ,YAAY,MAAM;mBACxC;AACT,uBAAc;AACd,wBAAe;MACnB;IACJ,CAAC;EACL;MACI,UAAU;;AACV,6BAAII,iBAAJ;SACK,mBAAID,YAAU,QACR;WACJE,kBAAiB,mBAAIF,WAAU;EAC1C;AACJ;;;IAUaG,iBAAa,IAAOP,eAAa;;;SCpCrCQ,WAAU,OAAO,QAAQ;UACtB,OAAK;SACJ;AACD,MAAA,YAAQ,MAAM;;SAEb;AACD,MAAA,gBAAY,MAAM;;;AAG9B;SACSC,YAAW,SAAS,OAAO,QAAQ,UAAO,CAAA,GAAO;UAC9C,OAAO,MAAK,IAAK;MAErB,SAAM,CAAI;MAKV,iBAAiB,MAAM,QAAQ,OAAO,IAAA,CAAA,IAEpC;AACN,EAAAD,WAAU,OAAK,MAAQ;UACb,SAAS,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAG,CAAE,WAAW,OAAM,CAAA,IAAM,QAAO;SAC9E,QAAQ;AACT,eAAS;AACT,uBAAiB;;IAErB;UACM,UAAU,QAAO,MAAO,OAAO,QAAQ,cAAc,CAAA;AAC3D,qBAAiB;WACV;EACX,CAAC;AACL;SACSE,gBAAe,SAAS,OAAO,QAAQ;QACtC,cAAW,YAAA,MAAsB;QAC/B,OAAO;AACX,IAAAD;MAAW;MAAS;OAAQ,QAAQ,mBAAmB;YAC/C,MAAM;AACN,sBAAW;;QAEf;cAEM,UAAU,OAAO,QAAQ,cAAc;AAC7C,eAAO;eACA;MACX;;;QAGE,MAAM,KAAI;;EAChB,CAAC;AACD,EAAA,YAAO,MAAO;WACH;EACX,CAAC;AACL;SACgBE,OAAM,SAAS,QAAQ,SAAS;AAC5C,EAAAF,YAAW,SAAS,QAAQ,QAAQ,OAAO;AAC/C;SACSG,UAAS,SAAS,QAAQ,SAAS;AACxC,EAAAH,YAAW,SAAS,OAAO,QAAQ,OAAO;AAC9C;AACAE,OAAM,MAAMC;SACIC,WAAU,QAAQ,QAAQ;AACtC,EAAAH,gBAAe,QAAQ,QAAQ,MAAM;AACzC;SACSI,cAAa,QAAQ,QAAQ;AAClC,EAAAJ,gBAAe,QAAQ,OAAO,MAAM;AACxC;AACAG,WAAU,MAAMC;;;IChEH,iBAAiB,IAAI,mBAAmB;IAIxC,kBAAkB,IAAI,oBAAoB;;;ACJhD,IAAM,QAAQ,CAAC,QAAQ,SAAS,QAAQ;AACxC,SAAS,YAAY,OAAO;AAC/B,MAAI,OAAO,UAAU;AACjB,WAAO;AACX,SAAO,MAAM,SAAS,KAAK;AAC/B;;;;ICJa,wBAAgB;EAkBzB,cAAc;;sCAjBE;kCACL,YAAY,eAAe;sCACtB,mBAAIC,WAAU,QAAQ,eAAe,OAAO;+BACnD,YAAY,mBAAI,cAAc,IAAI,mBAAI,iBAAiB,mBAAI;mCAC1D,MAAA,MAAU,sBAAI,8CAAJ,UAAmB,CAAA;sBAchB;aACR,MAAM,IAAG,MAAO,eAAe,SAAO,CAAG,GAAG,mBAAmB;cAC5D,gBAAa,IAAG,mBAAI,WAAW,EAAC;YACtC,mBAAI,aAAc,sBAAI,8CAAJ,WAAoB,gBAAa,IAAA;YAC/C,gBAAgB;AAChB,uBAAa,WAAW,cAAc;QAC1C;MACJ,CAAC;IACL,CAAC;EACL;MACI,UAAU;eACH,mBAAI,WAAW,EAAC;EAC3B;MACI,QAAQ,UAAU;QAClB,mBAAI,WAAW,EAAC,UAAU;EAC9B;AACJ;;;;;;;mBA7BkB,SAAC,QAAQ,mBAAI,SAAS;aACrB,eAAe,eAAe,SAAS,OAAK;IACnD,YAAU;MACN,WAAS,CAAG,MAAM;MAClB,aAAW,CAAG,MAAM;YACZ,YAAY,CAAC,EAAA,QACN;eACJ,mBAAI;MACf;;;AAGZ;;IAmBS,0BAAkB;EAe3B,cAAc;;+BAbL;kCACD,MAAA,MAAU,mBAAIC,eAAc,CAAA;yCACpB,cAAA,OAAU,QAAW,aAAW,KAAA,KAAA,cAAA,OAAW,OAAO,YAAe,UAAU,IAAA,IACjF,WAAW,6BAA6B,IAAA,EAC1C,SAAS,MAAK;sBAUC;AACf,MAAA,gBAAW,MAAO;aACT,mBAAI,QAAO;AAEhB,aAAK,MAAK;MACd,CAAC;IACL,CAAC;AACD,SAAK,QAAQ,KAAK,MAAM,KAAK,IAAI;AACjC,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;EAC3C;EAlBA,QAAQ;SACC,UAAS;QAEd,mBAAIC,YAAY,mBAAI,kBAAkB,UAAU,UAAU,QAAM,IAAA;EACpE;EACA,SAAS,QAAQ;AACb,uBAAI,QAAU;EAClB;MAYI,UAAU;eACH,mBAAIA,UAAS;EACxB;AACJ;;;;;IAKa,kBAAe,IAAO,gBAAe;IAIrC,oBAAiB,IAAO,kBAAiB;;;;IC5EhD,oBAAY;EAiBd,cAAc;;kCAhBH,YAAY,eAAe;uCACtB,mBAAIC,WAAU,QAAQ,gBAAgB,OAAO;gCACvD,cAAG,mBAAIC,iBAAmB,IAAI,KAAA,cAAI,mBAAIA,iBAAmB,MAAS,IAAG,KAAK,mBAAIA;oCAC1E,MAAA,MAAU,sBAAI,wBAAAC,mBAAJ,UAAmB,CAAA;sBAchB;aACR,MAAM,IAAG,MAAO,gBAAgB,SAAO,CAAG,GAAG,mBAAmB;cAC7D,gBAAa,IAAG,mBAAIC,YAAW,EAAC;YACtC,mBAAIA,cAAc,sBAAI,wBAAAD,mBAAJ,WAAoB,gBAAa,IAAA;YAC/C,gBAAgB;AAChB,uBAAa,WAAW,cAAc;QAC1C;MACJ,CAAC;IACL,CAAC;EACL;;;;;MAKI,UAAU;eACH,mBAAIC,YAAW,EAAC;EAC3B;;;;;MAKI,QAAQ,UAAU;QAClB,mBAAIA,YAAW,EAAC,UAAU;EAC9B;AACJ;;;;;;oBArCkB,SAAC,QAAQ,mBAAIC,UAAS;aACrB,eAAe,gBAAgB,SAAS,OAAK;IACpD,YAAU;MACN,WAAS,CAAG,MAAM;iCACH,GAAM,UAAQ,KAAA,EAAA,QACd;eACJ;MACX;MACA,aAAW,CAAG,MAAM;;;AAGhC;IA8BS,cAAW,IAAO,YAAW;;;AChD1C,IAAI;AACJ,IAAI;AAOJ,IAAI,YAAY;AAGT,SAAS,kBAAkB,QAAQ;AACtC,MAAI,OAAO,aAAa;AACpB;AACJ,MAAI,CAAC,WAAW;AACZ,gBAAY;AACZ,WAAO;AACP;AAAA,EACJ;AAEA,eAAa,aAAa;AAC1B,eAAa,aAAa;AAE1B,QAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,QAAM,MAAM,SAAS,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMpC;AACA,QAAM,YAAY,GAAG;AAErB,QAAM,UAAU,MAAM,SAAS,KAAK,YAAY,KAAK;AACrD,QAAM,SAAS,MAAM,SAAS,KAAK,YAAY,KAAK;AAEpD,MAAI,OAAO,OAAO,qBAAqB,aAAa;AAChD,YAAQ;AACR,WAAO;AACP,WAAO,iBAAiB,KAAK,EAAE;AAC/B,WAAO;AACP;AAAA,EACJ;AAEA,MAAI,OAAO,OAAO,0BAA0B,aAAa;AACrD,YAAQ;AACR,WAAO;AACP,WAAO,sBAAsB,MAAM;AACnC;AAAA,EACJ;AAEA,UAAQ;AACR,kBAAgB,OAAO,WAAW,MAAM;AACpC,WAAO;AACP,oBAAgB,OAAO,WAAW,QAAQ,GAAG;AAAA,EACjD,GAAG,GAAG;AACV;;;ICjDa,cAAc,IAAI,MAAS;IAI3B,qBAAqB,IAAI,IAAI;IAI7B,iBAAiB,IAAG,CAAA,CAAA;IAIpB,kBAAkB,IAAG,CAAA,CAAA;SACzB,oBAAoB;QACnB,UAAO,aAAA,MAAqB;SACzB,UAAS,QACH;UACLC,eAAW,cAAG,gBAAgB,SAAY,QAAQ,IAClD,kBAAkB,UAClB,gBAAgB;UAChB,0BAA0B,mBAAmB,eAAe,OAAO;UACnE,2BAA2B,mBAAmB,gBAAgB,OAAO;aAClE,SAAS;YACR,SAAS,SAAS;YAClB,eAAe,SAAS,cAAc,0BAA0B;wBAClEA,cAAgB,OAAO,GAAE;YACrB,wBAAwB,OACxB,QAAO,UAAU,OAAM,GAAI,uBAAuB;YAClD,yBAAyB,OACzB,QAAO,UAAU,IAAG,GAAI,wBAAwB;AACpD,eAAO,MAAM,cAAc;YACvB,gBAAgB,YAAY,SAAS;AACrC,uBAAa,aAAa,WAAW,YAAY,QAAQ,KAAK;QAClE;MACJ,OACK;YACG,yBAAyB,OACzB,QAAO,UAAU,OAAM,GAAI,wBAAwB;YACnD,wBAAwB,OACxB,QAAO,UAAU,IAAG,GAAI,uBAAuB;AACnD,eAAO,MAAM,cAAc;YACvB,gBAAgB,YAAY,SAAS;AACrC,uBAAa,aAAa,WAAW,YAAY,QAAQ,IAAI;QACjE;MACJ;IACJ;QACI,mBAAmB,SAAS;AAC5B,wBAAkB,MAAM;IAC5B,OACK;AACD,aAAM;IACV;WACOA;EACX,CAAC;;QAEO,UAAU;iBACH,OAAO;IAClB;;AAER;SACS,qBAAqB;QACpB,UAAO,aAAA,MAAqB;AAC9B,gBAAY;SACP,UAAS,QACH;aACF,SAAS;YACR,SAAS,SAAS;AACxB,aAAO,aAAa,cAAc,YAAY,OAAO;IACzD;QACI,mBAAmB,SAAS;AAC5B,wBAAkB,MAAM;IAC5B,OACK;AACD,aAAM;IACV;WACO,YAAY;EACvB,CAAC;;QAEO,UAAU;iBACH,OAAO;IAClB;;AAER;IAIa,cAAc,kBAAiB;IAI/B,eAAe,mBAAkB;;;AC9FvC,SAAS,aAAa;AACzB,kBAAgB,UAAU,YAAY,YAAY,SAAS,UAAU;AACzE;AAEO,SAAS,QAAQ,MAAM;AAC1B,kBAAgB,UAAU;AAC9B;AAEO,SAAS,YAAY;AACxB,kBAAgB,UAAU;AAC9B;AAEO,SAAS,SAAS,UAAU;AAC/B,cAAY,UAAU;AAC1B;AACO,SAAS,aAAa,QAAQ;AACjC,SAAO;AACX;AAEO,SAAS,eAAe,EAAE,cAAc,UAAU,aAAAC,cAAa,gBAAAC,kBAAiB,CAAC,MAAM,GAAG,iBAAAC,mBAAkB,CAAC,GAAG,eAAe,IAAI,gBAAAC,kBAAiB,qBAAqB,iBAAAC,mBAAkB,qBAAsB,GAAG;AACvN,QAAM,SAAS,SAAS;AACxB,QAAM,OAAO,aAAa,QAAQD,eAAc,KAAK;AACrD,QAAM,QAAQ,aAAa,QAAQC,gBAAe,KAAK;AACvD,QAAM,QAAQ,SAAS,WAClB,SAAS,YAAY,OAAO,WAAW,+BAA+B,EAAE;AAC7E,MAAI,OAAO;AACP,QAAIH,gBAAe;AACf,aAAO,UAAU,OAAO,GAAGA,gBAAe,OAAO,OAAO,CAAC;AAC7D,QAAIC,iBAAgB;AAChB,aAAO,UAAU,IAAI,GAAGA,iBAAgB,OAAO,OAAO,CAAC;AAAA,EAC/D,OACK;AACD,QAAIA,iBAAgB;AAChB,aAAO,UAAU,OAAO,GAAGA,iBAAgB,OAAO,OAAO,CAAC;AAC9D,QAAID,gBAAe;AACf,aAAO,UAAU,IAAI,GAAGA,gBAAe,OAAO,OAAO,CAAC;AAAA,EAC9D;AACA,SAAO,MAAM,cAAc,QAAQ,UAAU;AAC7C,MAAID,cAAa;AACb,UAAM,cAAc,SAAS,cAAc,0BAA0B;AACrE,QAAI,aAAa;AACb,kBAAY,aAAa,WAAW,SAAS,UAAUA,aAAY,QAAQA,aAAY,IAAI;AAAA,IAC/F;AAAA,EACJ;AACA,MAAI,OAAO;AACP,WAAO,aAAa,cAAc,KAAK;AACvC,iBAAa,QAAQI,kBAAiB,KAAK;AAAA,EAC/C;AACA,eAAa,QAAQD,iBAAgB,IAAI;AAC7C;AAMO,SAAS,iCAAiC,SAAS,CAAC,GAAG;AAC1D,SAAO,IAAI,eAAe,SAAS,CAAC,KAAK,KAAK,UAAU,MAAM,CAAC;AACnE;AAIO,IAAM,8BAA8B;;;;;;;;;;;;;+ECvDI,IAAI,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;MCLjD,YAAS,KAAA,SAAA,aAAA,GAAG,EAAE;;;;;;;iFAegC,IAAI,CAAA;;;;;;;;iCAGlC,UAAS,IAAA,UAAa,UAAS,CAAA,KAAK,EAAE,OACtD,eAAe,SAAQ,IAAA,OAEvB,KAAK,UAAS,QAAA,UAAA,IAAA,gBAAA,QAAA,QAAA,IAAA;;;;;;;;;;;;;;;;;;;MCPd,QAAK,KAAA,SAAA,SAAA,GAAG,IAAI,GACZ,cAAW,KAAA,SAAA,eAAA,GAAG,QAAQ,GAEF,yBAAsB,KAAA,SAAA,sBAAA,GAAG,IAAI,GACjC,qBAAkB,KAAA,SAAA,kBAAA,IAAA,MAAA,CAAI,MAAM,CAAA,GAC3B,sBAAmB,KAAA,SAAA,mBAAA,IAAA,MAAA,CAAA,CAAA,GACpC,eAAY,KAAA,SAAA,gBAAA,GAAG,EAAE,GACjB,QAAK,KAAA,SAAA,SAAA,GAAG,EAAE,GACO,sBAAmB,KAAA,SAAA,mBAAA,GAAG,oBAAoB,GAC3C,qBAAkB,KAAA,SAAA,kBAAA,GAAG,mBAAmB,GACxD,6BAA0B,KAAA,SAAA,8BAAA,GAAG,KAAK;AAGnC,iBAAe,UAAU,mBAAkB;AAC3C,kBAAgB,UAAU,oBAAmB;AAC7C,iBAAe,UAAU,mBAAkB;AAC3C,kBAAgB,UAAU,oBAAmB;AAC7C,qBAAmB,UAAU,uBAAsB;AACnD,cAAY,UAAO,QAAA;AAEnB,EAAA,gBAAW,MAAO;AACjB,uBAAmB,UAAU,uBAAsB;EACpD,CAAC;AAED,EAAA,gBAAW,MAAO;AACjB,gBAAY,UAAO,QAAA;EACpB,CAAC;AAED,EAAA,gBAAW,MAAO;AACjB,mBAAe,UAAU,mBAAkB;EAC5C,CAAC;AAED,EAAA,gBAAW,MAAO;AACjB,oBAAgB,UAAU,oBAAmB;EAC9C,CAAC;AAED,EAAA,gBAAW,MAAO;AACjB,mBAAe,UAAU,mBAAkB;EAC5C,CAAC;AAED,EAAA,gBAAW,MAAO;AACjB,oBAAgB,UAAU,oBAAmB;EAC9C,CAAC;AAED,EAAA,gBAAW,MAAO;AACjB,gBAAK;AACL,mBAAe;AACf,oBAAgB;AAChB,iBAAM;EACP,CAAC;AAED,UAAO,MAAO;AACb,sBAAkB,SAAS,MAAK,CAAA;AAChC,sBAAkB,MAAK;UACjB,mBAAmB,aAAa,QAAQ,eAAe,OAAO;AACpE,YAAQ,YAAY,gBAAgB,IAAI,mBAAmB,YAAW,CAAA;UAChE,oBAAoB,aAAa,QAAQ,gBAAgB,OAAO;AACtE,aAAS,qBAAqB,aAAY,CAAA;EAC3C,CAAC;QAEK,aAAa,aAAY;IAC9B,aAAA,YAAW;IACX,aAAW,QAAA;IACX,gBAAgB,mBAAkB;IAClC,iBAAiB,oBAAmB;IACpC,cAAA,aAAY;IACZ,gBAAgB,mBAAkB;IAClC,iBAAiB,oBAAmB;;QAG/B,YAAS,aAAA,MAAA,cAAA,OAAmB,QAAW,WAAW,IAAG,MAAK,IAAG,EAAE;;;;;;;;;iBAIvC,YAAY;;;;;;;;;;;;;;;;iBAEa,YAAY;;;;;;UAH/D,2BAA0B,EAAA,UAAA,UAAA;UAAA,UAAA,WAAA,KAAA;;;;;;;;;;;;;;", "names": ["comment", "prop", "StyleToObject", "document", "activeElement", "window", "document", "window", "_subscribe", "proxy", "value", "options", "_key", "_value", "isFunction", "isFunction", "StyleToObject", "defaultWindow", "defaultDocument", "defaultNavigator", "defaultLocation", "getActiveElement", "document", "activeElement", "ActiveElement", "window", "defaultWindow", "document", "_document", "_subscribe", "getActiveElement", "activeElement", "runEffect", "runW<PERSON><PERSON>", "runWatcherOnce", "watch", "watchPre", "watchOnce", "watchOncePre", "_storage", "_defaultValue", "_current", "_storage", "_initialValue", "makePersisted_fn", "_persisted", "_value", "derivedMode", "themeColors", "darkClassNames", "lightClassNames", "modeStorageKey", "themeStorageKey"]}