{"version": 3, "file": "presence-layer-B0FVaAYL.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/presence-layer.js"], "sourcesContent": ["import \"clsx\";\nimport { w as push, y as pop } from \"./index3.js\";\nimport { b as box, w as watch } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { o as on } from \"./events.js\";\nimport { e as executeCallbacks } from \"./use-ref-by-id.svelte.js\";\nimport { a as afterTick } from \"./after-tick.js\";\nclass Previous {\n  #previous = void 0;\n  #curr;\n  constructor(getter) {\n  }\n  get current() {\n    return this.#previous;\n  }\n}\nfunction useStateMachine(initialState, machine) {\n  const state = box(initialState);\n  function reducer(event) {\n    const nextState = machine[state.current][event];\n    return nextState ?? state.current;\n  }\n  const dispatch = (event) => {\n    state.current = reducer(event);\n  };\n  return { state, dispatch };\n}\nfunction usePresence(present, id) {\n  let styles = {};\n  let prevAnimationNameState = \"none\";\n  const initialState = present.current ? \"mounted\" : \"unmounted\";\n  let node = null;\n  const prevPresent = new Previous(() => present.current);\n  watch([() => id.current, () => present.current], ([id2, present2]) => {\n    if (!id2 || !present2) return;\n    afterTick(() => {\n      node = document.getElementById(id2);\n    });\n  });\n  const { state, dispatch } = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: { MOUNT: \"mounted\", ANIMATION_END: \"unmounted\" },\n    unmounted: { MOUNT: \"mounted\" }\n  });\n  watch(() => present.current, (currPresent) => {\n    if (!node) {\n      node = document.getElementById(id.current);\n    }\n    if (!node) return;\n    const hasPresentChanged = currPresent !== prevPresent.current;\n    if (!hasPresentChanged) return;\n    const prevAnimationName = prevAnimationNameState;\n    const currAnimationName = getAnimationName(node);\n    if (currPresent) {\n      dispatch(\"MOUNT\");\n    } else if (currAnimationName === \"none\" || styles.display === \"none\") {\n      dispatch(\"UNMOUNT\");\n    } else {\n      const isAnimating = prevAnimationName !== currAnimationName;\n      if (prevPresent && isAnimating) {\n        dispatch(\"ANIMATION_OUT\");\n      } else {\n        dispatch(\"UNMOUNT\");\n      }\n    }\n  });\n  function handleAnimationEnd(event) {\n    if (!node) node = document.getElementById(id.current);\n    if (!node) return;\n    const currAnimationName = getAnimationName(node);\n    const isCurrentAnimation = currAnimationName.includes(event.animationName) || currAnimationName === \"none\";\n    if (event.target === node && isCurrentAnimation) {\n      dispatch(\"ANIMATION_END\");\n    }\n  }\n  function handleAnimationStart(event) {\n    if (!node) node = document.getElementById(id.current);\n    if (!node) return;\n    if (event.target === node) {\n      prevAnimationNameState = getAnimationName(node);\n    }\n  }\n  watch(() => state.current, () => {\n    if (!node) node = document.getElementById(id.current);\n    if (!node) return;\n    const currAnimationName = getAnimationName(node);\n    prevAnimationNameState = state.current === \"mounted\" ? currAnimationName : \"none\";\n  });\n  watch(() => node, (node2) => {\n    if (!node2) return;\n    styles = getComputedStyle(node2);\n    return executeCallbacks(on(node2, \"animationstart\", handleAnimationStart), on(node2, \"animationcancel\", handleAnimationEnd), on(node2, \"animationend\", handleAnimationEnd));\n  });\n  const isPresentDerived = [\"mounted\", \"unmountSuspended\"].includes(state.current);\n  return {\n    get current() {\n      return isPresentDerived;\n    }\n  };\n}\nfunction getAnimationName(node) {\n  return node ? getComputedStyle(node).animationName || \"none\" : \"none\";\n}\nfunction Presence_layer($$payload, $$props) {\n  push();\n  let { present, forceMount, presence, id } = $$props;\n  const isPresent = usePresence(box.with(() => present), box.with(() => id));\n  if (forceMount || present || isPresent.current) {\n    $$payload.out += \"<!--[-->\";\n    presence?.($$payload, { present: isPresent });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]-->`;\n  pop();\n}\nexport {\n  Presence_layer as P,\n  Previous as a,\n  useStateMachine as u\n};\n"], "names": [], "mappings": ";;;;;;;AAOA,MAAM,QAAQ,CAAC;AACf,EAAE,SAAS,GAAG,MAAM;AACpB,EAAE,KAAK;AACP,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,SAAS;AACzB;AACA;AACA,SAAS,eAAe,CAAC,YAAY,EAAE,OAAO,EAAE;AAChD,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC;AACjC,EAAE,SAAS,OAAO,CAAC,KAAK,EAAE;AAC1B,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;AACnD,IAAI,OAAO,SAAS,IAAI,KAAK,CAAC,OAAO;AACrC;AACA,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,KAAK;AAC9B,IAAI,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;AAC5B;AACA,SAAS,WAAW,CAAC,OAAO,EAAE,EAAE,EAAE;AAClC,EAAE,IAAI,MAAM,GAAG,EAAE;AACjB,EAAE,IAAI,sBAAsB,GAAG,MAAM;AACrC,EAAE,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,GAAG,SAAS,GAAG,WAAW;AAChE,EAAE,IAAI,IAAI,GAAG,IAAI;AACjB,EAAE,MAAM,WAAW,GAAG,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC;AACzD,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK;AACxE,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,EAAE;AAC3B,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC;AACzC,KAAK,CAAC;AACN,GAAG,CAAC;AACJ,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,eAAe,CAAC,YAAY,EAAE;AAC5D,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,aAAa,EAAE;AACrB,KAAK;AACL,IAAI,gBAAgB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE;AACtE,IAAI,SAAS,EAAE,EAAE,KAAK,EAAE,SAAS;AACjC,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC,WAAW,KAAK;AAChD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC;AAChD;AACA,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,IAAI,MAAM,iBAAiB,GAAG,WAAW,KAAK,WAAW,CAAC,OAAO;AACjE,IAAI,IAAI,CAAC,iBAAiB,EAAE;AAC5B,IAAI,MAAM,iBAAiB,GAAG,sBAAsB;AACpD,IAAI,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACpD,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,QAAQ,CAAC,OAAO,CAAC;AACvB,KAAK,MAAM,IAAI,iBAAiB,KAAK,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,MAAM,EAAE;AAC1E,MAAM,QAAQ,CAAC,SAAS,CAAC;AACzB,KAAK,MAAM;AACX,MAAM,MAAM,WAAW,GAAG,iBAAiB,KAAK,iBAAiB;AACjE,MAAM,IAAI,WAAW,IAAI,WAAW,EAAE;AACtC,QAAQ,QAAQ,CAAC,eAAe,CAAC;AACjC,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,SAAS,CAAC;AAC3B;AACA;AACA,GAAG,CAAC;AACJ,EAAE,SAAS,kBAAkB,CAAC,KAAK,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC;AACzD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,IAAI,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACpD,IAAI,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,iBAAiB,KAAK,MAAM;AAC9G,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,IAAI,kBAAkB,EAAE;AACrD,MAAM,QAAQ,CAAC,eAAe,CAAC;AAC/B;AACA;AACA,EAAE,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACvC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC;AACzD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,EAAE;AAC/B,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACrD;AACA;AACA,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,OAAO,EAAE,MAAM;AACnC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC;AACzD,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,IAAI,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC;AACpD,IAAI,sBAAsB,GAAG,KAAK,CAAC,OAAO,KAAK,SAAS,GAAG,iBAAiB,GAAG,MAAM;AACrF,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE,CAAC,KAAK,KAAK;AAC/B,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,IAAI,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC;AACpC,IAAI,OAAO,gBAAgB,CAAC,EAAE,CAAC,KAAK,EAAE,gBAAgB,EAAE,oBAAoB,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,iBAAiB,EAAE,kBAAkB,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;AAC/K,GAAG,CAAC;AACJ,EAAE,MAAM,gBAAgB,GAAG,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC;AAClF,EAAE,OAAO;AACT,IAAI,IAAI,OAAO,GAAG;AAClB,MAAM,OAAO,gBAAgB;AAC7B;AACA,GAAG;AACH;AACA,SAAS,gBAAgB,CAAC,IAAI,EAAE;AAChC,EAAE,OAAO,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAC,CAAC,aAAa,IAAI,MAAM,GAAG,MAAM;AACvE;AACA,SAAS,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AAC5C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,GAAG,OAAO;AACrD,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAC5E,EAAE,IAAI,UAAU,IAAI,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE;AAClD,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,QAAQ,GAAG,SAAS,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC;AACjD,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP;;;;"}