import { a as getPlansFromDatabase } from './plan-sync-CZNz1Ayv.js';

async function mapStripePriceIdToPlanId(priceId) {
  console.log("🔍 Mapping Stripe price ID to plan ID", { priceId });
  try {
    const plans = await getPlansFromDatabase();
    const plan = plans.find(
      (p) => p.stripePriceMonthlyId === priceId || p.stripePriceYearlyId === priceId
    );
    if (plan) {
      console.log("✅ Found plan by direct match", {
        priceId,
        planId: plan.id,
        planName: plan.name
      });
      return plan.id;
    }
    console.log("⚠️ No direct match found in database plans, trying fallback mapping");
  } catch (error) {
    console.error("Error getting plans from database:", error);
    console.log("⚠️ Error getting plans from database, trying fallback mapping");
  }
  const priceMap = {
    price_1R9WTXPvxCOa4C056MICymub: "casual",
    // Monthly
    price_1R9WWUPvxCOa4C05NtpXaOq6: "casual",
    // Yearly
    price_1R9WVBPvxCOa4C05b5VrNJiw: "active",
    // Monthly
    price_1R9WUrPvxCOa4C05EspLpJR3: "active",
    // Yearly
    price_1R9WXPPvxCOa4C05MDynzxE2: "daily",
    // Monthly
    price_1R9WXxPvxCOa4C05hGzFxoDM: "daily",
    // Yearly
    price_1R9WZPPvxCOa4C05Z9D9ry48: "power",
    // Monthly
    price_1R9WZoPvxCOa4C05azkzddOJ: "power",
    // Yearly
    price_1R9WaFPvxCOa4C05xDSA2Ytb: "startup",
    // Monthly
    price_1R9WbEPvxCOa4C05ZYdSV7Ni: "startup",
    // Yearly
    price_1R9WcIPvxCOa4C05Yx9Ixvxl: "medium",
    // Monthly
    price_1R9WcgPvxCOa4C05Yx9Ixvxl: "medium",
    // Yearly
    price_1R9WfbPvxCOa4C055tBNHGf7: "enterprise",
    // Monthly
    price_1R9WfwPvxCOa4C05cWj25Amd: "enterprise",
    // Yearly
    price_1R9WhYPvxCOa4C05gnaa26BH: "custom",
    // Monthly
    price_1R9XC0PvxCOa4C053xed8EkB: "custom"
    // Yearly
  };
  const planId = priceMap[priceId];
  if (planId) {
    console.log("✅ Found plan in fallback mapping", { priceId, planId });
    return planId;
  }
  console.error("❌ Could not map price ID to plan ID", {
    priceId,
    availablePriceIds: [...Object.keys(priceMap)]
  });
  return null;
}
async function getStripePriceId(planId, billingCycle) {
  try {
    const plans = await getPlansFromDatabase();
    const plan = plans.find((p) => p.id === planId);
    if (!plan) return null;
    return billingCycle === "annual" ? plan.stripePriceYearlyId : plan.stripePriceMonthlyId;
  } catch (error) {
    console.error("Error getting plan from database:", error);
    return null;
  }
}

export { getStripePriceId as g, mapStripePriceIdToPlanId as m };
//# sourceMappingURL=stripe2-B7NQEZZC.js.map
