import { clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { format, formatDistance } from 'date-fns';

function cn(...inputs) {
  return twMerge(clsx(inputs));
}
function formatDistanceToNow(date) {
  if (!date) return "";
  return formatDistance(new Date(date), /* @__PURE__ */ new Date(), { addSuffix: false });
}
function debounce(func, wait) {
  let timeout = null;
  return function(...args) {
    const later = () => {
      timeout = null;
      func(...args);
    };
    if (timeout !== null) {
      clearTimeout(timeout);
    }
    timeout = setTimeout(later, wait);
  };
}
function formatDate(date) {
  if (!date) return "";
  return format(new Date(date), "MMM d, yyyy");
}

export { formatDate as a, cn as c, debounce as d, formatDistanceToNow as f };
//# sourceMappingURL=utils-pWl1tgmi.js.map
