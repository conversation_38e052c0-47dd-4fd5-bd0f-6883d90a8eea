import { p as prisma } from './prisma-Cit_HrSw.js';
import { r as redirect } from './index-Ddp2AB5f.js';
import '@prisma/client';

function formatFileUrl(doc) {
  if (!doc.fileUrl) return "";
  console.log(`Formatting URL for document ${doc.id}: ${doc.fileUrl}`);
  if (doc.fileUrl === "/placeholder.pdf" || doc.fileUrl.includes("placeholder")) {
    console.log(`Returning placeholder URL: ${doc.fileUrl}`);
    return doc.fileUrl;
  }
  if (doc.fileUrl.startsWith("http://") || doc.fileUrl.startsWith("https://")) {
    console.log(`Returning R2 absolute URL: ${doc.fileUrl}`);
    return doc.fileUrl;
  }
  if (doc.fileUrl.includes("/uploads/")) {
    console.log(`Legacy local file URL: ${doc.fileUrl}`);
    return doc.fileUrl;
  }
  if (doc.fileUrl.startsWith("/")) {
    const urlWithoutLeadingSlash = doc.fileUrl.substring(1);
    let folder2 = "documents";
    if (doc.type === "resume") folder2 = "resumes";
    else if (doc.type === "cover_letter") folder2 = "cover-letters";
    else if (doc.type === "reference") folder2 = "references";
    const formattedUrl2 = `/uploads/${folder2}/${urlWithoutLeadingSlash}`;
    console.log(`Legacy formatted URL: ${formattedUrl2}`);
    return formattedUrl2;
  }
  let folder = "documents";
  if (doc.type === "resume") folder = "resumes";
  else if (doc.type === "cover_letter") folder = "cover-letters";
  else if (doc.type === "reference") folder = "references";
  const formattedUrl = `/uploads/${folder}/${doc.fileUrl}`;
  console.log(`Legacy default formatted URL: ${formattedUrl}`);
  return formattedUrl;
}
const load = async ({ locals }) => {
  const user = locals.user;
  if (!user || !user.id) throw redirect(302, "/auth/sign-in");
  console.log("User ID:", user.id);
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [{ userId: user.id }, { team: { members: { some: { userId: user.id } } } }]
    },
    select: {
      id: true,
      name: true
    }
  });
  console.log(`Found ${profiles.length} profiles for user ${user.id}`);
  console.log(`Fetching documents for user ${user.id}...`);
  const documents = await prisma.document.findMany({
    where: {
      userId: user.id
    },
    include: {
      profile: true,
      resume: {
        include: {
          optimization: true,
          jobSearches: {
            take: 1,
            orderBy: { createdAt: "desc" }
          }
        }
      }
    },
    orderBy: {
      createdAt: "desc"
    }
  });
  console.log(`Found ${documents.length} documents for user ${user.id}:`, documents);
  const processedDocuments = documents.map((doc) => {
    const resume = doc.resume;
    const jobSearch = resume?.jobSearches?.[0] ?? null;
    const formattedUrl = formatFileUrl(doc);
    console.log(`Document ${doc.id}: Original URL: ${doc.fileUrl}, Formatted URL: ${formattedUrl}`);
    return {
      id: doc.id,
      label: doc.label ?? "Untitled Document",
      fileUrl: formattedUrl,
      fileName: doc.fileName ?? doc.fileUrl?.split("/").pop() ?? "unknown.pdf",
      type: doc.type ?? "document",
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt,
      isDefault: doc.isDefault ?? false,
      isParsed: resume?.isParsed ?? false,
      parsedAt: resume?.parsedAt ?? null,
      // Determine the source based on the document's characteristics
      source: (
        // If the document has a placeholder URL, it was created from scratch
        doc.fileUrl === "/placeholder.pdf" ? "created" : (
          // If the document is in the 'resumes' storage location, it was uploaded
          doc.storageLocation === "resumes" && doc.fileName ? "uploaded" : (
            // If the document's filename includes 'generated', it was generated
            doc.fileName?.includes("generated") ? "generated" : (
              // For documents created from scratch but not yet generated
              doc.fileUrl?.startsWith("/placeholder") || !doc.fileName ? "created" : (
                // Default to 'uploaded' for any other case
                "uploaded"
              )
            )
          )
        )
      ),
      score: resume?.score ?? null,
      profile: doc.profile ?? null,
      jobSearch
    };
  });
  const result = {
    profiles,
    documents: processedDocuments
  };
  console.log("Server returning data:", {
    profilesCount: profiles.length,
    documentsCount: processedDocuments.length
  });
  return result;
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 32;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-CyXejiF8.js')).default;
const server_id = "src/routes/dashboard/documents/+page.server.ts";
const imports = ["_app/immutable/nodes/32.ARwMVoQf.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/DjPYYl4Z.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/CWmzcjye.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/tdzGgazS.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BaVT73bJ.js","_app/immutable/chunks/BfX7a-t9.js","_app/immutable/chunks/BosuxZz1.js","_app/immutable/chunks/DT9WCdWY.js","_app/immutable/chunks/Bpi49Nrf.js","_app/immutable/chunks/OOsIR5sE.js","_app/immutable/chunks/Cb-3cdbh.js","_app/immutable/chunks/DX6rZLP_.js","_app/immutable/chunks/CIOgxH3l.js","_app/immutable/chunks/DuoUhxYL.js","_app/immutable/chunks/CnMg5bH0.js","_app/immutable/chunks/BJIrNhIJ.js","_app/immutable/chunks/DMoa_yM9.js","_app/immutable/chunks/Bd3zs5C6.js","_app/immutable/chunks/XESq6qWN.js","_app/immutable/chunks/CnpHcmx3.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/CGK0g3x_.js","_app/immutable/chunks/D2egQzE8.js","_app/immutable/chunks/Ntteq2n_.js","_app/immutable/chunks/DrQfh6BY.js","_app/immutable/chunks/DxW95yuQ.js","_app/immutable/chunks/D-o7ybA5.js","_app/immutable/chunks/BjCTmJLi.js","_app/immutable/chunks/CzsE_FAw.js","_app/immutable/chunks/DMTMHyMa.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/BvvicRXk.js","_app/immutable/chunks/CKh8VGVX.js","_app/immutable/chunks/BKLOCbjP.js","_app/immutable/chunks/B2lQHLf_.js","_app/immutable/chunks/BxlgRp1U.js","_app/immutable/chunks/BhzFx1Wy.js","_app/immutable/chunks/B3MJtjra.js","_app/immutable/chunks/DR5zc253.js","_app/immutable/chunks/yW0TxTga.js","_app/immutable/chunks/ChqRiddM.js","_app/immutable/chunks/7AwcL9ec.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/T7uRAIbG.js","_app/immutable/chunks/BniYvUIG.js","_app/immutable/chunks/WD4kvFhR.js","_app/immutable/chunks/OXTnUuEm.js","_app/immutable/chunks/hrXlVaSN.js","_app/immutable/chunks/CTn0v-X8.js","_app/immutable/chunks/DaBofrVv.js","_app/immutable/chunks/BnikQ10_.js","_app/immutable/chunks/CSGDlQPw.js","_app/immutable/chunks/C1FmrZbK.js","_app/immutable/chunks/BPvdPoic.js","_app/immutable/chunks/zNKWipEG.js","_app/immutable/chunks/tr-scC-m.js","_app/immutable/chunks/BIUPxhhl.js","_app/immutable/chunks/CTQ8y7hr.js","_app/immutable/chunks/KVutzy_p.js","_app/immutable/chunks/DumgozFE.js","_app/immutable/chunks/C33xR25f.js","_app/immutable/chunks/DW7T7T22.js","_app/immutable/chunks/Z6UAQTuv.js","_app/immutable/chunks/Dz4exfp3.js","_app/immutable/chunks/BBNNmnYR.js","_app/immutable/chunks/DkmCSZhC.js","_app/immutable/chunks/BgDjIxoO.js","_app/immutable/chunks/CfcZq63z.js","_app/immutable/chunks/Cf6rS4LV.js","_app/immutable/chunks/3WmhYGjL.js","_app/immutable/chunks/Dmwghw4a.js","_app/immutable/chunks/CDeW2UsS.js","_app/immutable/chunks/B5tu6DNS.js","_app/immutable/chunks/0ykhD7u6.js","_app/immutable/chunks/C8B1VUaq.js","_app/immutable/chunks/LESefvxV.js"];
const stylesheets = ["_app/immutable/assets/Toaster.DKF17Rty.css","_app/immutable/assets/index.CV-KWLNP.css"];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=32-C53ZlgQX.js.map
