{"version": 3, "file": "PlanFeaturesList-C6rEbMbF.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/PlanFeaturesList.js"], "sourcesContent": ["import \"clsx\";\nimport { w as push, y as pop } from \"./index3.js\";\nfunction PlanFeaturesList($$payload, $$props) {\n  push();\n  const {\n    plan = null,\n    compact = false,\n    showCount = true\n  } = $$props;\n  plan?.limits || {};\n  $$payload.out += `<div class=\"space-y-2\">`;\n  {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<p class=\"text-muted-foreground text-sm\">Loading features...</p>`;\n  }\n  $$payload.out += `<!--]--></div>`;\n  pop();\n}\nexport {\n  PlanFeaturesList as P\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC9C,EAAE,IAAI,EAAE;AACR,EAAE,MAAM;AACR,IAAI,IAAI,GAAG,IAAI;AACf,IAAI,OAAO,GAAG,KAAK;AACnB,IAAI,SAAS,GAAG;AAChB,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE;AACpB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AAC5C,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,gEAAgE,CAAC;AACvF;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACnC,EAAE,GAAG,EAAE;AACP;;;;"}