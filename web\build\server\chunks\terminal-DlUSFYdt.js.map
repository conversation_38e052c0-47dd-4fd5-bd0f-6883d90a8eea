{"version": 3, "file": "terminal-DlUSFYdt.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/terminal.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Terminal($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\"polyline\", { \"points\": \"4 17 10 11 4 5\" }],\n    [\n      \"line\",\n      {\n        \"x1\": \"12\",\n        \"x2\": \"20\",\n        \"y1\": \"19\",\n        \"y2\": \"19\"\n      }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"terminal\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Terminal as T\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC;AAChD,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,IAAI,EAAE;AACd;AACA;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE;AACxB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}