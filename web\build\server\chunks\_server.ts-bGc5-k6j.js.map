{"version": 3, "file": "_server.ts-bGc5-k6j.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/documents/_id_/view/_server.ts.js"], "sourcesContent": ["import { e as error, j as json } from \"../../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../../chunks/prisma.js\";\nconst GET = async ({ params, locals }) => {\n  if (!locals.user) {\n    throw error(401, \"Unauthorized\");\n  }\n  try {\n    const { id } = params;\n    if (!id) {\n      throw error(400, \"Document ID is required\");\n    }\n    const document = await prisma.document.findUnique({\n      where: { id }\n    });\n    if (!document || document.userId !== locals.user.id) {\n      throw error(404, \"Document not found\");\n    }\n    let directUrl = document.fileUrl || \"/placeholder.pdf\";\n    let isExternal = false;\n    if (directUrl.startsWith(\"http://\") || directUrl.startsWith(\"https://\")) {\n      isExternal = true;\n    } else if (directUrl !== \"/placeholder.pdf\") {\n      if (!directUrl.startsWith(\"/\")) {\n        directUrl = \"/\" + directUrl;\n      }\n      if (!directUrl.includes(\"/uploads/\")) {\n        const fileExtension = directUrl.split(\".\").pop()?.toLowerCase();\n        let folder = \"documents\";\n        if (document.type === \"resume\") folder = \"resumes\";\n        else if (document.type === \"cover_letter\") folder = \"cover-letters\";\n        else if (document.type === \"reference\") folder = \"references\";\n        directUrl = `/uploads/${folder}${directUrl}`;\n      }\n    }\n    return json({\n      success: true,\n      document: {\n        ...document,\n        directUrl,\n        isExternal,\n        fileExists: true\n        // Assume files exist since they're in database\n      }\n    });\n  } catch (err) {\n    console.error(\"Error serving document:\", err);\n    throw error(500, \"Failed to serve document\");\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;AAC1C,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;AACpC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,EAAE,EAAE;AACb,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,yBAAyB,CAAC;AACjD;AACA,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;AACtD,MAAM,KAAK,EAAE,EAAE,EAAE;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;AACzD,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC;AAC5C;AACA,IAAI,IAAI,SAAS,GAAG,QAAQ,CAAC,OAAO,IAAI,kBAAkB;AAC1D,IAAI,IAAI,UAAU,GAAG,KAAK;AAC1B,IAAI,IAAI,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE;AAC7E,MAAM,UAAU,GAAG,IAAI;AACvB,KAAK,MAAM,IAAI,SAAS,KAAK,kBAAkB,EAAE;AACjD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACtC,QAAQ,SAAS,GAAG,GAAG,GAAG,SAAS;AACnC;AACA,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;AAC5C,QAAQ,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,WAAW,EAAE;AACvE,QAAQ,IAAI,MAAM,GAAG,WAAW;AAChC,QAAQ,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE,MAAM,GAAG,SAAS;AAC1D,aAAa,IAAI,QAAQ,CAAC,IAAI,KAAK,cAAc,EAAE,MAAM,GAAG,eAAe;AAC3E,aAAa,IAAI,QAAQ,CAAC,IAAI,KAAK,WAAW,EAAE,MAAM,GAAG,YAAY;AACrE,QAAQ,SAAS,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;AACpD;AACA;AACA,IAAI,OAAO,IAAI,CAAC;AAChB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE;AAChB,QAAQ,GAAG,QAAQ;AACnB,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,QAAQ,UAAU,EAAE;AACpB;AACA;AACA,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC;AACjD,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,0BAA0B,CAAC;AAChD;AACA;;;;"}