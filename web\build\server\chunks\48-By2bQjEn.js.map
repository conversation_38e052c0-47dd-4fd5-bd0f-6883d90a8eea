{"version": 3, "file": "48-By2bQjEn.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/admin/email/audiences/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/48.js"], "sourcesContent": ["import { Resend } from \"resend\";\nimport { l as logger } from \"../../../../../../../chunks/logger.js\";\nconst resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;\nconst load = async () => {\n  try {\n    let audiences = [];\n    if (resend) {\n      try {\n        const response = await resend.audiences.list();\n        if (response.error) {\n          logger.error(\"Error fetching audiences:\", response.error);\n        } else {\n          audiences = response.data || [];\n          logger.info(`Successfully loaded ${audiences.length} audiences from Resend`);\n        }\n      } catch (error) {\n        logger.error(\"Error fetching audiences:\", error);\n      }\n    } else {\n      logger.warn(\"Resend API key not available, using mock data\");\n      audiences = [\n        { id: \"mock-1\", name: \"All Users (Mock)\", created_at: (/* @__PURE__ */ new Date()).toISOString() },\n        { id: \"mock-2\", name: \"Active Users (Mock)\", created_at: (/* @__PURE__ */ new Date()).toISOString() }\n      ];\n    }\n    return {\n      audiences,\n      apiStatus: {\n        isAvailable: true,\n        hasApiKey: !!process.env.RESEND_API_KEY\n      }\n    };\n  } catch (error) {\n    logger.error(\"Error in audiences page load:\", error);\n    return {\n      audiences: [],\n      apiStatus: {\n        isAvailable: false,\n        hasApiKey: !!process.env.RESEND_API_KEY,\n        error: error.message\n      }\n    };\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/admin/email/audiences/_page.server.ts.js';\n\nexport const index = 48;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/admin/email/audiences/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/admin/email/audiences/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/48.t0_DkjiB.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/LESefvxV.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/qwsZpUIl.js\",\"_app/immutable/chunks/CzSntoiK.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/CTO_B1Jk.js\",\"_app/immutable/chunks/BSHZ37s_.js\",\"_app/immutable/chunks/Bpd96RWU.js\",\"_app/immutable/chunks/DumgozFE.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;AAEA,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,IAAI;AACzF,MAAM,IAAI,GAAG,YAAY;AACzB,EAAE,IAAI;AACN,IAAI,IAAI,SAAS,GAAG,EAAE;AACtB,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,IAAI;AACV,QAAQ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE;AACtD,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE;AAC5B,UAAU,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,QAAQ,CAAC,KAAK,CAAC;AACnE,SAAS,MAAM;AACf,UAAU,SAAS,GAAG,QAAQ,CAAC,IAAI,IAAI,EAAE;AACzC,UAAU,MAAM,CAAC,IAAI,CAAC,CAAC,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;AACtF;AACA,OAAO,CAAC,OAAO,KAAK,EAAE;AACtB,QAAQ,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACxD;AACA,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC;AAClE,MAAM,SAAS,GAAG;AAClB,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,kBAAkB,EAAE,UAAU,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,EAAE;AAC1G,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,qBAAqB,EAAE,UAAU,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AAC3G,OAAO;AACP;AACA,IAAI,OAAO;AACX,MAAM,SAAS;AACf,MAAM,SAAS,EAAE;AACjB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;AACjC;AACA,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AACxD,IAAI,OAAO;AACX,MAAM,SAAS,EAAE,EAAE;AACnB,MAAM,SAAS,EAAE;AACjB,QAAQ,WAAW,EAAE,KAAK;AAC1B,QAAQ,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc;AAC/C,QAAQ,KAAK,EAAE,KAAK,CAAC;AACrB;AACA,KAAK;AACL;AACA,CAAC;;;;;;;ACzCW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA2E,CAAC,EAAE;AAEzI,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC7+D,MAAC,WAAW,GAAG,CAAC,4CAA4C;AAC5D,MAAC,KAAK,GAAG;;;;"}