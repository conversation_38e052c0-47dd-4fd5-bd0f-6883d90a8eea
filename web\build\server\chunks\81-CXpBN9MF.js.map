{"version": 3, "file": "81-CXpBN9MF.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/legal/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/81.js"], "sourcesContent": ["import { c as client } from \"../../../chunks/client2.js\";\nconst load = async ({ parent }) => {\n  const layoutData = await parent();\n  try {\n    const legalPage = await client.fetch(`\n      *[_type == \"page\" && pageType == \"legal\" && slug.current == \"legal\"][0] {\n        title,\n        description,\n        content,\n        legalContact {\n          email,\n          message\n        }\n      }\n    `);\n    const legalPagesWithIcons = layoutData.legalPages.map((page) => ({\n      ...page,\n      description: page.description || \"\",\n      icon: getIconForSlug(page.slug)\n    }));\n    console.log(\"Main legal page found:\", legalPage ? \"Yes\" : \"No\");\n    console.log(\"Legal pages with icons:\", legalPagesWithIcons.length);\n    if (!legalPage) {\n      console.log(\"Creating default legal page\");\n      return {\n        legalPage: {\n          title: \"Legal Center\",\n          description: \"Access Hirli's legal documents, policies, and compliance information.\",\n          content: null,\n          legalPages: legalPagesWithIcons\n        }\n      };\n    }\n    return {\n      legalPage: {\n        ...legalPage,\n        legalPages: legalPagesWithIcons\n      }\n    };\n  } catch (error) {\n    console.error(\"Error loading legal page data:\", error);\n    return {\n      legalPage: {\n        title: \"Legal Center\",\n        description: \"Access Hirli's legal documents, policies, and compliance information.\",\n        content: null,\n        legalPages: layoutData.legalPages.map((page) => ({\n          ...page,\n          description: page.description || \"\",\n          icon: getIconForSlug(page.slug)\n        }))\n      }\n    };\n  }\n};\nfunction getIconForSlug(slug) {\n  switch (slug) {\n    case \"legal\":\n      return \"FileText\";\n    case \"privacy\":\n      return \"Shield\";\n    case \"terms\":\n      return \"FileText\";\n    case \"cookies\":\n      return \"Cookie\";\n    case \"accessibility\":\n      return \"Accessibility\";\n    case \"data-security\":\n      return \"Database\";\n    case \"gdpr\":\n      return \"Globe\";\n    case \"legal-notices\":\n      return \"Scale\";\n    default:\n      if (slug.includes(\"privacy\")) return \"Shield\";\n      if (slug.includes(\"term\")) return \"FileText\";\n      if (slug.includes(\"cookie\")) return \"Cookie\";\n      if (slug.includes(\"access\")) return \"Accessibility\";\n      if (slug.includes(\"security\") || slug.includes(\"data\")) return \"Database\";\n      if (slug.includes(\"gdpr\") || slug.includes(\"compliance\")) return \"Globe\";\n      if (slug.includes(\"notice\")) return \"Scale\";\n      return \"FileText\";\n  }\n}\nexport {\n  load\n};\n", "import * as server from '../entries/pages/legal/_page.server.ts.js';\n\nexport const index = 81;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/legal/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/legal/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/81.Dq9Eddn7.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/BMgaXnEE.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/DosGZj-c.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/ChqRiddM.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/DDpHsKo4.js\",\"_app/immutable/chunks/D1zde6Ej.js\",\"_app/immutable/chunks/BRdyUBC_.js\",\"_app/immutable/chunks/DLZV8qTT.js\",\"_app/immutable/chunks/rNI1Perp.js\"];\nexport const stylesheets = [\"_app/immutable/assets/PortableText.DSHKgSkc.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;AACA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,UAAU,GAAG,MAAM,MAAM,EAAE;AACnC,EAAE,IAAI;AACN,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC;AACN,IAAI,MAAM,mBAAmB,GAAG,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AACrE,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;AACzC,MAAM,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI;AACpC,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,SAAS,GAAG,KAAK,GAAG,IAAI,CAAC;AACnE,IAAI,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,mBAAmB,CAAC,MAAM,CAAC;AACtE,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC;AAChD,MAAM,OAAO;AACb,QAAQ,SAAS,EAAE;AACnB,UAAU,KAAK,EAAE,cAAc;AAC/B,UAAU,WAAW,EAAE,uEAAuE;AAC9F,UAAU,OAAO,EAAE,IAAI;AACvB,UAAU,UAAU,EAAE;AACtB;AACA,OAAO;AACP;AACA,IAAI,OAAO;AACX,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,SAAS;AACpB,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC;AAC1D,IAAI,OAAO;AACX,MAAM,SAAS,EAAE;AACjB,QAAQ,KAAK,EAAE,cAAc;AAC7B,QAAQ,WAAW,EAAE,uEAAuE;AAC5F,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AACzD,UAAU,GAAG,IAAI;AACjB,UAAU,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;AAC7C,UAAU,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,IAAI;AACxC,SAAS,CAAC;AACV;AACA,KAAK;AACL;AACA,CAAC;AACD,SAAS,cAAc,CAAC,IAAI,EAAE;AAC9B,EAAE,QAAQ,IAAI;AACd,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,UAAU;AACvB,IAAI,KAAK,SAAS;AAClB,MAAM,OAAO,QAAQ;AACrB,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,UAAU;AACvB,IAAI,KAAK,SAAS;AAClB,MAAM,OAAO,QAAQ;AACrB,IAAI,KAAK,eAAe;AACxB,MAAM,OAAO,eAAe;AAC5B,IAAI,KAAK,eAAe;AACxB,MAAM,OAAO,UAAU;AACvB,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,OAAO;AACpB,IAAI,KAAK,eAAe;AACxB,MAAM,OAAO,OAAO;AACpB,IAAI;AACJ,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,QAAQ;AACnD,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,UAAU;AAClD,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,QAAQ;AAClD,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,eAAe;AACzD,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,UAAU;AAC/E,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,OAAO;AAC9E,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,OAAO;AACjD,MAAM,OAAO,UAAU;AACvB;AACA;;;;;;;ACjFY,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAwC,CAAC,EAAE;AAEtG,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC7/B,MAAC,WAAW,GAAG,CAAC,iDAAiD;AACjE,MAAC,KAAK,GAAG;;;;"}