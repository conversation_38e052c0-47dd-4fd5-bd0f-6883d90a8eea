{"version": 3, "file": "analysis-features-mLXwgzwo.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/analysis-features.js"], "sourcesContent": ["import { L as LimitType, a as FeatureCategory } from \"./features.js\";\nconst ANALYSIS_FEATURE_LIMITS = {\n  // Skill Analysis Limits\n  skill_analysis_monthly: {\n    id: \"skill_analysis_monthly\",\n    name: \"Skill Analysis Reports\",\n    description: \"Number of skill analysis reports you can generate per month\",\n    defaultValue: 5,\n    type: LimitType.Monthly,\n    unit: \"reports\",\n    resetDay: 1\n  },\n  // Career Path Analysis Limits\n  career_path_monthly: {\n    id: \"career_path_monthly\",\n    name: \"Career Path Reports\",\n    description: \"Number of career path analysis reports you can generate per month\",\n    defaultValue: 3,\n    type: LimitType.Monthly,\n    unit: \"reports\",\n    resetDay: 1\n  },\n  // Application Performance Limits\n  application_reports_monthly: {\n    id: \"application_reports_monthly\",\n    name: \"Application Performance Reports\",\n    description: \"Number of application performance reports you can generate per month\",\n    defaultValue: 10,\n    type: LimitType.Monthly,\n    unit: \"reports\",\n    resetDay: 1\n  },\n  // Resume Effectiveness Limits\n  resume_effectiveness_monthly: {\n    id: \"resume_effectiveness_monthly\",\n    name: \"Resume Effectiveness Reports\",\n    description: \"Number of resume effectiveness reports you can generate per month\",\n    defaultValue: 5,\n    type: LimitType.Monthly,\n    unit: \"reports\",\n    resetDay: 1\n  },\n  // Market Insights Limits\n  market_insights_monthly: {\n    id: \"market_insights_monthly\",\n    name: \"Market Insight Reports\",\n    description: \"Number of market insight reports you can access per month\",\n    defaultValue: 10,\n    type: LimitType.Monthly,\n    unit: \"reports\",\n    resetDay: 1\n  }\n};\nconst ANALYSIS_FEATURES = [\n  // Skill Analysis\n  {\n    id: \"skill_gap_analysis\",\n    name: \"Skill Gap Analysis\",\n    description: \"Analyze your skills against job requirements to identify areas for growth\",\n    category: FeatureCategory.Analytics,\n    icon: \"bar-chart\",\n    beta: true,\n    limits: [ANALYSIS_FEATURE_LIMITS.skill_analysis_monthly]\n  },\n  // Career Path Analysis\n  {\n    id: \"career_trajectory\",\n    name: \"Career Path Analysis\",\n    description: \"Visualize your career progression options based on your experience\",\n    category: FeatureCategory.Analytics,\n    icon: \"trending-up\",\n    beta: true,\n    limits: [ANALYSIS_FEATURE_LIMITS.career_path_monthly]\n  },\n  // Application Performance\n  {\n    id: \"application_analytics\",\n    name: \"Application Performance\",\n    description: \"Track and analyze your job application success rates\",\n    category: FeatureCategory.Analytics,\n    icon: \"briefcase\",\n    beta: true,\n    limits: [ANALYSIS_FEATURE_LIMITS.application_reports_monthly]\n  },\n  // Resume Effectiveness\n  {\n    id: \"resume_effectiveness\",\n    name: \"Resume Effectiveness\",\n    description: \"Measure how well your resume performs against job requirements\",\n    category: FeatureCategory.Analytics,\n    icon: \"file-text\",\n    beta: true,\n    limits: [ANALYSIS_FEATURE_LIMITS.resume_effectiveness_monthly]\n  },\n  // Market Insights\n  {\n    id: \"market_intelligence\",\n    name: \"Market Insights\",\n    description: \"Get insights on job market trends relevant to your career\",\n    category: FeatureCategory.Analytics,\n    icon: \"globe\",\n    beta: true,\n    limits: [ANALYSIS_FEATURE_LIMITS.market_insights_monthly]\n  },\n  // Skill Trend Analysis\n  {\n    id: \"skill_trend_analysis\",\n    name: \"Skill Trend Analysis\",\n    description: \"Track emerging skills and technologies in your industry\",\n    category: FeatureCategory.Analytics,\n    icon: \"line-chart\",\n    beta: true,\n    limits: [ANALYSIS_FEATURE_LIMITS.skill_analysis_monthly]\n  },\n  // Salary Insights\n  {\n    id: \"salary_insights\",\n    name: \"Salary Insights\",\n    description: \"Compare your salary expectations with market rates for your role\",\n    category: FeatureCategory.Analytics,\n    icon: \"dollar-sign\",\n    beta: true,\n    limits: [ANALYSIS_FEATURE_LIMITS.market_insights_monthly]\n  },\n  // Interview Performance\n  {\n    id: \"interview_analytics\",\n    name: \"Interview Performance\",\n    description: \"Track and analyze your interview success rates and feedback\",\n    category: FeatureCategory.Analytics,\n    icon: \"users\",\n    beta: true,\n    limits: [ANALYSIS_FEATURE_LIMITS.application_reports_monthly]\n  }\n];\nexport {\n  ANALYSIS_FEATURES as A\n};\n"], "names": [], "mappings": ";;AACA,MAAM,uBAAuB,GAAG;AAChC;AACA,EAAE,sBAAsB,EAAE;AAC1B,IAAI,EAAE,EAAE,wBAAwB;AAChC,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,WAAW,EAAE,6DAA6D;AAC9E,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH;AACA,EAAE,mBAAmB,EAAE;AACvB,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,mEAAmE;AACpF,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH;AACA,EAAE,2BAA2B,EAAE;AAC/B,IAAI,EAAE,EAAE,6BAA6B;AACrC,IAAI,IAAI,EAAE,iCAAiC;AAC3C,IAAI,WAAW,EAAE,sEAAsE;AACvF,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH;AACA,EAAE,4BAA4B,EAAE;AAChC,IAAI,EAAE,EAAE,8BAA8B;AACtC,IAAI,IAAI,EAAE,8BAA8B;AACxC,IAAI,WAAW,EAAE,mEAAmE;AACpF,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH;AACA,EAAE,uBAAuB,EAAE;AAC3B,IAAI,EAAE,EAAE,yBAAyB;AACjC,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,WAAW,EAAE,2DAA2D;AAC5E,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,QAAQ,EAAE;AACd;AACA,CAAC;AACI,MAAC,iBAAiB,GAAG;AAC1B;AACA,EAAE;AACF,IAAI,EAAE,EAAE,oBAAoB;AAC5B,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,2EAA2E;AAC5F,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,uBAAuB,CAAC,sBAAsB;AAC3D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,mBAAmB;AAC3B,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,WAAW,EAAE,oEAAoE;AACrF,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,uBAAuB,CAAC,mBAAmB;AACxD,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,uBAAuB;AAC/B,IAAI,IAAI,EAAE,yBAAyB;AACnC,IAAI,WAAW,EAAE,sDAAsD;AACvE,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,uBAAuB,CAAC,2BAA2B;AAChE,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,sBAAsB;AAC9B,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,WAAW,EAAE,gEAAgE;AACjF,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,uBAAuB,CAAC,4BAA4B;AACjE,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,2DAA2D;AAC5E,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,uBAAuB,CAAC,uBAAuB;AAC5D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,sBAAsB;AAC9B,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,WAAW,EAAE,yDAAyD;AAC1E,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,uBAAuB,CAAC,sBAAsB;AAC3D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,iBAAiB;AACzB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,kEAAkE;AACnF,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,uBAAuB,CAAC,uBAAuB;AAC5D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,uBAAuB;AACjC,IAAI,WAAW,EAAE,6DAA6D;AAC9E,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,uBAAuB,CAAC,2BAA2B;AAChE;AACA;;;;"}