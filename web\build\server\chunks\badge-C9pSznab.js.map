{"version": 3, "file": "badge-C9pSznab.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/badge.js"], "sourcesContent": ["import { a4 as element, N as bind_props, y as pop, w as push, M as spread_attributes, T as clsx } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { tv } from \"tailwind-variants\";\nconst badgeVariants = tv({\n  base: \"focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive inline-flex w-fit shrink-0 items-center justify-center gap-1 overflow-hidden whitespace-nowrap rounded-md border px-2 py-0.5 text-xs font-medium transition-[color,box-shadow] focus-visible:ring-[3px] [&>svg]:pointer-events-none [&>svg]:size-3\",\n  variants: {\n    variant: {\n      default: \"bg-primary text-primary-foreground [a&]:hover:bg-primary/90 border-transparent\",\n      secondary: \"bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90 border-transparent\",\n      destructive: \"bg-destructive [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/70 border-transparent text-white\",\n      outline: \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\"\n    }\n  },\n  defaultVariants: { variant: \"default\" }\n});\nfunction Badge($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    href,\n    class: className,\n    variant = \"default\",\n    children,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  element(\n    $$payload,\n    href ? \"a\" : \"span\",\n    () => {\n      $$payload.out += `${spread_attributes(\n        {\n          \"data-slot\": \"badge\",\n          href,\n          class: clsx(cn(badgeVariants({ variant }), className)),\n          ...restProps\n        },\n        null\n      )}`;\n    },\n    () => {\n      children?.($$payload);\n      $$payload.out += `<!---->`;\n    }\n  );\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Badge as B\n};\n"], "names": ["tv"], "mappings": ";;;;AAGA,MAAM,aAAa,GAAGA,EAAE,CAAC;AACzB,EAAE,IAAI,EAAE,gZAAgZ;AACxZ,EAAE,QAAQ,EAAE;AACZ,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,gFAAgF;AAC/F,MAAM,SAAS,EAAE,sFAAsF;AACvG,MAAM,WAAW,EAAE,2KAA2K;AAC9L,MAAM,OAAO,EAAE;AACf;AACA,GAAG;AACH,EAAE,eAAe,EAAE,EAAE,OAAO,EAAE,SAAS;AACvC,CAAC,CAAC;AACF,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,IAAI;AACR,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO,GAAG,SAAS;AACvB,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,IAAI,GAAG,GAAG,GAAG,MAAM;AACvB,IAAI,MAAM;AACV,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,iBAAiB;AAC3C,QAAQ;AACR,UAAU,WAAW,EAAE,OAAO;AAC9B,UAAU,IAAI;AACd,UAAU,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC;AAChE,UAAU,GAAG;AACb,SAAS;AACT,QAAQ;AACR,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM;AACV,MAAM,QAAQ,GAAG,SAAS,CAAC;AAC3B,MAAM,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAChC;AACA,GAAG;AACH,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}