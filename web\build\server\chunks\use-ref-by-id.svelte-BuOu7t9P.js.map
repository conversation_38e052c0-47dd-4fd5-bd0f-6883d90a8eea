{"version": 3, "file": "use-ref-by-id.svelte-BuOu7t9P.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/watch.svelte.js", "../../../.svelte-kit/adapter-node/chunks/use-ref-by-id.svelte.js"], "sourcesContent": ["import \"clsx\";\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\nfunction isObject(value) {\n  return value !== null && typeof value === \"object\";\n}\nconst CLASS_VALUE_PRIMITIVE_TYPES = [\"string\", \"number\", \"bigint\", \"boolean\"];\nfunction isClassValue(value) {\n  if (value === null || value === void 0)\n    return true;\n  if (CLASS_VALUE_PRIMITIVE_TYPES.includes(typeof value))\n    return true;\n  if (Array.isArray(value))\n    return value.every((item) => isClassValue(item));\n  if (typeof value === \"object\") {\n    if (Object.getPrototypeOf(value) !== Object.prototype)\n      return false;\n    return true;\n  }\n  return false;\n}\nconst BoxSymbol = Symbol(\"box\");\nconst isWritableSymbol = Symbol(\"is-writable\");\nfunction isBox(value) {\n  return isObject(value) && BoxSymbol in value;\n}\nfunction isWritableBox(value) {\n  return box.isBox(value) && isWritableSymbol in value;\n}\nfunction box(initialValue) {\n  let current = initialValue;\n  return {\n    [BoxSymbol]: true,\n    [isWritableSymbol]: true,\n    get current() {\n      return current;\n    },\n    set current(v) {\n      current = v;\n    }\n  };\n}\nfunction boxWith(getter, setter) {\n  const derived = getter();\n  if (setter) {\n    return {\n      [BoxSymbol]: true,\n      [isWritableSymbol]: true,\n      get current() {\n        return derived;\n      },\n      set current(v) {\n        setter(v);\n      }\n    };\n  }\n  return {\n    [BoxSymbol]: true,\n    get current() {\n      return getter();\n    }\n  };\n}\nfunction boxFrom(value) {\n  if (box.isBox(value)) return value;\n  if (isFunction(value)) return box.with(value);\n  return box(value);\n}\nfunction boxFlatten(boxes) {\n  return Object.entries(boxes).reduce(\n    (acc, [key, b]) => {\n      if (!box.isBox(b)) {\n        return Object.assign(acc, { [key]: b });\n      }\n      if (box.isWritableBox(b)) {\n        Object.defineProperty(acc, key, {\n          get() {\n            return b.current;\n          },\n          set(v) {\n            b.current = v;\n          }\n        });\n      } else {\n        Object.defineProperty(acc, key, {\n          get() {\n            return b.current;\n          }\n        });\n      }\n      return acc;\n    },\n    {}\n  );\n}\nfunction toReadonlyBox(b) {\n  if (!box.isWritableBox(b)) return b;\n  return {\n    [BoxSymbol]: true,\n    get current() {\n      return b.current;\n    }\n  };\n}\nbox.from = boxFrom;\nbox.with = boxWith;\nbox.flatten = boxFlatten;\nbox.readonly = toReadonlyBox;\nbox.isBox = isBox;\nbox.isWritableBox = isWritableBox;\nfunction createParser(matcher, replacer) {\n  const regex = RegExp(matcher, \"g\");\n  return (str) => {\n    if (typeof str !== \"string\") {\n      throw new TypeError(`expected an argument of type string, but got ${typeof str}`);\n    }\n    if (!str.match(regex))\n      return str;\n    return str.replace(regex, replacer);\n  };\n}\nconst camelToKebab = createParser(/[A-Z]/, (match) => `-${match.toLowerCase()}`);\nfunction styleToCSS(styleObj) {\n  if (!styleObj || typeof styleObj !== \"object\" || Array.isArray(styleObj)) {\n    throw new TypeError(`expected an argument of type object, but got ${typeof styleObj}`);\n  }\n  return Object.keys(styleObj).map((property) => `${camelToKebab(property)}: ${styleObj[property]};`).join(\"\\n\");\n}\nfunction styleToString(style = {}) {\n  return styleToCSS(style).replace(\"\\n\", \" \");\n}\nconst srOnlyStyles = {\n  position: \"absolute\",\n  width: \"1px\",\n  height: \"1px\",\n  padding: \"0\",\n  margin: \"-1px\",\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  borderWidth: \"0\",\n  transform: \"translateX(-100%)\"\n};\nconst srOnlyStylesString = styleToString(srOnlyStyles);\nconst defaultWindow = void 0;\nfunction getActiveElement(document) {\n  let activeElement = document.activeElement;\n  while (activeElement?.shadowRoot) {\n    const node = activeElement.shadowRoot.activeElement;\n    if (node === activeElement)\n      break;\n    else\n      activeElement = node;\n  }\n  return activeElement;\n}\nconst SvelteSet = globalThis.Set;\nconst SvelteMap = globalThis.Map;\nclass MediaQuery {\n  current;\n  /**\n   * @param {string} query\n   * @param {boolean} [matches]\n   */\n  constructor(query, matches = false) {\n    this.current = matches;\n  }\n}\nfunction createSubscriber(_) {\n  return () => {\n  };\n}\nclass ActiveElement {\n  #document;\n  #subscribe;\n  constructor(options = {}) {\n    const {\n      window = defaultWindow,\n      document = window?.document\n    } = options;\n    if (window === void 0) return;\n    this.#document = document;\n    this.#subscribe = createSubscriber();\n  }\n  get current() {\n    this.#subscribe?.();\n    if (!this.#document) return null;\n    return getActiveElement(this.#document);\n  }\n}\nnew ActiveElement();\nfunction runWatcher(sources, flush, effect, options = {}) {\n  const { lazy = false } = options;\n}\nfunction watch(sources, effect, options) {\n  runWatcher(sources, \"post\", effect, options);\n}\nfunction watchPre(sources, effect, options) {\n  runWatcher(sources, \"pre\", effect, options);\n}\nwatch.pre = watchPre;\nexport {\n  MediaQuery as M,\n  SvelteMap as S,\n  srOnlyStyles as a,\n  box as b,\n  createSubscriber as c,\n  defaultWindow as d,\n  srOnlyStylesString as e,\n  SvelteSet as f,\n  isClassValue as i,\n  styleToString as s,\n  watch as w\n};\n", "import { clsx } from \"clsx\";\nimport parse from \"style-to-object\";\nimport { i as isClassValue, s as styleToString, w as watch } from \"./watch.svelte.js\";\nfunction composeHandlers(...handlers) {\n  return function(e) {\n    for (const handler of handlers) {\n      if (!handler)\n        continue;\n      if (e.defaultPrevented)\n        return;\n      if (typeof handler === \"function\") {\n        handler.call(this, e);\n      } else {\n        handler.current?.call(this, e);\n      }\n    }\n  };\n}\nconst NUMBER_CHAR_RE = /\\d/;\nconst STR_SPLITTERS = [\"-\", \"_\", \"/\", \".\"];\nfunction isUppercase(char = \"\") {\n  if (NUMBER_CHAR_RE.test(char))\n    return void 0;\n  return char !== char.toLowerCase();\n}\nfunction splitByCase(str) {\n  const parts = [];\n  let buff = \"\";\n  let previousUpper;\n  let previousSplitter;\n  for (const char of str) {\n    const isSplitter = STR_SPLITTERS.includes(char);\n    if (isSplitter === true) {\n      parts.push(buff);\n      buff = \"\";\n      previousUpper = void 0;\n      continue;\n    }\n    const isUpper = isUppercase(char);\n    if (previousSplitter === false) {\n      if (previousUpper === false && isUpper === true) {\n        parts.push(buff);\n        buff = char;\n        previousUpper = isUpper;\n        continue;\n      }\n      if (previousUpper === true && isUpper === false && buff.length > 1) {\n        const lastChar = buff.at(-1);\n        parts.push(buff.slice(0, Math.max(0, buff.length - 1)));\n        buff = lastChar + char;\n        previousUpper = isUpper;\n        continue;\n      }\n    }\n    buff += char;\n    previousUpper = isUpper;\n    previousSplitter = isSplitter;\n  }\n  parts.push(buff);\n  return parts;\n}\nfunction pascalCase(str) {\n  if (!str)\n    return \"\";\n  return splitByCase(str).map((p) => upperFirst(p)).join(\"\");\n}\nfunction camelCase(str) {\n  return lowerFirst(pascalCase(str || \"\"));\n}\nfunction upperFirst(str) {\n  return str ? str[0].toUpperCase() + str.slice(1) : \"\";\n}\nfunction lowerFirst(str) {\n  return str ? str[0].toLowerCase() + str.slice(1) : \"\";\n}\nfunction cssToStyleObj(css) {\n  if (!css)\n    return {};\n  const styleObj = {};\n  function iterator(name, value) {\n    if (name.startsWith(\"-moz-\") || name.startsWith(\"-webkit-\") || name.startsWith(\"-ms-\") || name.startsWith(\"-o-\")) {\n      styleObj[pascalCase(name)] = value;\n      return;\n    }\n    if (name.startsWith(\"--\")) {\n      styleObj[name] = value;\n      return;\n    }\n    styleObj[camelCase(name)] = value;\n  }\n  parse(css, iterator);\n  return styleObj;\n}\nfunction executeCallbacks(...callbacks) {\n  return (...args) => {\n    for (const callback of callbacks) {\n      if (typeof callback === \"function\") {\n        callback(...args);\n      }\n    }\n  };\n}\nfunction isEventHandler(key) {\n  return key.length > 2 && key.startsWith(\"on\") && key[2] === key[2]?.toLowerCase();\n}\nfunction mergeProps(...args) {\n  const result = { ...args[0] };\n  for (let i = 1; i < args.length; i++) {\n    const props = args[i];\n    for (const key in props) {\n      const a = result[key];\n      const b = props[key];\n      const aIsFunction = typeof a === \"function\";\n      const bIsFunction = typeof b === \"function\";\n      if (aIsFunction && typeof bIsFunction && isEventHandler(key)) {\n        const aHandler = a;\n        const bHandler = b;\n        result[key] = composeHandlers(aHandler, bHandler);\n      } else if (aIsFunction && bIsFunction) {\n        result[key] = executeCallbacks(a, b);\n      } else if (key === \"class\") {\n        const aIsClassValue = isClassValue(a);\n        const bIsClassValue = isClassValue(b);\n        if (aIsClassValue && bIsClassValue) {\n          result[key] = clsx(a, b);\n        } else if (aIsClassValue) {\n          result[key] = clsx(a);\n        } else if (bIsClassValue) {\n          result[key] = clsx(b);\n        }\n      } else if (key === \"style\") {\n        const aIsObject = typeof a === \"object\";\n        const bIsObject = typeof b === \"object\";\n        const aIsString = typeof a === \"string\";\n        const bIsString = typeof b === \"string\";\n        if (aIsObject && bIsObject) {\n          result[key] = { ...a, ...b };\n        } else if (aIsObject && bIsString) {\n          const parsedStyle = cssToStyleObj(b);\n          result[key] = { ...a, ...parsedStyle };\n        } else if (aIsString && bIsObject) {\n          const parsedStyle = cssToStyleObj(a);\n          result[key] = { ...parsedStyle, ...b };\n        } else if (aIsString && bIsString) {\n          const parsedStyleA = cssToStyleObj(a);\n          const parsedStyleB = cssToStyleObj(b);\n          result[key] = { ...parsedStyleA, ...parsedStyleB };\n        } else if (aIsObject) {\n          result[key] = a;\n        } else if (bIsObject) {\n          result[key] = b;\n        } else if (aIsString) {\n          result[key] = a;\n        } else if (bIsString) {\n          result[key] = b;\n        }\n      } else {\n        result[key] = b !== void 0 ? b : a;\n      }\n    }\n  }\n  if (typeof result.style === \"object\") {\n    result.style = styleToString(result.style).replaceAll(\"\\n\", \" \");\n  }\n  if (result.hidden !== true) {\n    result.hidden = void 0;\n    delete result.hidden;\n  }\n  if (result.disabled !== true) {\n    result.disabled = void 0;\n    delete result.disabled;\n  }\n  return result;\n}\nfunction useRefById({\n  id,\n  ref,\n  deps = () => true,\n  onRefChange,\n  getRootNode\n}) {\n  watch([() => id.current, deps], ([_id]) => {\n    const rootNode = getRootNode?.() ?? document;\n    const node = rootNode?.getElementById(_id);\n    if (node) ref.current = node;\n    else ref.current = null;\n    onRefChange?.(ref.current);\n  });\n}\nexport {\n  cssToStyleObj as a,\n  composeHandlers as c,\n  executeCallbacks as e,\n  mergeProps as m,\n  useRefById as u\n};\n"], "names": [], "mappings": ";;;AACA,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,OAAO,OAAO,KAAK,KAAK,UAAU;AACpC;AACA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,OAAO,KAAK,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ;AACpD;AACA,MAAM,2BAA2B,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;AAC7E,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,MAAM;AACxC,IAAI,OAAO,IAAI;AACf,EAAE,IAAI,2BAA2B,CAAC,QAAQ,CAAC,OAAO,KAAK,CAAC;AACxD,IAAI,OAAO,IAAI;AACf,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1B,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACjC,IAAI,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS;AACzD,MAAM,OAAO,KAAK;AAClB,IAAI,OAAO,IAAI;AACf;AACA,EAAE,OAAO,KAAK;AACd;AACA,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC;AAC/B,MAAM,gBAAgB,GAAG,MAAM,CAAC,aAAa,CAAC;AAC9C,SAAS,KAAK,CAAC,KAAK,EAAE;AACtB,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,SAAS,IAAI,KAAK;AAC9C;AACA,SAAS,aAAa,CAAC,KAAK,EAAE;AAC9B,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,gBAAgB,IAAI,KAAK;AACtD;AACA,SAAS,GAAG,CAAC,YAAY,EAAE;AAC3B,EAAE,IAAI,OAAO,GAAG,YAAY;AAC5B,EAAE,OAAO;AACT,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,IAAI,CAAC,gBAAgB,GAAG,IAAI;AAC5B,IAAI,IAAI,OAAO,GAAG;AAClB,MAAM,OAAO,OAAO;AACpB,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,CAAC,EAAE;AACnB,MAAM,OAAO,GAAG,CAAC;AACjB;AACA,GAAG;AACH;AACA,SAAS,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE;AACjC,EAAE,MAAM,OAAO,GAAG,MAAM,EAAE;AAC1B,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,OAAO;AACX,MAAM,CAAC,SAAS,GAAG,IAAI;AACvB,MAAM,CAAC,gBAAgB,GAAG,IAAI;AAC9B,MAAM,IAAI,OAAO,GAAG;AACpB,QAAQ,OAAO,OAAO;AACtB,OAAO;AACP,MAAM,IAAI,OAAO,CAAC,CAAC,EAAE;AACrB,QAAQ,MAAM,CAAC,CAAC,CAAC;AACjB;AACA,KAAK;AACL;AACA,EAAE,OAAO;AACT,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,IAAI,IAAI,OAAO,GAAG;AAClB,MAAM,OAAO,MAAM,EAAE;AACrB;AACA,GAAG;AACH;AACA,SAAS,OAAO,CAAC,KAAK,EAAE;AACxB,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,OAAO,KAAK;AACpC,EAAE,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC;AAC/C,EAAE,OAAO,GAAG,CAAC,KAAK,CAAC;AACnB;AACA,SAAS,UAAU,CAAC,KAAK,EAAE;AAC3B,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM;AACrC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK;AACvB,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;AACzB,QAAQ,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;AAC/C;AACA,MAAM,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;AAChC,QAAQ,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;AACxC,UAAU,GAAG,GAAG;AAChB,YAAY,OAAO,CAAC,CAAC,OAAO;AAC5B,WAAW;AACX,UAAU,GAAG,CAAC,CAAC,EAAE;AACjB,YAAY,CAAC,CAAC,OAAO,GAAG,CAAC;AACzB;AACA,SAAS,CAAC;AACV,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;AACxC,UAAU,GAAG,GAAG;AAChB,YAAY,OAAO,CAAC,CAAC,OAAO;AAC5B;AACA,SAAS,CAAC;AACV;AACA,MAAM,OAAO,GAAG;AAChB,KAAK;AACL,IAAI;AACJ,GAAG;AACH;AACA,SAAS,aAAa,CAAC,CAAC,EAAE;AAC1B,EAAE,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;AACrC,EAAE,OAAO;AACT,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,IAAI,IAAI,OAAO,GAAG;AAClB,MAAM,OAAO,CAAC,CAAC,OAAO;AACtB;AACA,GAAG;AACH;AACA,GAAG,CAAC,IAAI,GAAG,OAAO;AAClB,GAAG,CAAC,IAAI,GAAG,OAAO;AAClB,GAAG,CAAC,OAAO,GAAG,UAAU;AACxB,GAAG,CAAC,QAAQ,GAAG,aAAa;AAC5B,GAAG,CAAC,KAAK,GAAG,KAAK;AACjB,GAAG,CAAC,aAAa,GAAG,aAAa;AACjC,SAAS,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE;AACzC,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC;AACpC,EAAE,OAAO,CAAC,GAAG,KAAK;AAClB,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;AACjC,MAAM,MAAM,IAAI,SAAS,CAAC,CAAC,6CAA6C,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;AACvF;AACA,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC;AACzB,MAAM,OAAO,GAAG;AAChB,IAAI,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC;AACvC,GAAG;AACH;AACA,MAAM,YAAY,GAAG,YAAY,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;AAChF,SAAS,UAAU,CAAC,QAAQ,EAAE;AAC9B,EAAE,IAAI,CAAC,QAAQ,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAC5E,IAAI,MAAM,IAAI,SAAS,CAAC,CAAC,6CAA6C,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;AAC1F;AACA,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAChH;AACA,SAAS,aAAa,CAAC,KAAK,GAAG,EAAE,EAAE;AACnC,EAAE,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AAC7C;AACK,MAAC,YAAY,GAAG;AACrB,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,MAAM,EAAE,KAAK;AACf,EAAE,OAAO,EAAE,GAAG;AACd,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,QAAQ,EAAE,QAAQ;AACpB,EAAE,IAAI,EAAE,kBAAkB;AAC1B,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,WAAW,EAAE,GAAG;AAClB,EAAE,SAAS,EAAE;AACb;AACK,MAAC,kBAAkB,GAAG,aAAa,CAAC,YAAY;AAChD,MAAC,aAAa,GAAG;AACtB,SAAS,gBAAgB,CAAC,QAAQ,EAAE;AACpC,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,aAAa;AAC5C,EAAE,OAAO,aAAa,EAAE,UAAU,EAAE;AACpC,IAAI,MAAM,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,aAAa;AACvD,IAAI,IAAI,IAAI,KAAK,aAAa;AAC9B,MAAM;AACN;AACA,MAAM,aAAa,GAAG,IAAI;AAC1B;AACA,EAAE,OAAO,aAAa;AACtB;AACK,MAAC,SAAS,GAAG,UAAU,CAAC;AACxB,MAAC,SAAS,GAAG,UAAU,CAAC;AAC7B,MAAM,UAAU,CAAC;AACjB,EAAE,OAAO;AACT;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,KAAK,EAAE,OAAO,GAAG,KAAK,EAAE;AACtC,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO;AAC1B;AACA;AACA,SAAS,gBAAgB,CAAC,CAAC,EAAE;AAC7B,EAAE,OAAO,MAAM;AACf,GAAG;AACH;AACA,MAAM,aAAa,CAAC;AACpB,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,EAAE,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC5B,IAAI,MAAM;AACV,MAAM,MAAM,GAAG,aAAa;AAC5B,MAAM,QAAQ,GAAG,MAAM,EAAE;AACzB,KAAK,GAAG,OAAO;AACf,IAAI,IAAI,MAAM,KAAK,MAAM,EAAE;AAC3B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ;AAC7B,IAAI,IAAI,CAAC,UAAU,GAAG,gBAAgB,EAAE;AACxC;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,IAAI,CAAC,UAAU,IAAI;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI;AACpC,IAAI,OAAO,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C;AACA;AACA,IAAI,aAAa,EAAE;AACnB,SAAS,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;AAC1D,EAAE,MAAM,EAAE,IAAI,GAAG,KAAK,EAAE,GAAG,OAAO;AAClC;AACA,SAAS,KAAK,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;AACzC,EAAE,UAAU,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AAC9C;AACA,SAAS,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC;AAC7C;AACA,KAAK,CAAC,GAAG,GAAG,QAAQ;;ACtMpB,SAAS,eAAe,CAAC,GAAG,QAAQ,EAAE;AACtC,EAAE,OAAO,SAAS,CAAC,EAAE;AACrB,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI,CAAC,OAAO;AAClB,QAAQ;AACR,MAAM,IAAI,CAAC,CAAC,gBAAgB;AAC5B,QAAQ;AACR,MAAM,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;AACzC,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAC7B,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACtC;AACA;AACA,GAAG;AACH;AACA,MAAM,cAAc,GAAG,IAAI;AAC3B,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;AAC1C,SAAS,WAAW,CAAC,IAAI,GAAG,EAAE,EAAE;AAChC,EAAE,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AAC/B,IAAI,OAAO,MAAM;AACjB,EAAE,OAAO,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;AACpC;AACA,SAAS,WAAW,CAAC,GAAG,EAAE;AAC1B,EAAE,MAAM,KAAK,GAAG,EAAE;AAClB,EAAE,IAAI,IAAI,GAAG,EAAE;AACf,EAAE,IAAI,aAAa;AACnB,EAAE,IAAI,gBAAgB;AACtB,EAAE,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AAC1B,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;AACnD,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE;AAC7B,MAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACtB,MAAM,IAAI,GAAG,EAAE;AACf,MAAM,aAAa,GAAG,MAAM;AAC5B,MAAM;AACN;AACA,IAAI,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC;AACrC,IAAI,IAAI,gBAAgB,KAAK,KAAK,EAAE;AACpC,MAAM,IAAI,aAAa,KAAK,KAAK,IAAI,OAAO,KAAK,IAAI,EAAE;AACvD,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,QAAQ,IAAI,GAAG,IAAI;AACnB,QAAQ,aAAa,GAAG,OAAO;AAC/B,QAAQ;AACR;AACA,MAAM,IAAI,aAAa,KAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1E,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC;AACpC,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/D,QAAQ,IAAI,GAAG,QAAQ,GAAG,IAAI;AAC9B,QAAQ,aAAa,GAAG,OAAO;AAC/B,QAAQ;AACR;AACA;AACA,IAAI,IAAI,IAAI,IAAI;AAChB,IAAI,aAAa,GAAG,OAAO;AAC3B,IAAI,gBAAgB,GAAG,UAAU;AACjC;AACA,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAClB,EAAE,OAAO,KAAK;AACd;AACA,SAAS,UAAU,CAAC,GAAG,EAAE;AACzB,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,OAAO,EAAE;AACb,EAAE,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;AAC5D;AACA,SAAS,SAAS,CAAC,GAAG,EAAE;AACxB,EAAE,OAAO,UAAU,CAAC,UAAU,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;AAC1C;AACA,SAAS,UAAU,CAAC,GAAG,EAAE;AACzB,EAAE,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;AACvD;AACA,SAAS,UAAU,CAAC,GAAG,EAAE;AACzB,EAAE,OAAO,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;AACvD;AACA,SAAS,aAAa,CAAC,GAAG,EAAE;AAC5B,EAAE,IAAI,CAAC,GAAG;AACV,IAAI,OAAO,EAAE;AACb,EAAE,MAAM,QAAQ,GAAG,EAAE;AACrB,EAAE,SAAS,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AACtH,MAAM,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;AACxC,MAAM;AACN;AACA,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AAC/B,MAAM,QAAQ,CAAC,IAAI,CAAC,GAAG,KAAK;AAC5B,MAAM;AACN;AACA,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;AACrC;AACA,EAAE,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC;AACtB,EAAE,OAAO,QAAQ;AACjB;AACA,SAAS,gBAAgB,CAAC,GAAG,SAAS,EAAE;AACxC,EAAE,OAAO,CAAC,GAAG,IAAI,KAAK;AACtB,IAAI,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AACtC,MAAM,IAAI,OAAO,QAAQ,KAAK,UAAU,EAAE;AAC1C,QAAQ,QAAQ,CAAC,GAAG,IAAI,CAAC;AACzB;AACA;AACA,GAAG;AACH;AACA,SAAS,cAAc,CAAC,GAAG,EAAE;AAC7B,EAAE,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE;AACnF;AACA,SAAS,UAAU,CAAC,GAAG,IAAI,EAAE;AAC7B,EAAE,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE;AAC/B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;AACzB,IAAI,KAAK,MAAM,GAAG,IAAI,KAAK,EAAE;AAC7B,MAAM,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC;AAC3B,MAAM,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;AAC1B,MAAM,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,UAAU;AACjD,MAAM,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,UAAU;AACjD,MAAM,IAAI,WAAW,IAAI,OAAO,WAAW,IAAI,cAAc,CAAC,GAAG,CAAC,EAAE;AACpE,QAAQ,MAAM,QAAQ,GAAG,CAAC;AAC1B,QAAQ,MAAM,QAAQ,GAAG,CAAC;AAC1B,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC;AACzD,OAAO,MAAM,IAAI,WAAW,IAAI,WAAW,EAAE;AAC7C,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5C,OAAO,MAAM,IAAI,GAAG,KAAK,OAAO,EAAE;AAClC,QAAQ,MAAM,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC;AAC7C,QAAQ,MAAM,aAAa,GAAG,YAAY,CAAC,CAAC,CAAC;AAC7C,QAAQ,IAAI,aAAa,IAAI,aAAa,EAAE;AAC5C,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;AAClC,SAAS,MAAM,IAAI,aAAa,EAAE;AAClC,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAC/B,SAAS,MAAM,IAAI,aAAa,EAAE;AAClC,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAC/B;AACA,OAAO,MAAM,IAAI,GAAG,KAAK,OAAO,EAAE;AAClC,QAAQ,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,QAAQ;AAC/C,QAAQ,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,QAAQ;AAC/C,QAAQ,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,QAAQ;AAC/C,QAAQ,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,QAAQ;AAC/C,QAAQ,IAAI,SAAS,IAAI,SAAS,EAAE;AACpC,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE;AACtC,SAAS,MAAM,IAAI,SAAS,IAAI,SAAS,EAAE;AAC3C,UAAU,MAAM,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC;AAC9C,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,GAAG,WAAW,EAAE;AAChD,SAAS,MAAM,IAAI,SAAS,IAAI,SAAS,EAAE;AAC3C,UAAU,MAAM,WAAW,GAAG,aAAa,CAAC,CAAC,CAAC;AAC9C,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,WAAW,EAAE,GAAG,CAAC,EAAE;AAChD,SAAS,MAAM,IAAI,SAAS,IAAI,SAAS,EAAE;AAC3C,UAAU,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC;AAC/C,UAAU,MAAM,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC;AAC/C,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,YAAY,EAAE,GAAG,YAAY,EAAE;AAC5D,SAAS,MAAM,IAAI,SAAS,EAAE;AAC9B,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACzB,SAAS,MAAM,IAAI,SAAS,EAAE;AAC9B,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACzB,SAAS,MAAM,IAAI,SAAS,EAAE;AAC9B,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACzB,SAAS,MAAM,IAAI,SAAS,EAAE;AAC9B,UAAU,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AACzB;AACA,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,GAAG,CAAC,GAAG,CAAC;AAC1C;AACA;AACA;AACA,EAAE,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,EAAE;AACxC,IAAI,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;AACpE;AACA,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;AAC9B,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM;AAC1B,IAAI,OAAO,MAAM,CAAC,MAAM;AACxB;AACA,EAAE,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;AAChC,IAAI,MAAM,CAAC,QAAQ,GAAG,MAAM;AAC5B,IAAI,OAAO,MAAM,CAAC,QAAQ;AAC1B;AACA,EAAE,OAAO,MAAM;AACf;AACA,SAAS,UAAU,CAAC;AACpB,EAAE,EAAE;AACJ,EAAE,GAAG;AACL,EAAE,IAAI,GAAG,MAAM,IAAI;AACnB,EAAE,WAAW;AACb,EAAE;AACF,CAAC,EAAE;AACH,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK;AAC7C,IAAI,MAAM,QAAQ,GAAG,WAAW,IAAI,IAAI,QAAQ;AAChD,IAAI,MAAM,IAAI,GAAG,QAAQ,EAAE,cAAc,CAAC,GAAG,CAAC;AAC9C,IAAI,IAAI,IAAI,EAAE,GAAG,CAAC,OAAO,GAAG,IAAI;AAChC,SAAS,GAAG,CAAC,OAAO,GAAG,IAAI;AAC3B,IAAI,WAAW,GAAG,GAAG,CAAC,OAAO,CAAC;AAC9B,GAAG,CAAC;AACJ;;;;"}