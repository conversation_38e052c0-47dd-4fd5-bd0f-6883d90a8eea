{"version": 3, "file": "use-grace-area.svelte-CrXiOQDy.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/use-grace-area.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { o as on } from \"./events.js\";\nimport { w as watch } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { e as executeCallbacks } from \"./use-ref-by-id.svelte.js\";\nimport { b as boxAutoReset } from \"./box-auto-reset.svelte.js\";\nimport { a as isHTMLElement, i as isElement } from \"./is.js\";\nfunction useGraceArea(opts) {\n  const enabled = opts.enabled();\n  const isPointerInTransit = boxAutoReset(false, opts.transitTimeout ?? 300, (value) => {\n    if (enabled) {\n      opts.setIsPointerInTransit?.(value);\n    }\n  });\n  let pointerGraceArea = null;\n  function handleRemoveGraceArea() {\n    pointerGraceArea = null;\n    isPointerInTransit.current = false;\n  }\n  function handleCreateGraceArea(e, hoverTarget) {\n    const currentTarget = e.currentTarget;\n    if (!isHTMLElement(currentTarget)) return;\n    const exitPoint = { x: e.clientX, y: e.clientY };\n    const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n    const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n    const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n    const graceArea = getHull([...paddedExitPoints, ...hoverTargetPoints]);\n    pointerGraceArea = graceArea;\n    isPointerInTransit.current = true;\n  }\n  watch(\n    [\n      opts.triggerNode,\n      opts.contentNode,\n      opts.enabled\n    ],\n    ([triggerNode, contentNode, enabled2]) => {\n      if (!triggerNode || !contentNode || !enabled2) return;\n      const handleTriggerLeave = (e) => {\n        handleCreateGraceArea(e, contentNode);\n      };\n      const handleContentLeave = (e) => {\n        handleCreateGraceArea(e, triggerNode);\n      };\n      return executeCallbacks(on(triggerNode, \"pointerleave\", handleTriggerLeave), on(contentNode, \"pointerleave\", handleContentLeave));\n    }\n  );\n  watch(() => pointerGraceArea, () => {\n    const handleTrackPointerGrace = (e) => {\n      if (!pointerGraceArea) return;\n      const target = e.target;\n      if (!isElement(target)) return;\n      const pointerPosition = { x: e.clientX, y: e.clientY };\n      const hasEnteredTarget = opts.triggerNode()?.contains(target) || opts.contentNode()?.contains(target);\n      const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n      if (hasEnteredTarget) {\n        handleRemoveGraceArea();\n      } else if (isPointerOutsideGraceArea) {\n        handleRemoveGraceArea();\n        opts.onPointerExit();\n      }\n    };\n    return on(document, \"pointermove\", handleTrackPointerGrace);\n  });\n  return { isPointerInTransit };\n}\nfunction getExitSideFromRect(point, rect) {\n  const top = Math.abs(rect.top - point.y);\n  const bottom = Math.abs(rect.bottom - point.y);\n  const right = Math.abs(rect.right - point.x);\n  const left = Math.abs(rect.left - point.x);\n  switch (Math.min(top, bottom, right, left)) {\n    case left:\n      return \"left\";\n    case right:\n      return \"right\";\n    case top:\n      return \"top\";\n    case bottom:\n      return \"bottom\";\n    default:\n      throw new Error(\"unreachable\");\n  }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n  const tipPadding = padding * 1.5;\n  switch (exitSide) {\n    case \"top\":\n      return [\n        {\n          x: exitPoint.x - padding,\n          y: exitPoint.y + padding\n        },\n        { x: exitPoint.x, y: exitPoint.y - tipPadding },\n        {\n          x: exitPoint.x + padding,\n          y: exitPoint.y + padding\n        }\n      ];\n    case \"bottom\":\n      return [\n        {\n          x: exitPoint.x - padding,\n          y: exitPoint.y - padding\n        },\n        { x: exitPoint.x, y: exitPoint.y + tipPadding },\n        {\n          x: exitPoint.x + padding,\n          y: exitPoint.y - padding\n        }\n      ];\n    case \"left\":\n      return [\n        {\n          x: exitPoint.x + padding,\n          y: exitPoint.y - padding\n        },\n        { x: exitPoint.x - tipPadding, y: exitPoint.y },\n        {\n          x: exitPoint.x + padding,\n          y: exitPoint.y + padding\n        }\n      ];\n    case \"right\":\n      return [\n        {\n          x: exitPoint.x - padding,\n          y: exitPoint.y - padding\n        },\n        { x: exitPoint.x + tipPadding, y: exitPoint.y },\n        {\n          x: exitPoint.x - padding,\n          y: exitPoint.y + padding\n        }\n      ];\n  }\n}\nfunction getPointsFromRect(rect) {\n  const { top, right, bottom, left } = rect;\n  return [\n    { x: left, y: top },\n    { x: right, y: top },\n    { x: right, y: bottom },\n    { x: left, y: bottom }\n  ];\n}\nfunction isPointInPolygon(point, polygon) {\n  const { x, y } = point;\n  let inside = false;\n  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {\n    const xi = polygon[i].x;\n    const yi = polygon[i].y;\n    const xj = polygon[j].x;\n    const yj = polygon[j].y;\n    const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n    if (intersect) inside = !inside;\n  }\n  return inside;\n}\nfunction getHull(points) {\n  const newPoints = points.slice();\n  newPoints.sort((a, b) => {\n    if (a.x < b.x) return -1;\n    else if (a.x > b.x) return 1;\n    else if (a.y < b.y) return -1;\n    else if (a.y > b.y) return 1;\n    else return 0;\n  });\n  return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n  if (points.length <= 1) return points.slice();\n  const upperHull = [];\n  for (let i = 0; i < points.length; i++) {\n    const p = points[i];\n    while (upperHull.length >= 2) {\n      const q = upperHull[upperHull.length - 1];\n      const r = upperHull[upperHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n      else break;\n    }\n    upperHull.push(p);\n  }\n  upperHull.pop();\n  const lowerHull = [];\n  for (let i = points.length - 1; i >= 0; i--) {\n    const p = points[i];\n    while (lowerHull.length >= 2) {\n      const q = lowerHull[lowerHull.length - 1];\n      const r = lowerHull[lowerHull.length - 2];\n      if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n      else break;\n    }\n    lowerHull.push(p);\n  }\n  lowerHull.pop();\n  if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) return upperHull;\n  else return upperHull.concat(lowerHull);\n}\nexport {\n  useGraceArea as u\n};\n"], "names": [], "mappings": ";;;;;;;AAOA,SAAS,YAAY,CAAC,IAAI,EAAE;AAC5B,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE;AAChC,EAAE,MAAM,kBAAkB,GAAG,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,IAAI,GAAG,EAAE,CAAC,KAAK,KAAK;AACxF,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACzC;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,gBAAgB,GAAG,IAAI;AAC7B,EAAE,SAAS,qBAAqB,GAAG;AACnC,IAAI,gBAAgB,GAAG,IAAI;AAC3B,IAAI,kBAAkB,CAAC,OAAO,GAAG,KAAK;AACtC;AACA,EAAE,SAAS,qBAAqB,CAAC,CAAC,EAAE,WAAW,EAAE;AACjD,IAAI,MAAM,aAAa,GAAG,CAAC,CAAC,aAAa;AACzC,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,EAAE;AACvC,IAAI,MAAM,SAAS,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE;AACpD,IAAI,MAAM,QAAQ,GAAG,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,qBAAqB,EAAE,CAAC;AAC1F,IAAI,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC;AACrE,IAAI,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,WAAW,CAAC,qBAAqB,EAAE,CAAC;AACpF,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,GAAG,gBAAgB,EAAE,GAAG,iBAAiB,CAAC,CAAC;AAC1E,IAAI,gBAAgB,GAAG,SAAS;AAChC,IAAI,kBAAkB,CAAC,OAAO,GAAG,IAAI;AACrC;AACA,EAAE,KAAK;AACP,IAAI;AACJ,MAAM,IAAI,CAAC,WAAW;AACtB,MAAM,IAAI,CAAC,WAAW;AACtB,MAAM,IAAI,CAAC;AACX,KAAK;AACL,IAAI,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE,QAAQ,CAAC,KAAK;AAC9C,MAAM,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ,EAAE;AACrD,MAAM,MAAM,kBAAkB,GAAG,CAAC,CAAC,KAAK;AACxC,QAAQ,qBAAqB,CAAC,CAAC,EAAE,WAAW,CAAC;AAC7C,OAAO;AACP,MAAM,MAAM,kBAAkB,GAAG,CAAC,CAAC,KAAK;AACxC,QAAQ,qBAAqB,CAAC,CAAC,EAAE,WAAW,CAAC;AAC7C,OAAO;AACP,MAAM,OAAO,gBAAgB,CAAC,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,kBAAkB,CAAC,EAAE,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;AACvI;AACA,GAAG;AACH,EAAE,KAAK,CAAC,MAAM,gBAAgB,EAAE,MAAM;AACtC,IAAI,MAAM,uBAAuB,GAAG,CAAC,CAAC,KAAK;AAC3C,MAAM,IAAI,CAAC,gBAAgB,EAAE;AAC7B,MAAM,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;AAC7B,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;AAC9B,MAAM,MAAM,eAAe,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE;AAC5D,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,QAAQ,CAAC,MAAM,CAAC;AAC3G,MAAM,MAAM,yBAAyB,GAAG,CAAC,gBAAgB,CAAC,eAAe,EAAE,gBAAgB,CAAC;AAC5F,MAAM,IAAI,gBAAgB,EAAE;AAC5B,QAAQ,qBAAqB,EAAE;AAC/B,OAAO,MAAM,IAAI,yBAAyB,EAAE;AAC5C,QAAQ,qBAAqB,EAAE;AAC/B,QAAQ,IAAI,CAAC,aAAa,EAAE;AAC5B;AACA,KAAK;AACL,IAAI,OAAO,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,uBAAuB,CAAC;AAC/D,GAAG,CAAC;AACJ,EAAE,OAAO,EAAE,kBAAkB,EAAE;AAC/B;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAE,IAAI,EAAE;AAC1C,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC;AAC1C,EAAE,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC;AAChD,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;AAC9C,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;AAC5C,EAAE,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;AAC5C,IAAI,KAAK,IAAI;AACb,MAAM,OAAO,MAAM;AACnB,IAAI,KAAK,KAAK;AACd,MAAM,OAAO,OAAO;AACpB,IAAI,KAAK,GAAG;AACZ,MAAM,OAAO,KAAK;AAClB,IAAI,KAAK,MAAM;AACf,MAAM,OAAO,QAAQ;AACrB,IAAI;AACJ,MAAM,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC;AACpC;AACA;AACA,SAAS,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,OAAO,GAAG,CAAC,EAAE;AAC/D,EAAE,MAAM,UAAU,GAAG,OAAO,GAAG,GAAG;AAClC,EAAE,QAAQ,QAAQ;AAClB,IAAI,KAAK,KAAK;AACd,MAAM,OAAO;AACb,QAAQ;AACR,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO;AAClC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;AAC3B,SAAS;AACT,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,UAAU,EAAE;AACvD,QAAQ;AACR,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO;AAClC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;AAC3B;AACA,OAAO;AACP,IAAI,KAAK,QAAQ;AACjB,MAAM,OAAO;AACb,QAAQ;AACR,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO;AAClC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;AAC3B,SAAS;AACT,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,UAAU,EAAE;AACvD,QAAQ;AACR,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO;AAClC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;AAC3B;AACA,OAAO;AACP,IAAI,KAAK,MAAM;AACf,MAAM,OAAO;AACb,QAAQ;AACR,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO;AAClC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;AAC3B,SAAS;AACT,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE;AACvD,QAAQ;AACR,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO;AAClC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;AAC3B;AACA,OAAO;AACP,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO;AACb,QAAQ;AACR,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO;AAClC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;AAC3B,SAAS;AACT,QAAQ,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,EAAE;AACvD,QAAQ;AACR,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO;AAClC,UAAU,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG;AAC3B;AACA,OAAO;AACP;AACA;AACA,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACjC,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI;AAC3C,EAAE,OAAO;AACT,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,EAAE;AACvB,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE;AACxB,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE;AAC3B,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM;AACxB,GAAG;AACH;AACA,SAAS,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE;AAC1C,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK;AACxB,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE;AACvE,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAI,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3B,IAAI,MAAM,SAAS,GAAG,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE;AACpF,IAAI,IAAI,SAAS,EAAE,MAAM,GAAG,CAAC,MAAM;AACnC;AACA,EAAE,OAAO,MAAM;AACf;AACA,SAAS,OAAO,CAAC,MAAM,EAAE;AACzB,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE;AAClC,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;AAC3B,IAAI,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;AAC5B,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;AAChC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;AACjC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;AAChC,SAAS,OAAO,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,OAAO,gBAAgB,CAAC,SAAS,CAAC;AACpC;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE;AAClC,EAAE,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE,OAAO,MAAM,CAAC,KAAK,EAAE;AAC/C,EAAE,MAAM,SAAS,GAAG,EAAE;AACtB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACvB,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;AAClC,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,EAAE;AACjF,WAAW;AACX;AACA,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACrB;AACA,EAAE,SAAS,CAAC,GAAG,EAAE;AACjB,EAAE,MAAM,SAAS,GAAG,EAAE;AACtB,EAAE,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC/C,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACvB,IAAI,OAAO,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;AAClC,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,MAAM,MAAM,CAAC,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,EAAE;AACjF,WAAW;AACX;AACA,IAAI,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACrB;AACA,EAAE,SAAS,CAAC,GAAG,EAAE;AACjB,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,SAAS;AAClJ,OAAO,OAAO,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC;AACzC;;;;"}