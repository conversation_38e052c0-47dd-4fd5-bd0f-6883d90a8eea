// Quick check of companies that need logos
import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function checkCompaniesNeedingLogos() {
  try {
    console.log('🔍 Checking companies that need logos...\n');
    
    // Get companies without logos that have websites
    const companiesNeedingLogos = await prisma.company.findMany({
      where: {
        logoUrl: null,
        website: { not: null }
      },
      select: {
        id: true,
        name: true,
        website: true,
        domain: true,
      },
      take: 20, // Show first 20 as examples
    });

    // Get total count
    const totalCount = await prisma.company.count({
      where: {
        logoUrl: null,
        website: { not: null }
      }
    });

    console.log(`📊 Total companies needing logos: ${totalCount}`);
    console.log(`📝 First ${Math.min(20, totalCount)} examples:\n`);

    companiesNeedingLogos.forEach((company, index) => {
      console.log(`${index + 1}. ${company.name}`);
      console.log(`   Website: ${company.website}`);
      console.log(`   Domain: ${company.domain || 'Not set'}`);
      console.log('');
    });

    // Also check companies with logos for comparison
    const companiesWithLogos = await prisma.company.count({
      where: {
        logoUrl: { not: null }
      }
    });

    console.log(`✅ Companies with logos: ${companiesWithLogos}`);
    console.log(`❌ Companies needing logos: ${totalCount}`);
    console.log(`📈 Logo coverage: ${((companiesWithLogos / (companiesWithLogos + totalCount)) * 100).toFixed(1)}%`);

    await prisma.$disconnect();
  } catch (error) {
    console.error('Error:', error);
    await prisma.$disconnect();
  }
}

checkCompaniesNeedingLogos();
