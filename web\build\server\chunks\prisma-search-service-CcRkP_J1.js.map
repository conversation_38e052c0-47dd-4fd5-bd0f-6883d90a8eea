{"version": 3, "file": "prisma-search-service-CcRkP_J1.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/prisma-search-service.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nasync function searchUsers(query, options = {}) {\n  try {\n    if (!query || query.length < 2) return { hits: [] };\n    const { limit = 10, teamId = null } = options;\n    const where = {\n      OR: [\n        { name: { contains: query, mode: \"insensitive\" } },\n        { email: { contains: query, mode: \"insensitive\" } }\n      ]\n    };\n    if (teamId) {\n      where.TeamMember = {\n        some: {\n          teamId\n        }\n      };\n    }\n    const users = await prisma.user.findMany({\n      where,\n      take: limit,\n      select: {\n        id: true,\n        name: true,\n        email: true,\n        image: true,\n        role: true,\n        createdAt: true,\n        updatedAt: true\n      }\n    });\n    return {\n      hits: users.map((user) => ({\n        objectID: user.id,\n        id: user.id,\n        ...user\n      }))\n    };\n  } catch (error) {\n    console.error(\"Error searching users:\", error);\n    return { hits: [] };\n  }\n}\nasync function searchJobs(query, options = {}) {\n  try {\n    if (!query || query.length < 2) return { hits: [] };\n    const { limit = 10, location = null } = options;\n    const where = {\n      title: { contains: query, mode: \"insensitive\" }\n    };\n    if (location) {\n      where.location = { contains: location, mode: \"insensitive\" };\n    }\n    const jobs = await prisma.job_listing.findMany({\n      where,\n      take: limit,\n      orderBy: {\n        postedDate: \"desc\"\n      },\n      select: {\n        id: true,\n        title: true,\n        company: true,\n        location: true,\n        url: true,\n        postedDate: true\n      }\n    });\n    return {\n      hits: jobs.map((job) => ({\n        objectID: job.id,\n        id: job.id,\n        ...job\n      }))\n    };\n  } catch (error) {\n    console.error(\"Error searching jobs:\", error);\n    return { hits: [] };\n  }\n}\nasync function searchDocuments(query, options = {}) {\n  try {\n    if (!query || query.length < 2) return { hits: [] };\n    const { limit = 10, userId = null } = options;\n    const where = {\n      OR: [\n        { label: { contains: query, mode: \"insensitive\" } },\n        { fileName: { contains: query, mode: \"insensitive\" } }\n      ]\n    };\n    if (userId) {\n      where.userId = userId;\n    }\n    const documents = await prisma.document.findMany({\n      where,\n      take: limit,\n      orderBy: {\n        updatedAt: \"desc\"\n      },\n      select: {\n        id: true,\n        label: true,\n        type: true,\n        fileName: true,\n        fileUrl: true,\n        createdAt: true,\n        updatedAt: true\n      }\n    });\n    return {\n      hits: documents.map((doc) => ({\n        objectID: doc.id,\n        id: doc.id,\n        title: doc.label,\n        type: doc.type,\n        ...doc\n      }))\n    };\n  } catch (error) {\n    console.error(\"Error searching documents:\", error);\n    return { hits: [] };\n  }\n}\nexport {\n  searchJobs as a,\n  searchDocuments as b,\n  searchUsers as s\n};\n"], "names": [], "mappings": ";;AACA,eAAe,WAAW,CAAC,KAAK,EAAE,OAAO,GAAG,EAAE,EAAE;AAChD,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;AACvD,IAAI,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,OAAO;AACjD,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,EAAE,EAAE;AACV,QAAQ,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;AAC1D,QAAQ,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE;AACzD;AACA,KAAK;AACL,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,KAAK,CAAC,UAAU,GAAG;AACzB,QAAQ,IAAI,EAAE;AACd,UAAU;AACV;AACA,OAAO;AACP;AACA,IAAI,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC7C,MAAM,KAAK;AACX,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM;AACjC,QAAQ,QAAQ,EAAE,IAAI,CAAC,EAAE;AACzB,QAAQ,EAAE,EAAE,IAAI,CAAC,EAAE;AACnB,QAAQ,GAAG;AACX,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC;AAClD,IAAI,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;AACvB;AACA;AACA,eAAe,UAAU,CAAC,KAAK,EAAE,OAAO,GAAG,EAAE,EAAE;AAC/C,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;AACvD,IAAI,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO;AACnD,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa;AACnD,KAAK;AACL,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,KAAK,CAAC,QAAQ,GAAG,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE;AAClE;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AACnD,MAAM,KAAK;AACX,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,OAAO,EAAE;AACf,QAAQ,UAAU,EAAE;AACpB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,UAAU,EAAE;AACpB;AACA,KAAK,CAAC;AACN,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AAC/B,QAAQ,QAAQ,EAAE,GAAG,CAAC,EAAE;AACxB,QAAQ,EAAE,EAAE,GAAG,CAAC,EAAE;AAClB,QAAQ,GAAG;AACX,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC;AACjD,IAAI,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;AACvB;AACA;AACA,eAAe,eAAe,CAAC,KAAK,EAAE,OAAO,GAAG,EAAE,EAAE;AACpD,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;AACvD,IAAI,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,OAAO;AACjD,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,EAAE,EAAE;AACV,QAAQ,EAAE,KAAK,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE,EAAE;AAC3D,QAAQ,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE;AAC5D;AACA,KAAK;AACL,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,KAAK,CAAC,MAAM,GAAG,MAAM;AAC3B;AACA,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACrD,MAAM,KAAK;AACX,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,OAAO,EAAE;AACf,QAAQ,SAAS,EAAE;AACnB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,SAAS,EAAE,IAAI;AACvB,QAAQ,SAAS,EAAE;AACnB;AACA,KAAK,CAAC;AACN,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;AACpC,QAAQ,QAAQ,EAAE,GAAG,CAAC,EAAE;AACxB,QAAQ,EAAE,EAAE,GAAG,CAAC,EAAE;AAClB,QAAQ,KAAK,EAAE,GAAG,CAAC,KAAK;AACxB,QAAQ,IAAI,EAAE,GAAG,CAAC,IAAI;AACtB,QAAQ,GAAG;AACX,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC;AACtD,IAAI,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;AACvB;AACA;;;;"}