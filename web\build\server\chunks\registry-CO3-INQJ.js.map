{"version": 3, "file": "registry-CO3-INQJ.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/registry.js"], "sourcesContent": ["import { L as LimitType, a as FeatureCategory } from \"./features.js\";\nconst FEATURE_LIMITS = {\n  // Resume limits\n  resume_scans_per_month: {\n    id: \"resume_scans_per_month\",\n    name: \"Resume Scans\",\n    description: \"Number of resumes you can scan per month\",\n    defaultValue: 10,\n    type: LimitType.Monthly,\n    unit: \"scans\",\n    resetDay: 1\n  },\n  ats_scans_monthly: {\n    id: \"ats_scans_monthly\",\n    name: \"ATS Scans\",\n    description: \"Number of ATS analyses you can run per month\",\n    defaultValue: 5,\n    type: LimitType.Monthly,\n    unit: \"scans\",\n    resetDay: 1\n  },\n  resume_versions: {\n    id: \"resume_versions\",\n    name: \"Resume Versions\",\n    description: \"Number of different resume versions you can create\",\n    defaultValue: 3,\n    type: LimitType.Total,\n    unit: \"versions\"\n  },\n  resume_templates: {\n    id: \"resume_templates\",\n    name: \"Resume Templates\",\n    description: \"Number of resume templates you can access\",\n    defaultValue: 5,\n    type: LimitType.Total,\n    unit: \"templates\"\n  },\n  // Job search limits\n  saved_jobs: {\n    id: \"saved_jobs\",\n    name: \"Saved Jobs\",\n    description: \"Number of jobs you can save\",\n    defaultValue: 25,\n    type: LimitType.Total,\n    unit: \"jobs\"\n  },\n  job_search_profiles: {\n    id: \"job_search_profiles\",\n    name: \"Job Search Profiles\",\n    description: \"Number of job search profiles you can create\",\n    defaultValue: 3,\n    type: LimitType.Total,\n    unit: \"profiles\"\n  },\n  job_alerts: {\n    id: \"job_alerts\",\n    name: \"Job Alerts\",\n    description: \"Number of job alerts you can create\",\n    defaultValue: 5,\n    type: LimitType.Total,\n    unit: \"alerts\"\n  },\n  // Application limits\n  applications_per_month: {\n    id: \"applications_per_month\",\n    name: \"Applications\",\n    description: \"Number of job applications you can submit per month\",\n    defaultValue: 25,\n    type: LimitType.Monthly,\n    unit: \"applications\",\n    resetDay: 1\n  },\n  cover_letters_per_month: {\n    id: \"cover_letters_per_month\",\n    name: \"Cover Letters\",\n    description: \"Number of cover letters you can generate per month\",\n    defaultValue: 10,\n    type: LimitType.Monthly,\n    unit: \"letters\",\n    resetDay: 1\n  },\n  // Team limits\n  team_members: {\n    id: \"team_members\",\n    name: \"Team Members\",\n    description: \"Number of team members you can add\",\n    defaultValue: 1,\n    type: LimitType.Concurrent,\n    unit: \"members\"\n  },\n  team_projects: {\n    id: \"team_projects\",\n    name: \"Team Projects\",\n    description: \"Number of team projects you can create\",\n    defaultValue: 3,\n    type: LimitType.Total,\n    unit: \"projects\"\n  },\n  // Integration limits\n  api_calls_per_month: {\n    id: \"api_calls_per_month\",\n    name: \"API Calls\",\n    description: \"Number of API calls you can make per month\",\n    defaultValue: 100,\n    type: LimitType.Monthly,\n    unit: \"calls\",\n    resetDay: 1\n  },\n  custom_integrations: {\n    id: \"custom_integrations\",\n    name: \"Custom Integrations\",\n    description: \"Number of custom integrations you can create\",\n    defaultValue: 0,\n    type: LimitType.Total,\n    unit: \"integrations\"\n  },\n  // Storage limits\n  storage_gb: {\n    id: \"storage_gb\",\n    name: \"Storage\",\n    description: \"Amount of storage space for your documents\",\n    defaultValue: 1,\n    type: LimitType.Total,\n    unit: \"GB\"\n  }\n};\nconst FEATURES = [\n  // Core features\n  {\n    id: \"dashboard\",\n    name: \"Dashboard\",\n    description: \"Access to the main dashboard\",\n    category: FeatureCategory.Core,\n    icon: \"layout-dashboard\"\n  },\n  {\n    id: \"profile\",\n    name: \"User Profile\",\n    description: \"Manage your user profile\",\n    category: FeatureCategory.Core,\n    icon: \"user\"\n  },\n  // Resume features\n  {\n    id: \"resume_scanner\",\n    name: \"Resume Scanner\",\n    description: \"Scan and analyze your resume against job descriptions\",\n    category: FeatureCategory.Resume,\n    icon: \"file-scan\",\n    limits: [FEATURE_LIMITS.resume_scans_per_month]\n  },\n  {\n    id: \"ats_optimization\",\n    name: \"ATS Optimization\",\n    description: \"Analyze your resume for ATS compatibility and get improvement suggestions\",\n    category: FeatureCategory.Resume,\n    icon: \"sparkles\",\n    limits: [FEATURE_LIMITS.ats_scans_monthly]\n  },\n  {\n    id: \"resume_builder\",\n    name: \"Resume Builder\",\n    description: \"Create and edit professional resumes\",\n    category: FeatureCategory.Resume,\n    icon: \"file-edit\",\n    limits: [FEATURE_LIMITS.resume_versions, FEATURE_LIMITS.resume_templates]\n  },\n  {\n    id: \"resume_ai\",\n    name: \"Resume AI\",\n    description: \"AI-powered resume improvement suggestions\",\n    category: FeatureCategory.Resume,\n    icon: \"sparkles\",\n    requiredFeatures: [\"resume_builder\"]\n  },\n  // Job search features\n  {\n    id: \"job_search\",\n    name: \"Job Search\",\n    description: \"Search for jobs across multiple platforms\",\n    category: FeatureCategory.JobSearch,\n    icon: \"search\"\n  },\n  {\n    id: \"job_save\",\n    name: \"Save Jobs\",\n    description: \"Save jobs for later review\",\n    category: FeatureCategory.JobSearch,\n    icon: \"bookmark\",\n    limits: [FEATURE_LIMITS.saved_jobs],\n    requiredFeatures: [\"job_search\"]\n  },\n  {\n    id: \"job_alerts\",\n    name: \"Job Alerts\",\n    description: \"Get notified about new job postings\",\n    category: FeatureCategory.JobSearch,\n    icon: \"bell\",\n    limits: [FEATURE_LIMITS.job_alerts],\n    requiredFeatures: [\"job_search\"]\n  },\n  {\n    id: \"job_search_profiles\",\n    name: \"Job Search Profiles\",\n    description: \"Create multiple job search profiles\",\n    category: FeatureCategory.JobSearch,\n    icon: \"users\",\n    limits: [FEATURE_LIMITS.job_search_profiles],\n    requiredFeatures: [\"job_search\"]\n  },\n  {\n    id: \"advanced_search_filters\",\n    name: \"Advanced Job Search Filters\",\n    description: \"Filter jobs by multiple criteria including salary, experience level, and more\",\n    category: FeatureCategory.JobSearch,\n    icon: \"filter\",\n    requiredFeatures: [\"job_search\"]\n  },\n  {\n    id: \"saved_searches\",\n    name: \"Saved Searches\",\n    description: \"Save your search criteria for quick access later\",\n    category: FeatureCategory.JobSearch,\n    icon: \"bookmark-plus\",\n    requiredFeatures: [\"job_search\"]\n  },\n  {\n    id: \"job_recommendations\",\n    name: \"Job Recommendations\",\n    description: \"Receive personalized job recommendations based on your profile and preferences\",\n    category: FeatureCategory.JobSearch,\n    icon: \"thumbs-up\",\n    requiredFeatures: [\"job_search\"]\n  },\n  {\n    id: \"company_research\",\n    name: \"Company Research\",\n    description: \"Access detailed information about companies\",\n    category: FeatureCategory.JobSearch,\n    icon: \"building\",\n    requiredFeatures: [\"job_search\"]\n  },\n  {\n    id: \"salary_insights\",\n    name: \"Salary Insights\",\n    description: \"View salary data for different positions and companies\",\n    category: FeatureCategory.JobSearch,\n    icon: \"dollar-sign\",\n    requiredFeatures: [\"job_search\"]\n  },\n  // Application features\n  {\n    id: \"application_tracker\",\n    name: \"Application Tracker\",\n    description: \"Track your job applications and their status\",\n    category: FeatureCategory.Applications,\n    icon: \"list-checks\"\n  },\n  {\n    id: \"application_submit\",\n    name: \"Application Submission\",\n    description: \"Submit job applications directly from the platform\",\n    category: FeatureCategory.Applications,\n    icon: \"send\",\n    limits: [FEATURE_LIMITS.applications_per_month],\n    requiredFeatures: [\"application_tracker\"]\n  },\n  {\n    id: \"cover_letter_generator\",\n    name: \"Cover Letter Generator\",\n    description: \"Generate personalized cover letters for job applications\",\n    category: FeatureCategory.Applications,\n    icon: \"file-text\",\n    limits: [FEATURE_LIMITS.cover_letters_per_month]\n  },\n  // Analytics features\n  {\n    id: \"basic_analytics\",\n    name: \"Basic Analytics\",\n    description: \"View basic analytics about your job search\",\n    category: FeatureCategory.Analytics,\n    icon: \"bar-chart\"\n  },\n  {\n    id: \"advanced_analytics\",\n    name: \"Advanced Analytics\",\n    description: \"Access advanced analytics and insights\",\n    category: FeatureCategory.Analytics,\n    icon: \"line-chart\",\n    requiredFeatures: [\"basic_analytics\"]\n  },\n  {\n    id: \"custom_reports\",\n    name: \"Custom Reports\",\n    description: \"Create and export custom reports\",\n    category: FeatureCategory.Analytics,\n    icon: \"file-bar-chart\",\n    requiredFeatures: [\"advanced_analytics\"]\n  },\n  // Team features\n  {\n    id: \"team_collaboration\",\n    name: \"Team Collaboration\",\n    description: \"Collaborate with team members\",\n    category: FeatureCategory.Team,\n    icon: \"users\",\n    limits: [FEATURE_LIMITS.team_members, FEATURE_LIMITS.team_projects]\n  },\n  {\n    id: \"team_admin\",\n    name: \"Team Administration\",\n    description: \"Manage team members and permissions\",\n    category: FeatureCategory.Team,\n    icon: \"shield\",\n    requiredFeatures: [\"team_collaboration\"]\n  },\n  // Integration features\n  {\n    id: \"api_access\",\n    name: \"API Access\",\n    description: \"Access the API for custom integrations\",\n    category: FeatureCategory.Integration,\n    icon: \"code\",\n    limits: [FEATURE_LIMITS.api_calls_per_month]\n  },\n  {\n    id: \"custom_integrations\",\n    name: \"Custom Integrations\",\n    description: \"Create custom integrations with other services\",\n    category: FeatureCategory.Integration,\n    icon: \"plug\",\n    limits: [FEATURE_LIMITS.custom_integrations],\n    requiredFeatures: [\"api_access\"]\n  }\n  // Support features\n  // Support features have been removed\n];\nfunction getFeatureById(id) {\n  return FEATURES.find((feature) => feature.id === id);\n}\nfunction getFeatureLimitById(id) {\n  return FEATURE_LIMITS[id];\n}\nexport {\n  getFeatureLimitById as a,\n  getFeatureById as g\n};\n"], "names": [], "mappings": ";;AACA,MAAM,cAAc,GAAG;AACvB;AACA,EAAE,sBAAsB,EAAE;AAC1B,IAAI,EAAE,EAAE,wBAAwB;AAChC,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,WAAW,EAAE,0CAA0C;AAC3D,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,iBAAiB,EAAE;AACrB,IAAI,EAAE,EAAE,mBAAmB;AAC3B,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,WAAW,EAAE,8CAA8C;AAC/D,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,EAAE,EAAE,iBAAiB;AACzB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,oDAAoD;AACrE,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,EAAE,EAAE,kBAAkB;AAC1B,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,WAAW,EAAE,2CAA2C;AAC5D,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE,UAAU,EAAE;AACd,IAAI,EAAE,EAAE,YAAY;AACpB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,WAAW,EAAE,6BAA6B;AAC9C,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,8CAA8C;AAC/D,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,EAAE,EAAE,YAAY;AACpB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,WAAW,EAAE,qCAAqC;AACtD,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE,sBAAsB,EAAE;AAC1B,IAAI,EAAE,EAAE,wBAAwB;AAChC,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,WAAW,EAAE,qDAAqD;AACtE,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,uBAAuB,EAAE;AAC3B,IAAI,EAAE,EAAE,yBAAyB;AACjC,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,WAAW,EAAE,oDAAoD;AACrE,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH;AACA,EAAE,YAAY,EAAE;AAChB,IAAI,EAAE,EAAE,cAAc;AACtB,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,WAAW,EAAE,oCAAoC;AACrD,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,UAAU;AAC9B,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,EAAE,EAAE,eAAe;AACvB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,WAAW,EAAE,wCAAwC;AACzD,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE,mBAAmB,EAAE;AACvB,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,WAAW,EAAE,4CAA4C;AAC7D,IAAI,YAAY,EAAE,GAAG;AACrB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,8CAA8C;AAC/D,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE,UAAU,EAAE;AACd,IAAI,EAAE,EAAE,YAAY;AACpB,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,WAAW,EAAE,4CAA4C;AAC7D,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACD,MAAM,QAAQ,GAAG;AACjB;AACA,EAAE;AACF,IAAI,EAAE,EAAE,WAAW;AACnB,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,WAAW,EAAE,8BAA8B;AAC/C,IAAI,QAAQ,EAAE,eAAe,CAAC,IAAI;AAClC,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,SAAS;AACjB,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,WAAW,EAAE,0BAA0B;AAC3C,IAAI,QAAQ,EAAE,eAAe,CAAC,IAAI;AAClC,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,gBAAgB;AACxB,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,WAAW,EAAE,uDAAuD;AACxE,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,sBAAsB;AAClD,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,kBAAkB;AAC1B,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,WAAW,EAAE,2EAA2E;AAC5F,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,iBAAiB;AAC7C,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,gBAAgB;AACxB,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,WAAW,EAAE,sCAAsC;AACvD,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,eAAe,EAAE,cAAc,CAAC,gBAAgB;AAC5E,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,WAAW;AACnB,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,WAAW,EAAE,2CAA2C;AAC5D,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,gBAAgB,EAAE,CAAC,gBAAgB;AACvC,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,YAAY;AACpB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,WAAW,EAAE,2CAA2C;AAC5D,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,UAAU;AAClB,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,WAAW,EAAE,4BAA4B;AAC7C,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC;AACvC,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,YAAY;AACpB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,WAAW,EAAE,qCAAqC;AACtD,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC;AACvC,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,qCAAqC;AACtD,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,mBAAmB,CAAC;AAChD,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,yBAAyB;AACjC,IAAI,IAAI,EAAE,6BAA6B;AACvC,IAAI,WAAW,EAAE,+EAA+E;AAChG,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,gBAAgB;AACxB,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,WAAW,EAAE,kDAAkD;AACnE,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,gFAAgF;AACjG,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,kBAAkB;AAC1B,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,WAAW,EAAE,6CAA6C;AAC9D,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,iBAAiB;AACzB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,wDAAwD;AACzE,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,8CAA8C;AAC/D,IAAI,QAAQ,EAAE,eAAe,CAAC,YAAY;AAC1C,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,oBAAoB;AAC5B,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,WAAW,EAAE,oDAAoD;AACrE,IAAI,QAAQ,EAAE,eAAe,CAAC,YAAY;AAC1C,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,sBAAsB,CAAC;AACnD,IAAI,gBAAgB,EAAE,CAAC,qBAAqB;AAC5C,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,wBAAwB;AAChC,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,WAAW,EAAE,0DAA0D;AAC3E,IAAI,QAAQ,EAAE,eAAe,CAAC,YAAY;AAC1C,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,uBAAuB;AACnD,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,iBAAiB;AACzB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,4CAA4C;AAC7D,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,oBAAoB;AAC5B,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,wCAAwC;AACzD,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,gBAAgB,EAAE,CAAC,iBAAiB;AACxC,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,gBAAgB;AACxB,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,WAAW,EAAE,kCAAkC;AACnD,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,gBAAgB,EAAE,CAAC,oBAAoB;AAC3C,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,oBAAoB;AAC5B,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,+BAA+B;AAChD,IAAI,QAAQ,EAAE,eAAe,CAAC,IAAI;AAClC,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,YAAY,EAAE,cAAc,CAAC,aAAa;AACtE,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,YAAY;AACpB,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,qCAAqC;AACtD,IAAI,QAAQ,EAAE,eAAe,CAAC,IAAI;AAClC,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,gBAAgB,EAAE,CAAC,oBAAoB;AAC3C,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,YAAY;AACpB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,WAAW,EAAE,wCAAwC;AACzD,IAAI,QAAQ,EAAE,eAAe,CAAC,WAAW;AACzC,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,mBAAmB;AAC/C,GAAG;AACH,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,gDAAgD;AACjE,IAAI,QAAQ,EAAE,eAAe,CAAC,WAAW;AACzC,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,cAAc,CAAC,mBAAmB,CAAC;AAChD,IAAI,gBAAgB,EAAE,CAAC,YAAY;AACnC;AACA;AACA;AACA,CAAC;AACD,SAAS,cAAc,CAAC,EAAE,EAAE;AAC5B,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC;AACtD;AACA,SAAS,mBAAmB,CAAC,EAAE,EAAE;AACjC,EAAE,OAAO,cAAc,CAAC,EAAE,CAAC;AAC3B;;;;"}