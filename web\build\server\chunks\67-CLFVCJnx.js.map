{"version": 3, "file": "67-CLFVCJnx.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/referrals/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/67.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { r as redirect } from \"../../../../../chunks/index.js\";\nfunction generateReferralCode(name, email) {\n  const prefix = name ? name.replace(/[^a-zA-Z]/g, \"\").substring(0, 3).toUpperCase() : email?.substring(0, 3).toUpperCase() || \"REF\";\n  const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase();\n  return `${prefix}${randomSuffix}`;\n}\nconst load = async ({ locals }) => {\n  const user = locals.user;\n  if (!user?.email) {\n    throw redirect(302, \"/auth/sign-in\");\n  }\n  try {\n    const userData = await prisma.user.findUnique({\n      where: { email: user.email },\n      include: {\n        referralsMade: {\n          include: {\n            referred: {\n              select: {\n                id: true,\n                name: true,\n                email: true,\n                createdAt: true\n              }\n            }\n          },\n          orderBy: { createdAt: \"desc\" }\n        },\n        referrals: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            createdAt: true\n          }\n        },\n        referredBy: {\n          select: {\n            id: true,\n            name: true,\n            email: true,\n            referralCode: true\n          }\n        }\n      }\n    });\n    if (!userData) {\n      throw redirect(302, \"/auth/sign-in\");\n    }\n    let referralCode = userData.referralCode;\n    if (!referralCode) {\n      referralCode = generateReferralCode(userData.name, userData.email);\n      let attempts = 0;\n      while (attempts < 5) {\n        const existing = await prisma.user.findUnique({\n          where: { referralCode }\n        });\n        if (!existing) break;\n        referralCode = generateReferralCode(userData.name, userData.email);\n        attempts++;\n      }\n      await prisma.user.update({\n        where: { id: userData.id },\n        data: { referralCode }\n      });\n    }\n    const baseUrl = process.env.PUBLIC_BASE_URL || \"http://localhost:5173\";\n    const referralLink = `${baseUrl}/auth/sign-up?ref=${referralCode}`;\n    const referralData = {\n      referralCode,\n      referralLink,\n      referralCount: userData.referralCount ?? 0,\n      referralRewards: userData.referralRewards ?? 0,\n      referrals: userData.referralsMade ?? [],\n      referredBy: userData.referredBy\n    };\n    return {\n      referralData\n    };\n  } catch (error) {\n    console.error(\"Error loading referral data:\", error);\n    throw redirect(302, \"/auth/sign-in\");\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/referrals/_page.server.ts.js';\n\nexport const index = 67;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/referrals/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/referrals/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/67.B9a21eQj.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/nZgk9enP.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/XnZcpgwi.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/Dq03aqGn.js\",\"_app/immutable/chunks/BHEV2D3b.js\",\"_app/immutable/chunks/BniYvUIG.js\",\"_app/immutable/chunks/BYB878do.js\",\"_app/immutable/chunks/BQ5jqT_2.js\",\"_app/immutable/chunks/CbynRejM.js\",\"_app/immutable/chunks/Csk_I0QV.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/BSHZ37s_.js\",\"_app/immutable/chunks/DZCYCPd3.js\",\"_app/immutable/chunks/BnV6AXQp.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/0ykhD7u6.js\",\"_app/immutable/chunks/BEVim9wJ.js\",\"_app/immutable/chunks/BAawoUIy.js\",\"_app/immutable/chunks/Dt_Sfkn6.js\",\"_app/immutable/chunks/yPulTJ2h.js\",\"_app/immutable/chunks/qwsZpUIl.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/DmZyh-PW.js\"];\nexport const stylesheets = [\"_app/immutable/assets/chart-tooltip.BTdU6mpn.css\",\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,SAAS,oBAAoB,CAAC,IAAI,EAAE,KAAK,EAAE;AAC3C,EAAE,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,IAAI,KAAK;AACpI,EAAE,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE;AAC/E,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,EAAE,YAAY,CAAC,CAAC;AACnC;AACA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI;AAC1B,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE;AACpB,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,MAAM,KAAK,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;AAClC,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,OAAO,EAAE;AACnB,YAAY,QAAQ,EAAE;AACtB,cAAc,MAAM,EAAE;AACtB,gBAAgB,EAAE,EAAE,IAAI;AACxB,gBAAgB,IAAI,EAAE,IAAI;AAC1B,gBAAgB,KAAK,EAAE,IAAI;AAC3B,gBAAgB,SAAS,EAAE;AAC3B;AACA;AACA,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM;AACtC,SAAS;AACT,QAAQ,SAAS,EAAE;AACnB,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,SAAS,EAAE;AACvB;AACA,SAAS;AACT,QAAQ,UAAU,EAAE;AACpB,UAAU,MAAM,EAAE;AAClB,YAAY,EAAE,EAAE,IAAI;AACpB,YAAY,IAAI,EAAE,IAAI;AACtB,YAAY,KAAK,EAAE,IAAI;AACvB,YAAY,YAAY,EAAE;AAC1B;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AAC1C;AACA,IAAI,IAAI,YAAY,GAAG,QAAQ,CAAC,YAAY;AAC5C,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,YAAY,GAAG,oBAAoB,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC;AACxE,MAAM,IAAI,QAAQ,GAAG,CAAC;AACtB,MAAM,OAAO,QAAQ,GAAG,CAAC,EAAE;AAC3B,QAAQ,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACtD,UAAU,KAAK,EAAE,EAAE,YAAY;AAC/B,SAAS,CAAC;AACV,QAAQ,IAAI,CAAC,QAAQ,EAAE;AACvB,QAAQ,YAAY,GAAG,oBAAoB,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC;AAC1E,QAAQ,QAAQ,EAAE;AAClB;AACA,MAAM,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;AAC/B,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE;AAClC,QAAQ,IAAI,EAAE,EAAE,YAAY;AAC5B,OAAO,CAAC;AACR;AACA,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,uBAAuB;AAC1E,IAAI,MAAM,YAAY,GAAG,CAAC,EAAE,OAAO,CAAC,kBAAkB,EAAE,YAAY,CAAC,CAAC;AACtE,IAAI,MAAM,YAAY,GAAG;AACzB,MAAM,YAAY;AAClB,MAAM,YAAY;AAClB,MAAM,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,CAAC;AAChD,MAAM,eAAe,EAAE,QAAQ,CAAC,eAAe,IAAI,CAAC;AACpD,MAAM,SAAS,EAAE,QAAQ,CAAC,aAAa,IAAI,EAAE;AAC7C,MAAM,UAAU,EAAE,QAAQ,CAAC;AAC3B,KAAK;AACL,IAAI,OAAO;AACX,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC;AACxD,IAAI,MAAM,QAAQ,CAAC,GAAG,EAAE,eAAe,CAAC;AACxC;AACA,CAAC;;;;;;;AClFW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAA+D,CAAC,EAAE;AAE7H,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACzuE,MAAC,WAAW,GAAG,CAAC,kDAAkD,CAAC,4CAA4C;AAC/G,MAAC,KAAK,GAAG;;;;"}