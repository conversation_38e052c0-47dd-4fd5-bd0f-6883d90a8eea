{"version": 3, "file": "types-S7ZK-xcu.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/types.js"], "sourcesContent": ["var EmailTemplate = /* @__PURE__ */ ((EmailTemplate2) => {\n  EmailTemplate2[\"WELCOME\"] = \"welcome\";\n  EmailTemplate2[\"VERIFICATION\"] = \"verification\";\n  EmailTemplate2[\"PASSWORD_RESET\"] = \"password-reset\";\n  EmailTemplate2[\"PASSWORD_CHANGED\"] = \"password-changed\";\n  EmailTemplate2[\"JOB_APPLICATION_SUBMITTED\"] = \"job-application-submitted\";\n  EmailTemplate2[\"JOB_APPLICATION_STATUS_UPDATE\"] = \"job-application-status-update\";\n  EmailTemplate2[\"RESUME_OPTIMIZATION_COMPLETE\"] = \"resume-optimization-complete\";\n  EmailTemplate2[\"WEEKLY_SUMMARY\"] = \"weekly-summary\";\n  EmailTemplate2[\"TEST_TEMPLATE\"] = \"test-template\";\n  return EmailTemplate2;\n})(EmailTemplate || {});\nexport {\n  EmailTemplate as E\n};\n"], "names": [], "mappings": "AAAG,IAAC,aAAa,mBAAmB,CAAC,CAAC,cAAc,KAAK;AACzD,EAAE,cAAc,CAAC,SAAS,CAAC,GAAG,SAAS;AACvC,EAAE,cAAc,CAAC,cAAc,CAAC,GAAG,cAAc;AACjD,EAAE,cAAc,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;AACrD,EAAE,cAAc,CAAC,kBAAkB,CAAC,GAAG,kBAAkB;AACzD,EAAE,cAAc,CAAC,2BAA2B,CAAC,GAAG,2BAA2B;AAC3E,EAAE,cAAc,CAAC,+BAA+B,CAAC,GAAG,+BAA+B;AACnF,EAAE,cAAc,CAAC,8BAA8B,CAAC,GAAG,8BAA8B;AACjF,EAAE,cAAc,CAAC,gBAAgB,CAAC,GAAG,gBAAgB;AACrD,EAAE,cAAc,CAAC,eAAe,CAAC,GAAG,eAAe;AACnD,EAAE,OAAO,cAAc;AACvB,CAAC,EAAE,aAAa,IAAI,EAAE;;;;"}