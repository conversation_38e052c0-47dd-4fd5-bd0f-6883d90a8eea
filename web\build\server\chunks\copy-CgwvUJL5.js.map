{"version": 3, "file": "copy-CgwvUJL5.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/copy.js"], "sourcesContent": ["import { Z as sanitize_props, Q as spread_props, a0 as slot } from \"./index3.js\";\nimport { I as Icon } from \"./Icon.js\";\nfunction Copy($$payload, $$props) {\n  const $$sanitized_props = sanitize_props($$props);\n  const iconNode = [\n    [\n      \"rect\",\n      {\n        \"width\": \"14\",\n        \"height\": \"14\",\n        \"x\": \"8\",\n        \"y\": \"8\",\n        \"rx\": \"2\",\n        \"ry\": \"2\"\n      }\n    ],\n    [\n      \"path\",\n      {\n        \"d\": \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\"\n      }\n    ]\n  ];\n  Icon($$payload, spread_props([\n    { name: \"copy\" },\n    $$sanitized_props,\n    {\n      iconNode,\n      children: ($$payload2) => {\n        $$payload2.out += `<!---->`;\n        slot($$payload2, $$props, \"default\", {}, null);\n        $$payload2.out += `<!---->`;\n      },\n      $$slots: { default: true }\n    }\n  ]));\n}\nexport {\n  Copy as C\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE;AAClC,EAAE,MAAM,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC;AACnD,EAAE,MAAM,QAAQ,GAAG;AACnB,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,QAAQ,EAAE,IAAI;AACtB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,GAAG,EAAE,GAAG;AAChB,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,IAAI,EAAE;AACd;AACA,KAAK;AACL,IAAI;AACJ,MAAM,MAAM;AACZ,MAAM;AACN,QAAQ,GAAG,EAAE;AACb;AACA;AACA,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC;AAC/B,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AACpB,IAAI,iBAAiB;AACrB,IAAI;AACJ,MAAM,QAAQ;AACd,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,QAAQ,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,CAAC;AACtD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B;AACA,GAAG,CAAC,CAAC;AACL;;;;"}