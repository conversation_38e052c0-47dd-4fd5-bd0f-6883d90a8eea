{"version": 3, "file": "service-features-B8SQcO_C.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/service-features.js"], "sourcesContent": ["import { L as LimitType, a as FeatureCategory } from \"./features.js\";\nconst SERVICE_FEATURE_LIMITS = {\n  // Auto-Apply Limits\n  auto_apply_jobs_monthly: {\n    id: \"auto_apply_jobs_monthly\",\n    name: \"Auto-Apply Jobs\",\n    description: \"Number of jobs you can auto-apply to per month\",\n    defaultValue: 50,\n    type: LimitType.Monthly,\n    unit: \"applications\",\n    resetDay: 1\n  },\n  job_search_monthly: {\n    id: \"job_search_monthly\",\n    name: \"Job Searches\",\n    description: \"Number of job searches you can perform per month\",\n    defaultValue: 100,\n    type: LimitType.Monthly,\n    unit: \"searches\",\n    resetDay: 1\n  },\n  saved_jobs_total: {\n    id: \"saved_jobs_total\",\n    name: \"Saved Jobs\",\n    description: \"Maximum number of jobs you can save\",\n    defaultValue: 200,\n    type: LimitType.Total,\n    unit: \"jobs\"\n  },\n  // Co-Pilot Limits\n  ai_interview_sessions_monthly: {\n    id: \"ai_interview_sessions_monthly\",\n    name: \"AI Interview Sessions\",\n    description: \"Number of AI interview coaching sessions per month\",\n    defaultValue: 5,\n    type: LimitType.Monthly,\n    unit: \"sessions\",\n    resetDay: 1\n  },\n  career_advice_requests_monthly: {\n    id: \"career_advice_requests_monthly\",\n    name: \"Career Advice Requests\",\n    description: \"Number of personalized career advice requests per month\",\n    defaultValue: 10,\n    type: LimitType.Monthly,\n    unit: \"requests\",\n    resetDay: 1\n  },\n  job_match_analysis_monthly: {\n    id: \"job_match_analysis_monthly\",\n    name: \"Job Match Analysis\",\n    description: \"Number of job match analyses you can perform per month\",\n    defaultValue: 20,\n    type: LimitType.Monthly,\n    unit: \"analyses\",\n    resetDay: 1\n  },\n  // Job Tracker Limits\n  tracked_applications_total: {\n    id: \"tracked_applications_total\",\n    name: \"Tracked Applications\",\n    description: \"Maximum number of job applications you can track\",\n    defaultValue: 100,\n    type: LimitType.Total,\n    unit: \"applications\"\n  },\n  application_notes_per_job: {\n    id: \"application_notes_per_job\",\n    name: \"Notes Per Application\",\n    description: \"Maximum number of notes you can add per job application\",\n    defaultValue: 20,\n    type: LimitType.Total,\n    unit: \"notes\"\n  },\n  // Resume Builder Limits\n  resume_versions_total: {\n    id: \"resume_versions_total\",\n    name: \"Resume Versions\",\n    description: \"Maximum number of resume versions you can create\",\n    defaultValue: 5,\n    type: LimitType.Total,\n    unit: \"versions\"\n  },\n  resume_templates_total: {\n    id: \"resume_templates_total\",\n    name: \"Resume Templates\",\n    description: \"Number of premium resume templates available\",\n    defaultValue: 10,\n    type: LimitType.Total,\n    unit: \"templates\"\n  },\n  // Automation Limits\n  automation_runs_monthly: {\n    id: \"automation_runs_monthly\",\n    name: \"Automation Runs\",\n    description: \"Number of automation workflows you can run per month\",\n    defaultValue: 5,\n    type: LimitType.Monthly,\n    unit: \"runs\",\n    resetDay: 1\n  },\n  automation_tasks_per_run: {\n    id: \"automation_tasks_per_run\",\n    name: \"Tasks Per Automation\",\n    description: \"Maximum number of tasks per automation workflow\",\n    defaultValue: 10,\n    type: LimitType.Total,\n    unit: \"tasks\"\n  }\n};\nconst AUTO_APPLY_FEATURES = [\n  // One-Click Apply\n  {\n    id: \"one_click_apply\",\n    name: \"One-Click Apply\",\n    description: \"Apply to jobs with a single click using your saved profile and resume\",\n    category: FeatureCategory.JobSearch,\n    icon: \"mouse-pointer-click\",\n    beta: false,\n    limits: [SERVICE_FEATURE_LIMITS.auto_apply_jobs_monthly]\n  },\n  // Advanced Job Search\n  {\n    id: \"advanced_job_search\",\n    name: \"Advanced Job Search\",\n    description: \"Access advanced filters and search options to find the perfect job match\",\n    category: FeatureCategory.JobSearch,\n    icon: \"search\",\n    beta: false,\n    limits: [SERVICE_FEATURE_LIMITS.job_search_monthly]\n  },\n  // Job Matching\n  {\n    id: \"job_matching\",\n    name: \"AI Job Matching\",\n    description: \"Get AI-powered job recommendations based on your skills and experience\",\n    category: FeatureCategory.JobSearch,\n    icon: \"sparkles\",\n    beta: false\n  },\n  // Job Bookmarking\n  {\n    id: \"job_bookmarking\",\n    name: \"Job Bookmarking\",\n    description: \"Save and organize jobs you're interested in for later review\",\n    category: FeatureCategory.JobSearch,\n    icon: \"bookmark\",\n    beta: false,\n    limits: [SERVICE_FEATURE_LIMITS.saved_jobs_total]\n  }\n];\nconst CO_PILOT_FEATURES = [\n  // AI Interview Coach\n  {\n    id: \"ai_interview_coach\",\n    name: \"AI Interview Coach\",\n    description: \"Practice interviews with AI that provides feedback and improvement suggestions\",\n    category: FeatureCategory.Advanced,\n    icon: \"message-square\",\n    beta: true,\n    limits: [SERVICE_FEATURE_LIMITS.ai_interview_sessions_monthly]\n  },\n  // Career Advisor\n  {\n    id: \"career_advisor\",\n    name: \"Career Advisor\",\n    description: \"Get personalized career advice and guidance based on your goals\",\n    category: FeatureCategory.Advanced,\n    icon: \"compass\",\n    beta: true,\n    limits: [SERVICE_FEATURE_LIMITS.career_advice_requests_monthly]\n  },\n  // Job Match Analyzer\n  {\n    id: \"job_match_analyzer\",\n    name: \"Job Match Analyzer\",\n    description: \"Analyze how well your profile matches specific job requirements\",\n    category: FeatureCategory.Advanced,\n    icon: \"target\",\n    beta: true,\n    limits: [SERVICE_FEATURE_LIMITS.job_match_analysis_monthly]\n  },\n  // Career Roadmap\n  {\n    id: \"career_roadmap\",\n    name: \"Career Roadmap\",\n    description: \"Create a strategic career development plan with milestones and goals\",\n    category: FeatureCategory.Advanced,\n    icon: \"map\",\n    beta: true\n  }\n];\nconst JOB_TRACKER_FEATURES = [\n  // Application Tracking\n  {\n    id: \"application_tracking\",\n    name: \"Application Tracking\",\n    description: \"Track the status and progress of all your job applications\",\n    category: FeatureCategory.Applications,\n    icon: \"list-checks\",\n    beta: false,\n    limits: [SERVICE_FEATURE_LIMITS.tracked_applications_total]\n  },\n  // Application Notes\n  {\n    id: \"application_notes\",\n    name: \"Application Notes\",\n    description: \"Add detailed notes and reminders to your job applications\",\n    category: FeatureCategory.Applications,\n    icon: \"clipboard\",\n    beta: false,\n    limits: [SERVICE_FEATURE_LIMITS.application_notes_per_job]\n  },\n  // Interview Scheduler\n  {\n    id: \"interview_scheduler\",\n    name: \"Interview Scheduler\",\n    description: \"Schedule and manage your job interviews with reminders\",\n    category: FeatureCategory.Applications,\n    icon: \"calendar\",\n    beta: false\n  },\n  // Application Analytics\n  {\n    id: \"application_analytics\",\n    name: \"Application Analytics\",\n    description: \"View insights and statistics about your job application performance\",\n    category: FeatureCategory.Applications,\n    icon: \"bar-chart\",\n    beta: false\n  }\n];\nconst RESUME_BUILDER_FEATURES = [\n  // Resume Creator\n  {\n    id: \"resume_creator\",\n    name: \"Resume Creator\",\n    description: \"Create professional resumes with our easy-to-use builder\",\n    category: FeatureCategory.Resume,\n    icon: \"file-edit\",\n    beta: false,\n    limits: [SERVICE_FEATURE_LIMITS.resume_versions_total]\n  },\n  // ATS Optimization\n  {\n    id: \"ats_optimization\",\n    name: \"ATS Optimization\",\n    description: \"Optimize your resume to pass through Applicant Tracking Systems\",\n    category: FeatureCategory.Resume,\n    icon: \"check-circle\",\n    beta: false\n  },\n  // Premium Templates\n  {\n    id: \"premium_templates\",\n    name: \"Premium Templates\",\n    description: \"Access to premium, professionally designed resume templates\",\n    category: FeatureCategory.Resume,\n    icon: \"layout-template\",\n    beta: false,\n    limits: [SERVICE_FEATURE_LIMITS.resume_templates_total]\n  },\n  // Resume Export\n  {\n    id: \"resume_export\",\n    name: \"Resume Export\",\n    description: \"Export your resume in multiple formats (PDF, DOCX, TXT)\",\n    category: FeatureCategory.Resume,\n    icon: \"download\",\n    beta: false\n  }\n];\nconst AUTOMATION_FEATURES = [\n  // Workflow Automation\n  {\n    id: \"workflow_automation\",\n    name: \"Workflow Automation\",\n    description: \"Create automated workflows for your job search process\",\n    category: FeatureCategory.Automation,\n    icon: \"workflow\",\n    beta: true,\n    limits: [SERVICE_FEATURE_LIMITS.automation_runs_monthly]\n  },\n  // Job Alerts\n  {\n    id: \"job_alerts\",\n    name: \"Job Alerts\",\n    description: \"Receive customized job alerts based on your preferences\",\n    category: FeatureCategory.Automation,\n    icon: \"bell\",\n    beta: false\n  },\n  // Auto Resume Tailoring\n  {\n    id: \"auto_resume_tailoring\",\n    name: \"Auto Resume Tailoring\",\n    description: \"Automatically tailor your resume for specific job applications\",\n    category: FeatureCategory.Automation,\n    icon: \"scissors\",\n    beta: true\n  },\n  // Scheduled Applications\n  {\n    id: \"scheduled_applications\",\n    name: \"Scheduled Applications\",\n    description: \"Schedule job applications to be submitted at optimal times\",\n    category: FeatureCategory.Automation,\n    icon: \"clock\",\n    beta: true,\n    limits: [SERVICE_FEATURE_LIMITS.automation_tasks_per_run]\n  }\n];\nconst MATCHES_FEATURES = [\n  // AI Job Matching\n  {\n    id: \"ai_job_matching\",\n    name: \"AI Job Matching\",\n    description: \"Get AI-powered job recommendations based on your skills and experience\",\n    category: FeatureCategory.JobSearch,\n    icon: \"sparkles\",\n    beta: false,\n    limits: [SERVICE_FEATURE_LIMITS.job_match_analysis_monthly]\n  },\n  // Match Scoring\n  {\n    id: \"match_scoring\",\n    name: \"Match Scoring\",\n    description: \"See detailed compatibility scores for each job match\",\n    category: FeatureCategory.JobSearch,\n    icon: \"percent\",\n    beta: false\n  },\n  // Skill Gap Analysis\n  {\n    id: \"skill_gap_analysis_matches\",\n    name: \"Skill Gap Analysis\",\n    description: \"Identify missing skills for your matched job opportunities\",\n    category: FeatureCategory.JobSearch,\n    icon: \"puzzle\",\n    beta: true\n  }\n];\nconst JOBS_FEATURES = [\n  // Job Board\n  {\n    id: \"job_board\",\n    name: \"Job Board\",\n    description: \"Browse and search through thousands of job listings\",\n    category: FeatureCategory.JobSearch,\n    icon: \"briefcase\",\n    beta: false\n  },\n  // Job Filtering\n  {\n    id: \"job_filtering\",\n    name: \"Advanced Job Filtering\",\n    description: \"Filter jobs by salary, location, experience level, and more\",\n    category: FeatureCategory.JobSearch,\n    icon: \"filter\",\n    beta: false\n  },\n  // Salary Insights\n  {\n    id: \"salary_insights_jobs\",\n    name: \"Salary Insights\",\n    description: \"View salary ranges and compensation details for job listings\",\n    category: FeatureCategory.JobSearch,\n    icon: \"dollar-sign\",\n    beta: false\n  }\n];\nconst DOCUMENTS_FEATURES = [\n  // Document Storage\n  {\n    id: \"document_storage\",\n    name: \"Document Storage\",\n    description: \"Securely store and manage your resumes, cover letters, and other documents\",\n    category: FeatureCategory.Resume,\n    icon: \"folder\",\n    beta: false,\n    limits: [\n      {\n        id: \"storage_gb\",\n        name: \"Storage\",\n        description: \"Amount of storage space for your documents\",\n        defaultValue: 1,\n        type: LimitType.Total,\n        unit: \"GB\"\n      },\n      {\n        id: \"document_count\",\n        name: \"Document Count\",\n        description: \"Maximum number of documents you can upload\",\n        defaultValue: 20,\n        type: LimitType.Total,\n        unit: \"documents\"\n      },\n      {\n        id: \"document_size_mb\",\n        name: \"Document Size\",\n        description: \"Maximum size of individual documents\",\n        defaultValue: 10,\n        type: LimitType.Total,\n        unit: \"MB\"\n      },\n      {\n        id: \"document_sharing\",\n        name: \"Document Sharing\",\n        description: \"Number of documents you can share with others\",\n        defaultValue: 5,\n        type: LimitType.Total,\n        unit: \"documents\"\n      }\n    ]\n  },\n  // Document Sharing\n  {\n    id: \"document_sharing\",\n    name: \"Document Sharing\",\n    description: \"Share your documents with recruiters or team members\",\n    category: FeatureCategory.Resume,\n    icon: \"share\",\n    beta: false\n  },\n  // Document Versioning\n  {\n    id: \"document_versioning\",\n    name: \"Document Versioning\",\n    description: \"Track changes and maintain multiple versions of your documents\",\n    category: FeatureCategory.Resume,\n    icon: \"history\",\n    beta: false\n  }\n];\nconst SERVICE_ANALYSIS_FEATURES = [\n  // Resume Analysis\n  {\n    id: \"resume_analysis\",\n    name: \"Resume Analysis\",\n    description: \"Get detailed feedback and improvement suggestions for your resume\",\n    category: FeatureCategory.Analytics,\n    icon: \"file-text\",\n    beta: false\n  },\n  // Job Market Analysis\n  {\n    id: \"job_market_analysis\",\n    name: \"Job Market Analysis\",\n    description: \"Analyze trends and insights in your target job market\",\n    category: FeatureCategory.Analytics,\n    icon: \"trending-up\",\n    beta: true\n  },\n  // Application Performance Analysis\n  {\n    id: \"application_performance_analysis\",\n    name: \"Application Performance Analysis\",\n    description: \"Analyze your application success rates and identify improvement areas\",\n    category: FeatureCategory.Analytics,\n    icon: \"activity\",\n    beta: true\n  }\n];\nconst SERVICE_FEATURES = [\n  ...AUTO_APPLY_FEATURES,\n  ...CO_PILOT_FEATURES,\n  ...JOB_TRACKER_FEATURES,\n  ...RESUME_BUILDER_FEATURES,\n  ...AUTOMATION_FEATURES,\n  ...MATCHES_FEATURES,\n  ...JOBS_FEATURES,\n  ...DOCUMENTS_FEATURES,\n  ...SERVICE_ANALYSIS_FEATURES\n];\nexport {\n  SERVICE_FEATURES as S\n};\n"], "names": [], "mappings": ";;AACA,MAAM,sBAAsB,GAAG;AAC/B;AACA,EAAE,uBAAuB,EAAE;AAC3B,IAAI,EAAE,EAAE,yBAAyB;AACjC,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,gDAAgD;AACjE,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,EAAE,EAAE,oBAAoB;AAC5B,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,WAAW,EAAE,kDAAkD;AACnE,IAAI,YAAY,EAAE,GAAG;AACrB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,gBAAgB,EAAE;AACpB,IAAI,EAAE,EAAE,kBAAkB;AAC1B,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,WAAW,EAAE,qCAAqC;AACtD,IAAI,YAAY,EAAE,GAAG;AACrB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE,6BAA6B,EAAE;AACjC,IAAI,EAAE,EAAE,+BAA+B;AACvC,IAAI,IAAI,EAAE,uBAAuB;AACjC,IAAI,WAAW,EAAE,oDAAoD;AACrE,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,8BAA8B,EAAE;AAClC,IAAI,EAAE,EAAE,gCAAgC;AACxC,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,WAAW,EAAE,yDAAyD;AAC1E,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,0BAA0B,EAAE;AAC9B,IAAI,EAAE,EAAE,4BAA4B;AACpC,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,wDAAwD;AACzE,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH;AACA,EAAE,0BAA0B,EAAE;AAC9B,IAAI,EAAE,EAAE,4BAA4B;AACpC,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,WAAW,EAAE,kDAAkD;AACnE,IAAI,YAAY,EAAE,GAAG;AACrB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,yBAAyB,EAAE;AAC7B,IAAI,EAAE,EAAE,2BAA2B;AACnC,IAAI,IAAI,EAAE,uBAAuB;AACjC,IAAI,WAAW,EAAE,yDAAyD;AAC1E,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE,qBAAqB,EAAE;AACzB,IAAI,EAAE,EAAE,uBAAuB;AAC/B,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,kDAAkD;AACnE,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,sBAAsB,EAAE;AAC1B,IAAI,EAAE,EAAE,wBAAwB;AAChC,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,WAAW,EAAE,8CAA8C;AAC/D,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE,uBAAuB,EAAE;AAC3B,IAAI,EAAE,EAAE,yBAAyB;AACjC,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,sDAAsD;AACvE,IAAI,YAAY,EAAE,CAAC;AACnB,IAAI,IAAI,EAAE,SAAS,CAAC,OAAO;AAC3B,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,QAAQ,EAAE;AACd,GAAG;AACH,EAAE,wBAAwB,EAAE;AAC5B,IAAI,EAAE,EAAE,0BAA0B;AAClC,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,WAAW,EAAE,iDAAiD;AAClE,IAAI,YAAY,EAAE,EAAE;AACpB,IAAI,IAAI,EAAE,SAAS,CAAC,KAAK;AACzB,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACD,MAAM,mBAAmB,GAAG;AAC5B;AACA,EAAE;AACF,IAAI,EAAE,EAAE,iBAAiB;AACzB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,uEAAuE;AACxF,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,uBAAuB;AAC3D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,0EAA0E;AAC3F,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,kBAAkB;AACtD,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,cAAc;AACtB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,wEAAwE;AACzF,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,iBAAiB;AACzB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,8DAA8D;AAC/E,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,gBAAgB;AACpD;AACA,CAAC;AACD,MAAM,iBAAiB,GAAG;AAC1B;AACA,EAAE;AACF,IAAI,EAAE,EAAE,oBAAoB;AAC5B,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,gFAAgF;AACjG,IAAI,QAAQ,EAAE,eAAe,CAAC,QAAQ;AACtC,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,6BAA6B;AACjE,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,gBAAgB;AACxB,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,WAAW,EAAE,iEAAiE;AAClF,IAAI,QAAQ,EAAE,eAAe,CAAC,QAAQ;AACtC,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,8BAA8B;AAClE,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,oBAAoB;AAC5B,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,iEAAiE;AAClF,IAAI,QAAQ,EAAE,eAAe,CAAC,QAAQ;AACtC,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,0BAA0B;AAC9D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,gBAAgB;AACxB,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,WAAW,EAAE,sEAAsE;AACvF,IAAI,QAAQ,EAAE,eAAe,CAAC,QAAQ;AACtC,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACD,MAAM,oBAAoB,GAAG;AAC7B;AACA,EAAE;AACF,IAAI,EAAE,EAAE,sBAAsB;AAC9B,IAAI,IAAI,EAAE,sBAAsB;AAChC,IAAI,WAAW,EAAE,4DAA4D;AAC7E,IAAI,QAAQ,EAAE,eAAe,CAAC,YAAY;AAC1C,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,0BAA0B;AAC9D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,mBAAmB;AAC3B,IAAI,IAAI,EAAE,mBAAmB;AAC7B,IAAI,WAAW,EAAE,2DAA2D;AAC5E,IAAI,QAAQ,EAAE,eAAe,CAAC,YAAY;AAC1C,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,yBAAyB;AAC7D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,wDAAwD;AACzE,IAAI,QAAQ,EAAE,eAAe,CAAC,YAAY;AAC1C,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,uBAAuB;AAC/B,IAAI,IAAI,EAAE,uBAAuB;AACjC,IAAI,WAAW,EAAE,qEAAqE;AACtF,IAAI,QAAQ,EAAE,eAAe,CAAC,YAAY;AAC1C,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACD,MAAM,uBAAuB,GAAG;AAChC;AACA,EAAE;AACF,IAAI,EAAE,EAAE,gBAAgB;AACxB,IAAI,IAAI,EAAE,gBAAgB;AAC1B,IAAI,WAAW,EAAE,0DAA0D;AAC3E,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,qBAAqB;AACzD,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,kBAAkB;AAC1B,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,WAAW,EAAE,iEAAiE;AAClF,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,cAAc;AACxB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,mBAAmB;AAC3B,IAAI,IAAI,EAAE,mBAAmB;AAC7B,IAAI,WAAW,EAAE,6DAA6D;AAC9E,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,sBAAsB;AAC1D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,eAAe;AACvB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,WAAW,EAAE,yDAAyD;AAC1E,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACD,MAAM,mBAAmB,GAAG;AAC5B;AACA,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,wDAAwD;AACzE,IAAI,QAAQ,EAAE,eAAe,CAAC,UAAU;AACxC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,uBAAuB;AAC3D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,YAAY;AACpB,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,WAAW,EAAE,yDAAyD;AAC1E,IAAI,QAAQ,EAAE,eAAe,CAAC,UAAU;AACxC,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,uBAAuB;AAC/B,IAAI,IAAI,EAAE,uBAAuB;AACjC,IAAI,WAAW,EAAE,gEAAgE;AACjF,IAAI,QAAQ,EAAE,eAAe,CAAC,UAAU;AACxC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,wBAAwB;AAChC,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,WAAW,EAAE,4DAA4D;AAC7E,IAAI,QAAQ,EAAE,eAAe,CAAC,UAAU;AACxC,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,wBAAwB;AAC5D;AACA,CAAC;AACD,MAAM,gBAAgB,GAAG;AACzB;AACA,EAAE;AACF,IAAI,EAAE,EAAE,iBAAiB;AACzB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,wEAAwE;AACzF,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE,CAAC,sBAAsB,CAAC,0BAA0B;AAC9D,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,eAAe;AACvB,IAAI,IAAI,EAAE,eAAe;AACzB,IAAI,WAAW,EAAE,sDAAsD;AACvE,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,4BAA4B;AACpC,IAAI,IAAI,EAAE,oBAAoB;AAC9B,IAAI,WAAW,EAAE,4DAA4D;AAC7E,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACD,MAAM,aAAa,GAAG;AACtB;AACA,EAAE;AACF,IAAI,EAAE,EAAE,WAAW;AACnB,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,WAAW,EAAE,qDAAqD;AACtE,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,eAAe;AACvB,IAAI,IAAI,EAAE,wBAAwB;AAClC,IAAI,WAAW,EAAE,6DAA6D;AAC9E,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,sBAAsB;AAC9B,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,8DAA8D;AAC/E,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACD,MAAM,kBAAkB,GAAG;AAC3B;AACA,EAAE;AACF,IAAI,EAAE,EAAE,kBAAkB;AAC1B,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,WAAW,EAAE,4EAA4E;AAC7F,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,QAAQ;AAClB,IAAI,IAAI,EAAE,KAAK;AACf,IAAI,MAAM,EAAE;AACZ,MAAM;AACN,QAAQ,EAAE,EAAE,YAAY;AACxB,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,WAAW,EAAE,4CAA4C;AACjE,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,IAAI,EAAE,SAAS,CAAC,KAAK;AAC7B,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,gBAAgB;AAC5B,QAAQ,IAAI,EAAE,gBAAgB;AAC9B,QAAQ,WAAW,EAAE,4CAA4C;AACjE,QAAQ,YAAY,EAAE,EAAE;AACxB,QAAQ,IAAI,EAAE,SAAS,CAAC,KAAK;AAC7B,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,kBAAkB;AAC9B,QAAQ,IAAI,EAAE,eAAe;AAC7B,QAAQ,WAAW,EAAE,sCAAsC;AAC3D,QAAQ,YAAY,EAAE,EAAE;AACxB,QAAQ,IAAI,EAAE,SAAS,CAAC,KAAK;AAC7B,QAAQ,IAAI,EAAE;AACd,OAAO;AACP,MAAM;AACN,QAAQ,EAAE,EAAE,kBAAkB;AAC9B,QAAQ,IAAI,EAAE,kBAAkB;AAChC,QAAQ,WAAW,EAAE,+CAA+C;AACpE,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,IAAI,EAAE,SAAS,CAAC,KAAK;AAC7B,QAAQ,IAAI,EAAE;AACd;AACA;AACA,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,kBAAkB;AAC1B,IAAI,IAAI,EAAE,kBAAkB;AAC5B,IAAI,WAAW,EAAE,sDAAsD;AACvE,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,gEAAgE;AACjF,IAAI,QAAQ,EAAE,eAAe,CAAC,MAAM;AACpC,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACD,MAAM,yBAAyB,GAAG;AAClC;AACA,EAAE;AACF,IAAI,EAAE,EAAE,iBAAiB;AACzB,IAAI,IAAI,EAAE,iBAAiB;AAC3B,IAAI,WAAW,EAAE,mEAAmE;AACpF,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,qBAAqB;AAC7B,IAAI,IAAI,EAAE,qBAAqB;AAC/B,IAAI,WAAW,EAAE,uDAAuD;AACxE,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE;AACV,GAAG;AACH;AACA,EAAE;AACF,IAAI,EAAE,EAAE,kCAAkC;AAC1C,IAAI,IAAI,EAAE,kCAAkC;AAC5C,IAAI,WAAW,EAAE,uEAAuE;AACxF,IAAI,QAAQ,EAAE,eAAe,CAAC,SAAS;AACvC,IAAI,IAAI,EAAE,UAAU;AACpB,IAAI,IAAI,EAAE;AACV;AACA,CAAC;AACI,MAAC,gBAAgB,GAAG;AACzB,EAAE,GAAG,mBAAmB;AACxB,EAAE,GAAG,iBAAiB;AACtB,EAAE,GAAG,oBAAoB;AACzB,EAAE,GAAG,uBAAuB;AAC5B,EAAE,GAAG,mBAAmB;AACxB,EAAE,GAAG,gBAAgB;AACrB,EAAE,GAAG,aAAa;AAClB,EAAE,GAAG,kBAAkB;AACvB,EAAE,GAAG;AACL;;;;"}