import 'clsx';
import { B as Button } from './button-CrucCo1G.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { S as Square_pen } from './square-pen-DCE_ltl5.js';
import { C as Check } from './check-WP_4Msti.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { D as Download } from './download-CLn66Ope.js';
import { A as Arrow_right } from './arrow-right-8SE89OuT.js';
import './index3-CqUPEnZw.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import 'date-fns';
import './index-DjwFQdT_.js';
import './Icon-A4vzmk-O.js';

function _page($$payload) {
  SEO($$payload, {
    title: "Hirli Resume Builder - Create Professional Resumes",
    description: "Create a professional resume that stands out with our easy-to-use builder. Customize your design and content to match your career goals.",
    keywords: "resume builder, professional resume, ATS optimized, resume templates",
    url: "https://hirli.com/resume-builder",
    image: "/assets/og-image-resume-builder.jpg"
  });
  $$payload.out += `<!----> <section class="py-32 md:py-40"><div class="container mx-auto px-4"><div class="mx-auto max-w-3xl"><h1 class="mb-8 text-5xl font-light md:text-6xl lg:text-7xl">Build a resume that <span class="text-blue-600">gets noticed</span></h1> <p class="mb-12 text-xl text-gray-600">Our professional resume builder helps you create a standout resume in minutes. Designed to
        pass ATS systems and impress recruiters.</p> <div class="flex flex-col space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">`;
  Button($$payload, {
    class: "bg-black px-8 py-4 text-lg text-white hover:bg-gray-800",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Create Your Resume`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "border-gray-300 px-8 py-4 text-lg",
    children: ($$payload2) => {
      $$payload2.out += `<!---->View Templates`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div></section> <section class="bg-gray-50 py-16"><div class="container mx-auto px-4"><img src="/images/resume-template.png" alt="Resume Template" class="h-auto w-full shadow-lg"/></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-5xl"><div class="grid grid-cols-1 gap-16 md:grid-cols-3"><div><div class="mb-4 text-5xl font-light text-black">3x</div> <p class="text-xl text-gray-600">More interview callbacks with our professionally designed templates</p></div> <div><div class="mb-4 text-5xl font-light text-black">90%</div> <p class="text-xl text-gray-600">ATS pass rate ensuring your resume gets seen by recruiters</p></div> <div><div class="mb-4 text-5xl font-light text-black">15<span class="text-3xl">min</span></div> <p class="text-xl text-gray-600">Average time to create a professional, tailored resume</p></div></div></div></div></section> <section class="border-t border-gray-100 bg-gray-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">Powerful Resume Building Features</h2> <p class="text-xl text-gray-600">Our resume builder is designed to help you create professional, ATS-optimized resumes in
        minutes.</p></div> <div class="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3"><div><div class="mb-6">`;
  Square_pen($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-light">Easy to Use</h3> <p class="mb-6 text-lg text-gray-600">Our intuitive interface makes building a professional resume simple and fast. No design
          skills required. Just fill in your information and our builder does the rest.</p> <ul class="space-y-3"><li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">Drag-and-drop interface</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">Pre-written content suggestions</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">Real-time preview</span></li></ul></div> <div><div class="mb-6">`;
  Circle_check_big($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-light">ATS Optimized</h3> <p class="mb-6 text-lg text-gray-600">Our resumes are designed to pass through Applicant Tracking Systems with flying colors,
          ensuring your application gets seen by recruiters.</p> <ul class="space-y-3"><li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">Keyword optimization</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">ATS-friendly formatting</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">90% pass rate</span></li></ul></div> <div><div class="mb-6">`;
  Download($$payload, { class: "h-8 w-8 text-blue-600" });
  $$payload.out += `<!----></div> <h3 class="mb-4 text-2xl font-light">Multiple Formats</h3> <p class="mb-6 text-lg text-gray-600">Download your resume in PDF, Word, or plain text formats to suit any application
          requirements.</p> <ul class="space-y-3"><li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">PDF for professional look</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">DOCX for editability</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span class="text-gray-600">TXT for ATS submission</span></li></ul></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">Professional Resume Templates</h2> <p class="text-xl text-gray-600">Choose from our collection of professionally designed templates that are both visually
        appealing and ATS-friendly.</p></div> <div class="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3"><div><img src="/images/resume-template-1.jpg" alt="Professional Template" class="mb-4 h-auto w-full"/> <h3 class="mb-2 text-2xl font-light">Professional</h3> <p class="mb-4 text-gray-600">Clean and modern design for corporate roles</p> `;
  Button($$payload, {
    class: "w-full bg-black text-white hover:bg-gray-800",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Use This Template`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div><img src="/images/resume-template-2.jpg" alt="Creative Template" class="mb-4 h-auto w-full"/> <h3 class="mb-2 text-2xl font-light">Creative</h3> <p class="mb-4 text-gray-600">Bold design for creative industries</p> `;
  Button($$payload, {
    class: "w-full bg-black text-white hover:bg-gray-800",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Use This Template`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div><img src="/images/resume-template-3.jpg" alt="Executive Template" class="mb-4 h-auto w-full"/> <h3 class="mb-2 text-2xl font-light">Executive</h3> <p class="mb-4 text-gray-600">Sophisticated design for senior positions</p> `;
  Button($$payload, {
    class: "w-full bg-black text-white hover:bg-gray-800",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Use This Template`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div> <div class="mt-12 text-center">`;
  Button($$payload, {
    variant: "outline",
    class: "border-gray-300 px-8 py-4 text-lg",
    children: ($$payload2) => {
      $$payload2.out += `<!---->View All Templates `;
      Arrow_right($$payload2, { class: "ml-2 h-4 w-4" });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></section> <section class="border-t border-gray-100 bg-gray-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">How It Works</h2> <p class="text-xl text-gray-600">Create a professional resume in just three simple steps.</p></div> <div class="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3"><div><div class="mb-6 inline-flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-xl font-medium text-blue-600">1</div> <h3 class="mb-4 text-2xl font-light">Choose a Template</h3> <p class="mb-6 text-gray-600">Select from our collection of professional, ATS-friendly resume templates.</p> <img src="/images/step-1-template.jpg" alt="Choose Template" class="h-auto w-full"/></div> <div><div class="mb-6 inline-flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-xl font-medium text-blue-600">2</div> <h3 class="mb-4 text-2xl font-light">Add Your Content</h3> <p class="mb-6 text-gray-600">Fill in your details with our guided form, or import from LinkedIn to save time.</p> <img src="/images/step-2-content.jpg" alt="Add Content" class="h-auto w-full"/></div> <div><div class="mb-6 inline-flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 text-xl font-medium text-blue-600">3</div> <h3 class="mb-4 text-2xl font-light">Download &amp; Apply</h3> <p class="mb-6 text-gray-600">Export your polished resume in your preferred format and start applying with confidence.</p> <img src="/images/step-3-download.jpg" alt="Download Resume" class="h-auto w-full"/></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-4xl text-center"><div class="mb-8"><img src="https://randomuser.me/api/portraits/women/67.jpg" alt="User" class="mx-auto h-20 w-20 rounded-full"/></div> <blockquote class="mb-8 text-3xl font-light italic">"The templates helped me create a resume that stands out. I landed a job within 3 weeks of
        using this builder!"</blockquote> <div><p class="text-xl font-medium">Alex R.</p> <p class="text-gray-600">Marketing Specialist at Facebook</p></div></div></div></section> <section class="border-t border-gray-100 bg-gray-50 py-24"><div class="container mx-auto px-4"><div class="mx-auto mb-20 max-w-3xl text-center"><h2 class="mb-6 text-4xl font-light">Simple, Transparent Pricing</h2> <p class="text-xl text-gray-600">Choose the plan that fits your needs. All plans include our core resume builder features.</p></div> <div class="mx-auto grid max-w-5xl grid-cols-1 gap-8 md:grid-cols-2"><div class="bg-white p-12"><h3 class="mb-2 text-2xl font-light">Basic</h3> <p class="mb-6 text-5xl font-light">$0<span class="text-lg font-normal text-gray-500">/month</span></p> <p class="mb-6 border-b border-gray-100 pb-6 text-gray-600">Perfect for creating a simple, professional resume.</p> <ul class="mb-8 space-y-4"><li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span>1 resume template</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span>PDF downloads</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span>Basic ATS optimization</span></li></ul> `;
  Button($$payload, {
    variant: "outline",
    class: "w-full border-gray-300 p-4 text-lg font-medium",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Get Started`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <div class="bg-white p-12"><h3 class="mb-2 text-2xl font-light">Pro</h3> <p class="mb-6 text-5xl font-light">$12<span class="text-lg font-normal text-gray-500">/month</span></p> <p class="mb-6 border-b border-gray-100 pb-6 text-gray-600">For job seekers who want to stand out from the crowd.</p> <ul class="mb-8 space-y-4"><li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span>All templates</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span>Multiple formats (PDF, DOCX, TXT)</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span>Advanced ATS optimization</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span>Content suggestions</span></li> <li class="flex items-start">`;
  Check($$payload, {
    class: "mr-3 mt-1 h-5 w-5 flex-shrink-0 text-blue-600"
  });
  $$payload.out += `<!----> <span>Unlimited resume versions</span></li></ul> `;
  Button($$payload, {
    class: "w-full bg-black p-4 text-lg font-medium text-white hover:bg-gray-800",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Start 7-Day Free Trial`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div></div></div></section> <section class="py-24"><div class="container mx-auto px-4"><div class="mx-auto max-w-3xl"><h2 class="mb-16 text-center text-4xl font-light">Frequently Asked Questions</h2> <div class="space-y-12"><div><h3 class="mb-4 text-2xl font-light">How does the resume builder work?</h3> <p class="text-lg text-gray-600">Our resume builder guides you through the process of creating a professional resume.
            Simply choose a template, fill in your information, and download your completed resume
            in your preferred format.</p></div> <div><h3 class="mb-4 text-2xl font-light">Are the templates ATS-friendly?</h3> <p class="text-lg text-gray-600">Yes, all of our templates are designed to be ATS-friendly. They use clean, simple
            formatting that can be easily read by Applicant Tracking Systems, ensuring your resume
            gets past the initial screening.</p></div> <div><h3 class="mb-4 text-2xl font-light">Can I create multiple resumes?</h3> <p class="text-lg text-gray-600">With our Pro plan, you can create unlimited resume versions. This allows you to tailor
            your resume for different job applications, maximizing your chances of success.</p></div> <div><h3 class="mb-4 text-2xl font-light">Can I cancel my subscription anytime?</h3> <p class="text-lg text-gray-600">Yes, you can cancel your subscription at any time. If you cancel, you'll continue to
            have access until the end of your billing period.</p></div></div></div></div></section> <section class="bg-black py-24 text-white"><div class="container mx-auto px-4 text-center"><h2 class="mb-8 text-4xl font-light">Ready to Build Your Professional Resume?</h2> <p class="mx-auto mb-12 max-w-3xl text-xl text-white/80">Join thousands of job seekers who have successfully landed interviews with our resume builder.</p> <div class="flex flex-col justify-center gap-4 sm:flex-row">`;
  Button($$payload, {
    class: "bg-white px-10 py-5 text-lg font-medium text-black hover:bg-gray-100",
    children: ($$payload2) => {
      $$payload2.out += `<!---->Create Your Resume Now`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  Button($$payload, {
    variant: "outline",
    class: "border-white px-10 py-5 text-lg font-medium text-white hover:bg-white/10",
    children: ($$payload2) => {
      $$payload2.out += `<!---->View Templates`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> <p class="mt-8 text-white/60">No credit card required. Start for free.</p></div></section>`;
}

export { _page as default };
//# sourceMappingURL=_page.svelte-KeWfxnEC.js.map
