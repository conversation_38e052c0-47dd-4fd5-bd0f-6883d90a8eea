{"version": 3, "file": "_page.svelte-4Uo51tPk.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/documents/_id_/_page.svelte.js"], "sourcesContent": ["import { O as copy_payload, P as assign_payload, N as bind_props, y as pop, w as push, V as escape_html, S as attr_class, W as stringify } from \"../../../../../chunks/index3.js\";\nimport { g as goto } from \"../../../../../chunks/client.js\";\nimport { a as toast } from \"../../../../../chunks/Toaster.svelte_svelte_type_style_lang.js\";\nimport \"clsx\";\nimport { S as SEO } from \"../../../../../chunks/SEO.js\";\nimport { B as Button } from \"../../../../../chunks/button.js\";\nimport { I as Input } from \"../../../../../chunks/input.js\";\nimport { C as Card } from \"../../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../../chunks/card-content.js\";\nimport { C as Card_description } from \"../../../../../chunks/card-description.js\";\nimport { C as Card_footer } from \"../../../../../chunks/card-footer.js\";\nimport { C as Card_header } from \"../../../../../chunks/card-header.js\";\nimport { C as Card_title } from \"../../../../../chunks/card-title.js\";\nimport { R as Root, a as Alert_dialog_content, b as Alert_dialog_header, c as Alert_dialog_title, d as Alert_dialog_description, e as Alert_dialog_footer, f as Alert_dialog_cancel, g as Alert_dialog_action } from \"../../../../../chunks/index11.js\";\nimport { U as UniversalDocumentViewer } from \"../../../../../chunks/UniversalDocumentViewer.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  let document = data.document || null;\n  let loading = !document;\n  let saving = false;\n  let documentName = document ? document.label : \"\";\n  let showDeleteDialog = false;\n  async function saveDocument() {\n    if (!document) return;\n    saving = true;\n    try {\n      const response = await fetch(`/api/documents/${document.id}`, {\n        method: \"PATCH\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ label: documentName })\n      });\n      if (response.ok) {\n        const updatedDocument = await response.json();\n        toast.success(\"Document updated\", {\n          description: \"Your document has been updated successfully.\"\n        });\n        document.label = updatedDocument.label || documentName;\n        document.updatedAt = updatedDocument.updatedAt || (/* @__PURE__ */ new Date()).toISOString();\n        if (updatedDocument.source) {\n          document.source = updatedDocument.source;\n        }\n        documentName = document.label;\n      } else {\n        toast.error(\"Error updating document\", {\n          description: \"Could not update the document. Please try again.\"\n        });\n      }\n    } catch (error) {\n      console.error(\"Error updating document:\", error);\n      toast.error(\"Error updating document\", {\n        description: \"Could not update the document. Please try again.\"\n      });\n    } finally {\n      saving = false;\n    }\n  }\n  async function parseResume() {\n    if (!document) return;\n    try {\n      toast.loading(\"Sending resume for parsing...\", { id: \"parse-resume\" });\n      const response = await fetch(`/api/documents/${document.id}/parse`, { method: \"POST\" });\n      if (response.ok) {\n        toast.success(\"Resume parsing started\", {\n          id: \"parse-resume\",\n          description: \"Your resume is being analyzed. This may take a moment.\"\n        });\n        document = { ...document, isParsed: false, parsedAt: null };\n      } else {\n        let errorMessage = \"Failed to start resume parsing\";\n        try {\n          const errorData = await response.json();\n          console.error(\"Parse API error details:\", errorData);\n          if (errorData.error) {\n            errorMessage = errorData.error;\n          }\n        } catch (e) {\n          console.error(\"Could not parse error response:\", e);\n        }\n        console.error(errorMessage);\n        toast.error(\"Resume parsing failed\", { id: \"parse-resume\", description: errorMessage });\n      }\n    } catch (error) {\n      console.error(\"Error sending resume to parsing queue:\", error);\n      toast.error(\"Resume parsing failed\", {\n        id: \"parse-resume\",\n        description: \"An unexpected error occurred. Please try again.\"\n      });\n    }\n  }\n  function confirmDelete() {\n    if (!document) return;\n    fetch(`/api/documents/${document.id}`, { method: \"DELETE\" }).then((res) => {\n      if (res.ok) {\n        toast.success(\"Document deleted\", {\n          description: `\"${document?.label}\" has been removed from your documents.`\n        });\n        goto();\n      } else {\n        toast.error(\"Delete failed\", {\n          description: \"There was a problem deleting your document. Please try again.\"\n        });\n      }\n      showDeleteDialog = false;\n    }).catch((err) => {\n      console.error(\"Delete error:\", err);\n      toast.error(\"Delete failed\", {\n        description: \"There was a problem deleting your document. Please try again.\"\n      });\n      showDeleteDialog = false;\n    });\n  }\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    SEO($$payload2, {\n      title: \"Edit Document | Auto Apply\",\n      description: \"Edit your document details\"\n    });\n    $$payload2.out += `<!----> <div class=\"container mx-auto p-6\"><div class=\"mb-6 flex items-center justify-between\"><div><h1 class=\"text-3xl font-bold\">Edit Document</h1> <p class=\"text-gray-500\">Update your document details</p></div> <div class=\"flex gap-2\">`;\n    Button($$payload2, {\n      variant: \"outline\",\n      onclick: () => goto(),\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Cancel`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----> `;\n    Button($$payload2, {\n      onclick: () => showDeleteDialog = true,\n      variant: \"destructive\",\n      children: ($$payload3) => {\n        $$payload3.out += `<!---->Delete`;\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!----></div></div> `;\n    if (loading) {\n      $$payload2.out += \"<!--[-->\";\n      $$payload2.out += `<div class=\"flex h-64 items-center justify-center\"><p class=\"text-lg text-gray-500\">Loading document...</p></div>`;\n    } else if (document) {\n      $$payload2.out += \"<!--[1-->\";\n      $$payload2.out += `<div class=\"grid grid-cols-1 gap-6 lg:grid-cols-2\"><div class=\"order-2 lg:order-1\"><div style=\"height: 600px;\">`;\n      UniversalDocumentViewer($$payload2, { document });\n      $$payload2.out += `<!----></div> `;\n      if (document.type === \"resume\" && document.resume) {\n        $$payload2.out += \"<!--[-->\";\n        $$payload2.out += `<div class=\"mt-4\">`;\n        Button($$payload2, {\n          variant: \"outline\",\n          onclick: () => goto(`/dashboard/documents/${document.id}/ats`),\n          children: ($$payload3) => {\n            $$payload3.out += `<span class=\"mr-2\">ATS Optimization</span> <span class=\"inline-flex h-5 items-center justify-center rounded-full bg-blue-100 px-2 text-xs font-medium text-blue-800\">AI</span>`;\n          },\n          $$slots: { default: true }\n        });\n        $$payload2.out += `<!----></div>`;\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n      }\n      $$payload2.out += `<!--]--></div> <div class=\"order-1 lg:order-2\">`;\n      Card($$payload2, {\n        children: ($$payload3) => {\n          Card_header($$payload3, {\n            children: ($$payload4) => {\n              Card_title($$payload4, {\n                children: ($$payload5) => {\n                  $$payload5.out += `<!---->Document Details`;\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!----> `;\n              Card_description($$payload4, {\n                children: ($$payload5) => {\n                  $$payload5.out += `<!---->Update the information for your document`;\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!---->`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> `;\n          Card_content($$payload3, {\n            children: ($$payload4) => {\n              $$payload4.out += `<div class=\"space-y-4\"><div><label for=\"documentName\" class=\"mb-2 block text-sm font-medium\">Document Name</label> `;\n              Input($$payload4, {\n                id: \"documentName\",\n                placeholder: \"Enter document name\",\n                get value() {\n                  return documentName;\n                },\n                set value($$value) {\n                  documentName = $$value;\n                  $$settled = false;\n                }\n              });\n              $$payload4.out += `<!----></div> <div><span class=\"mb-2 block text-sm font-medium\">Document Type</span> <p class=\"text-sm text-gray-500\">${escape_html(document.type === \"resume\" ? \"Resume\" : document.type === \"cover_letter\" ? \"Cover Letter\" : document.type)}</p></div> <div><span class=\"mb-2 block text-sm font-medium\">Created</span> <div class=\"flex flex-col\"><span class=\"text-sm text-gray-500\">${escape_html(new Date(document.createdAt).toLocaleDateString())}</span> <span class=\"text-xs text-gray-500\">${escape_html(new Date(document.createdAt).toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" }))}</span></div></div> `;\n              if (document.updatedAt) {\n                $$payload4.out += \"<!--[-->\";\n                $$payload4.out += `<div><span class=\"mb-2 block text-sm font-medium\">Last Edited</span> <div class=\"flex flex-col\"><span class=\"text-sm text-gray-500\">${escape_html(new Date(document.updatedAt).toLocaleDateString())}</span> <span class=\"text-xs text-gray-500\">${escape_html(new Date(document.updatedAt).toLocaleTimeString([], { hour: \"2-digit\", minute: \"2-digit\" }))}</span></div></div>`;\n              } else {\n                $$payload4.out += \"<!--[!-->\";\n              }\n              $$payload4.out += `<!--]--> <div><span class=\"mb-2 block text-sm font-medium\">File Name</span> <p class=\"text-sm text-gray-500\">${escape_html(document.fileName || \"unknown.pdf\")}</p></div> `;\n              if (document.source) {\n                $$payload4.out += \"<!--[-->\";\n                $$payload4.out += `<div><span class=\"mb-2 block text-sm font-medium\">Source</span> <span${attr_class(`inline-block rounded px-2 py-0.5 text-xs ${stringify(document.source === \"generated\" ? \"bg-purple-100 text-purple-800\" : document.source === \"created\" ? \"bg-blue-100 text-blue-800\" : \"bg-green-100 text-green-800\")}`)}>${escape_html(document.source === \"generated\" ? \"Generated\" : document.source === \"created\" ? \"Created\" : \"Uploaded\")}</span></div>`;\n              } else {\n                $$payload4.out += \"<!--[!-->\";\n              }\n              $$payload4.out += `<!--]--> `;\n              if (document.isDefault !== void 0) {\n                $$payload4.out += \"<!--[-->\";\n                $$payload4.out += `<div><span class=\"mb-2 block text-sm font-medium\">Status</span> `;\n                if (document.isDefault) {\n                  $$payload4.out += \"<!--[-->\";\n                  $$payload4.out += `<span class=\"inline-block rounded bg-blue-100 px-2 py-0.5 text-xs text-blue-800\">Default</span>`;\n                } else {\n                  $$payload4.out += \"<!--[!-->\";\n                  $$payload4.out += `<span class=\"inline-block rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-800\">Regular</span>`;\n                }\n                $$payload4.out += `<!--]--></div>`;\n              } else {\n                $$payload4.out += \"<!--[!-->\";\n              }\n              $$payload4.out += `<!--]--> `;\n              if (document.type === \"resume\") {\n                $$payload4.out += \"<!--[-->\";\n                $$payload4.out += `<div><span class=\"mb-2 block text-sm font-medium\">Parse Status</span> <div class=\"flex items-center gap-2\">`;\n                if (document.isParsed) {\n                  $$payload4.out += \"<!--[-->\";\n                  $$payload4.out += `<span class=\"inline-block rounded bg-green-100 px-2 py-0.5 text-xs text-green-800\">Parsed</span> `;\n                  if (document.parsedAt) {\n                    $$payload4.out += \"<!--[-->\";\n                    $$payload4.out += `<span class=\"text-xs text-gray-500\">on ${escape_html(new Date(document.parsedAt).toLocaleDateString())}</span>`;\n                  } else {\n                    $$payload4.out += \"<!--[!-->\";\n                  }\n                  $$payload4.out += `<!--]-->`;\n                } else {\n                  $$payload4.out += \"<!--[!-->\";\n                  $$payload4.out += `<span class=\"inline-block rounded bg-yellow-100 px-2 py-0.5 text-xs text-yellow-800\">Not Parsed</span> `;\n                  Button($$payload4, {\n                    size: \"sm\",\n                    variant: \"outline\",\n                    class: \"ml-2\",\n                    onclick: parseResume,\n                    children: ($$payload5) => {\n                      $$payload5.out += `<!---->Parse Now`;\n                    },\n                    $$slots: { default: true }\n                  });\n                  $$payload4.out += `<!---->`;\n                }\n                $$payload4.out += `<!--]--></div></div>`;\n              } else {\n                $$payload4.out += \"<!--[!-->\";\n              }\n              $$payload4.out += `<!--]--></div>`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!----> `;\n          Card_footer($$payload3, {\n            class: \"flex justify-between\",\n            children: ($$payload4) => {\n              Button($$payload4, {\n                variant: \"outline\",\n                onclick: () => goto(),\n                children: ($$payload5) => {\n                  $$payload5.out += `<!---->Cancel`;\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!----> `;\n              Button($$payload4, {\n                onclick: saveDocument,\n                disabled: saving || documentName === document.label,\n                children: ($$payload5) => {\n                  $$payload5.out += `<!---->${escape_html(saving ? \"Saving...\" : \"Save Changes\")}`;\n                },\n                $$slots: { default: true }\n              });\n              $$payload4.out += `<!---->`;\n            },\n            $$slots: { default: true }\n          });\n          $$payload3.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----></div></div>`;\n    } else {\n      $$payload2.out += \"<!--[!-->\";\n      $$payload2.out += `<div class=\"flex h-64 items-center justify-center\"><p class=\"text-lg text-gray-500\">Document not found</p></div>`;\n    }\n    $$payload2.out += `<!--]--></div> `;\n    Root($$payload2, {\n      get open() {\n        return showDeleteDialog;\n      },\n      set open($$value) {\n        showDeleteDialog = $$value;\n        $$settled = false;\n      },\n      children: ($$payload3) => {\n        Alert_dialog_content($$payload3, {\n          children: ($$payload4) => {\n            Alert_dialog_header($$payload4, {\n              children: ($$payload5) => {\n                Alert_dialog_title($$payload5, {\n                  children: ($$payload6) => {\n                    $$payload6.out += `<!---->Are you sure?`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> `;\n                Alert_dialog_description($$payload5, {\n                  children: ($$payload6) => {\n                    $$payload6.out += `<!---->This will permanently delete \"${escape_html(document?.label)}\" from your documents. This action cannot be\n        undone.`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!----> `;\n            Alert_dialog_footer($$payload4, {\n              children: ($$payload5) => {\n                Alert_dialog_cancel($$payload5, {\n                  onclick: () => showDeleteDialog = false,\n                  children: ($$payload6) => {\n                    $$payload6.out += `<!---->Cancel`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!----> `;\n                Alert_dialog_action($$payload5, {\n                  onclick: confirmDelete,\n                  children: ($$payload6) => {\n                    $$payload6.out += `<!---->Delete`;\n                  },\n                  $$slots: { default: true }\n                });\n                $$payload5.out += `<!---->`;\n              },\n              $$slots: { default: true }\n            });\n            $$payload4.out += `<!---->`;\n          },\n          $$slots: { default: true }\n        });\n      },\n      $$slots: { default: true }\n    });\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI;AACtC,EAAE,IAAI,OAAO,GAAG,CAAC,QAAQ;AACzB,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,IAAI,YAAY,GAAG,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,EAAE;AACnD,EAAE,IAAI,gBAAgB,GAAG,KAAK;AAC9B,EAAE,eAAe,YAAY,GAAG;AAChC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,IAAI;AACR,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;AACpE,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;AACvD,QAAQ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE;AACpD,OAAO,CAAC;AACR,MAAM,IAAI,QAAQ,CAAC,EAAE,EAAE;AACvB,QAAQ,MAAM,eAAe,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACrD,QAAQ,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE;AAC1C,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV,QAAQ,QAAQ,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,IAAI,YAAY;AAC9D,QAAQ,QAAQ,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,IAAI,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE;AACpG,QAAQ,IAAI,eAAe,CAAC,MAAM,EAAE;AACpC,UAAU,QAAQ,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM;AAClD;AACA,QAAQ,YAAY,GAAG,QAAQ,CAAC,KAAK;AACrC,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,KAAK,CAAC,yBAAyB,EAAE;AAC/C,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC;AACtD,MAAM,KAAK,CAAC,KAAK,CAAC,yBAAyB,EAAE;AAC7C,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,KAAK,SAAS;AACd,MAAM,MAAM,GAAG,KAAK;AACpB;AACA;AACA,EAAE,eAAe,WAAW,GAAG;AAC/B,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,IAAI,IAAI;AACR,MAAM,KAAK,CAAC,OAAO,CAAC,+BAA+B,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,CAAC;AAC5E,MAAM,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;AAC7F,MAAM,IAAI,QAAQ,CAAC,EAAE,EAAE;AACvB,QAAQ,KAAK,CAAC,OAAO,CAAC,wBAAwB,EAAE;AAChD,UAAU,EAAE,EAAE,cAAc;AAC5B,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV,QAAQ,QAAQ,GAAG,EAAE,GAAG,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE;AACnE,OAAO,MAAM;AACb,QAAQ,IAAI,YAAY,GAAG,gCAAgC;AAC3D,QAAQ,IAAI;AACZ,UAAU,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE;AACjD,UAAU,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,SAAS,CAAC;AAC9D,UAAU,IAAI,SAAS,CAAC,KAAK,EAAE;AAC/B,YAAY,YAAY,GAAG,SAAS,CAAC,KAAK;AAC1C;AACA,SAAS,CAAC,OAAO,CAAC,EAAE;AACpB,UAAU,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,CAAC,CAAC;AAC7D;AACA,QAAQ,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC;AACnC,QAAQ,KAAK,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,EAAE,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;AAC/F;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC;AACpE,MAAM,KAAK,CAAC,KAAK,CAAC,uBAAuB,EAAE;AAC3C,QAAQ,EAAE,EAAE,cAAc;AAC1B,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR;AACA;AACA,EAAE,SAAS,aAAa,GAAG;AAC3B,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,IAAI,KAAK,CAAC,CAAC,eAAe,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK;AAC/E,MAAM,IAAI,GAAG,CAAC,EAAE,EAAE;AAClB,QAAQ,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE;AAC1C,UAAU,WAAW,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,uCAAuC;AAClF,SAAS,CAAC;AACV,QAAQ,IAAI,EAAE;AACd,OAAO,MAAM;AACb,QAAQ,KAAK,CAAC,KAAK,CAAC,eAAe,EAAE;AACrC,UAAU,WAAW,EAAE;AACvB,SAAS,CAAC;AACV;AACA,MAAM,gBAAgB,GAAG,KAAK;AAC9B,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK;AACtB,MAAM,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,CAAC;AACzC,MAAM,KAAK,CAAC,KAAK,CAAC,eAAe,EAAE;AACnC,QAAQ,WAAW,EAAE;AACrB,OAAO,CAAC;AACR,MAAM,gBAAgB,GAAG,KAAK;AAC9B,KAAK,CAAC;AACN;AACA,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,GAAG,CAAC,UAAU,EAAE;AACpB,MAAM,KAAK,EAAE,4BAA4B;AACzC,MAAM,WAAW,EAAE;AACnB,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,8OAA8O,CAAC;AACtQ,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,MAAM,IAAI,EAAE;AAC3B,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAChC,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,OAAO,EAAE,MAAM,gBAAgB,GAAG,IAAI;AAC5C,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC5C,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,UAAU,CAAC,GAAG,IAAI,UAAU;AAClC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,iHAAiH,CAAC;AAC3I,KAAK,MAAM,IAAI,QAAQ,EAAE;AACzB,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,+GAA+G,CAAC;AACzI,MAAM,uBAAuB,CAAC,UAAU,EAAE,EAAE,QAAQ,EAAE,CAAC;AACvD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACxC,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;AACzD,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;AAC9C,QAAQ,MAAM,CAAC,UAAU,EAAE;AAC3B,UAAU,OAAO,EAAE,SAAS;AAC5B,UAAU,OAAO,EAAE,MAAM,IAAI,CAAC,CAAC,qBAAqB,EAAE,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACxE,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,8KAA8K,CAAC;AAC9M,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACzC,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,+CAA+C,CAAC;AACzE,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,WAAW,CAAC,UAAU,EAAE;AAClC,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,UAAU,EAAE;AACrC,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AAC7D,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1C,cAAc,gBAAgB,CAAC,UAAU,EAAE;AAC3C,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,+CAA+C,CAAC;AACrF,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,YAAY,CAAC,UAAU,EAAE;AACnC,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,mHAAmH,CAAC;AACrJ,cAAc,KAAK,CAAC,UAAU,EAAE;AAChC,gBAAgB,EAAE,EAAE,cAAc;AAClC,gBAAgB,WAAW,EAAE,qBAAqB;AAClD,gBAAgB,IAAI,KAAK,GAAG;AAC5B,kBAAkB,OAAO,YAAY;AACrC,iBAAiB;AACjB,gBAAgB,IAAI,KAAK,CAAC,OAAO,EAAE;AACnC,kBAAkB,YAAY,GAAG,OAAO;AACxC,kBAAkB,SAAS,GAAG,KAAK;AACnC;AACA,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,sHAAsH,EAAE,WAAW,CAAC,QAAQ,CAAC,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,IAAI,KAAK,cAAc,GAAG,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,2IAA2I,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,oBAAoB,CAAC;AACxoB,cAAc,IAAI,QAAQ,CAAC,SAAS,EAAE;AACtC,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,oIAAoI,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,kBAAkB,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,mBAAmB,CAAC;AACnZ,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,6GAA6G,EAAE,WAAW,CAAC,QAAQ,CAAC,QAAQ,IAAI,aAAa,CAAC,CAAC,WAAW,CAAC;AAC5M,cAAc,IAAI,QAAQ,CAAC,MAAM,EAAE;AACnC,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,qEAAqE,EAAE,UAAU,CAAC,CAAC,yCAAyC,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK,WAAW,GAAG,+BAA+B,GAAG,QAAQ,CAAC,MAAM,KAAK,SAAS,GAAG,2BAA2B,GAAG,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM,KAAK,WAAW,GAAG,WAAW,GAAG,QAAQ,CAAC,MAAM,KAAK,SAAS,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC,aAAa,CAAC;AACpd,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC3C,cAAc,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM,EAAE;AACjD,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,gEAAgE,CAAC;AACpG,gBAAgB,IAAI,QAAQ,CAAC,SAAS,EAAE;AACxC,kBAAkB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC9C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,+FAA+F,CAAC;AACrI,iBAAiB,MAAM;AACvB,kBAAkB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC/C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,+FAA+F,CAAC;AACrI;AACA,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAClD,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC3C,cAAc,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC9C,gBAAgB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC5C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,2GAA2G,CAAC;AAC/I,gBAAgB,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACvC,kBAAkB,UAAU,CAAC,GAAG,IAAI,UAAU;AAC9C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,iGAAiG,CAAC;AACvI,kBAAkB,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACzC,oBAAoB,UAAU,CAAC,GAAG,IAAI,UAAU;AAChD,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,uCAAuC,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,OAAO,CAAC;AACtJ,mBAAmB,MAAM;AACzB,oBAAoB,UAAU,CAAC,GAAG,IAAI,WAAW;AACjD;AACA,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9C,iBAAiB,MAAM;AACvB,kBAAkB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC/C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,uGAAuG,CAAC;AAC7I,kBAAkB,MAAM,CAAC,UAAU,EAAE;AACrC,oBAAoB,IAAI,EAAE,IAAI;AAC9B,oBAAoB,OAAO,EAAE,SAAS;AACtC,oBAAoB,KAAK,EAAE,MAAM;AACjC,oBAAoB,OAAO,EAAE,WAAW;AACxC,oBAAoB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9C,sBAAsB,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC1D,qBAAqB;AACrB,oBAAoB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5C,mBAAmB,CAAC;AACpB,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7C;AACA,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AACxD,eAAe,MAAM;AACrB,gBAAgB,UAAU,CAAC,GAAG,IAAI,WAAW;AAC7C;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAChD,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,UAAU,WAAW,CAAC,UAAU,EAAE;AAClC,YAAY,KAAK,EAAE,sBAAsB;AACzC,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,MAAM,CAAC,UAAU,EAAE;AACjC,gBAAgB,OAAO,EAAE,SAAS;AAClC,gBAAgB,OAAO,EAAE,MAAM,IAAI,EAAE;AACrC,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACnD,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1C,cAAc,MAAM,CAAC,UAAU,EAAE;AACjC,gBAAgB,OAAO,EAAE,YAAY;AACrC,gBAAgB,QAAQ,EAAE,MAAM,IAAI,YAAY,KAAK,QAAQ,CAAC,KAAK;AACnE,gBAAgB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC1C,kBAAkB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,MAAM,GAAG,WAAW,GAAG,cAAc,CAAC,CAAC,CAAC;AAClG,iBAAiB;AACjB,gBAAgB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACxC,eAAe,CAAC;AAChB,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AAC7C,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,GAAG,IAAI,WAAW;AACnC,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,gHAAgH,CAAC;AAC1I;AACA,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AACvC,IAAI,IAAI,CAAC,UAAU,EAAE;AACrB,MAAM,IAAI,IAAI,GAAG;AACjB,QAAQ,OAAO,gBAAgB;AAC/B,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;AACxB,QAAQ,gBAAgB,GAAG,OAAO;AAClC,QAAQ,SAAS,GAAG,KAAK;AACzB,OAAO;AACP,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,oBAAoB,CAAC,UAAU,EAAE;AACzC,UAAU,QAAQ,EAAE,CAAC,UAAU,KAAK;AACpC,YAAY,mBAAmB,CAAC,UAAU,EAAE;AAC5C,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,kBAAkB,CAAC,UAAU,EAAE;AAC/C,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,oBAAoB,CAAC;AAC5D,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,gBAAgB,wBAAwB,CAAC,UAAU,EAAE;AACrD,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,qCAAqC,EAAE,WAAW,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC3G,eAAe,CAAC;AAChB,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACxC,YAAY,mBAAmB,CAAC,UAAU,EAAE;AAC5C,cAAc,QAAQ,EAAE,CAAC,UAAU,KAAK;AACxC,gBAAgB,mBAAmB,CAAC,UAAU,EAAE;AAChD,kBAAkB,OAAO,EAAE,MAAM,gBAAgB,GAAG,KAAK;AACzD,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACrD,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC5C,gBAAgB,mBAAmB,CAAC,UAAU,EAAE;AAChD,kBAAkB,OAAO,EAAE,aAAa;AACxC,kBAAkB,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC5C,oBAAoB,UAAU,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;AACrD,mBAAmB;AACnB,kBAAkB,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC1C,iBAAiB,CAAC;AAClB,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC3C,eAAe;AACf,cAAc,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACtC,aAAa,CAAC;AACd,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACvC,WAAW;AACX,UAAU,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAClC,SAAS,CAAC;AACV,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}