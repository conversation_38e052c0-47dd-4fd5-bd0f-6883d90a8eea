{"version": 3, "file": "box-auto-reset.svelte-BDripiF0.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/box-auto-reset.svelte.js"], "sourcesContent": ["import \"clsx\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { n as noop } from \"./noop.js\";\nfunction boxAutoReset(defaultValue, afterMs = 1e4, onChange = noop) {\n  let timeout = null;\n  let value = defaultValue;\n  function resetAfter() {\n    return window.setTimeout(\n      () => {\n        value = defaultValue;\n        onChange(defaultValue);\n      },\n      afterMs\n    );\n  }\n  return box.with(() => value, (v) => {\n    value = v;\n    onChange(v);\n    if (timeout) clearTimeout(timeout);\n    timeout = resetAfter();\n  });\n}\nexport {\n  boxAutoReset as b\n};\n"], "names": [], "mappings": ";;;;;AAIA,SAAS,YAAY,CAAC,YAAY,EAAE,OAAO,GAAG,GAAG,EAAE,QAAQ,GAAG,IAAI,EAAE;AACpE,EAAE,IAAI,OAAO,GAAG,IAAI;AACpB,EAAE,IAAI,KAAK,GAAG,YAAY;AAC1B,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,OAAO,MAAM,CAAC,UAAU;AAC5B,MAAM,MAAM;AACZ,QAAQ,KAAK,GAAG,YAAY;AAC5B,QAAQ,QAAQ,CAAC,YAAY,CAAC;AAC9B,OAAO;AACP,MAAM;AACN,KAAK;AACL;AACA,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,KAAK;AACtC,IAAI,KAAK,GAAG,CAAC;AACb,IAAI,QAAQ,CAAC,CAAC,CAAC;AACf,IAAI,IAAI,OAAO,EAAE,YAAY,CAAC,OAAO,CAAC;AACtC,IAAI,OAAO,GAAG,UAAU,EAAE;AAC1B,GAAG,CAAC;AACJ;;;;"}