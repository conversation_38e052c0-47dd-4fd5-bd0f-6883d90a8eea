{"version": 3, "file": "mode-CboidaPj.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/mode.js"], "sourcesContent": ["import \"clsx\";\nimport { c as createSubscriber, b as box, M as MediaQuery } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nconst defaultWindow = void 0;\nfunction getActiveElement(document2) {\n  let activeElement = document2.activeElement;\n  while (activeElement?.shadowRoot) {\n    const node = activeElement.shadowRoot.activeElement;\n    if (node === activeElement)\n      break;\n    else\n      activeElement = node;\n  }\n  return activeElement;\n}\nclass ActiveElement {\n  #document;\n  #subscribe;\n  constructor(options = {}) {\n    const {\n      window: window2 = defaultWindow,\n      document: document2 = window2?.document\n    } = options;\n    if (window2 === void 0) return;\n    this.#document = document2;\n    this.#subscribe = createSubscriber();\n  }\n  get current() {\n    this.#subscribe?.();\n    if (!this.#document) return null;\n    return getActiveElement(this.#document);\n  }\n}\nnew ActiveElement();\nfunction getStorage(storageType, window2) {\n  switch (storageType) {\n    case \"local\":\n      return window2.localStorage;\n    case \"session\":\n      return window2.sessionStorage;\n  }\n}\nclass PersistedState {\n  #current;\n  #key;\n  #serializer;\n  #storage;\n  #subscribe;\n  #version = 0;\n  constructor(key, initialValue, options = {}) {\n    const {\n      storage: storageType = \"local\",\n      serializer = {\n        serialize: JSON.stringify,\n        deserialize: JSON.parse\n      },\n      syncTabs = true,\n      window: window2 = defaultWindow\n    } = options;\n    this.#current = initialValue;\n    this.#key = key;\n    this.#serializer = serializer;\n    if (window2 === void 0) return;\n    const storage = getStorage(storageType, window2);\n    this.#storage = storage;\n    const existingValue = storage.getItem(key);\n    if (existingValue !== null) {\n      this.#current = this.#deserialize(existingValue);\n    } else {\n      this.#serialize(initialValue);\n    }\n    if (syncTabs && storageType === \"local\") {\n      this.#subscribe = createSubscriber();\n    }\n  }\n  get current() {\n    this.#subscribe?.();\n    this.#version;\n    const root = this.#deserialize(this.#storage?.getItem(this.#key)) ?? this.#current;\n    const proxies = /* @__PURE__ */ new WeakMap();\n    const proxy = (value) => {\n      if (value === null || value?.constructor.name === \"Date\" || typeof value !== \"object\") {\n        return value;\n      }\n      let p = proxies.get(value);\n      if (!p) {\n        p = new Proxy(value, {\n          get: (target, property) => {\n            this.#version;\n            return proxy(Reflect.get(target, property));\n          },\n          set: (target, property, value2) => {\n            this.#version += 1;\n            Reflect.set(target, property, value2);\n            this.#serialize(root);\n            return true;\n          }\n        });\n        proxies.set(value, p);\n      }\n      return p;\n    };\n    return proxy(root);\n  }\n  set current(newValue) {\n    this.#serialize(newValue);\n    this.#version += 1;\n  }\n  #handleStorageEvent = (event) => {\n    if (event.key !== this.#key || event.newValue === null) return;\n    this.#current = this.#deserialize(event.newValue);\n    this.#version += 1;\n  };\n  #deserialize(value) {\n    try {\n      return this.#serializer.deserialize(value);\n    } catch (error) {\n      console.error(`Error when parsing \"${value}\" from persisted store \"${this.#key}\"`, error);\n      return;\n    }\n  }\n  #serialize(value) {\n    try {\n      if (value != void 0) {\n        this.#storage?.setItem(this.#key, this.#serializer.serialize(value));\n      }\n    } catch (error) {\n      console.error(`Error when writing value from persisted store \"${this.#key}\" to ${this.#storage}`, error);\n    }\n  }\n}\nfunction sanitizeClassNames(classNames) {\n  return classNames.filter((className) => className.length > 0);\n}\nconst noopStorage = {\n  getItem: (_key) => null,\n  setItem: (_key, _value) => {\n  }\n};\nconst isBrowser = typeof document !== \"undefined\";\nconst modeStorageKey = box(\"mode-watcher-mode\");\nconst themeStorageKey = box(\"mode-watcher-theme\");\nconst modes = [\"dark\", \"light\", \"system\"];\nfunction isValidMode(value) {\n  if (typeof value !== \"string\")\n    return false;\n  return modes.includes(value);\n}\nclass UserPrefersMode {\n  #defaultValue = \"system\";\n  #storage = isBrowser ? localStorage : noopStorage;\n  #initialValue = this.#storage.getItem(modeStorageKey.current);\n  #value = isValidMode(this.#initialValue) ? this.#initialValue : this.#defaultValue;\n  #persisted = this.#makePersisted();\n  #makePersisted(value = this.#value) {\n    return new PersistedState(modeStorageKey.current, value, {\n      serializer: {\n        serialize: (v) => v,\n        deserialize: (v) => {\n          if (isValidMode(v)) return v;\n          return this.#defaultValue;\n        }\n      }\n    });\n  }\n  constructor() {\n  }\n  get current() {\n    return this.#persisted.current;\n  }\n  set current(newValue) {\n    this.#persisted.current = newValue;\n  }\n}\nclass SystemPrefersMode {\n  #defaultValue = void 0;\n  #track = true;\n  #current = this.#defaultValue;\n  #mediaQueryState = typeof window !== \"undefined\" && typeof window.matchMedia === \"function\" ? new MediaQuery(\"prefers-color-scheme: light\") : { current: false };\n  query() {\n    if (!isBrowser) return;\n    this.#current = this.#mediaQueryState.current ? \"light\" : \"dark\";\n  }\n  tracking(active) {\n    this.#track = active;\n  }\n  constructor() {\n    this.query = this.query.bind(this);\n    this.tracking = this.tracking.bind(this);\n  }\n  get current() {\n    return this.#current;\n  }\n}\nconst userPrefersMode = new UserPrefersMode();\nconst systemPrefersMode = new SystemPrefersMode();\nclass CustomTheme {\n  #storage = isBrowser ? localStorage : noopStorage;\n  #initialValue = this.#storage.getItem(themeStorageKey.current);\n  #value = this.#initialValue === null || this.#initialValue === void 0 ? \"\" : this.#initialValue;\n  #persisted = this.#makePersisted();\n  #makePersisted(value = this.#value) {\n    return new PersistedState(themeStorageKey.current, value, {\n      serializer: {\n        serialize: (v) => {\n          if (typeof v !== \"string\") return \"\";\n          return v;\n        },\n        deserialize: (v) => v\n      }\n    });\n  }\n  constructor() {\n  }\n  /**\n   * The current theme.\n   * @returns The current theme.\n   */\n  get current() {\n    return this.#persisted.current;\n  }\n  /**\n   * Set the current theme.\n   * @param newValue The new theme to set.\n   */\n  set current(newValue) {\n    this.#persisted.current = newValue;\n  }\n}\nconst customTheme = new CustomTheme();\nlet timeoutAction;\nlet timeoutEnable;\nlet hasLoaded = false;\nfunction withoutTransition(action) {\n  if (typeof document === \"undefined\")\n    return;\n  if (!hasLoaded) {\n    hasLoaded = true;\n    action();\n    return;\n  }\n  clearTimeout(timeoutAction);\n  clearTimeout(timeoutEnable);\n  const style = document.createElement(\"style\");\n  const css = document.createTextNode(`* {\n     -webkit-transition: none !important;\n     -moz-transition: none !important;\n     -o-transition: none !important;\n     -ms-transition: none !important;\n     transition: none !important;\n  }`);\n  style.appendChild(css);\n  const disable = () => document.head.appendChild(style);\n  const enable = () => document.head.removeChild(style);\n  if (typeof window.getComputedStyle !== \"undefined\") {\n    disable();\n    action();\n    window.getComputedStyle(style).opacity;\n    enable();\n    return;\n  }\n  if (typeof window.requestAnimationFrame !== \"undefined\") {\n    disable();\n    action();\n    window.requestAnimationFrame(enable);\n    return;\n  }\n  disable();\n  timeoutAction = window.setTimeout(() => {\n    action();\n    timeoutEnable = window.setTimeout(enable, 120);\n  }, 120);\n}\nconst themeColors = box(void 0);\nconst disableTransitions = box(true);\nconst darkClassNames = box([]);\nconst lightClassNames = box([]);\nfunction createDerivedMode() {\n  const current = (() => {\n    if (!isBrowser) return void 0;\n    const derivedMode2 = userPrefersMode.current === \"system\" ? systemPrefersMode.current : userPrefersMode.current;\n    const sanitizedDarkClassNames = sanitizeClassNames(darkClassNames.current);\n    const sanitizedLightClassNames = sanitizeClassNames(lightClassNames.current);\n    function update() {\n      const htmlEl = document.documentElement;\n      const themeColorEl = document.querySelector('meta[name=\"theme-color\"]');\n      if (derivedMode2 === \"light\") {\n        if (sanitizedDarkClassNames.length) htmlEl.classList.remove(...sanitizedDarkClassNames);\n        if (sanitizedLightClassNames.length) htmlEl.classList.add(...sanitizedLightClassNames);\n        htmlEl.style.colorScheme = \"light\";\n        if (themeColorEl && themeColors.current) {\n          themeColorEl.setAttribute(\"content\", themeColors.current.light);\n        }\n      } else {\n        if (sanitizedLightClassNames.length) htmlEl.classList.remove(...sanitizedLightClassNames);\n        if (sanitizedDarkClassNames.length) htmlEl.classList.add(...sanitizedDarkClassNames);\n        htmlEl.style.colorScheme = \"dark\";\n        if (themeColorEl && themeColors.current) {\n          themeColorEl.setAttribute(\"content\", themeColors.current.dark);\n        }\n      }\n    }\n    if (disableTransitions.current) {\n      withoutTransition(update);\n    } else {\n      update();\n    }\n    return derivedMode2;\n  })();\n  return {\n    get current() {\n      return current;\n    }\n  };\n}\nfunction createDerivedTheme() {\n  const current = (() => {\n    customTheme.current;\n    if (!isBrowser) return void 0;\n    function update() {\n      const htmlEl = document.documentElement;\n      htmlEl.setAttribute(\"data-theme\", customTheme.current);\n    }\n    if (disableTransitions.current) {\n      withoutTransition(update);\n    } else {\n      update();\n    }\n    return customTheme.current;\n  })();\n  return {\n    get current() {\n      return current;\n    }\n  };\n}\nconst derivedMode = createDerivedMode();\ncreateDerivedTheme();\nfunction setMode(mode) {\n  userPrefersMode.current = mode;\n}\nfunction defineConfig(config) {\n  return config;\n}\nfunction setInitialMode({ defaultMode = \"system\", themeColors: themeColors2, darkClassNames: darkClassNames2 = [\"dark\"], lightClassNames: lightClassNames2 = [], defaultTheme = \"\", modeStorageKey: modeStorageKey2 = \"mode-watcher-mode\", themeStorageKey: themeStorageKey2 = \"mode-watcher-theme\" }) {\n  const rootEl = document.documentElement;\n  const mode = localStorage.getItem(modeStorageKey2) ?? defaultMode;\n  const theme = localStorage.getItem(themeStorageKey2) ?? defaultTheme;\n  const light = mode === \"light\" || mode === \"system\" && window.matchMedia(\"(prefers-color-scheme: light)\").matches;\n  if (light) {\n    if (darkClassNames2.length)\n      rootEl.classList.remove(...darkClassNames2.filter(Boolean));\n    if (lightClassNames2.length)\n      rootEl.classList.add(...lightClassNames2.filter(Boolean));\n  } else {\n    if (lightClassNames2.length)\n      rootEl.classList.remove(...lightClassNames2.filter(Boolean));\n    if (darkClassNames2.length)\n      rootEl.classList.add(...darkClassNames2.filter(Boolean));\n  }\n  rootEl.style.colorScheme = light ? \"light\" : \"dark\";\n  if (themeColors2) {\n    const themeMetaEl = document.querySelector('meta[name=\"theme-color\"]');\n    if (themeMetaEl) {\n      themeMetaEl.setAttribute(\"content\", mode === \"light\" ? themeColors2.light : themeColors2.dark);\n    }\n  }\n  if (theme) {\n    rootEl.setAttribute(\"data-theme\", theme);\n    localStorage.setItem(themeStorageKey2, theme);\n  }\n  localStorage.setItem(modeStorageKey2, mode);\n}\nexport {\n  disableTransitions as a,\n  themeColors as b,\n  defineConfig as c,\n  darkClassNames as d,\n  derivedMode as e,\n  setMode as f,\n  lightClassNames as l,\n  modeStorageKey as m,\n  setInitialMode as s,\n  themeStorageKey as t\n};\n"], "names": [], "mappings": ";;;;AAGA,MAAM,aAAa,GAAG,MAAM;AAC5B,SAAS,gBAAgB,CAAC,SAAS,EAAE;AACrC,EAAE,IAAI,aAAa,GAAG,SAAS,CAAC,aAAa;AAC7C,EAAE,OAAO,aAAa,EAAE,UAAU,EAAE;AACpC,IAAI,MAAM,IAAI,GAAG,aAAa,CAAC,UAAU,CAAC,aAAa;AACvD,IAAI,IAAI,IAAI,KAAK,aAAa;AAC9B,MAAM;AACN;AACA,MAAM,aAAa,GAAG,IAAI;AAC1B;AACA,EAAE,OAAO,aAAa;AACtB;AACA,MAAM,aAAa,CAAC;AACpB,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,EAAE,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC5B,IAAI,MAAM;AACV,MAAM,MAAM,EAAE,OAAO,GAAG,aAAa;AACrC,MAAM,QAAQ,EAAE,SAAS,GAAG,OAAO,EAAE;AACrC,KAAK,GAAG,OAAO;AACf,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,UAAU,GAAG,gBAAgB,EAAE;AACxC;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,IAAI,CAAC,UAAU,IAAI;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI;AACpC,IAAI,OAAO,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC;AAC3C;AACA;AACA,IAAI,aAAa,EAAE;AACnB,SAAS,UAAU,CAAC,WAAW,EAAE,OAAO,EAAE;AAC1C,EAAE,QAAQ,WAAW;AACrB,IAAI,KAAK,OAAO;AAChB,MAAM,OAAO,OAAO,CAAC,YAAY;AACjC,IAAI,KAAK,SAAS;AAClB,MAAM,OAAO,OAAO,CAAC,cAAc;AACnC;AACA;AACA,MAAM,cAAc,CAAC;AACrB,EAAE,QAAQ;AACV,EAAE,IAAI;AACN,EAAE,WAAW;AACb,EAAE,QAAQ;AACV,EAAE,UAAU;AACZ,EAAE,QAAQ,GAAG,CAAC;AACd,EAAE,WAAW,CAAC,GAAG,EAAE,YAAY,EAAE,OAAO,GAAG,EAAE,EAAE;AAC/C,IAAI,MAAM;AACV,MAAM,OAAO,EAAE,WAAW,GAAG,OAAO;AACpC,MAAM,UAAU,GAAG;AACnB,QAAQ,SAAS,EAAE,IAAI,CAAC,SAAS;AACjC,QAAQ,WAAW,EAAE,IAAI,CAAC;AAC1B,OAAO;AACP,MAAM,QAAQ,GAAG,IAAI;AACrB,MAAM,MAAM,EAAE,OAAO,GAAG;AACxB,KAAK,GAAG,OAAO;AACf,IAAI,IAAI,CAAC,QAAQ,GAAG,YAAY;AAChC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG;AACnB,IAAI,IAAI,CAAC,WAAW,GAAG,UAAU;AACjC,IAAI,IAAI,OAAO,KAAK,MAAM,EAAE;AAC5B,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,OAAO,CAAC;AACpD,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO;AAC3B,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;AAC9C,IAAI,IAAI,aAAa,KAAK,IAAI,EAAE;AAChC,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;AACtD,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;AACnC;AACA,IAAI,IAAI,QAAQ,IAAI,WAAW,KAAK,OAAO,EAAE;AAC7C,MAAM,IAAI,CAAC,UAAU,GAAG,gBAAgB,EAAE;AAC1C;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,IAAI,CAAC,UAAU,IAAI;AACvB,IAAI,IAAI,CAAC,QAAQ;AACjB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ;AACtF,IAAI,MAAM,OAAO,mBAAmB,IAAI,OAAO,EAAE;AACjD,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK;AAC7B,MAAM,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,EAAE,WAAW,CAAC,IAAI,KAAK,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7F,QAAQ,OAAO,KAAK;AACpB;AACA,MAAM,IAAI,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;AAChC,MAAM,IAAI,CAAC,CAAC,EAAE;AACd,QAAQ,CAAC,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE;AAC7B,UAAU,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,KAAK;AACrC,YAAY,IAAI,CAAC,QAAQ;AACzB,YAAY,OAAO,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACvD,WAAW;AACX,UAAU,GAAG,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,KAAK;AAC7C,YAAY,IAAI,CAAC,QAAQ,IAAI,CAAC;AAC9B,YAAY,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC;AACjD,YAAY,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACjC,YAAY,OAAO,IAAI;AACvB;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;AAC7B;AACA,MAAM,OAAO,CAAC;AACd,KAAK;AACL,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC;AACtB;AACA,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC;AACtB;AACA,EAAE,mBAAmB,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,EAAE;AAC5D,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;AACrD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC;AACtB,GAAG;AACH,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI;AACR,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC;AAChD,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,CAAC,wBAAwB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;AAC/F,MAAM;AACN;AACA;AACA,EAAE,UAAU,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI;AACR,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,EAAE;AAC3B,QAAQ,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC5E;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,+CAA+C,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC;AAC9G;AACA;AACA;AACA,SAAS,kBAAkB,CAAC,UAAU,EAAE;AACxC,EAAE,OAAO,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/D;AACA,MAAM,WAAW,GAAG;AACpB,EAAE,OAAO,EAAE,CAAC,IAAI,KAAK,IAAI;AACzB,EAAE,OAAO,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK;AAC7B;AACA,CAAC;AACD,MAAM,SAAS,GAAG,OAAO,QAAQ,KAAK,WAAW;AAC5C,MAAC,cAAc,GAAG,GAAG,CAAC,mBAAmB;AACzC,MAAC,eAAe,GAAG,GAAG,CAAC,oBAAoB;AAChD,MAAM,KAAK,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;AACzC,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,IAAI,OAAO,KAAK,KAAK,QAAQ;AAC/B,IAAI,OAAO,KAAK;AAChB,EAAE,OAAO,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;AAC9B;AACA,MAAM,eAAe,CAAC;AACtB,EAAE,aAAa,GAAG,QAAQ;AAC1B,EAAE,QAAQ,GAAG,SAAS,GAAG,YAAY,GAAG,WAAW;AACnD,EAAE,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC;AAC/D,EAAE,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa;AACpF,EAAE,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE;AACpC,EAAE,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AACtC,IAAI,OAAO,IAAI,cAAc,CAAC,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE;AAC7D,MAAM,UAAU,EAAE;AAClB,QAAQ,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC;AAC3B,QAAQ,WAAW,EAAE,CAAC,CAAC,KAAK;AAC5B,UAAU,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC;AACtC,UAAU,OAAO,IAAI,CAAC,aAAa;AACnC;AACA;AACA,KAAK,CAAC;AACN;AACA,EAAE,WAAW,GAAG;AAChB;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;AAClC;AACA,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,QAAQ;AACtC;AACA;AACA,MAAM,iBAAiB,CAAC;AACxB,EAAE,aAAa,GAAG,MAAM;AACxB,EAAE,MAAM,GAAG,IAAI;AACf,EAAE,QAAQ,GAAG,IAAI,CAAC,aAAa;AAC/B,EAAE,gBAAgB,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,UAAU,KAAK,UAAU,GAAG,IAAI,UAAU,CAAC,6BAA6B,CAAC,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE;AAClK,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,OAAO,GAAG,MAAM;AACpE;AACA,EAAE,QAAQ,CAAC,MAAM,EAAE;AACnB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;AAC5C;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,QAAQ;AACxB;AACA;AACA,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE;AAC7C,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE;AACjD,MAAM,WAAW,CAAC;AAClB,EAAE,QAAQ,GAAG,SAAS,GAAG,YAAY,GAAG,WAAW;AACnD,EAAE,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC;AAChE,EAAE,MAAM,GAAG,IAAI,CAAC,aAAa,KAAK,IAAI,IAAI,IAAI,CAAC,aAAa,KAAK,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,aAAa;AACjG,EAAE,UAAU,GAAG,IAAI,CAAC,cAAc,EAAE;AACpC,EAAE,cAAc,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE;AACtC,IAAI,OAAO,IAAI,cAAc,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE;AAC9D,MAAM,UAAU,EAAE;AAClB,QAAQ,SAAS,EAAE,CAAC,CAAC,KAAK;AAC1B,UAAU,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,OAAO,EAAE;AAC9C,UAAU,OAAO,CAAC;AAClB,SAAS;AACT,QAAQ,WAAW,EAAE,CAAC,CAAC,KAAK;AAC5B;AACA,KAAK,CAAC;AACN;AACA,EAAE,WAAW,GAAG;AAChB;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;AAClC;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,GAAG,QAAQ;AACtC;AACA;AACA,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE;AACrC,IAAI,aAAa;AACjB,IAAI,aAAa;AACjB,IAAI,SAAS,GAAG,KAAK;AACrB,SAAS,iBAAiB,CAAC,MAAM,EAAE;AACnC,EAAE,IAAI,OAAO,QAAQ,KAAK,WAAW;AACrC,IAAI;AACJ,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,MAAM,EAAE;AACZ,IAAI;AACJ;AACA,EAAE,YAAY,CAAC,aAAa,CAAC;AAC7B,EAAE,YAAY,CAAC,aAAa,CAAC;AAC7B,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;AAC/C,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,cAAc,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC;AACxB,EAAE,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;AACxD,EAAE,MAAM,MAAM,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;AACvD,EAAE,IAAI,OAAO,MAAM,CAAC,gBAAgB,KAAK,WAAW,EAAE;AACtD,IAAI,OAAO,EAAE;AACb,IAAI,MAAM,EAAE;AACZ,IAAI,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,OAAO;AAC1C,IAAI,MAAM,EAAE;AACZ,IAAI;AACJ;AACA,EAAE,IAAI,OAAO,MAAM,CAAC,qBAAqB,KAAK,WAAW,EAAE;AAC3D,IAAI,OAAO,EAAE;AACb,IAAI,MAAM,EAAE;AACZ,IAAI,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC;AACxC,IAAI;AACJ;AACA,EAAE,OAAO,EAAE;AACX,EAAE,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM;AAC1C,IAAI,MAAM,EAAE;AACZ,IAAI,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC;AAClD,GAAG,EAAE,GAAG,CAAC;AACT;AACK,MAAC,WAAW,GAAG,GAAG,CAAC,MAAM;AACzB,MAAC,kBAAkB,GAAG,GAAG,CAAC,IAAI;AAC9B,MAAC,cAAc,GAAG,GAAG,CAAC,EAAE;AACxB,MAAC,eAAe,GAAG,GAAG,CAAC,EAAE;AAC9B,SAAS,iBAAiB,GAAG;AAC7B,EAAE,MAAM,OAAO,GAAG,CAAC,MAAM;AACzB,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,MAAM;AACjC,IAAI,MAAM,YAAY,GAAG,eAAe,CAAC,OAAO,KAAK,QAAQ,GAAG,iBAAiB,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO;AACnH,IAAI,MAAM,uBAAuB,GAAG,kBAAkB,CAAC,cAAc,CAAC,OAAO,CAAC;AAC9E,IAAI,MAAM,wBAAwB,GAAG,kBAAkB,CAAC,eAAe,CAAC,OAAO,CAAC;AAChF,IAAI,SAAS,MAAM,GAAG;AACtB,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC,eAAe;AAC7C,MAAM,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,CAAC;AAC7E,MAAM,IAAI,YAAY,KAAK,OAAO,EAAE;AACpC,QAAQ,IAAI,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,uBAAuB,CAAC;AAC/F,QAAQ,IAAI,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,wBAAwB,CAAC;AAC9F,QAAQ,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,OAAO;AAC1C,QAAQ,IAAI,YAAY,IAAI,WAAW,CAAC,OAAO,EAAE;AACjD,UAAU,YAAY,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC;AACzE;AACA,OAAO,MAAM;AACb,QAAQ,IAAI,wBAAwB,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,wBAAwB,CAAC;AACjG,QAAQ,IAAI,uBAAuB,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,uBAAuB,CAAC;AAC5F,QAAQ,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM;AACzC,QAAQ,IAAI,YAAY,IAAI,WAAW,CAAC,OAAO,EAAE;AACjD,UAAU,YAAY,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;AACxE;AACA;AACA;AACA,IAAI,IAAI,kBAAkB,CAAC,OAAO,EAAE;AACpC,MAAM,iBAAiB,CAAC,MAAM,CAAC;AAC/B,KAAK,MAAM;AACX,MAAM,MAAM,EAAE;AACd;AACA,IAAI,OAAO,YAAY;AACvB,GAAG,GAAG;AACN,EAAE,OAAO;AACT,IAAI,IAAI,OAAO,GAAG;AAClB,MAAM,OAAO,OAAO;AACpB;AACA,GAAG;AACH;AACA,SAAS,kBAAkB,GAAG;AAC9B,EAAE,MAAM,OAAO,GAAG,CAAC,MAAM;AACzB,IAAI,WAAW,CAAC,OAAO;AACvB,IAAI,IAAI,CAAC,SAAS,EAAE,OAAO,MAAM;AACjC,IAAI,SAAS,MAAM,GAAG;AACtB,MAAM,MAAM,MAAM,GAAG,QAAQ,CAAC,eAAe;AAC7C,MAAM,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,WAAW,CAAC,OAAO,CAAC;AAC5D;AACA,IAAI,IAAI,kBAAkB,CAAC,OAAO,EAAE;AACpC,MAAM,iBAAiB,CAAC,MAAM,CAAC;AAC/B,KAAK,MAAM;AACX,MAAM,MAAM,EAAE;AACd;AACA,IAAI,OAAO,WAAW,CAAC,OAAO;AAC9B,GAAG,GAAG;AACN,EAAE,OAAO;AACT,IAAI,IAAI,OAAO,GAAG;AAClB,MAAM,OAAO,OAAO;AACpB;AACA,GAAG;AACH;AACK,MAAC,WAAW,GAAG,iBAAiB;AACrC,kBAAkB,EAAE;AACpB,SAAS,OAAO,CAAC,IAAI,EAAE;AACvB,EAAE,eAAe,CAAC,OAAO,GAAG,IAAI;AAChC;AACA,SAAS,YAAY,CAAC,MAAM,EAAE;AAC9B,EAAE,OAAO,MAAM;AACf;AACA,SAAS,cAAc,CAAC,EAAE,WAAW,GAAG,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,cAAc,EAAE,eAAe,GAAG,CAAC,MAAM,CAAC,EAAE,eAAe,EAAE,gBAAgB,GAAG,EAAE,EAAE,YAAY,GAAG,EAAE,EAAE,cAAc,EAAE,eAAe,GAAG,mBAAmB,EAAE,eAAe,EAAE,gBAAgB,GAAG,oBAAoB,EAAE,EAAE;AACvS,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,eAAe;AACzC,EAAE,MAAM,IAAI,GAAG,YAAY,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,WAAW;AACnE,EAAE,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,YAAY;AACtE,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,UAAU,CAAC,+BAA+B,CAAC,CAAC,OAAO;AACnH,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,IAAI,eAAe,CAAC,MAAM;AAC9B,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACjE,IAAI,IAAI,gBAAgB,CAAC,MAAM;AAC/B,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC/D,GAAG,MAAM;AACT,IAAI,IAAI,gBAAgB,CAAC,MAAM;AAC/B,MAAM,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAClE,IAAI,IAAI,eAAe,CAAC,MAAM;AAC9B,MAAM,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC9D;AACA,EAAE,MAAM,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,GAAG,OAAO,GAAG,MAAM;AACrD,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,0BAA0B,CAAC;AAC1E,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,WAAW,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,KAAK,OAAO,GAAG,YAAY,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;AACpG;AACA;AACA,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,MAAM,CAAC,YAAY,CAAC,YAAY,EAAE,KAAK,CAAC;AAC5C,IAAI,YAAY,CAAC,OAAO,CAAC,gBAAgB,EAAE,KAAK,CAAC;AACjD;AACA,EAAE,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC;AAC7C;;;;"}