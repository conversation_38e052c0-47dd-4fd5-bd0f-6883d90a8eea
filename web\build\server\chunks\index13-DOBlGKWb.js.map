{"version": 3, "file": "index13-DOBlGKWb.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/index13.js"], "sourcesContent": ["import { F as FeatureAccessLevel, L as LimitType } from \"./features.js\";\nimport { g as getFeatureLimitById, b as getFeatureById, F as FEATURES } from \"./dynamic-registry.js\";\nclass FeatureAccess {\n  userData;\n  constructor(userData) {\n    this.userData = userData;\n  }\n  /**\n   * Check if a feature is available to the user\n   * @param featureId Feature ID\n   * @returns True if the feature is available\n   */\n  hasAccess(featureId) {\n    if (!this.userData.plan) return false;\n    const planFeature = this.getPlanFeature(featureId);\n    if (!planFeature) return false;\n    return planFeature.accessLevel !== FeatureAccessLevel.NotIncluded;\n  }\n  /**\n   * Get the access level for a feature\n   * @param featureId Feature ID\n   * @returns Access level or NotIncluded if not available\n   */\n  getAccessLevel(featureId) {\n    if (!this.userData.plan) return FeatureAccessLevel.NotIncluded;\n    const planFeature = this.getPlanFeature(featureId);\n    return planFeature?.accessLevel || FeatureAccessLevel.NotIncluded;\n  }\n  /**\n   * Check if a feature has a specific limit\n   * @param featureId Feature ID\n   * @param limitId Limit ID\n   * @returns True if the feature has the limit\n   */\n  hasLimit(featureId, limitId) {\n    if (!this.userData.plan) return false;\n    const planFeature = this.getPlanFeature(featureId);\n    if (!planFeature) return false;\n    if (planFeature.accessLevel === FeatureAccessLevel.Unlimited) return false;\n    return !!this.getLimitValue(featureId, limitId);\n  }\n  /**\n   * Get the limit value for a feature\n   * @param featureId Feature ID\n   * @param limitId Limit ID\n   * @returns Limit value or undefined if not found\n   */\n  getLimitValue(featureId, limitId) {\n    if (!this.userData.plan) return void 0;\n    const planFeature = this.getPlanFeature(featureId);\n    if (!planFeature) return void 0;\n    if (planFeature.accessLevel === FeatureAccessLevel.Unlimited) return \"unlimited\";\n    if (planFeature.accessLevel !== FeatureAccessLevel.Limited) return void 0;\n    const limitValue = planFeature.limits?.find((limit) => limit.limitId === limitId);\n    return limitValue?.value;\n  }\n  /**\n   * Get the numeric limit value for a feature\n   * @param featureId Feature ID\n   * @param limitId Limit ID\n   * @param defaultValue Default value if not found\n   * @returns Numeric limit value\n   */\n  getNumericLimitValue(featureId, limitId, defaultValue) {\n    const limitValue = this.getLimitValue(featureId, limitId);\n    if (limitValue === void 0) return defaultValue;\n    if (limitValue === \"unlimited\") return Infinity;\n    return limitValue;\n  }\n  /**\n   * Get the current usage for a feature limit\n   * @param featureId Feature ID\n   * @param limitId Limit ID\n   * @returns Current usage or 0 if not found\n   */\n  getCurrentUsage(featureId, limitId) {\n    const limit = getFeatureLimitById(limitId);\n    if (!limit) return 0;\n    if (limit.type === LimitType.Monthly) {\n      const now = /* @__PURE__ */ new Date();\n      const period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, \"0\")}`;\n      const usage2 = this.userData.usage.find(\n        (u) => u.featureId === featureId && u.limitId === limitId && u.period === period\n      );\n      return usage2?.used || 0;\n    }\n    const usage = this.userData.usage.find(\n      (u) => u.featureId === featureId && u.limitId === limitId\n    );\n    return usage?.used || 0;\n  }\n  /**\n   * Check if a user has reached their limit for a feature\n   * @param featureId Feature ID\n   * @param limitId Limit ID\n   * @returns True if the user has reached their limit\n   */\n  hasReachedLimit(featureId, limitId) {\n    if (!this.userData.plan) return true;\n    const planFeature = this.getPlanFeature(featureId);\n    if (!planFeature) return true;\n    if (planFeature.accessLevel === FeatureAccessLevel.Unlimited) return false;\n    if (planFeature.accessLevel !== FeatureAccessLevel.Limited) return false;\n    const limitValue = this.getLimitValue(featureId, limitId);\n    if (limitValue === void 0) return false;\n    if (limitValue === \"unlimited\") return false;\n    const currentUsage = this.getCurrentUsage(featureId, limitId);\n    return currentUsage >= limitValue;\n  }\n  /**\n   * Get the remaining usage for a feature limit\n   * @param featureId Feature ID\n   * @param limitId Limit ID\n   * @returns Remaining usage or Infinity if unlimited\n   */\n  getRemainingUsage(featureId, limitId) {\n    if (!this.userData.plan) return 0;\n    const planFeature = this.getPlanFeature(featureId);\n    if (!planFeature) return 0;\n    if (planFeature.accessLevel === FeatureAccessLevel.Unlimited) return Infinity;\n    if (planFeature.accessLevel !== FeatureAccessLevel.Limited) return Infinity;\n    const limitValue = this.getLimitValue(featureId, limitId);\n    if (limitValue === void 0) return Infinity;\n    if (limitValue === \"unlimited\") return Infinity;\n    const currentUsage = this.getCurrentUsage(featureId, limitId);\n    return Math.max(0, limitValue - currentUsage);\n  }\n  /**\n   * Check if a user can perform an action that requires a feature\n   * @param featureId Feature ID\n   * @param limitId Optional limit ID to check\n   * @returns True if the user can perform the action\n   */\n  canPerformAction(featureId, limitId) {\n    if (!this.hasAccess(featureId)) return false;\n    if (!limitId) return true;\n    return !this.hasReachedLimit(featureId, limitId);\n  }\n  /**\n   * Get the reason why a user can't perform an action\n   * @param featureId Feature ID\n   * @param limitId Optional limit ID to check\n   * @returns Reason or null if the user can perform the action\n   */\n  getBlockReason(featureId, limitId) {\n    const feature = getFeatureById(featureId);\n    if (!feature) return \"Feature not found\";\n    if (!this.hasAccess(featureId)) {\n      return `Your current plan does not include the ${feature.name} feature.`;\n    }\n    if (!limitId) return null;\n    if (this.hasReachedLimit(featureId, limitId)) {\n      const limit = getFeatureLimitById(limitId);\n      if (!limit) return \"Limit not found\";\n      const limitValue = this.getLimitValue(featureId, limitId);\n      if (limitValue === void 0) return null;\n      if (limitValue === \"unlimited\") return null;\n      return `You've reached your limit of ${limitValue} ${limit.unit || \"\"} for ${feature.name}.`;\n    }\n    return null;\n  }\n  /**\n   * Get all features available to the user\n   * @returns Array of available features\n   */\n  getAvailableFeatures() {\n    return FEATURES.filter((feature) => this.hasAccess(feature.id));\n  }\n  /**\n   * Get all features available to the user by category\n   * @returns Record of features by category\n   */\n  getAvailableFeaturesByCategory() {\n    const features = this.getAvailableFeatures();\n    const result = {};\n    for (const feature of features) {\n      if (!result[feature.category]) {\n        result[feature.category] = [];\n      }\n      result[feature.category].push(feature);\n    }\n    return result;\n  }\n  /**\n   * Get the plan feature configuration\n   * @param featureId Feature ID\n   * @returns Plan feature configuration or undefined if not found\n   */\n  getPlanFeature(featureId) {\n    if (!this.userData.plan) return void 0;\n    return this.userData.plan.features.find((feature) => feature.featureId === featureId);\n  }\n  /**\n   * Create a feature access instance from user data\n   * @param userData User data\n   * @returns Feature access instance\n   */\n  static fromUserData(userData) {\n    return new FeatureAccess(userData);\n  }\n}\nfunction createFeatureAccess(userData) {\n  const planId = userData.subscription?.planId || userData.role || \"free\";\n  const plan = userData.plan;\n  let subscription;\n  if (userData.subscription) {\n    subscription = {\n      planId: userData.subscription.planId || planId,\n      startDate: userData.subscription.startDate || /* @__PURE__ */ new Date(),\n      endDate: userData.subscription.endDate,\n      cancelAtPeriodEnd: userData.subscription.cancelAtPeriodEnd || false,\n      status: userData.subscription.status || \"active\",\n      trialEndDate: userData.subscription.trialEndDate\n    };\n  }\n  const usage = [];\n  if (userData.usage) {\n    for (const feature of FEATURES) {\n      if (!feature.limits) continue;\n      for (const limit of feature.limits) {\n        const usageKey = `${feature.id}_${limit.id}`;\n        if (usageKey in userData.usage) {\n          const now = /* @__PURE__ */ new Date();\n          const period = limit.type === LimitType.Monthly ? `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, \"0\")}` : void 0;\n          usage.push({\n            featureId: feature.id,\n            limitId: limit.id,\n            used: userData.usage[usageKey],\n            period,\n            lastUpdated: /* @__PURE__ */ new Date()\n          });\n        }\n      }\n    }\n  }\n  const featureUserData = {\n    id: userData.id,\n    subscription,\n    plan,\n    usage\n  };\n  return FeatureAccess.fromUserData(featureUserData);\n}\nexport {\n  createFeatureAccess as c\n};\n"], "names": [], "mappings": ";;;AAEA,MAAM,aAAa,CAAC;AACpB,EAAE,QAAQ;AACV,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC,SAAS,EAAE;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,KAAK;AACzC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,KAAK;AAClC,IAAI,OAAO,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,WAAW;AACrE;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,kBAAkB,CAAC,WAAW;AAClE,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AACtD,IAAI,OAAO,WAAW,EAAE,WAAW,IAAI,kBAAkB,CAAC,WAAW;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,KAAK;AACzC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,KAAK;AAClC,IAAI,IAAI,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,SAAS,EAAE,OAAO,KAAK;AAC9E,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,MAAM;AAC1C,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,MAAM;AACnC,IAAI,IAAI,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,SAAS,EAAE,OAAO,WAAW;AACpF,IAAI,IAAI,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,OAAO,EAAE,OAAO,MAAM;AAC7E,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC;AACrF,IAAI,OAAO,UAAU,EAAE,KAAK;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE;AACzD,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC;AAC7D,IAAI,IAAI,UAAU,KAAK,MAAM,EAAE,OAAO,YAAY;AAClD,IAAI,IAAI,UAAU,KAAK,WAAW,EAAE,OAAO,QAAQ;AACnD,IAAI,OAAO,UAAU;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,IAAI,MAAM,KAAK,GAAG,mBAAmB,CAAC,OAAO,CAAC;AAC9C,IAAI,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;AACxB,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,OAAO,EAAE;AAC1C,MAAM,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC5C,MAAM,MAAM,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC1F,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI;AAC7C,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,IAAI,CAAC,CAAC,MAAM,KAAK;AAClF,OAAO;AACP,MAAM,OAAO,MAAM,EAAE,IAAI,IAAI,CAAC;AAC9B;AACA,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI;AAC1C,MAAM,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,KAAK,SAAS,IAAI,CAAC,CAAC,OAAO,KAAK;AACxD,KAAK;AACL,IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,IAAI;AACxC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,IAAI;AACjC,IAAI,IAAI,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,SAAS,EAAE,OAAO,KAAK;AAC9E,IAAI,IAAI,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,OAAO,EAAE,OAAO,KAAK;AAC5E,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC;AAC7D,IAAI,IAAI,UAAU,KAAK,MAAM,EAAE,OAAO,KAAK;AAC3C,IAAI,IAAI,UAAU,KAAK,WAAW,EAAE,OAAO,KAAK;AAChD,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC;AACjE,IAAI,OAAO,YAAY,IAAI,UAAU;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AACxC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;AACrC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AACtD,IAAI,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC;AAC9B,IAAI,IAAI,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,SAAS,EAAE,OAAO,QAAQ;AACjF,IAAI,IAAI,WAAW,CAAC,WAAW,KAAK,kBAAkB,CAAC,OAAO,EAAE,OAAO,QAAQ;AAC/E,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC;AAC7D,IAAI,IAAI,UAAU,KAAK,MAAM,EAAE,OAAO,QAAQ;AAC9C,IAAI,IAAI,UAAU,KAAK,WAAW,EAAE,OAAO,QAAQ;AACnD,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC;AACjE,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,YAAY,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE;AACvC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,OAAO,KAAK;AAChD,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;AAC7B,IAAI,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,IAAI,MAAM,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC;AAC7C,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO,mBAAmB;AAC5C,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;AACpC,MAAM,OAAO,CAAC,uCAAuC,EAAE,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC;AAC9E;AACA,IAAI,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI;AAC7B,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;AAClD,MAAM,MAAM,KAAK,GAAG,mBAAmB,CAAC,OAAO,CAAC;AAChD,MAAM,IAAI,CAAC,KAAK,EAAE,OAAO,iBAAiB;AAC1C,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC;AAC/D,MAAM,IAAI,UAAU,KAAK,MAAM,EAAE,OAAO,IAAI;AAC5C,MAAM,IAAI,UAAU,KAAK,WAAW,EAAE,OAAO,IAAI;AACjD,MAAM,OAAO,CAAC,6BAA6B,EAAE,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;AAClG;AACA,IAAI,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA,EAAE,oBAAoB,GAAG;AACzB,IAAI,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA,EAAE,8BAA8B,GAAG;AACnC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,EAAE;AAChD,IAAI,MAAM,MAAM,GAAG,EAAE;AACrB,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AACrC,QAAQ,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE;AACrC;AACA,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5C;AACA,IAAI,OAAO,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,MAAM;AAC1C,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;AACzF;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,YAAY,CAAC,QAAQ,EAAE;AAChC,IAAI,OAAO,IAAI,aAAa,CAAC,QAAQ,CAAC;AACtC;AACA;AACA,SAAS,mBAAmB,CAAC,QAAQ,EAAE;AACvC,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,MAAM,IAAI,QAAQ,CAAC,IAAI,IAAI,MAAM;AACzE,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI;AAC5B,EAAE,IAAI,YAAY;AAClB,EAAE,IAAI,QAAQ,CAAC,YAAY,EAAE;AAC7B,IAAI,YAAY,GAAG;AACnB,MAAM,MAAM,EAAE,QAAQ,CAAC,YAAY,CAAC,MAAM,IAAI,MAAM;AACpD,MAAM,SAAS,EAAE,QAAQ,CAAC,YAAY,CAAC,SAAS,oBAAoB,IAAI,IAAI,EAAE;AAC9E,MAAM,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,OAAO;AAC5C,MAAM,iBAAiB,EAAE,QAAQ,CAAC,YAAY,CAAC,iBAAiB,IAAI,KAAK;AACzE,MAAM,MAAM,EAAE,QAAQ,CAAC,YAAY,CAAC,MAAM,IAAI,QAAQ;AACtD,MAAM,YAAY,EAAE,QAAQ,CAAC,YAAY,CAAC;AAC1C,KAAK;AACL;AACA,EAAE,MAAM,KAAK,GAAG,EAAE;AAClB,EAAE,IAAI,QAAQ,CAAC,KAAK,EAAE;AACtB,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;AAC3B,MAAM,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,MAAM,EAAE;AAC1C,QAAQ,MAAM,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AACpD,QAAQ,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,EAAE;AACxC,UAAU,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAChD,UAAU,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,OAAO,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM;AAC1I,UAAU,KAAK,CAAC,IAAI,CAAC;AACrB,YAAY,SAAS,EAAE,OAAO,CAAC,EAAE;AACjC,YAAY,OAAO,EAAE,KAAK,CAAC,EAAE;AAC7B,YAAY,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC;AAC1C,YAAY,MAAM;AAClB,YAAY,WAAW,kBAAkB,IAAI,IAAI;AACjD,WAAW,CAAC;AACZ;AACA;AACA;AACA;AACA,EAAE,MAAM,eAAe,GAAG;AAC1B,IAAI,EAAE,EAAE,QAAQ,CAAC,EAAE;AACnB,IAAI,YAAY;AAChB,IAAI,IAAI;AACR,IAAI;AACJ,GAAG;AACH,EAAE,OAAO,aAAa,CAAC,YAAY,CAAC,eAAe,CAAC;AACpD;;;;"}