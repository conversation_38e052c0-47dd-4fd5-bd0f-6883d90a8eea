{"version": 3, "file": "sanityClient-BQ6Z_2a-.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/sanityClient.js"], "sourcesContent": ["import { createClient } from \"@sanity/client\";\nconst projectId = \"fqw18aoo\";\nconst dataset = \"production\";\nconst apiVersion = \"2023-05-03\";\ncreateClient({\n  projectId,\n  dataset,\n  apiVersion,\n  useCdn: process.env.NODE_ENV === \"production\"\n  // Use CDN in production for better performance\n});\nfunction urlFor(source, options) {\n  return imageUrl(source, options || {});\n}\nfunction imageUrl(source, options = {}) {\n  if (!source || !source.asset) {\n    return \"\";\n  }\n  const refId = source.asset._ref || \"\";\n  if (!refId.startsWith(\"image-\")) {\n    return \"\";\n  }\n  const [, id, dimensions, format] = refId.split(\"-\");\n  if (!id) {\n    return \"\";\n  }\n  let url = `https://cdn.sanity.io/images/${projectId}/${dataset}/${id}`;\n  if (format) {\n    url += `.${format}`;\n  } else {\n    url += \".jpg\";\n  }\n  const params = [];\n  const width = options.width || 800;\n  params.push(`w=${width}`);\n  if (options.height) {\n    params.push(`h=${options.height}`);\n  }\n  const fit = options.fit || \"max\";\n  params.push(`fit=${fit}`);\n  params.push(\"auto=format\");\n  const quality = options.quality || 80;\n  params.push(`q=${quality}`);\n  if (params.length > 0) {\n    url += `?${params.join(\"&\")}`;\n  }\n  return url;\n}\nexport {\n  urlFor as u\n};\n"], "names": [], "mappings": ";;AACA,MAAM,SAAS,GAAG,UAAU;AAC5B,MAAM,OAAO,GAAG,YAAY;AAC5B,MAAM,UAAU,GAAG,YAAY;AAC/B,YAAY,CAAC;AACb,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK;AACnC;AACA,CAAC,CAAC;AACF,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE;AACjC,EAAE,OAAO,QAAQ,CAAC,MAAM,EAAE,OAAO,IAAI,EAAE,CAAC;AACxC;AACA,SAAS,QAAQ,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;AACxC,EAAE,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;AAChC,IAAI,OAAO,EAAE;AACb;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,EAAE;AACvC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;AACnC,IAAI,OAAO,EAAE;AACb;AACA,EAAE,MAAM,GAAG,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;AACrD,EAAE,IAAI,CAAC,EAAE,EAAE;AACX,IAAI,OAAO,EAAE;AACb;AACA,EAAE,IAAI,GAAG,GAAG,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACxE,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACvB,GAAG,MAAM;AACT,IAAI,GAAG,IAAI,MAAM;AACjB;AACA,EAAE,MAAM,MAAM,GAAG,EAAE;AACnB,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,GAAG;AACpC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3B,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;AACtB,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,IAAI,KAAK;AAClC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;AAC3B,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;AAC5B,EAAE,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE;AACvC,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;AACzB,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACjC;AACA,EAAE,OAAO,GAAG;AACZ;;;;"}