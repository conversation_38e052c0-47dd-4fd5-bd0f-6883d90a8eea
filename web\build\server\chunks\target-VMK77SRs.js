import { X as sanitize_props, R as spread_props, a0 as slot } from './index3-CqUPEnZw.js';
import { I as Icon } from './Icon-A4vzmk-O.js';

function Target($$payload, $$props) {
  const $$sanitized_props = sanitize_props($$props);
  const iconNode = [
    [
      "circle",
      { "cx": "12", "cy": "12", "r": "10" }
    ],
    [
      "circle",
      { "cx": "12", "cy": "12", "r": "6" }
    ],
    [
      "circle",
      { "cx": "12", "cy": "12", "r": "2" }
    ]
  ];
  Icon($$payload, spread_props([
    { name: "target" },
    $$sanitized_props,
    {
      iconNode,
      children: ($$payload2) => {
        $$payload2.out += `<!---->`;
        slot($$payload2, $$props, "default", {}, null);
        $$payload2.out += `<!---->`;
      },
      $$slots: { default: true }
    }
  ]));
}

export { Target as T };
//# sourceMappingURL=target-VMK77SRs.js.map
