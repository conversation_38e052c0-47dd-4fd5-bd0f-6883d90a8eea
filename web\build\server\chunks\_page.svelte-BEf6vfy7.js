import { Q as bind_props, p as push, O as escape_html, q as pop, M as ensure_array_like, K as fallback, $ as attr_style, P as stringify } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { C as Card } from './card-D-TLkt4h.js';
import { C as Card_content } from './card-content-CrxB5iaZ.js';
import { C as Card_description } from './card-description-CMuO6f9m.js';
import { C as Card_header } from './card-header-BSbSWnCH.js';
import { C as Card_title } from './card-title-DNJv4RN2.js';
import { o as onDestroy } from './index-server-CezSOnuG.js';
import 'clsx';
import { B as Badge } from './badge-C9pSznab.js';
import { C as Chart_container, B as BarChart, a as Chart_tooltip } from './chart-tooltip-RMuhks-A.js';
import 'date-fns';
import './index-DwwMqnhu.js';
import { T as Triangle_alert } from './triangle-alert-DOwM8mYc.js';
import { C as Clock } from './clock-BHOPwoCS.js';
import { C as Circle_x } from './circle-x-DFm9GVXv.js';
import { C as Circle_check_big } from './circle-check-big-DBrIQZ7A.js';
import { A as Accordion_root } from './accordion-trigger-DwieKZVA.js';
import { M as MaintenanceAccordion } from './MaintenanceAccordion-hfzOCBt1.js';
import { H as History } from './history-T6kmbk1Q.js';
import './false-CRHihH2U.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import './index-DjwFQdT_.js';
import './html-FW6Ia4bL.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './clone-BRGVxGEr.js';
import 'svelte/store';
import './Icon-A4vzmk-O.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './chevron-down-xGjWLrZH.js';
import './StatusBar-DynsEX84.js';
import './circle-alert-BcRZk-Zc.js';
import './search-B0oHlTPS.js';
import './play-DKNYqs4c.js';
import './info-Ce09B-Yv.js';
import './message-square-D57Olt6y.js';

function ServiceTrendChart($$payload, $$props) {
  push();
  let {
    historyData = [],
    metric = "successRate",
    title = "Last 30 Days",
    height = 100
  } = $$props;
  const chartData = () => {
    if (!historyData || historyData.length === 0) {
      const mockData = [];
      const today = /* @__PURE__ */ new Date();
      for (let i = 29; i >= 0; i--) {
        const date = /* @__PURE__ */ new Date();
        date.setDate(today.getDate() - i);
        const rand = Math.random();
        let status = "operational";
        if (rand > 0.9) status = "outage";
        else if (rand > 0.8) status = "degraded";
        else if (rand > 0.7) status = "maintenance";
        mockData.push({
          date: formatDate(date.toISOString()),
          status,
          value: status === "operational" ? 100 : status === "degraded" ? 80 : status === "maintenance" ? 60 : 0,
          statusColor: getStatusColor(status)
        });
      }
      return mockData;
    }
    return historyData.map((item) => ({
      date: formatDate(item.date),
      status: item.status,
      value: item[metric] || 0,
      statusColor: getStatusColor(item.status)
    }));
  };
  function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", { month: "short", day: "numeric" });
  }
  function getStatusColor(status) {
    switch (status) {
      case "operational":
        return "var(--chart-3)";
      case "degraded":
        return "var(--chart-4)";
      case "outage":
        return "var(--chart-5)";
      case "maintenance":
        return "var(--chart-2)";
      default:
        return "hsl(var(--muted-foreground))";
    }
  }
  const chartConfig = {
    operational: { label: "Operational", color: "var(--chart-3)" },
    degraded: { label: "Degraded", color: "var(--chart-4)" },
    outage: { label: "Outage", color: "var(--chart-5)" },
    maintenance: { label: "Maintenance", color: "var(--chart-2)" }
  };
  $$payload.out += `<div class="w-full"><h4 class="mb-2 text-sm font-medium">${escape_html(title)}</h4> `;
  if (chartData.length === 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="flex items-center justify-center"${attr_style(`height: ${stringify(height)}px;`)}><div class="text-muted-foreground text-sm">No data available</div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<!---->`;
    Chart_container($$payload, {
      config: chartConfig,
      class: "w-full",
      style: `height: ${stringify(height)}px;`,
      children: ($$payload2) => {
        {
          let tooltip = function($$payload3) {
            $$payload3.out += `<!---->`;
            Chart_tooltip($$payload3, {});
            $$payload3.out += `<!---->`;
          };
          BarChart($$payload2, {
            data: chartData(),
            x: "date",
            axis: "x",
            series: [
              {
                key: "value",
                label: "Status",
                color: "var(--chart-1)"
              }
            ],
            props: {
              xAxis: { format: (d) => d.slice(0, 3) }
            },
            tooltip,
            $$slots: { tooltip: true }
          });
        }
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!----> `;
    if (historyData && historyData.length > 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="text-muted-foreground mt-1 flex justify-between text-xs"><span>${escape_html(formatDate(historyData[0].date))}</span> <span>${escape_html(formatDate(historyData[historyData.length - 1].date))}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]-->`;
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
function ServiceStatusCard($$payload, $$props) {
  push();
  let chartColor;
  let name = $$props["name"];
  let status = $$props["status"];
  let description = fallback($$props["description"], "");
  let historyData = fallback($$props["historyData"], () => [], true);
  let metrics = fallback($$props["metrics"], () => ({}), true);
  function getStatusIcon(status2) {
    switch (status2) {
      case "operational":
        return Circle_check_big;
      case "degraded":
        return Triangle_alert;
      case "outage":
        return Circle_x;
      case "maintenance":
        return Clock;
      default:
        return Triangle_alert;
    }
  }
  function getStatusColor(status2) {
    switch (status2) {
      case "operational":
        return "#4CAF50";
      case "degraded":
        return "#FFC107";
      case "outage":
        return "#F44336";
      case "maintenance":
        return "#2196F3";
      default:
        return "#9E9E9E";
    }
  }
  function getStatusVariant(status2) {
    switch (status2) {
      case "operational":
        return "success";
      case "degraded":
        return "warning";
      case "outage":
        return "destructive";
      case "maintenance":
        return "secondary";
      default:
        return "outline";
    }
  }
  function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }
  function formatTime(ms) {
    return ms >= 1e3 ? `${(ms / 1e3).toFixed(1)}s` : `${ms}ms`;
  }
  chartColor = getStatusColor(status);
  $$payload.out += `<div class="rounded-lg border p-4"><div class="mb-3 flex items-center justify-between"><div class="flex items-center gap-3"><div class="h-3 w-3 rounded-full"${attr_style(`background-color: ${stringify(chartColor)}`)}></div> <h3 class="font-medium">${escape_html(name)}</h3></div> `;
  Badge($$payload, {
    variant: getStatusVariant(status),
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      getStatusIcon(status)?.($$payload2, { class: "mr-1 h-3 w-3" });
      $$payload2.out += `<!----> ${escape_html(status.charAt(0).toUpperCase() + status.slice(1))}`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----></div> `;
  if (description) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="text-muted-foreground mb-3 text-sm">${escape_html(description)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> <div class="mb-4">`;
  ServiceTrendChart($$payload, {
    historyData,
    metric: "successRate",
    title: "Last 30 Days",
    height: 80
  });
  $$payload.out += `<!----></div> `;
  if (metrics && Object.keys(metrics).length > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="grid grid-cols-2 gap-2 text-xs">`;
    if (metrics.responseTime !== void 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex justify-between"><span class="text-muted-foreground">Response Time:</span> <span class="font-medium">${escape_html(formatTime(metrics.responseTime))}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (metrics.successRate !== void 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex justify-between"><span class="text-muted-foreground">Success Rate:</span> <span class="font-medium">${escape_html(metrics.successRate.toFixed(1))}%</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (metrics.errorRate !== void 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex justify-between"><span class="text-muted-foreground">Error Rate:</span> <span class="font-medium">${escape_html(metrics.errorRate.toFixed(1))}%</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (metrics.requestCount !== void 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex justify-between"><span class="text-muted-foreground">Requests:</span> <span class="font-medium">${escape_html(formatNumber(metrics.requestCount))}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (metrics.queueSize !== void 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex justify-between"><span class="text-muted-foreground">Queue Size:</span> <span class="font-medium">${escape_html(formatNumber(metrics.queueSize))}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (metrics.processingCount !== void 0) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<div class="flex justify-between"><span class="text-muted-foreground">Processing:</span> <span class="font-medium">${escape_html(formatNumber(metrics.processingCount))}</span></div>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div>`;
  bind_props($$props, {
    name,
    status,
    description,
    historyData,
    metrics
  });
  pop();
}
function SystemMetrics($$payload, $$props) {
  push();
  const { metrics } = $$props;
  $$payload.out += `<!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_content($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<div class="grid gap-4 md:grid-cols-4"><div class="flex flex-col items-center justify-center rounded-lg border p-4"><div class="text-2xl font-bold">${escape_html(metrics.uptime.toFixed(2))}%</div> <p class="text-muted-foreground text-sm">Uptime (30 days)</p></div> <div class="flex flex-col items-center justify-center rounded-lg border p-4"><div class="text-2xl font-bold">${escape_html(metrics.emailDeliveryRate.toFixed(1))}%</div> <p class="text-muted-foreground text-sm">Email Delivery Rate</p></div> <div class="flex flex-col items-center justify-center rounded-lg border p-4"><div class="text-2xl font-bold">${escape_html(metrics.apiResponseTime)}ms</div> <p class="text-muted-foreground text-sm">API Response Time</p></div> <div class="flex flex-col items-center justify-center rounded-lg border p-4"><div class="text-2xl font-bold">${escape_html(metrics.jobSuccessRate.toFixed(1))}%</div> <p class="text-muted-foreground text-sm">Job Success Rate</p></div></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!---->`;
  pop();
}
function MaintenanceEvents($$payload, $$props) {
  push();
  const { recentIncidents } = $$props;
  $$payload.out += `<div class="max-auto container mb-8 px-8"><h2 class="mb-4 text-xl font-semibold">Recent Notices</h2> `;
  if (recentIncidents.length > 0) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<!---->`;
    Accordion_root($$payload, {
      type: "multiple",
      class: "w-full space-y-4",
      children: ($$payload2) => {
        const each_array = ensure_array_like(recentIncidents);
        $$payload2.out += `<!--[-->`;
        for (let i = 0, $$length = each_array.length; i < $$length; i++) {
          let incident = each_array[i];
          MaintenanceAccordion($$payload2, { incident, index: i });
        }
        $$payload2.out += `<!--]-->`;
      },
      $$slots: { default: true }
    });
    $$payload.out += `<!---->`;
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<div class="rounded-lg border p-6 text-center"><div class="flex items-center justify-center"><div class="mr-2 h-4 w-4 rounded-full bg-green-500"></div> <p class="text-muted-foreground">No notices reported for the past 7 days</p></div></div>`;
  }
  $$payload.out += `<!--]--></div>`;
  pop();
}
function StatusPage($$payload, $$props) {
  push();
  const { pageData } = $$props;
  let services = [];
  let metrics = {
    uptime: 99.9,
    emailDeliveryRate: 0,
    apiResponseTime: 250,
    jobSuccessRate: 0
  };
  let lastUpdated = /* @__PURE__ */ new Date();
  let recentIncidents = [];
  function formatDate(date) {
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(date);
  }
  function getServiceMetrics(serviceName) {
    const defaultMetrics = {
      responseTime: 0,
      successRate: 0,
      requestCount: 0,
      errorRate: 0
    };
    const serviceHealth = pageData.serviceHealth || {};
    const serviceMapping = {
      Matches: { status: "operational", details: {} },
      Jobs: { status: "operational", details: {} },
      Tracker: { status: "operational", details: {} },
      Documents: { status: "operational", details: {} },
      Automation: { status: "operational", details: {} },
      System: { status: "operational", details: {} },
      Website: { status: "operational", details: {} }
    };
    if (serviceHealth.web) {
      serviceMapping.Website = serviceHealth.web;
    }
    if (serviceHealth.api) {
      serviceMapping.Jobs = serviceHealth.api;
      serviceMapping.Matches = serviceHealth.api;
    }
    if (serviceHealth.worker) {
      serviceMapping.Automation = serviceHealth.worker;
    }
    if (serviceHealth.database) {
      serviceMapping.System = serviceHealth.database;
      serviceMapping.Tracker = serviceHealth.database;
      serviceMapping.Documents = serviceHealth.database;
    }
    const serviceData = serviceMapping[serviceName] || {};
    const details = serviceData.details || {};
    return {
      responseTime: details.responseTime || defaultMetrics.responseTime,
      successRate: details.successRate || (serviceName === "Jobs" ? metrics.jobSuccessRate : serviceName === "System" ? serviceHealth.database?.status === "operational" ? 100 : 80 : serviceName === "Website" ? serviceHealth.web?.status === "operational" ? 100 : 80 : defaultMetrics.successRate),
      requestCount: details.requestCount || defaultMetrics.requestCount,
      errorRate: details.errorRate || (serviceName === "Jobs" ? 100 - metrics.jobSuccessRate : serviceName === "System" ? serviceHealth.database?.status === "operational" ? 0 : 20 : serviceName === "Website" ? serviceHealth.web?.status === "operational" ? 0 : 20 : defaultMetrics.errorRate),
      // Add additional metrics if available
      ...details.queueSize !== void 0 ? { queueSize: details.queueSize } : {},
      ...details.processingCount !== void 0 ? { processingCount: details.processingCount } : {},
      ...details.memoryUsage !== void 0 ? { memoryUsage: details.memoryUsage } : {},
      ...details.dbSizeMB !== void 0 ? { dbSizeMB: details.dbSizeMB } : {},
      ...details.activeConnections !== void 0 ? { activeConnections: details.activeConnections } : {},
      ...details.connectedClients !== void 0 ? { connectedClients: details.connectedClients } : {}
    };
  }
  onDestroy(() => {
  });
  $$payload.out += `<div class="flex flex-col gap-4"><div class="border-border flex items-center justify-between border-b p-6"><div class="flex flex-col"><h1 class="text-3xl font-bold">System Status</h1> <p class="text-muted-foreground">Current status of Hirli services</p></div> <div class="flex items-center gap-2 self-end"><p class="text-muted-foreground text-sm">Last updated: ${escape_html(formatDate(lastUpdated))}</p></div></div> `;
  SystemMetrics($$payload, { metrics });
  $$payload.out += `<!----> <!---->`;
  Card($$payload, {
    children: ($$payload2) => {
      $$payload2.out += `<!---->`;
      Card_header($$payload2, {
        children: ($$payload3) => {
          $$payload3.out += `<!---->`;
          Card_title($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Service Status`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!----> <!---->`;
          Card_description($$payload3, {
            children: ($$payload4) => {
              $$payload4.out += `<!---->Current status of core application services`;
            },
            $$slots: { default: true }
          });
          $$payload3.out += `<!---->`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!----> <!---->`;
      Card_content($$payload2, {
        children: ($$payload3) => {
          const each_array = ensure_array_like(services);
          $$payload3.out += `<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4"><!--[-->`;
          for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
            let service = each_array[$$index];
            ServiceStatusCard($$payload3, {
              name: service.name,
              status: service.status,
              description: service.description || "",
              historyData: pageData.serviceHistory?.[service.name] || [],
              metrics: getServiceMetrics(service.name)
            });
          }
          $$payload3.out += `<!--]--></div>`;
        },
        $$slots: { default: true }
      });
      $$payload2.out += `<!---->`;
    },
    $$slots: { default: true }
  });
  $$payload.out += `<!----> `;
  MaintenanceEvents($$payload, { recentIncidents });
  $$payload.out += `<!----></div>`;
  pop();
}
function _page($$payload, $$props) {
  let data = $$props["data"];
  SEO($$payload, {
    title: "System Status | Hirli",
    description: "Check the current status of Hirli services and systems.",
    keywords: "system status, service status, uptime, Hirli status"
  });
  $$payload.out += `<!----> `;
  StatusPage($$payload, { pageData: data });
  $$payload.out += `<!----> <div class="container mb-8"><a href="/system-status/history" class="flex w-full items-center justify-center rounded-lg border p-3 text-center hover:bg-gray-50 dark:hover:bg-gray-900">`;
  History($$payload, { class: "mr-2 h-4 w-4" });
  $$payload.out += `<!----> <span>View notice history</span></a></div>`;
  bind_props($$props, { data });
}

export { _page as default };
//# sourceMappingURL=_page.svelte-BEf6vfy7.js.map
