{"version": 3, "file": "92-BUWjUDkn.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/resources/_slug_/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/92.js"], "sourcesContent": ["import { h as getResourceBySlug } from \"../../../../chunks/client2.js\";\nimport { e as error } from \"../../../../chunks/index.js\";\nasync function load({ params }) {\n  const { slug } = params;\n  try {\n    const resource = await getResourceBySlug(slug);\n    if (!resource) {\n      throw error(404, \"Resource not found\");\n    }\n    return {\n      resource\n    };\n  } catch (err) {\n    console.error(\"Error loading resource:\", err);\n    throw error(500, \"Error loading resource\");\n  }\n}\nexport {\n  load\n};\n", "import * as server from '../entries/pages/resources/_slug_/_page.server.ts.js';\n\nexport const index = 92;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/resources/_slug_/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/resources/[slug]/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/92.aVwzhetP.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/jRvHGFcG.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/BMgaXnEE.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/DosGZj-c.js\",\"_app/immutable/chunks/Ce6y1v79.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/Cs0qIT7f.js\"];\nexport const stylesheets = [\"_app/immutable/assets/PortableText.DSHKgSkc.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,eAAe,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE;AAChC,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC;AAClD,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC;AAC5C;AACA,IAAI,OAAO;AACX,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC;AACjD,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,wBAAwB,CAAC;AAC9C;AACA;;;;;;;ACdY,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAmD,CAAC,EAAE;AAEjH,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACrkC,MAAC,WAAW,GAAG,CAAC,iDAAiD;AACjE,MAAC,KAAK,GAAG;;;;"}