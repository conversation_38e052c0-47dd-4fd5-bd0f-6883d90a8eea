import { p as push, O as escape_html, N as attr, M as ensure_array_like, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { u as urlFor } from './client2-BLTPQNYX.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import { A as Arrow_left } from './arrow-left-DyZbJRhp.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { M as Map_pin } from './map-pin-BBU2RKZV.js';
import { D as Download } from './download-CLn66Ope.js';
import { E as External_link } from './external-link-ZBG7aazC.js';
import 'clsx';
import './false-CRHihH2U.js';
import '@sanity/client';
import './sanityClient-BQ6Z_2a-.js';
import './html-FW6Ia4bL.js';
import './Icon-A4vzmk-O.js';

function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  const { pressRelease, relatedPressReleases } = data;
  function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    });
  }
  SEO($$payload, {
    title: pressRelease.title,
    description: pressRelease.excerpt || "",
    keywords: pressRelease.categories?.map((c) => c.title).join(", ") || ""
  });
  $$payload.out += `<!----> <div class="container mx-auto px-4 py-12"><div class="mb-8"><a href="/press/releases" class="text-primary inline-flex items-center text-sm hover:underline">`;
  Arrow_left($$payload, { class: "mr-1 h-4 w-4" });
  $$payload.out += `<!----> Back to Press Releases</a></div> <div class="grid gap-8 lg:grid-cols-3"><div class="lg:col-span-2"><article><header class="mb-8"><div class="mb-4 flex items-center gap-2">`;
  Calendar($$payload, { class: "h-5 w-5 text-gray-500" });
  $$payload.out += `<!----> <p class="text-muted-foreground text-sm">${escape_html(formatDate(pressRelease.publishedAt))}</p> `;
  if (pressRelease.location) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<span class="mx-1 text-gray-400">•</span> `;
    Map_pin($$payload, { class: "h-5 w-5 text-gray-500" });
    $$payload.out += `<!----> <p class="text-muted-foreground text-sm">${escape_html(pressRelease.location)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div> <h1 class="mb-4 text-3xl font-bold">${escape_html(pressRelease.title)}</h1> `;
  if (pressRelease.subtitle) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<p class="mb-4 text-xl text-gray-700">${escape_html(pressRelease.subtitle)}</p>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></header> `;
  if (pressRelease.mainImage) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mb-8 overflow-hidden rounded-lg"><img${attr("src", urlFor(pressRelease.mainImage, { width: 800 }))}${attr("alt", pressRelease.mainImage.alt || pressRelease.title)} class="w-full"/></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (pressRelease.body) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="prose prose-lg max-w-none">`;
    PortableText($$payload, { value: pressRelease.body });
    $$payload.out += `<!----></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (pressRelease.pdfAttachment) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-8"><a${attr("href", pressRelease.pdfAttachment.asset.url)} target="_blank" rel="noopener noreferrer" class="bg-primary hover:bg-primary/90 inline-flex items-center rounded-md px-4 py-2 text-white">`;
    Download($$payload, { class: "mr-2 h-5 w-5" });
    $$payload.out += `<!----> Download PDF</a></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (pressRelease.boilerplate) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-12 border-t pt-8"><h2 class="mb-4 text-xl font-semibold">About Hirli</h2> <p class="text-gray-700">${escape_html(pressRelease.boilerplate)}</p></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (pressRelease.contactInfo) {
    $$payload.out += "<!--[-->";
    $$payload.out += `<div class="mt-8 border-t pt-8"><h2 class="mb-4 text-xl font-semibold">Media Contact</h2> <div class="text-gray-700"><p class="font-medium">${escape_html(pressRelease.contactInfo.name)}</p> `;
    if (pressRelease.contactInfo.email) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p><a${attr("href", `mailto:${pressRelease.contactInfo.email}`)} class="text-primary hover:underline">${escape_html(pressRelease.contactInfo.email)}</a></p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--> `;
    if (pressRelease.contactInfo.phone) {
      $$payload.out += "<!--[-->";
      $$payload.out += `<p>${escape_html(pressRelease.contactInfo.phone)}</p>`;
    } else {
      $$payload.out += "<!--[!-->";
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></article></div> <div>`;
  if (relatedPressReleases && relatedPressReleases.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(relatedPressReleases);
    $$payload.out += `<div class="sticky top-8 rounded-lg bg-gray-50 p-6"><h3 class="mb-6 text-xl font-semibold">Related Press Releases</h3> <div class="space-y-6"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let release = each_array[$$index];
      $$payload.out += `<div class="border-b pb-4 last:border-0 last:pb-0"><p class="text-muted-foreground mb-2 text-sm">${escape_html(formatDate(release.publishedAt))}</p> <h4 class="mb-2 font-medium">${escape_html(release.title)}</h4> `;
      if (release.excerpt) {
        $$payload.out += "<!--[-->";
        $$payload.out += `<p class="mb-2 text-sm text-gray-600">${escape_html(release.excerpt.substring(0, 100))}...</p>`;
      } else {
        $$payload.out += "<!--[!-->";
      }
      $$payload.out += `<!--]--> <a${attr("href", `/press/releases/${release.slug.current}`)} class="text-primary inline-flex items-center text-sm font-medium hover:underline">Read More `;
      External_link($$payload, { class: "ml-1 h-3 w-3" });
      $$payload.out += `<!----></a></div>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></div></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-ht_zBWzz.js.map
