{"version": 3, "file": "stores-DSLMNPqo.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/stores.js"], "sourcesContent": ["import { a7 as getContext } from \"./index3.js\";\nimport \"./client.js\";\nconst getStores = () => {\n  const stores = getContext(\"__svelte__\");\n  return {\n    /** @type {typeof page} */\n    page: {\n      subscribe: stores.page.subscribe\n    },\n    /** @type {typeof navigating} */\n    navigating: {\n      subscribe: stores.navigating.subscribe\n    },\n    /** @type {typeof updated} */\n    updated: stores.updated\n  };\n};\nconst page = {\n  subscribe(fn) {\n    const store = getStores().page;\n    return store.subscribe(fn);\n  }\n};\nconst navigating = {\n  subscribe(fn) {\n    const store = getStores().navigating;\n    return store.subscribe(fn);\n  }\n};\nexport {\n  getStores as g,\n  navigating as n,\n  page as p\n};\n"], "names": [], "mappings": ";;AAEK,MAAC,SAAS,GAAG,MAAM;AACxB,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC,YAAY,CAAC;AACzC,EAAE,OAAO;AACT;AACA,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,MAAM,CAAC,IAAI,CAAC;AAC7B,KAAK;AACL;AACA,IAAI,UAAU,EAAE;AAChB,MAAM,SAAS,EAAE,MAAM,CAAC,UAAU,CAAC;AACnC,KAAK;AACL;AACA,IAAI,OAAO,EAAE,MAAM,CAAC;AACpB,GAAG;AACH;AACK,MAAC,IAAI,GAAG;AACb,EAAE,SAAS,CAAC,EAAE,EAAE;AAChB,IAAI,MAAM,KAAK,GAAG,SAAS,EAAE,CAAC,IAAI;AAClC,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;AAC9B;AACA;AACK,MAAC,UAAU,GAAG;AACnB,EAAE,SAAS,CAAC,EAAE,EAAE;AAChB,IAAI,MAAM,KAAK,GAAG,SAAS,EAAE,CAAC,UAAU;AACxC,IAAI,OAAO,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC;AAC9B;AACA;;;;"}