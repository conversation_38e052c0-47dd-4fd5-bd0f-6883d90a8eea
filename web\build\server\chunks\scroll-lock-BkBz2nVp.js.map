{"version": 3, "file": "scroll-lock-BkBz2nVp.js", "sources": ["../../../node_modules/tabbable/dist/index.esm.js", "../../../.svelte-kit/adapter-node/chunks/scroll-lock.js"], "sourcesContent": ["/*!\n* tabbable 6.2.0\n* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE\n*/\n// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nvar candidateSelectors = ['input:not([inert])', 'select:not([inert])', 'textarea:not([inert])', 'a[href]:not([inert])', 'button:not([inert])', '[tabindex]:not(slot):not([inert])', 'audio[controls]:not([inert])', 'video[controls]:not([inert])', '[contenteditable]:not([contenteditable=\"false\"]):not([inert])', 'details>summary:first-of-type:not([inert])', 'details:not([inert])'];\nvar candidateSelector = /* #__PURE__ */candidateSelectors.join(',');\nvar NoElement = typeof Element === 'undefined';\nvar matches = NoElement ? function () {} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\nvar getRootNode = !NoElement && Element.prototype.getRootNode ? function (element) {\n  var _element$getRootNode;\n  return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);\n} : function (element) {\n  return element === null || element === void 0 ? void 0 : element.ownerDocument;\n};\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nvar isInert = function isInert(node, lookUp) {\n  var _node$getAttribute;\n  if (lookUp === void 0) {\n    lookUp = true;\n  }\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'inert');\n  var inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  var result = inert || lookUp && node && isInert(node.parentNode); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nvar isContentEditable = function isContentEditable(node) {\n  var _node$getAttribute2;\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, 'contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nvar getCandidates = function getCandidates(el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nvar getCandidatesIteratively = function getCandidatesIteratively(elements, includeContainer, options) {\n  var candidates = [];\n  var elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    var element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      var assigned = element.assignedElements();\n      var content = assigned.length ? assigned : element.children;\n      var nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push.apply(candidates, nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates\n        });\n      }\n    } else {\n      // check candidate element\n      var validCandidate = matches.call(element, candidateSelector);\n      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      var shadowRoot = element.shadowRoot ||\n      // check for an undisclosed shadow\n      typeof options.getShadowRoot === 'function' && options.getShadowRoot(element);\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        var _nestedCandidates = getCandidatesIteratively(shadowRoot === true ? element.children : shadowRoot.children, true, options);\n        if (options.flatten) {\n          candidates.push.apply(candidates, _nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: _nestedCandidates\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift.apply(elementsToCheck, element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nvar hasTabIndex = function hasTabIndex(node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nvar getTabIndex = function getTabIndex(node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if ((/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && !hasTabIndex(node)) {\n      return 0;\n    }\n  }\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nvar getSortOrderTabIndex = function getSortOrderTabIndex(node, isScope) {\n  var tabIndex = getTabIndex(node);\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n  return tabIndex;\n};\nvar sortOrderedTabbables = function sortOrderedTabbables(a, b) {\n  return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;\n};\nvar isInput = function isInput(node) {\n  return node.tagName === 'INPUT';\n};\nvar isHiddenInput = function isHiddenInput(node) {\n  return isInput(node) && node.type === 'hidden';\n};\nvar isDetailsWithSummary = function isDetailsWithSummary(node) {\n  var r = node.tagName === 'DETAILS' && Array.prototype.slice.apply(node.children).some(function (child) {\n    return child.tagName === 'SUMMARY';\n  });\n  return r;\n};\nvar getCheckedRadio = function getCheckedRadio(nodes, form) {\n  for (var i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\nvar isTabbableRadio = function isTabbableRadio(node) {\n  if (!node.name) {\n    return true;\n  }\n  var radioScope = node.form || getRootNode(node);\n  var queryRadios = function queryRadios(name) {\n    return radioScope.querySelectorAll('input[type=\"radio\"][name=\"' + name + '\"]');\n  };\n  var radioSet;\n  if (typeof window !== 'undefined' && typeof window.CSS !== 'undefined' && typeof window.CSS.escape === 'function') {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error('Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s', err.message);\n      return false;\n    }\n  }\n  var checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\nvar isRadio = function isRadio(node) {\n  return isInput(node) && node.type === 'radio';\n};\nvar isNonTabbableRadio = function isNonTabbableRadio(node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nvar isNodeAttached = function isNodeAttached(node) {\n  var _nodeRoot;\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  var nodeRoot = node && getRootNode(node);\n  var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  var attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;\n    attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));\n    while (!attached && nodeRootHost) {\n      var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;\n      attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));\n    }\n  }\n  return attached;\n};\nvar isZeroArea = function isZeroArea(node) {\n  var _node$getBoundingClie = node.getBoundingClientRect(),\n    width = _node$getBoundingClie.width,\n    height = _node$getBoundingClie.height;\n  return width === 0 && height === 0;\n};\nvar isHidden = function isHidden(node, _ref) {\n  var displayCheck = _ref.displayCheck,\n    getShadowRoot = _ref.getShadowRoot;\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n  var isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n  if (!displayCheck || displayCheck === 'full' || displayCheck === 'legacy-full') {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      var originalNode = node;\n      while (node) {\n        var parentElement = node.parentElement;\n        var rootNode = getRootNode(node);\n        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nvar isDisabledFromFieldset = function isDisabledFromFieldset(node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    var parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (var i = 0; i < parentNode.children.length; i++) {\n          var child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *') ? true : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\nvar isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable(options, node) {\n  if (node.disabled ||\n  // we must do an inert look up to filter out any elements inside an inert ancestor\n  //  because we're limited in the type of selectors we can use in JSDom (see related\n  //  note related to `candidateSelectors`)\n  isInert(node) || isHiddenInput(node) || isHidden(node, options) ||\n  // For a details element with a summary, the summary element gets the focus\n  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {\n    return false;\n  }\n  return true;\n};\nvar isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable(options, node) {\n  if (isNonTabbableRadio(node) || getTabIndex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {\n    return false;\n  }\n  return true;\n};\nvar isValidShadowRootTabbable = function isValidShadowRootTabbable(shadowHostNode) {\n  var tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nvar sortByOrder = function sortByOrder(candidates) {\n  var regularTabbables = [];\n  var orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    var isScope = !!item.scopeParent;\n    var element = isScope ? item.scopeParent : item;\n    var candidateTabindex = getSortOrderTabIndex(element, isScope);\n    var elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements\n      });\n    }\n  });\n  return orderedTabbables.sort(sortOrderedTabbables).reduce(function (acc, sortable) {\n    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);\n    return acc;\n  }, []).concat(regularTabbables);\n};\nvar tabbable = function tabbable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));\n  }\n  return sortByOrder(candidates);\n};\nvar focusable = function focusable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorFocusable.bind(null, options));\n  }\n  return candidates;\n};\nvar isTabbable = function isTabbable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\nvar focusableCandidateSelector = /* #__PURE__ */candidateSelectors.concat('iframe').join(',');\nvar isFocusable = function isFocusable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\nexport { focusable, getTabIndex, isFocusable, isTabbable, tabbable };\n//# sourceMappingURL=index.esm.js.map\n", "import \"clsx\";\nimport { w as push, a5 as getAllContexts, y as pop } from \"./index3.js\";\nimport { m as mount, u as unmount } from \"./index-server.js\";\nimport { w as watch, b as box } from \"./watch.svelte.js\";\nimport { c as isBrowser, i as isElement, d as isSelectableInput, e as isElementHidden, a as isHTMLElement } from \"./is.js\";\nimport \"style-to-object\";\nimport { o as on } from \"./events.js\";\nimport { u as useRefById, e as executeCallbacks, c as composeHandlers } from \"./use-ref-by-id.svelte.js\";\nimport { a as afterTick } from \"./after-tick.js\";\nimport { n as noop } from \"./noop.js\";\nimport { l as ESCAPE, T as TAB } from \"./kbd-constants.js\";\nimport { C as Context } from \"./context.js\";\nimport { u as useId } from \"./use-id.js\";\nimport { isTabbable } from \"tabbable\";\nfunction afterSleep(ms, cb) {\n  return setTimeout(cb, ms);\n}\nfunction Portal($$payload, $$props) {\n  push();\n  let { to = \"body\", children, disabled } = $$props;\n  getAllContexts();\n  let target = getTarget();\n  function getTarget() {\n    if (!isBrowser || disabled) return null;\n    let localTarget = null;\n    if (typeof to === \"string\") {\n      localTarget = document.querySelector(to);\n    } else if (to instanceof HTMLElement || to instanceof DocumentFragment) {\n      localTarget = to;\n    } else ;\n    return localTarget;\n  }\n  let instance;\n  function unmountInstance() {\n    if (instance) {\n      unmount();\n      instance = null;\n    }\n  }\n  watch([() => target, () => disabled], ([target2, disabled2]) => {\n    if (!target2 || disabled2) {\n      unmountInstance();\n      return;\n    }\n    instance = mount();\n    return () => {\n      unmountInstance();\n    };\n  });\n  if (disabled) {\n    $$payload.out += \"<!--[-->\";\n    children?.($$payload);\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]-->`;\n  pop();\n}\nfunction addEventListener(target, event, handler, options) {\n  const events = Array.isArray(event) ? event : [event];\n  events.forEach((_event) => target.addEventListener(_event, handler, options));\n  return () => {\n    events.forEach((_event) => target.removeEventListener(_event, handler, options));\n  };\n}\nclass CustomEventDispatcher {\n  eventName;\n  options;\n  constructor(eventName, options = { bubbles: true, cancelable: true }) {\n    this.eventName = eventName;\n    this.options = options;\n  }\n  createEvent(detail) {\n    return new CustomEvent(this.eventName, {\n      ...this.options,\n      detail\n    });\n  }\n  dispatch(element, detail) {\n    const event = this.createEvent(detail);\n    element.dispatchEvent(event);\n    return event;\n  }\n  listen(element, callback, options) {\n    const handler = (event) => {\n      callback(event);\n    };\n    return on(element, this.eventName, handler, options);\n  }\n}\nfunction debounce(fn, wait = 500) {\n  let timeout = null;\n  const debounced = (...args) => {\n    if (timeout !== null) {\n      clearTimeout(timeout);\n    }\n    timeout = setTimeout(() => {\n      fn(...args);\n    }, wait);\n  };\n  debounced.destroy = () => {\n    if (timeout !== null) {\n      clearTimeout(timeout);\n      timeout = null;\n    }\n  };\n  return debounced;\n}\nfunction isOrContainsTarget(node, target) {\n  return node === target || node.contains(target);\n}\nfunction getOwnerDocument(el) {\n  return el?.ownerDocument ?? document;\n}\nfunction getDocument(element) {\n  return element?.ownerDocument ?? document;\n}\nfunction getFirstNonCommentChild(element) {\n  if (!element)\n    return null;\n  for (const child of element.childNodes) {\n    if (child.nodeType !== Node.COMMENT_NODE) {\n      return child;\n    }\n  }\n  return null;\n}\nfunction isClickTrulyOutside(event, contentNode) {\n  const { clientX, clientY } = event;\n  const rect = contentNode.getBoundingClientRect();\n  return clientX < rect.left || clientX > rect.right || clientY < rect.top || clientY > rect.bottom;\n}\nglobalThis.bitsDismissableLayers ??= /* @__PURE__ */ new Map();\nclass DismissibleLayerState {\n  opts;\n  #interactOutsideProp;\n  #behaviorType;\n  #interceptedEvents = { pointerdown: false };\n  #isResponsibleLayer = false;\n  #isFocusInsideDOMTree = false;\n  node = box(null);\n  #documentObj = void 0;\n  #onFocusOutside;\n  currNode = null;\n  #unsubClickListener = noop;\n  constructor(opts) {\n    this.opts = opts;\n    useRefById({\n      id: opts.id,\n      ref: this.node,\n      deps: () => opts.enabled.current,\n      onRefChange: (node) => {\n        this.currNode = node;\n      }\n    });\n    this.#behaviorType = opts.interactOutsideBehavior;\n    this.#interactOutsideProp = opts.onInteractOutside;\n    this.#onFocusOutside = opts.onFocusOutside;\n    let unsubEvents = noop;\n    const cleanup = () => {\n      this.#resetState();\n      globalThis.bitsDismissableLayers.delete(this);\n      this.#handleInteractOutside.destroy();\n      unsubEvents();\n    };\n    watch(\n      [\n        () => this.opts.enabled.current,\n        () => this.currNode\n      ],\n      ([enabled, currNode]) => {\n        if (!enabled || !currNode) return;\n        afterSleep(1, () => {\n          if (!this.currNode) return;\n          globalThis.bitsDismissableLayers.set(this, this.#behaviorType);\n          unsubEvents();\n          unsubEvents = this.#addEventListeners();\n        });\n        return cleanup;\n      }\n    );\n  }\n  #handleFocus = (event) => {\n    if (event.defaultPrevented) return;\n    if (!this.currNode) return;\n    afterTick(() => {\n      if (!this.currNode || this.#isTargetWithinLayer(event.target)) return;\n      if (event.target && !this.#isFocusInsideDOMTree) {\n        this.#onFocusOutside.current?.(event);\n      }\n    });\n  };\n  #addEventListeners() {\n    return executeCallbacks(\n      /**\n      * CAPTURE INTERACTION START\n      * mark interaction-start event as intercepted.\n      * mark responsible layer during interaction start\n      * to avoid checking if is responsible layer during interaction end\n      * when a new floating element may have been opened.\n      */\n      on(this.#documentObj, \"pointerdown\", executeCallbacks(this.#markInterceptedEvent, this.#markResponsibleLayer), { capture: true }),\n      /**\n      * BUBBLE INTERACTION START\n      * Mark interaction-start event as non-intercepted. Debounce `onInteractOutsideStart`\n      * to avoid prematurely checking if other events were intercepted.\n      */\n      on(this.#documentObj, \"pointerdown\", executeCallbacks(this.#markNonInterceptedEvent, this.#handleInteractOutside)),\n      /**\n      * HANDLE FOCUS OUTSIDE\n      */\n      on(this.#documentObj, \"focusin\", this.#handleFocus)\n    );\n  }\n  #handleDismiss = (e) => {\n    let event = e;\n    if (event.defaultPrevented) {\n      event = createWrappedEvent(e);\n    }\n    this.#interactOutsideProp.current(e);\n  };\n  #handleInteractOutside = debounce(\n    (e) => {\n      if (!this.currNode) {\n        this.#unsubClickListener();\n        return;\n      }\n      const isEventValid = this.opts.isValidEvent.current(e, this.currNode) || isValidEvent(e, this.currNode);\n      if (!this.#isResponsibleLayer || this.#isAnyEventIntercepted() || !isEventValid) {\n        this.#unsubClickListener();\n        return;\n      }\n      let event = e;\n      if (event.defaultPrevented) {\n        event = createWrappedEvent(event);\n      }\n      if (this.#behaviorType.current !== \"close\" && this.#behaviorType.current !== \"defer-otherwise-close\") {\n        this.#unsubClickListener();\n        return;\n      }\n      if (e.pointerType === \"touch\") {\n        this.#unsubClickListener();\n        this.#unsubClickListener = addEventListener(this.#documentObj, \"click\", this.#handleDismiss, { once: true });\n      } else {\n        this.#interactOutsideProp.current(event);\n      }\n    },\n    10\n  );\n  #markInterceptedEvent = (e) => {\n    this.#interceptedEvents[e.type] = true;\n  };\n  #markNonInterceptedEvent = (e) => {\n    this.#interceptedEvents[e.type] = false;\n  };\n  #markResponsibleLayer = () => {\n    if (!this.node.current) return;\n    this.#isResponsibleLayer = isResponsibleLayer(this.node.current);\n  };\n  #isTargetWithinLayer = (target) => {\n    if (!this.node.current) return false;\n    return isOrContainsTarget(this.node.current, target);\n  };\n  #resetState = debounce(\n    () => {\n      for (const eventType in this.#interceptedEvents) {\n        this.#interceptedEvents[eventType] = false;\n      }\n      this.#isResponsibleLayer = false;\n    },\n    20\n  );\n  #isAnyEventIntercepted() {\n    const i = Object.values(this.#interceptedEvents).some(Boolean);\n    return i;\n  }\n  #onfocuscapture = () => {\n    this.#isFocusInsideDOMTree = true;\n  };\n  #onblurcapture = () => {\n    this.#isFocusInsideDOMTree = false;\n  };\n  props = {\n    onfocuscapture: this.#onfocuscapture,\n    onblurcapture: this.#onblurcapture\n  };\n}\nfunction useDismissibleLayer(props) {\n  return new DismissibleLayerState(props);\n}\nfunction getTopMostLayer(layersArr) {\n  return layersArr.findLast(([_, { current: behaviorType }]) => behaviorType === \"close\" || behaviorType === \"ignore\");\n}\nfunction isResponsibleLayer(node) {\n  const layersArr = [...globalThis.bitsDismissableLayers];\n  const topMostLayer = getTopMostLayer(layersArr);\n  if (topMostLayer) return topMostLayer[0].node.current === node;\n  const [firstLayerNode] = layersArr[0];\n  return firstLayerNode.node.current === node;\n}\nfunction isValidEvent(e, node) {\n  if (\"button\" in e && e.button > 0) return false;\n  const target = e.target;\n  if (!isElement(target)) return false;\n  const ownerDocument = getOwnerDocument(target);\n  const isValid = ownerDocument.documentElement.contains(target) && !isOrContainsTarget(node, target) && isClickTrulyOutside(e, node);\n  return isValid;\n}\nfunction createWrappedEvent(e) {\n  const capturedCurrentTarget = e.currentTarget;\n  const capturedTarget = e.target;\n  let newEvent;\n  if (e instanceof PointerEvent) {\n    newEvent = new PointerEvent(e.type, e);\n  } else {\n    newEvent = new PointerEvent(\"pointerdown\", e);\n  }\n  let isPrevented = false;\n  const wrappedEvent = new Proxy(newEvent, {\n    get: (target, prop) => {\n      if (prop === \"currentTarget\") {\n        return capturedCurrentTarget;\n      }\n      if (prop === \"target\") {\n        return capturedTarget;\n      }\n      if (prop === \"preventDefault\") {\n        return () => {\n          isPrevented = true;\n          if (typeof target.preventDefault === \"function\") {\n            target.preventDefault();\n          }\n        };\n      }\n      if (prop === \"defaultPrevented\") {\n        return isPrevented;\n      }\n      if (prop in target) {\n        return target[prop];\n      }\n      return e[prop];\n    }\n  });\n  return wrappedEvent;\n}\nfunction Dismissible_layer($$payload, $$props) {\n  push();\n  let {\n    interactOutsideBehavior = \"close\",\n    onInteractOutside = noop,\n    onFocusOutside = noop,\n    id,\n    children,\n    enabled,\n    isValidEvent: isValidEvent2 = () => false\n  } = $$props;\n  const dismissibleLayerState = useDismissibleLayer({\n    id: box.with(() => id),\n    interactOutsideBehavior: box.with(() => interactOutsideBehavior),\n    onInteractOutside: box.with(() => onInteractOutside),\n    enabled: box.with(() => enabled),\n    onFocusOutside: box.with(() => onFocusOutside),\n    isValidEvent: box.with(() => isValidEvent2)\n  });\n  children?.($$payload, { props: dismissibleLayerState.props });\n  $$payload.out += `<!---->`;\n  pop();\n}\nglobalThis.bitsEscapeLayers ??= /* @__PURE__ */ new Map();\nclass EscapeLayerState {\n  opts;\n  constructor(opts) {\n    this.opts = opts;\n    let unsubEvents = noop;\n    watch(() => opts.enabled.current, (enabled) => {\n      if (enabled) {\n        globalThis.bitsEscapeLayers.set(this, opts.escapeKeydownBehavior);\n        unsubEvents = this.#addEventListener();\n      }\n      return () => {\n        unsubEvents();\n        globalThis.bitsEscapeLayers.delete(this);\n      };\n    });\n  }\n  #addEventListener = () => {\n    return on(document, \"keydown\", this.#onkeydown, { passive: false });\n  };\n  #onkeydown = (e) => {\n    if (e.key !== ESCAPE || !isResponsibleEscapeLayer(this)) return;\n    const clonedEvent = new KeyboardEvent(e.type, e);\n    e.preventDefault();\n    const behaviorType = this.opts.escapeKeydownBehavior.current;\n    if (behaviorType !== \"close\" && behaviorType !== \"defer-otherwise-close\") return;\n    this.opts.onEscapeKeydown.current(clonedEvent);\n  };\n}\nfunction useEscapeLayer(props) {\n  return new EscapeLayerState(props);\n}\nfunction isResponsibleEscapeLayer(instance) {\n  const layersArr = [...globalThis.bitsEscapeLayers];\n  const topMostLayer = layersArr.findLast(([_, { current: behaviorType }]) => behaviorType === \"close\" || behaviorType === \"ignore\");\n  if (topMostLayer) return topMostLayer[0] === instance;\n  const [firstLayerNode] = layersArr[0];\n  return firstLayerNode === instance;\n}\nfunction Escape_layer($$payload, $$props) {\n  push();\n  let {\n    escapeKeydownBehavior = \"close\",\n    onEscapeKeydown = noop,\n    children,\n    enabled\n  } = $$props;\n  useEscapeLayer({\n    escapeKeydownBehavior: box.with(() => escapeKeydownBehavior),\n    onEscapeKeydown: box.with(() => onEscapeKeydown),\n    enabled: box.with(() => enabled)\n  });\n  children?.($$payload);\n  $$payload.out += `<!---->`;\n  pop();\n}\nconst focusStack = box([]);\nfunction createFocusScopeStack() {\n  return {\n    add(focusScope) {\n      const activeFocusScope = focusStack.current[0];\n      if (activeFocusScope && focusScope.id !== activeFocusScope.id) {\n        activeFocusScope.pause();\n      }\n      focusStack.current = removeFromFocusScopeArray(focusStack.current, focusScope);\n      focusStack.current.unshift(focusScope);\n    },\n    remove(focusScope) {\n      focusStack.current = removeFromFocusScopeArray(focusStack.current, focusScope);\n      focusStack.current[0]?.resume();\n    },\n    get current() {\n      return focusStack.current;\n    }\n  };\n}\nfunction createFocusScopeAPI() {\n  let paused = false;\n  let isHandlingFocus = false;\n  return {\n    id: useId(),\n    get paused() {\n      return paused;\n    },\n    get isHandlingFocus() {\n      return isHandlingFocus;\n    },\n    set isHandlingFocus(value) {\n      isHandlingFocus = value;\n    },\n    pause() {\n      paused = true;\n    },\n    resume() {\n      paused = false;\n    }\n  };\n}\nfunction removeFromFocusScopeArray(arr, item) {\n  return [...arr].filter((i) => i.id !== item.id);\n}\nfunction removeLinks(items) {\n  return items.filter((item) => item.tagName !== \"A\");\n}\nfunction focus(element, { select = false } = {}) {\n  if (!(element && element.focus))\n    return;\n  if (document.activeElement === element)\n    return;\n  const previouslyFocusedElement = document.activeElement;\n  element.focus({ preventScroll: true });\n  if (element !== previouslyFocusedElement && isSelectableInput(element) && select) {\n    element.select();\n  }\n}\nfunction focusFirst(candidates, { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement)\n      return true;\n  }\n}\nfunction findVisible(elements, container) {\n  for (const element of elements) {\n    if (!isElementHidden(element, container))\n      return element;\n  }\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput)\n        return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode())\n    nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction getTabbableEdges(container) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last];\n}\nconst AutoFocusOnMountEvent = new CustomEventDispatcher(\"focusScope.autoFocusOnMount\", { bubbles: false, cancelable: true });\nconst AutoFocusOnDestroyEvent = new CustomEventDispatcher(\"focusScope.autoFocusOnDestroy\", { bubbles: false, cancelable: true });\nconst FocusScopeContext = new Context(\"FocusScope\");\nfunction useFocusScope({\n  id,\n  loop,\n  enabled,\n  onOpenAutoFocus,\n  onCloseAutoFocus,\n  forceMount\n}) {\n  const focusScopeStack = createFocusScopeStack();\n  const focusScope = createFocusScopeAPI();\n  const ref = box(null);\n  const ctx = FocusScopeContext.getOr({ ignoreCloseAutoFocus: false });\n  let lastFocusedElement = null;\n  useRefById({ id, ref, deps: () => enabled.current });\n  function manageFocus(event) {\n    if (focusScope.paused || !ref.current || focusScope.isHandlingFocus) return;\n    focusScope.isHandlingFocus = true;\n    try {\n      const target = event.target;\n      if (!isHTMLElement(target)) return;\n      const isWithinActiveScope = ref.current.contains(target);\n      if (event.type === \"focusin\") {\n        if (isWithinActiveScope) {\n          lastFocusedElement = target;\n        } else {\n          if (ctx.ignoreCloseAutoFocus) return;\n          focus(lastFocusedElement, { select: true });\n        }\n      } else if (event.type === \"focusout\") {\n        if (!isWithinActiveScope && !ctx.ignoreCloseAutoFocus) {\n          focus(lastFocusedElement, { select: true });\n        }\n      }\n    } finally {\n      focusScope.isHandlingFocus = false;\n    }\n  }\n  function handleMutations(mutations) {\n    if (!lastFocusedElement || !ref.current) return;\n    let elementWasRemoved = false;\n    for (const mutation of mutations) {\n      if (mutation.type === \"childList\" && mutation.removedNodes.length > 0) {\n        for (const removedNode of mutation.removedNodes) {\n          if (removedNode === lastFocusedElement) {\n            elementWasRemoved = true;\n            break;\n          }\n          if (removedNode.nodeType === Node.ELEMENT_NODE && removedNode.contains(lastFocusedElement)) {\n            elementWasRemoved = true;\n            break;\n          }\n        }\n      }\n      if (elementWasRemoved) break;\n    }\n    if (elementWasRemoved && ref.current && !ref.current.contains(document.activeElement)) {\n      focus(ref.current);\n    }\n  }\n  watch([() => ref.current, () => enabled.current], ([container, enabled2]) => {\n    if (!container || !enabled2) return;\n    const removeEvents = executeCallbacks(on(document, \"focusin\", manageFocus), on(document, \"focusout\", manageFocus));\n    const mutationObserver = new MutationObserver(handleMutations);\n    mutationObserver.observe(container, {\n      childList: true,\n      subtree: true,\n      attributes: false\n    });\n    return () => {\n      removeEvents();\n      mutationObserver.disconnect();\n    };\n  });\n  watch([() => forceMount.current, () => ref.current], ([forceMount2, container]) => {\n    if (forceMount2) return;\n    const prevFocusedElement = document.activeElement;\n    handleOpen(container, prevFocusedElement);\n    return () => {\n      if (!container) return;\n      handleClose(prevFocusedElement);\n    };\n  });\n  watch(\n    [\n      () => forceMount.current,\n      () => ref.current,\n      () => enabled.current\n    ],\n    ([forceMount2, container]) => {\n      if (!forceMount2) return;\n      const prevFocusedElement = document.activeElement;\n      handleOpen(container, prevFocusedElement);\n      return () => {\n        if (!container) return;\n        handleClose(prevFocusedElement);\n      };\n    }\n  );\n  function handleOpen(container, prevFocusedElement) {\n    if (!container) container = document.getElementById(id.current);\n    if (!container || !enabled.current) return;\n    focusScopeStack.add(focusScope);\n    const hasFocusedCandidate = container.contains(prevFocusedElement);\n    if (!hasFocusedCandidate) {\n      const mountEvent = AutoFocusOnMountEvent.createEvent();\n      onOpenAutoFocus.current(mountEvent);\n      if (!mountEvent.defaultPrevented) {\n        afterTick(() => {\n          if (!container) return;\n          const result = focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (!result) focus(container);\n        });\n      }\n    }\n  }\n  function handleClose(prevFocusedElement) {\n    const destroyEvent = AutoFocusOnDestroyEvent.createEvent();\n    onCloseAutoFocus.current?.(destroyEvent);\n    const shouldIgnore = ctx.ignoreCloseAutoFocus;\n    afterSleep(0, () => {\n      if (!destroyEvent.defaultPrevented && prevFocusedElement && !shouldIgnore) {\n        focus(isTabbable(prevFocusedElement) ? prevFocusedElement : document.body, { select: true });\n      }\n      focusScopeStack.remove(focusScope);\n    });\n  }\n  function handleKeydown(e) {\n    if (!enabled.current) return;\n    if (!loop.current && !enabled.current) return;\n    if (focusScope.paused) return;\n    const isTabKey = e.key === TAB && !e.ctrlKey && !e.altKey && !e.metaKey;\n    const focusedElement = document.activeElement;\n    if (!(isTabKey && focusedElement)) return;\n    const container = ref.current;\n    if (!container) return;\n    const [first, last] = getTabbableEdges(container);\n    const hasTabbableElementsInside = first && last;\n    if (!hasTabbableElementsInside) {\n      if (focusedElement === container) {\n        e.preventDefault();\n      }\n    } else {\n      if (!e.shiftKey && focusedElement === last) {\n        e.preventDefault();\n        if (loop.current) focus(first, { select: true });\n      } else if (e.shiftKey && focusedElement === first) {\n        e.preventDefault();\n        if (loop.current) focus(last, { select: true });\n      }\n    }\n  }\n  const props = (() => ({\n    id: id.current,\n    tabindex: -1,\n    onkeydown: handleKeydown\n  }))();\n  return {\n    get props() {\n      return props;\n    }\n  };\n}\nfunction Focus_scope($$payload, $$props) {\n  push();\n  let {\n    id,\n    trapFocus = false,\n    loop = false,\n    onCloseAutoFocus = noop,\n    onOpenAutoFocus = noop,\n    focusScope,\n    forceMount = false\n  } = $$props;\n  const focusScopeState = useFocusScope({\n    enabled: box.with(() => trapFocus),\n    loop: box.with(() => loop),\n    onCloseAutoFocus: box.with(() => onCloseAutoFocus),\n    onOpenAutoFocus: box.with(() => onOpenAutoFocus),\n    id: box.with(() => id),\n    forceMount: box.with(() => forceMount)\n  });\n  focusScope?.($$payload, { props: focusScopeState.props });\n  $$payload.out += `<!---->`;\n  pop();\n}\nglobalThis.bitsTextSelectionLayers ??= /* @__PURE__ */ new Map();\nclass TextSelectionLayerState {\n  opts;\n  #unsubSelectionLock = noop;\n  #ref = box(null);\n  constructor(opts) {\n    this.opts = opts;\n    useRefById({\n      id: opts.id,\n      ref: this.#ref,\n      deps: () => this.opts.enabled.current\n    });\n    let unsubEvents = noop;\n    watch(() => this.opts.enabled.current, (isEnabled) => {\n      if (isEnabled) {\n        globalThis.bitsTextSelectionLayers.set(this, this.opts.enabled);\n        unsubEvents();\n        unsubEvents = this.#addEventListeners();\n      }\n      return () => {\n        unsubEvents();\n        this.#resetSelectionLock();\n        globalThis.bitsTextSelectionLayers.delete(this);\n      };\n    });\n  }\n  #addEventListeners() {\n    return executeCallbacks(on(document, \"pointerdown\", this.#pointerdown), on(document, \"pointerup\", composeHandlers(this.#resetSelectionLock, this.opts.onPointerUp.current)));\n  }\n  #pointerdown = (e) => {\n    const node = this.#ref.current;\n    const target = e.target;\n    if (!isHTMLElement(node) || !isHTMLElement(target) || !this.opts.enabled.current) return;\n    if (!isHighestLayer(this) || !isOrContainsTarget(node, target)) return;\n    this.opts.onPointerDown.current(e);\n    if (e.defaultPrevented) return;\n    this.#unsubSelectionLock = preventTextSelectionOverflow(node);\n  };\n  #resetSelectionLock = () => {\n    this.#unsubSelectionLock();\n    this.#unsubSelectionLock = noop;\n  };\n}\nfunction useTextSelectionLayer(props) {\n  return new TextSelectionLayerState(props);\n}\nconst getUserSelect = (node) => node.style.userSelect || node.style.webkitUserSelect;\nfunction preventTextSelectionOverflow(node) {\n  const body = document.body;\n  const originalBodyUserSelect = getUserSelect(body);\n  const originalNodeUserSelect = getUserSelect(node);\n  setUserSelect(body, \"none\");\n  setUserSelect(node, \"text\");\n  return () => {\n    setUserSelect(body, originalBodyUserSelect);\n    setUserSelect(node, originalNodeUserSelect);\n  };\n}\nfunction setUserSelect(node, value) {\n  node.style.userSelect = value;\n  node.style.webkitUserSelect = value;\n}\nfunction isHighestLayer(instance) {\n  const layersArr = [...globalThis.bitsTextSelectionLayers];\n  if (!layersArr.length) return false;\n  const highestLayer = layersArr.at(-1);\n  if (!highestLayer) return false;\n  return highestLayer[0] === instance;\n}\nfunction Text_selection_layer($$payload, $$props) {\n  push();\n  let {\n    preventOverflowTextSelection = true,\n    onPointerDown = noop,\n    onPointerUp = noop,\n    id,\n    children,\n    enabled\n  } = $$props;\n  useTextSelectionLayer({\n    id: box.with(() => id),\n    onPointerDown: box.with(() => onPointerDown),\n    onPointerUp: box.with(() => onPointerUp),\n    enabled: box.with(() => enabled && preventOverflowTextSelection)\n  });\n  children?.($$payload);\n  $$payload.out += `<!---->`;\n  pop();\n}\nfunction createSharedHook(factory) {\n  let state = void 0;\n  return (...args) => {\n    return state;\n  };\n}\nconst useBodyLockStackCount = createSharedHook();\nfunction useBodyScrollLock(initialState, restoreScrollDelay = () => null) {\n  useId();\n  useBodyLockStackCount();\n  return;\n}\nfunction Scroll_lock($$payload, $$props) {\n  push();\n  let {\n    preventScroll = true,\n    restoreScrollDelay = null\n  } = $$props;\n  useBodyScrollLock(preventScroll, () => restoreScrollDelay);\n  pop();\n}\nexport {\n  CustomEventDispatcher as C,\n  Dismissible_layer as D,\n  Escape_layer as E,\n  FocusScopeContext as F,\n  Portal as P,\n  Scroll_lock as S,\n  Text_selection_layer as T,\n  afterSleep as a,\n  getDocument as b,\n  Focus_scope as c,\n  getFirstNonCommentChild as d,\n  focusFirst as f,\n  getTabbableCandidates as g\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,kBAAkB,GAAG,CAAC,oBAAoB,EAAE,qBAAqB,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,mCAAmC,EAAE,8BAA8B,EAAE,8BAA8B,EAAE,+DAA+D,EAAE,4CAA4C,EAAE,sBAAsB,CAAC;AAC1X,IAAI,iBAAiB,kBAAkB,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC;AACnE,IAAI,SAAS,GAAG,OAAO,OAAO,KAAK,WAAW;AAC9C,IAAI,OAAO,GAAG,SAAS,GAAG,YAAY,EAAE,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,CAAC,iBAAiB,IAAI,OAAO,CAAC,SAAS,CAAC,qBAAqB;AACtJ,IAAI,WAAW,GAAG,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,WAAW,GAAG,UAAU,OAAO,EAAE;AACnF,EAAE,IAAI,oBAAoB;AAC1B,EAAE,OAAO,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,CAAC,oBAAoB,GAAG,OAAO,CAAC,WAAW,MAAM,IAAI,IAAI,oBAAoB,KAAK,MAAM,GAAG,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;AACjM,CAAC,GAAG,UAAU,OAAO,EAAE;AACvB,EAAE,OAAO,OAAO,KAAK,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,aAAa;AAChF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAG,SAAS,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE;AAC7C,EAAE,IAAI,kBAAkB;AACxB,EAAE,IAAI,MAAM,KAAK,MAAM,EAAE;AACzB,IAAI,MAAM,GAAG,IAAI;AACjB;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI,kBAAkB,KAAK,MAAM,GAAG,MAAM,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACjM,EAAE,IAAI,KAAK,GAAG,QAAQ,KAAK,EAAE,IAAI,QAAQ,KAAK,MAAM;;AAEpD;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,MAAM,GAAG,KAAK,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;;AAEnE,EAAE,OAAO,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAI,iBAAiB,GAAG,SAAS,iBAAiB,CAAC,IAAI,EAAE;AACzD,EAAE,IAAI,mBAAmB;AACzB;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI,mBAAmB,KAAK,MAAM,GAAG,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC;AAC9M,EAAE,OAAO,QAAQ,KAAK,EAAE,IAAI,QAAQ,KAAK,MAAM;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAG,SAAS,aAAa,CAAC,EAAE,EAAE,gBAAgB,EAAE,MAAM,EAAE;AACzE;AACA;AACA,EAAE,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE;AACnB,IAAI,OAAO,EAAE;AACb;AACA,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AACtF,EAAE,IAAI,gBAAgB,IAAI,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,iBAAiB,CAAC,EAAE;AAC/D,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;AAC1B;AACA,EAAE,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC;AACxC,EAAE,OAAO,UAAU;AACnB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,wBAAwB,GAAG,SAAS,wBAAwB,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE;AACtG,EAAE,IAAI,UAAU,GAAG,EAAE;AACrB,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC5C,EAAE,OAAO,eAAe,CAAC,MAAM,EAAE;AACjC,IAAI,IAAI,OAAO,GAAG,eAAe,CAAC,KAAK,EAAE;AACzC,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;AACjC;AACA;AACA,MAAM;AACN;AACA,IAAI,IAAI,OAAO,CAAC,OAAO,KAAK,MAAM,EAAE;AACpC;AACA,MAAM,IAAI,QAAQ,GAAG,OAAO,CAAC,gBAAgB,EAAE;AAC/C,MAAM,IAAI,OAAO,GAAG,QAAQ,CAAC,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAC,QAAQ;AACjE,MAAM,IAAI,gBAAgB,GAAG,wBAAwB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC;AAC7E,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE;AAC3B,QAAQ,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,gBAAgB,CAAC;AAC3D,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,IAAI,CAAC;AACxB,UAAU,WAAW,EAAE,OAAO;AAC9B,UAAU,UAAU,EAAE;AACtB,SAAS,CAAC;AACV;AACA,KAAK,MAAM;AACX;AACA,MAAM,IAAI,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC;AACnE,MAAM,IAAI,cAAc,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,gBAAgB,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE;AAC1G,QAAQ,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;AAChC;;AAEA;AACA,MAAM,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU;AACzC;AACA,MAAM,OAAO,OAAO,CAAC,aAAa,KAAK,UAAU,IAAI,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC;;AAEnF;AACA;AACA;AACA,MAAM,IAAI,eAAe,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC3H,MAAM,IAAI,UAAU,IAAI,eAAe,EAAE;AACzC;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,IAAI,iBAAiB,GAAG,wBAAwB,CAAC,UAAU,KAAK,IAAI,GAAG,OAAO,CAAC,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC;AACrI,QAAQ,IAAI,OAAO,CAAC,OAAO,EAAE;AAC7B,UAAU,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,iBAAiB,CAAC;AAC9D,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,IAAI,CAAC;AAC1B,YAAY,WAAW,EAAE,OAAO;AAChC,YAAY,UAAU,EAAE;AACxB,WAAW,CAAC;AACZ;AACA,OAAO,MAAM;AACb;AACA;AACA,QAAQ,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,OAAO,CAAC,QAAQ,CAAC;AACxE;AACA;AACA;AACA,EAAE,OAAO,UAAU;AACnB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,GAAG,SAAS,WAAW,CAAC,IAAI,EAAE;AAC7C,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,WAAW,GAAG,SAAS,WAAW,CAAC,IAAI,EAAE;AAC7C,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC;AACvC;AACA,EAAE,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAE;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AACzG,MAAM,OAAO,CAAC;AACd;AACA;AACA,EAAE,OAAO,IAAI,CAAC,QAAQ;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,oBAAoB,GAAG,SAAS,oBAAoB,CAAC,IAAI,EAAE,OAAO,EAAE;AACxE,EAAE,IAAI,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC;AAClC,EAAE,IAAI,QAAQ,GAAG,CAAC,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;AACrD,IAAI,OAAO,CAAC;AACZ;AACA,EAAE,OAAO,QAAQ;AACjB,CAAC;AACD,IAAI,oBAAoB,GAAG,SAAS,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/D,EAAE,OAAO,CAAC,CAAC,QAAQ,KAAK,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ;AAChG,CAAC;AACD,IAAI,OAAO,GAAG,SAAS,OAAO,CAAC,IAAI,EAAE;AACrC,EAAE,OAAO,IAAI,CAAC,OAAO,KAAK,OAAO;AACjC,CAAC;AACD,IAAI,aAAa,GAAG,SAAS,aAAa,CAAC,IAAI,EAAE;AACjD,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ;AAChD,CAAC;AACD,IAAI,oBAAoB,GAAG,SAAS,oBAAoB,CAAC,IAAI,EAAE;AAC/D,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,UAAU,KAAK,EAAE;AACzG,IAAI,OAAO,KAAK,CAAC,OAAO,KAAK,SAAS;AACtC,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC;AACV,CAAC;AACD,IAAI,eAAe,GAAG,SAAS,eAAe,CAAC,KAAK,EAAE,IAAI,EAAE;AAC5D,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AACpD,MAAM,OAAO,KAAK,CAAC,CAAC,CAAC;AACrB;AACA;AACA,CAAC;AACD,IAAI,eAAe,GAAG,SAAS,eAAe,CAAC,IAAI,EAAE;AACrD,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AAClB,IAAI,OAAO,IAAI;AACf;AACA,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;AACjD,EAAE,IAAI,WAAW,GAAG,SAAS,WAAW,CAAC,IAAI,EAAE;AAC/C,IAAI,OAAO,UAAU,CAAC,gBAAgB,CAAC,4BAA4B,GAAG,IAAI,GAAG,IAAI,CAAC;AAClF,GAAG;AACH,EAAE,IAAI,QAAQ;AACd,EAAE,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,GAAG,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC,GAAG,CAAC,MAAM,KAAK,UAAU,EAAE;AACrH,IAAI,QAAQ,GAAG,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACxD,GAAG,MAAM;AACT,IAAI,IAAI;AACR,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AACvC,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB;AACA,MAAM,OAAO,CAAC,KAAK,CAAC,0IAA0I,EAAE,GAAG,CAAC,OAAO,CAAC;AAC5K,MAAM,OAAO,KAAK;AAClB;AACA;AACA,EAAE,IAAI,OAAO,GAAG,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC;AACpD,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI;AACrC,CAAC;AACD,IAAI,OAAO,GAAG,SAAS,OAAO,CAAC,IAAI,EAAE;AACrC,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO;AAC/C,CAAC;AACD,IAAI,kBAAkB,GAAG,SAAS,kBAAkB,CAAC,IAAI,EAAE;AAC3D,EAAE,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AAChD,CAAC;;AAED;AACA,IAAI,cAAc,GAAG,SAAS,cAAc,CAAC,IAAI,EAAE;AACnD,EAAE,IAAI,SAAS;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,QAAQ,GAAG,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC;AAC1C,EAAE,IAAI,YAAY,GAAG,CAAC,SAAS,GAAG,QAAQ,MAAM,IAAI,IAAI,SAAS,KAAK,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC,IAAI;;AAEtG;AACA;AACA,EAAE,IAAI,QAAQ,GAAG,KAAK;AACtB,EAAE,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE;AACrC,IAAI,IAAI,aAAa,EAAE,qBAAqB,EAAE,mBAAmB;AACjE,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC,aAAa,GAAG,YAAY,MAAM,IAAI,IAAI,aAAa,KAAK,MAAM,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC,aAAa,MAAM,IAAI,IAAI,qBAAqB,KAAK,MAAM,IAAI,qBAAqB,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,MAAM,IAAI,IAAI,mBAAmB,KAAK,MAAM,IAAI,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC/Y,IAAI,OAAO,CAAC,QAAQ,IAAI,YAAY,EAAE;AACtC,MAAM,IAAI,UAAU,EAAE,cAAc,EAAE,qBAAqB;AAC3D;AACA;AACA;AACA,MAAM,QAAQ,GAAG,WAAW,CAAC,YAAY,CAAC;AAC1C,MAAM,YAAY,GAAG,CAAC,UAAU,GAAG,QAAQ,MAAM,IAAI,IAAI,UAAU,KAAK,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC,IAAI;AACzG,MAAM,QAAQ,GAAG,CAAC,EAAE,CAAC,cAAc,GAAG,YAAY,MAAM,IAAI,IAAI,cAAc,KAAK,MAAM,IAAI,CAAC,qBAAqB,GAAG,cAAc,CAAC,aAAa,MAAM,IAAI,IAAI,qBAAqB,KAAK,MAAM,IAAI,qBAAqB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AACjP;AACA;AACA,EAAE,OAAO,QAAQ;AACjB,CAAC;AACD,IAAI,UAAU,GAAG,SAAS,UAAU,CAAC,IAAI,EAAE;AAC3C,EAAE,IAAI,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,EAAE;AAC1D,IAAI,KAAK,GAAG,qBAAqB,CAAC,KAAK;AACvC,IAAI,MAAM,GAAG,qBAAqB,CAAC,MAAM;AACzC,EAAE,OAAO,KAAK,KAAK,CAAC,IAAI,MAAM,KAAK,CAAC;AACpC,CAAC;AACD,IAAI,QAAQ,GAAG,SAAS,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE;AAC7C,EAAE,IAAI,YAAY,GAAG,IAAI,CAAC,YAAY;AACtC,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa;AACtC;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,UAAU,KAAK,QAAQ,EAAE;AACtD,IAAI,OAAO,IAAI;AACf;AACA,EAAE,IAAI,eAAe,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,+BAA+B,CAAC;AAC3E,EAAE,IAAI,gBAAgB,GAAG,eAAe,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI;AACpE,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,gBAAgB,EAAE,uBAAuB,CAAC,EAAE;AAC/D,IAAI,OAAO,IAAI;AACf;AACA,EAAE,IAAI,CAAC,YAAY,IAAI,YAAY,KAAK,MAAM,IAAI,YAAY,KAAK,aAAa,EAAE;AAClF,IAAI,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE;AAC7C;AACA;AACA,MAAM,IAAI,YAAY,GAAG,IAAI;AAC7B,MAAM,OAAO,IAAI,EAAE;AACnB,QAAQ,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa;AAC9C,QAAQ,IAAI,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC;AACxC,QAAQ,IAAI,aAAa,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,aAAa,CAAC,aAAa,CAAC,KAAK,IAAI;AAC/F,UAAU;AACV;AACA;AACA,UAAU,OAAO,UAAU,CAAC,IAAI,CAAC;AACjC,SAAS,MAAM,IAAI,IAAI,CAAC,YAAY,EAAE;AACtC;AACA,UAAU,IAAI,GAAG,IAAI,CAAC,YAAY;AAClC,SAAS,MAAM,IAAI,CAAC,aAAa,IAAI,QAAQ,KAAK,IAAI,CAAC,aAAa,EAAE;AACtE;AACA,UAAU,IAAI,GAAG,QAAQ,CAAC,IAAI;AAC9B,SAAS,MAAM;AACf;AACA,UAAU,IAAI,GAAG,aAAa;AAC9B;AACA;AACA,MAAM,IAAI,GAAG,YAAY;AACzB;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA,IAAI,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;AAC9B;AACA;AACA;AACA;AACA,MAAM,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM;AAC1C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,IAAI,YAAY,KAAK,aAAa,EAAE;AACxC,MAAM,OAAO,IAAI,CAAC;AAClB;AACA;AACA,GAAG,MAAM,IAAI,YAAY,KAAK,eAAe,EAAE;AAC/C;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,UAAU,CAAC,IAAI,CAAC;AAC3B;;AAEA;AACA;AACA,EAAE,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA,IAAI,sBAAsB,GAAG,SAAS,sBAAsB,CAAC,IAAI,EAAE;AACnE,EAAE,IAAI,kCAAkC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;AAC7D,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC,aAAa;AACvC;AACA,IAAI,OAAO,UAAU,EAAE;AACvB,MAAM,IAAI,UAAU,CAAC,OAAO,KAAK,UAAU,IAAI,UAAU,CAAC,QAAQ,EAAE;AACpE;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7D,UAAU,IAAI,KAAK,GAAG,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACjD;AACA,UAAU,IAAI,KAAK,CAAC,OAAO,KAAK,QAAQ,EAAE;AAC1C;AACA;AACA,YAAY,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,sBAAsB,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;AAClG;AACA;AACA;AACA,QAAQ,OAAO,IAAI;AACnB;AACA,MAAM,UAAU,GAAG,UAAU,CAAC,aAAa;AAC3C;AACA;;AAEA;AACA;AACA,EAAE,OAAO,KAAK;AACd,CAAC;AACD,IAAI,+BAA+B,GAAG,SAAS,+BAA+B,CAAC,OAAO,EAAE,IAAI,EAAE;AAC9F,EAAE,IAAI,IAAI,CAAC,QAAQ;AACnB;AACA;AACA;AACA,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC;AACjE;AACA,EAAE,oBAAoB,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;AAC9D,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,IAAI;AACb,CAAC;AACD,IAAI,8BAA8B,GAAG,SAAS,8BAA8B,CAAC,OAAO,EAAE,IAAI,EAAE;AAC5F,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,+BAA+B,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;AAC5G,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,IAAI;AACb,CAAC;AACD,IAAI,yBAAyB,GAAG,SAAS,yBAAyB,CAAC,cAAc,EAAE;AACnF,EAAE,IAAI,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,CAAC;AACtE,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE;AACxC,IAAI,OAAO,IAAI;AACf;AACA;AACA;AACA,EAAE,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAI,WAAW,GAAG,SAAS,WAAW,CAAC,UAAU,EAAE;AACnD,EAAE,IAAI,gBAAgB,GAAG,EAAE;AAC3B,EAAE,IAAI,gBAAgB,GAAG,EAAE;AAC3B,EAAE,UAAU,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE;AACxC,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW;AACpC,IAAI,IAAI,OAAO,GAAG,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI;AACnD,IAAI,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,OAAO,EAAE,OAAO,CAAC;AAClE,IAAI,IAAI,QAAQ,GAAG,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,OAAO;AACnE,IAAI,IAAI,iBAAiB,KAAK,CAAC,EAAE;AACjC,MAAM,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE,QAAQ,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC;AACxG,KAAK,MAAM;AACX,MAAM,gBAAgB,CAAC,IAAI,CAAC;AAC5B,QAAQ,aAAa,EAAE,CAAC;AACxB,QAAQ,QAAQ,EAAE,iBAAiB;AACnC,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,OAAO,EAAE;AACjB,OAAO,CAAC;AACR;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,gBAAgB,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,QAAQ,EAAE;AACrF,IAAI,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACzF,IAAI,OAAO,GAAG;AACd,GAAG,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC;AACjC,CAAC;AACE,IAAC,QAAQ,GAAG,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACrD,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE;AACzB,EAAE,IAAI,UAAU;AAChB,EAAE,IAAI,OAAO,CAAC,aAAa,EAAE;AAC7B,IAAI,UAAU,GAAG,wBAAwB,CAAC,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,gBAAgB,EAAE;AACjF,MAAM,MAAM,EAAE,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AAChE,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,aAAa,EAAE,OAAO,CAAC,aAAa;AAC1C,MAAM,gBAAgB,EAAE;AACxB,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,UAAU,GAAG,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,gBAAgB,EAAE,8BAA8B,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACvH;AACA,EAAE,OAAO,WAAW,CAAC,UAAU,CAAC;AAChC;AACG,IAAC,SAAS,GAAG,SAAS,SAAS,CAAC,SAAS,EAAE,OAAO,EAAE;AACvD,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE;AACzB,EAAE,IAAI,UAAU;AAChB,EAAE,IAAI,OAAO,CAAC,aAAa,EAAE;AAC7B,IAAI,UAAU,GAAG,wBAAwB,CAAC,CAAC,SAAS,CAAC,EAAE,OAAO,CAAC,gBAAgB,EAAE;AACjF,MAAM,MAAM,EAAE,+BAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC;AACjE,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,aAAa,EAAE,OAAO,CAAC;AAC7B,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,UAAU,GAAG,aAAa,CAAC,SAAS,EAAE,OAAO,CAAC,gBAAgB,EAAE,+BAA+B,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACxH;AACA,EAAE,OAAO,UAAU;AACnB;AACG,IAAC,UAAU,GAAG,SAAS,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE;AACpD,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE;AACzB,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC;AACvC;AACA,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,KAAK,EAAE;AACvD,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,8BAA8B,CAAC,OAAO,EAAE,IAAI,CAAC;AACtD;AACA,IAAI,0BAA0B,kBAAkB,kBAAkB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;AAC1F,IAAC,WAAW,GAAG,SAAS,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE;AACtD,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE;AACzB,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC;AACvC;AACA,EAAE,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,0BAA0B,CAAC,KAAK,KAAK,EAAE;AAChE,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,+BAA+B,CAAC,OAAO,EAAE,IAAI,CAAC;AACvD;;ACziBA,SAAS,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE;AAC5B,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,EAAE,CAAC;AAC3B;AACA,SAAS,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;AACpC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO;AACnD,EAAE,cAAc,EAAE;AAClB,EAAE,IAAI,MAAM,GAAG,SAAS,EAAE;AAC1B,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,IAAI,CAAC,SAAS,IAAI,QAAQ,EAAE,OAAO,IAAI;AAC3C,IAAI,IAAI,WAAW,GAAG,IAAI;AAC1B,IAAI,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;AAChC,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;AAC9C,KAAK,MAAM,IAAI,EAAE,YAAY,WAAW,IAAI,EAAE,YAAY,gBAAgB,EAAE;AAC5E,MAAM,WAAW,GAAG,EAAE;AACtB,KAAK,MAAM;AACX,IAAI,OAAO,WAAW;AACtB;AACA,EAAE,IAAI,QAAQ;AACd,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,OAAO,EAAE;AACf,MAAM,QAAQ,GAAG,IAAI;AACrB;AACA;AACA,EAAE,KAAK,CAAC,CAAC,MAAM,MAAM,EAAE,MAAM,QAAQ,CAAC,EAAE,CAAC,CAAC,OAAO,EAAE,SAAS,CAAC,KAAK;AAClE,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE;AAC/B,MAAM,eAAe,EAAE;AACvB,MAAM;AACN;AACA,IAAI,QAAQ,GAAG,KAAK,EAAE;AACtB,IAAI,OAAO,MAAM;AACjB,MAAM,eAAe,EAAE;AACvB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,QAAQ,GAAG,SAAS,CAAC;AACzB,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE;AAC3D,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC;AACvD,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC/E,EAAE,OAAO,MAAM;AACf,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACpF,GAAG;AACH;AACA,MAAM,qBAAqB,CAAC;AAC5B,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,WAAW,CAAC,SAAS,EAAE,OAAO,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE;AACxE,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS;AAC9B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO;AAC1B;AACA,EAAE,WAAW,CAAC,MAAM,EAAE;AACtB,IAAI,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,SAAS,EAAE;AAC3C,MAAM,GAAG,IAAI,CAAC,OAAO;AACrB,MAAM;AACN,KAAK,CAAC;AACN;AACA,EAAE,QAAQ,CAAC,OAAO,EAAE,MAAM,EAAE;AAC5B,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;AAC1C,IAAI,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;AAChC,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,MAAM,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE;AACrC,IAAI,MAAM,OAAO,GAAG,CAAC,KAAK,KAAK;AAC/B,MAAM,QAAQ,CAAC,KAAK,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;AACxD;AACA;AACA,SAAS,QAAQ,CAAC,EAAE,EAAE,IAAI,GAAG,GAAG,EAAE;AAClC,EAAE,IAAI,OAAO,GAAG,IAAI;AACpB,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG,IAAI,KAAK;AACjC,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;AAC1B,MAAM,YAAY,CAAC,OAAO,CAAC;AAC3B;AACA,IAAI,OAAO,GAAG,UAAU,CAAC,MAAM;AAC/B,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC;AACjB,KAAK,EAAE,IAAI,CAAC;AACZ,GAAG;AACH,EAAE,SAAS,CAAC,OAAO,GAAG,MAAM;AAC5B,IAAI,IAAI,OAAO,KAAK,IAAI,EAAE;AAC1B,MAAM,YAAY,CAAC,OAAO,CAAC;AAC3B,MAAM,OAAO,GAAG,IAAI;AACpB;AACA,GAAG;AACH,EAAE,OAAO,SAAS;AAClB;AACA,SAAS,kBAAkB,CAAC,IAAI,EAAE,MAAM,EAAE;AAC1C,EAAE,OAAO,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AACjD;AACA,SAAS,gBAAgB,CAAC,EAAE,EAAE;AAC9B,EAAE,OAAO,EAAE,EAAE,aAAa,IAAI,QAAQ;AACtC;AACA,SAAS,WAAW,CAAC,OAAO,EAAE;AAC9B,EAAE,OAAO,OAAO,EAAE,aAAa,IAAI,QAAQ;AAC3C;AACA,SAAS,uBAAuB,CAAC,OAAO,EAAE;AAC1C,EAAE,IAAI,CAAC,OAAO;AACd,IAAI,OAAO,IAAI;AACf,EAAE,KAAK,MAAM,KAAK,IAAI,OAAO,CAAC,UAAU,EAAE;AAC1C,IAAI,IAAI,KAAK,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,EAAE;AAC9C,MAAM,OAAO,KAAK;AAClB;AACA;AACA,EAAE,OAAO,IAAI;AACb;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE;AACjD,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,KAAK;AACpC,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC,qBAAqB,EAAE;AAClD,EAAE,OAAO,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM;AACnG;AACA,UAAU,CAAC,qBAAqB,qBAAqB,IAAI,GAAG,EAAE;AAC9D,MAAM,qBAAqB,CAAC;AAC5B,EAAE,IAAI;AACN,EAAE,oBAAoB;AACtB,EAAE,aAAa;AACf,EAAE,kBAAkB,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE;AAC7C,EAAE,mBAAmB,GAAG,KAAK;AAC7B,EAAE,qBAAqB,GAAG,KAAK;AAC/B,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AAClB,EAAE,YAAY,GAAG,MAAM;AACvB,EAAE,eAAe;AACjB,EAAE,QAAQ,GAAG,IAAI;AACjB,EAAE,mBAAmB,GAAG,IAAI;AAC5B,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,GAAG,EAAE,IAAI,CAAC,IAAI;AACpB,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO;AACtC,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC5B;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,uBAAuB;AACrD,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,iBAAiB;AACtD,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc;AAC9C,IAAI,IAAI,WAAW,GAAG,IAAI;AAC1B,IAAI,MAAM,OAAO,GAAG,MAAM;AAC1B,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,MAAM,UAAU,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC;AACnD,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE;AAC3C,MAAM,WAAW,EAAE;AACnB,KAAK;AACL,IAAI,KAAK;AACT,MAAM;AACN,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO;AACvC,QAAQ,MAAM,IAAI,CAAC;AACnB,OAAO;AACP,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK;AAC/B,QAAQ,IAAI,CAAC,OAAO,IAAI,CAAC,QAAQ,EAAE;AACnC,QAAQ,UAAU,CAAC,CAAC,EAAE,MAAM;AAC5B,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC9B,UAAU,UAAU,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC;AACxE,UAAU,WAAW,EAAE;AACvB,UAAU,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE;AACjD,SAAS,CAAC;AACV,QAAQ,OAAO,OAAO;AACtB;AACA,KAAK;AACL;AACA,EAAE,YAAY,GAAG,CAAC,KAAK,KAAK;AAC5B,IAAI,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AACxB,IAAI,SAAS,CAAC,MAAM;AACpB,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AACrE,MAAM,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;AACvD,QAAQ,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG,KAAK,CAAC;AAC7C;AACA,KAAK,CAAC;AACN,GAAG;AACH,EAAE,kBAAkB,GAAG;AACvB,IAAI,OAAO,gBAAgB;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AACvI;AACA;AACA;AACA;AACA;AACA,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,gBAAgB,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,sBAAsB,CAAC,CAAC;AACxH;AACA;AACA;AACA,MAAM,EAAE,CAAC,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,CAAC,YAAY;AACxD,KAAK;AACL;AACA,EAAE,cAAc,GAAG,CAAC,CAAC,KAAK;AAC1B,IAAI,IAAI,KAAK,GAAG,CAAC;AACjB,IAAI,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAChC,MAAM,KAAK,GAAG,kBAAkB,CAAC,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,sBAAsB,GAAG,QAAQ;AACnC,IAAI,CAAC,CAAC,KAAK;AACX,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAC1B,QAAQ,IAAI,CAAC,mBAAmB,EAAE;AAClC,QAAQ;AACR;AACA,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC;AAC7G,MAAM,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,YAAY,EAAE;AACvF,QAAQ,IAAI,CAAC,mBAAmB,EAAE;AAClC,QAAQ;AACR;AACA,MAAM,IAAI,KAAK,GAAG,CAAC;AACnB,MAAM,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAClC,QAAQ,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC;AACzC;AACA,MAAM,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,KAAK,uBAAuB,EAAE;AAC5G,QAAQ,IAAI,CAAC,mBAAmB,EAAE;AAClC,QAAQ;AACR;AACA,MAAM,IAAI,CAAC,CAAC,WAAW,KAAK,OAAO,EAAE;AACrC,QAAQ,IAAI,CAAC,mBAAmB,EAAE;AAClC,QAAQ,IAAI,CAAC,mBAAmB,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACpH,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC;AAChD;AACA,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE,qBAAqB,GAAG,CAAC,CAAC,KAAK;AACjC,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI;AAC1C,GAAG;AACH,EAAE,wBAAwB,GAAG,CAAC,CAAC,KAAK;AACpC,IAAI,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,KAAK;AAC3C,GAAG;AACH,EAAE,qBAAqB,GAAG,MAAM;AAChC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AAC5B,IAAI,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpE,GAAG;AACH,EAAE,oBAAoB,GAAG,CAAC,MAAM,KAAK;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK;AACxC,IAAI,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC;AACxD,GAAG;AACH,EAAE,WAAW,GAAG,QAAQ;AACxB,IAAI,MAAM;AACV,MAAM,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACvD,QAAQ,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,GAAG,KAAK;AAClD;AACA,MAAM,IAAI,CAAC,mBAAmB,GAAG,KAAK;AACtC,KAAK;AACL,IAAI;AACJ,GAAG;AACH,EAAE,sBAAsB,GAAG;AAC3B,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;AAClE,IAAI,OAAO,CAAC;AACZ;AACA,EAAE,eAAe,GAAG,MAAM;AAC1B,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI;AACrC,GAAG;AACH,EAAE,cAAc,GAAG,MAAM;AACzB,IAAI,IAAI,CAAC,qBAAqB,GAAG,KAAK;AACtC,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,cAAc,EAAE,IAAI,CAAC,eAAe;AACxC,IAAI,aAAa,EAAE,IAAI,CAAC;AACxB,GAAG;AACH;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAE;AACpC,EAAE,OAAO,IAAI,qBAAqB,CAAC,KAAK,CAAC;AACzC;AACA,SAAS,eAAe,CAAC,SAAS,EAAE;AACpC,EAAE,OAAO,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,KAAK,YAAY,KAAK,OAAO,IAAI,YAAY,KAAK,QAAQ,CAAC;AACtH;AACA,SAAS,kBAAkB,CAAC,IAAI,EAAE;AAClC,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG,UAAU,CAAC,qBAAqB,CAAC;AACzD,EAAE,MAAM,YAAY,GAAG,eAAe,CAAC,SAAS,CAAC;AACjD,EAAE,IAAI,YAAY,EAAE,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI;AAChE,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACvC,EAAE,OAAO,cAAc,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI;AAC7C;AACA,SAAS,YAAY,CAAC,CAAC,EAAE,IAAI,EAAE;AAC/B,EAAE,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK;AACjD,EAAE,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;AACzB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,OAAO,KAAK;AACtC,EAAE,MAAM,aAAa,GAAG,gBAAgB,CAAC,MAAM,CAAC;AAChD,EAAE,MAAM,OAAO,GAAG,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,mBAAmB,CAAC,CAAC,EAAE,IAAI,CAAC;AACrI,EAAE,OAAO,OAAO;AAChB;AACA,SAAS,kBAAkB,CAAC,CAAC,EAAE;AAC/B,EAAE,MAAM,qBAAqB,GAAG,CAAC,CAAC,aAAa;AAC/C,EAAE,MAAM,cAAc,GAAG,CAAC,CAAC,MAAM;AACjC,EAAE,IAAI,QAAQ;AACd,EAAE,IAAI,CAAC,YAAY,YAAY,EAAE;AACjC,IAAI,QAAQ,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1C,GAAG,MAAM;AACT,IAAI,QAAQ,GAAG,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC,CAAC;AACjD;AACA,EAAE,IAAI,WAAW,GAAG,KAAK;AACzB,EAAE,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC3C,IAAI,GAAG,EAAE,CAAC,MAAM,EAAE,IAAI,KAAK;AAC3B,MAAM,IAAI,IAAI,KAAK,eAAe,EAAE;AACpC,QAAQ,OAAO,qBAAqB;AACpC;AACA,MAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;AAC7B,QAAQ,OAAO,cAAc;AAC7B;AACA,MAAM,IAAI,IAAI,KAAK,gBAAgB,EAAE;AACrC,QAAQ,OAAO,MAAM;AACrB,UAAU,WAAW,GAAG,IAAI;AAC5B,UAAU,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,EAAE;AAC3D,YAAY,MAAM,CAAC,cAAc,EAAE;AACnC;AACA,SAAS;AACT;AACA,MAAM,IAAI,IAAI,KAAK,kBAAkB,EAAE;AACvC,QAAQ,OAAO,WAAW;AAC1B;AACA,MAAM,IAAI,IAAI,IAAI,MAAM,EAAE;AAC1B,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC;AAC3B;AACA,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC;AACpB;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,YAAY;AACrB;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,uBAAuB,GAAG,OAAO;AACrC,IAAI,iBAAiB,GAAG,IAAI;AAC5B,IAAI,cAAc,GAAG,IAAI;AACzB,IAAI,EAAE;AACN,IAAI,QAAQ;AACZ,IAAI,OAAO;AACX,IAAI,YAAY,EAAE,aAAa,GAAG,MAAM;AACxC,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,qBAAqB,GAAG,mBAAmB,CAAC;AACpD,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,uBAAuB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,uBAAuB,CAAC;AACpE,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,iBAAiB,CAAC;AACxD,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC;AACpC,IAAI,cAAc,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,CAAC;AAClD,IAAI,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,aAAa;AAC9C,GAAG,CAAC;AACJ,EAAE,QAAQ,GAAG,SAAS,EAAE,EAAE,KAAK,EAAE,qBAAqB,CAAC,KAAK,EAAE,CAAC;AAC/D,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,UAAU,CAAC,gBAAgB,qBAAqB,IAAI,GAAG,EAAE;AACzD,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,WAAW,GAAG,IAAI;AAC1B,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,OAAO,KAAK;AACnD,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,UAAU,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,qBAAqB,CAAC;AACzE,QAAQ,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE;AAC9C;AACA,MAAM,OAAO,MAAM;AACnB,QAAQ,WAAW,EAAE;AACrB,QAAQ,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;AAChD,OAAO;AACP,KAAK,CAAC;AACN;AACA,EAAE,iBAAiB,GAAG,MAAM;AAC5B,IAAI,OAAO,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;AACvE,GAAG;AACH,EAAE,UAAU,GAAG,CAAC,CAAC,KAAK;AACtB,IAAI,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,EAAE;AAC7D,IAAI,MAAM,WAAW,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AACpD,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;AAChE,IAAI,IAAI,YAAY,KAAK,OAAO,IAAI,YAAY,KAAK,uBAAuB,EAAE;AAC9E,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;AAClD,GAAG;AACH;AACA,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,IAAI,gBAAgB,CAAC,KAAK,CAAC;AACpC;AACA,SAAS,wBAAwB,CAAC,QAAQ,EAAE;AAC5C,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG,UAAU,CAAC,gBAAgB,CAAC;AACpD,EAAE,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,KAAK,YAAY,KAAK,OAAO,IAAI,YAAY,KAAK,QAAQ,CAAC;AACpI,EAAE,IAAI,YAAY,EAAE,OAAO,YAAY,CAAC,CAAC,CAAC,KAAK,QAAQ;AACvD,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;AACvC,EAAE,OAAO,cAAc,KAAK,QAAQ;AACpC;AACA,SAAS,YAAY,CAAC,SAAS,EAAE,OAAO,EAAE;AAC1C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,qBAAqB,GAAG,OAAO;AACnC,IAAI,eAAe,GAAG,IAAI;AAC1B,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,cAAc,CAAC;AACjB,IAAI,qBAAqB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,qBAAqB,CAAC;AAChE,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,eAAe,CAAC;AACpD,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO;AACnC,GAAG,CAAC;AACJ,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,MAAM,UAAU,GAAG,GAAG,CAAC,EAAE,CAAC;AAC1B,SAAS,qBAAqB,GAAG;AACjC,EAAE,OAAO;AACT,IAAI,GAAG,CAAC,UAAU,EAAE;AACpB,MAAM,MAAM,gBAAgB,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;AACpD,MAAM,IAAI,gBAAgB,IAAI,UAAU,CAAC,EAAE,KAAK,gBAAgB,CAAC,EAAE,EAAE;AACrE,QAAQ,gBAAgB,CAAC,KAAK,EAAE;AAChC;AACA,MAAM,UAAU,CAAC,OAAO,GAAG,yBAAyB,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC;AACpF,MAAM,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC;AAC5C,KAAK;AACL,IAAI,MAAM,CAAC,UAAU,EAAE;AACvB,MAAM,UAAU,CAAC,OAAO,GAAG,yBAAyB,CAAC,UAAU,CAAC,OAAO,EAAE,UAAU,CAAC;AACpF,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE;AACrC,KAAK;AACL,IAAI,IAAI,OAAO,GAAG;AAClB,MAAM,OAAO,UAAU,CAAC,OAAO;AAC/B;AACA,GAAG;AACH;AACA,SAAS,mBAAmB,GAAG;AAC/B,EAAE,IAAI,MAAM,GAAG,KAAK;AACpB,EAAE,IAAI,eAAe,GAAG,KAAK;AAC7B,EAAE,OAAO;AACT,IAAI,EAAE,EAAE,KAAK,EAAE;AACf,IAAI,IAAI,MAAM,GAAG;AACjB,MAAM,OAAO,MAAM;AACnB,KAAK;AACL,IAAI,IAAI,eAAe,GAAG;AAC1B,MAAM,OAAO,eAAe;AAC5B,KAAK;AACL,IAAI,IAAI,eAAe,CAAC,KAAK,EAAE;AAC/B,MAAM,eAAe,GAAG,KAAK;AAC7B,KAAK;AACL,IAAI,KAAK,GAAG;AACZ,MAAM,MAAM,GAAG,IAAI;AACnB,KAAK;AACL,IAAI,MAAM,GAAG;AACb,MAAM,MAAM,GAAG,KAAK;AACpB;AACA,GAAG;AACH;AACA,SAAS,yBAAyB,CAAC,GAAG,EAAE,IAAI,EAAE;AAC9C,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC;AACjD;AACA,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,KAAK,GAAG,CAAC;AACrD;AACA,SAAS,KAAK,CAAC,OAAO,EAAE,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AACjD,EAAE,IAAI,EAAE,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC;AACjC,IAAI;AACJ,EAAE,IAAI,QAAQ,CAAC,aAAa,KAAK,OAAO;AACxC,IAAI;AACJ,EAAE,MAAM,wBAAwB,GAAG,QAAQ,CAAC,aAAa;AACzD,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC;AACxC,EAAE,IAAI,OAAO,KAAK,wBAAwB,IAAI,iBAAiB,CAAC,OAAO,CAAC,IAAI,MAAM,EAAE;AACpF,IAAI,OAAO,CAAC,MAAM,EAAE;AACpB;AACA;AACA,SAAS,UAAU,CAAC,UAAU,EAAE,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AACzD,EAAE,MAAM,wBAAwB,GAAG,QAAQ,CAAC,aAAa;AACzD,EAAE,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;AACtC,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC;AAChC,IAAI,IAAI,QAAQ,CAAC,aAAa,KAAK,wBAAwB;AAC3D,MAAM,OAAO,IAAI;AACjB;AACA;AACA,SAAS,WAAW,CAAC,QAAQ,EAAE,SAAS,EAAE;AAC1C,EAAE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAClC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC;AAC5C,MAAM,OAAO,OAAO;AACpB;AACA;AACA,SAAS,qBAAqB,CAAC,SAAS,EAAE;AAC1C,EAAE,MAAM,KAAK,GAAG,EAAE;AAClB,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,UAAU,CAAC,YAAY,EAAE;AAC/E;AACA,IAAI,UAAU,EAAE,CAAC,IAAI,KAAK;AAC1B,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ;AAC9E,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,aAAa;AACvD,QAAQ,OAAO,UAAU,CAAC,WAAW;AACrC,MAAM,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,WAAW;AACnF;AACA,GAAG,CAAC;AACJ,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE;AAC1B,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;AAClC,EAAE,OAAO,KAAK;AACd;AACA,SAAS,gBAAgB,CAAC,SAAS,EAAE;AACrC,EAAE,MAAM,UAAU,GAAG,qBAAqB,CAAC,SAAS,CAAC;AACrD,EAAE,MAAM,KAAK,GAAG,WAAW,CAAC,UAAU,EAAE,SAAS,CAAC;AAClD,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,SAAS,CAAC;AAC3D,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;AACtB;AACA,MAAM,qBAAqB,GAAG,IAAI,qBAAqB,CAAC,6BAA6B,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AAC5H,MAAM,uBAAuB,GAAG,IAAI,qBAAqB,CAAC,+BAA+B,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;AAC3H,MAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,YAAY;AAClD,SAAS,aAAa,CAAC;AACvB,EAAE,EAAE;AACJ,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,eAAe;AACjB,EAAE,gBAAgB;AAClB,EAAE;AACF,CAAC,EAAE;AACH,EAAE,MAAM,eAAe,GAAG,qBAAqB,EAAE;AACjD,EAAE,MAAM,UAAU,GAAG,mBAAmB,EAAE;AAC1C,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC;AACvB,EAAE,MAAM,GAAG,GAAG,iBAAiB,CAAC,KAAK,CAAC,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;AACtE,EAAE,IAAI,kBAAkB,GAAG,IAAI;AAC/B,EAAE,UAAU,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,OAAO,CAAC,OAAO,EAAE,CAAC;AACtD,EAAE,SAAS,WAAW,CAAC,KAAK,EAAE;AAC9B,IAAI,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI,UAAU,CAAC,eAAe,EAAE;AACzE,IAAI,UAAU,CAAC,eAAe,GAAG,IAAI;AACrC,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM;AACjC,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;AAClC,MAAM,MAAM,mBAAmB,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC;AAC9D,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;AACpC,QAAQ,IAAI,mBAAmB,EAAE;AACjC,UAAU,kBAAkB,GAAG,MAAM;AACrC,SAAS,MAAM;AACf,UAAU,IAAI,GAAG,CAAC,oBAAoB,EAAE;AACxC,UAAU,KAAK,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACrD;AACA,OAAO,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;AAC5C,QAAQ,IAAI,CAAC,mBAAmB,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE;AAC/D,UAAU,KAAK,CAAC,kBAAkB,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACrD;AACA;AACA,KAAK,SAAS;AACd,MAAM,UAAU,CAAC,eAAe,GAAG,KAAK;AACxC;AACA;AACA,EAAE,SAAS,eAAe,CAAC,SAAS,EAAE;AACtC,IAAI,IAAI,CAAC,kBAAkB,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;AAC7C,IAAI,IAAI,iBAAiB,GAAG,KAAK;AACjC,IAAI,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AACtC,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,WAAW,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7E,QAAQ,KAAK,MAAM,WAAW,IAAI,QAAQ,CAAC,YAAY,EAAE;AACzD,UAAU,IAAI,WAAW,KAAK,kBAAkB,EAAE;AAClD,YAAY,iBAAiB,GAAG,IAAI;AACpC,YAAY;AACZ;AACA,UAAU,IAAI,WAAW,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACtG,YAAY,iBAAiB,GAAG,IAAI;AACpC,YAAY;AACZ;AACA;AACA;AACA,MAAM,IAAI,iBAAiB,EAAE;AAC7B;AACA,IAAI,IAAI,iBAAiB,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE;AAC3F,MAAM,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC;AACxB;AACA;AACA,EAAE,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,OAAO,EAAE,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK;AAC/E,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,QAAQ,EAAE;AACjC,IAAI,MAAM,YAAY,GAAG,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;AACtH,IAAI,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,eAAe,CAAC;AAClE,IAAI,gBAAgB,CAAC,OAAO,CAAC,SAAS,EAAE;AACxC,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE;AAClB,KAAK,CAAC;AACN,IAAI,OAAO,MAAM;AACjB,MAAM,YAAY,EAAE;AACpB,MAAM,gBAAgB,CAAC,UAAU,EAAE;AACnC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,CAAC,MAAM,UAAU,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,KAAK;AACrF,IAAI,IAAI,WAAW,EAAE;AACrB,IAAI,MAAM,kBAAkB,GAAG,QAAQ,CAAC,aAAa;AACrD,IAAI,UAAU,CAAC,SAAS,EAAE,kBAAkB,CAAC;AAC7C,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,CAAC,SAAS,EAAE;AACtB,MAAM,WAAW,CAAC,kBAAkB,CAAC;AACrC,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,KAAK;AACP,IAAI;AACJ,MAAM,MAAM,UAAU,CAAC,OAAO;AAC9B,MAAM,MAAM,GAAG,CAAC,OAAO;AACvB,MAAM,MAAM,OAAO,CAAC;AACpB,KAAK;AACL,IAAI,CAAC,CAAC,WAAW,EAAE,SAAS,CAAC,KAAK;AAClC,MAAM,IAAI,CAAC,WAAW,EAAE;AACxB,MAAM,MAAM,kBAAkB,GAAG,QAAQ,CAAC,aAAa;AACvD,MAAM,UAAU,CAAC,SAAS,EAAE,kBAAkB,CAAC;AAC/C,MAAM,OAAO,MAAM;AACnB,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,QAAQ,WAAW,CAAC,kBAAkB,CAAC;AACvC,OAAO;AACP;AACA,GAAG;AACH,EAAE,SAAS,UAAU,CAAC,SAAS,EAAE,kBAAkB,EAAE;AACrD,IAAI,IAAI,CAAC,SAAS,EAAE,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,OAAO,CAAC;AACnE,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AACxC,IAAI,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC;AACnC,IAAI,MAAM,mBAAmB,GAAG,SAAS,CAAC,QAAQ,CAAC,kBAAkB,CAAC;AACtE,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC9B,MAAM,MAAM,UAAU,GAAG,qBAAqB,CAAC,WAAW,EAAE;AAC5D,MAAM,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC;AACzC,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE;AACxC,QAAQ,SAAS,CAAC,MAAM;AACxB,UAAU,IAAI,CAAC,SAAS,EAAE;AAC1B,UAAU,MAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACpG,UAAU,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,SAAS,CAAC;AACvC,SAAS,CAAC;AACV;AACA;AACA;AACA,EAAE,SAAS,WAAW,CAAC,kBAAkB,EAAE;AAC3C,IAAI,MAAM,YAAY,GAAG,uBAAuB,CAAC,WAAW,EAAE;AAC9D,IAAI,gBAAgB,CAAC,OAAO,GAAG,YAAY,CAAC;AAC5C,IAAI,MAAM,YAAY,GAAG,GAAG,CAAC,oBAAoB;AACjD,IAAI,UAAU,CAAC,CAAC,EAAE,MAAM;AACxB,MAAM,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,kBAAkB,IAAI,CAAC,YAAY,EAAE;AACjF,QAAQ,KAAK,CAAC,UAAU,CAAC,kBAAkB,CAAC,GAAG,kBAAkB,GAAG,QAAQ,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACpG;AACA,MAAM,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC;AACxC,KAAK,CAAC;AACN;AACA,EAAE,SAAS,aAAa,CAAC,CAAC,EAAE;AAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AAC3C,IAAI,IAAI,UAAU,CAAC,MAAM,EAAE;AAC3B,IAAI,MAAM,QAAQ,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,OAAO;AAC3E,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa;AACjD,IAAI,IAAI,EAAE,QAAQ,IAAI,cAAc,CAAC,EAAE;AACvC,IAAI,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO;AACjC,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,gBAAgB,CAAC,SAAS,CAAC;AACrD,IAAI,MAAM,yBAAyB,GAAG,KAAK,IAAI,IAAI;AACnD,IAAI,IAAI,CAAC,yBAAyB,EAAE;AACpC,MAAM,IAAI,cAAc,KAAK,SAAS,EAAE;AACxC,QAAQ,CAAC,CAAC,cAAc,EAAE;AAC1B;AACA,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,cAAc,KAAK,IAAI,EAAE;AAClD,QAAQ,CAAC,CAAC,cAAc,EAAE;AAC1B,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACxD,OAAO,MAAM,IAAI,CAAC,CAAC,QAAQ,IAAI,cAAc,KAAK,KAAK,EAAE;AACzD,QAAQ,CAAC,CAAC,cAAc,EAAE;AAC1B,QAAQ,IAAI,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;AACvD;AACA;AACA;AACA,EAAE,MAAM,KAAK,GAAG,CAAC,OAAO;AACxB,IAAI,EAAE,EAAE,EAAE,CAAC,OAAO;AAClB,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,SAAS,EAAE;AACf,GAAG,CAAC,GAAG;AACP,EAAE,OAAO;AACT,IAAI,IAAI,KAAK,GAAG;AAChB,MAAM,OAAO,KAAK;AAClB;AACA,GAAG;AACH;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,EAAE;AACN,IAAI,SAAS,GAAG,KAAK;AACrB,IAAI,IAAI,GAAG,KAAK;AAChB,IAAI,gBAAgB,GAAG,IAAI;AAC3B,IAAI,eAAe,GAAG,IAAI;AAC1B,IAAI,UAAU;AACd,IAAI,UAAU,GAAG;AACjB,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,eAAe,GAAG,aAAa,CAAC;AACxC,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,SAAS,CAAC;AACtC,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;AAC9B,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,gBAAgB,CAAC;AACtD,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,eAAe,CAAC;AACpD,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,UAAU,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,UAAU;AACzC,GAAG,CAAC;AACJ,EAAE,UAAU,GAAG,SAAS,EAAE,EAAE,KAAK,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC;AAC3D,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,UAAU,CAAC,uBAAuB,qBAAqB,IAAI,GAAG,EAAE;AAChE,MAAM,uBAAuB,CAAC;AAC9B,EAAE,IAAI;AACN,EAAE,mBAAmB,GAAG,IAAI;AAC5B,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;AAClB,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE;AACjB,MAAM,GAAG,EAAE,IAAI,CAAC,IAAI;AACpB,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACpC,KAAK,CAAC;AACN,IAAI,IAAI,WAAW,GAAG,IAAI;AAC1B,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,SAAS,KAAK;AAC1D,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,UAAU,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACvE,QAAQ,WAAW,EAAE;AACrB,QAAQ,WAAW,GAAG,IAAI,CAAC,kBAAkB,EAAE;AAC/C;AACA,MAAM,OAAO,MAAM;AACnB,QAAQ,WAAW,EAAE;AACrB,QAAQ,IAAI,CAAC,mBAAmB,EAAE;AAClC,QAAQ,UAAU,CAAC,uBAAuB,CAAC,MAAM,CAAC,IAAI,CAAC;AACvD,OAAO;AACP,KAAK,CAAC;AACN;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,OAAO,gBAAgB,CAAC,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,eAAe,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC;AAChL;AACA,EAAE,YAAY,GAAG,CAAC,CAAC,KAAK;AACxB,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO;AAClC,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM;AAC3B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;AACtF,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;AACpE,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;AACtC,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,IAAI,CAAC,mBAAmB,GAAG,4BAA4B,CAAC,IAAI,CAAC;AACjE,GAAG;AACH,EAAE,mBAAmB,GAAG,MAAM;AAC9B,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC9B,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI;AACnC,GAAG;AACH;AACA,SAAS,qBAAqB,CAAC,KAAK,EAAE;AACtC,EAAE,OAAO,IAAI,uBAAuB,CAAC,KAAK,CAAC;AAC3C;AACA,MAAM,aAAa,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB;AACpF,SAAS,4BAA4B,CAAC,IAAI,EAAE;AAC5C,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI;AAC5B,EAAE,MAAM,sBAAsB,GAAG,aAAa,CAAC,IAAI,CAAC;AACpD,EAAE,MAAM,sBAAsB,GAAG,aAAa,CAAC,IAAI,CAAC;AACpD,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;AAC7B,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC;AAC7B,EAAE,OAAO,MAAM;AACf,IAAI,aAAa,CAAC,IAAI,EAAE,sBAAsB,CAAC;AAC/C,IAAI,aAAa,CAAC,IAAI,EAAE,sBAAsB,CAAC;AAC/C,GAAG;AACH;AACA,SAAS,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE;AACpC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK;AAC/B,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,KAAK;AACrC;AACA,SAAS,cAAc,CAAC,QAAQ,EAAE;AAClC,EAAE,MAAM,SAAS,GAAG,CAAC,GAAG,UAAU,CAAC,uBAAuB,CAAC;AAC3D,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,OAAO,KAAK;AACrC,EAAE,MAAM,YAAY,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;AACvC,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,KAAK;AACjC,EAAE,OAAO,YAAY,CAAC,CAAC,CAAC,KAAK,QAAQ;AACrC;AACA,SAAS,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE;AAClD,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,4BAA4B,GAAG,IAAI;AACvC,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,WAAW,GAAG,IAAI;AACtB,IAAI,EAAE;AACN,IAAI,QAAQ;AACZ,IAAI;AACJ,GAAG,GAAG,OAAO;AACb,EAAE,qBAAqB,CAAC;AACxB,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,aAAa,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,aAAa,CAAC;AAChD,IAAI,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,WAAW,CAAC;AAC5C,IAAI,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,IAAI,4BAA4B;AACnE,GAAG,CAAC;AACJ,EAAE,QAAQ,GAAG,SAAS,CAAC;AACvB,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,gBAAgB,CAAC,OAAO,EAAE;AACnC,EAAE,IAAI,KAAK,GAAG,MAAM;AACpB,EAAE,OAAO,CAAC,GAAG,IAAI,KAAK;AACtB,IAAI,OAAO,KAAK;AAChB,GAAG;AACH;AACA,MAAM,qBAAqB,GAAG,gBAAgB,EAAE;AAChD,SAAS,iBAAiB,CAAC,YAAY,EAAE,kBAAkB,GAAG,MAAM,IAAI,EAAE;AAC1E,EAAE,KAAK,EAAE;AACT,EAAE,qBAAqB,EAAE;AACzB,EAAE;AACF;AACA,SAAS,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE;AACzC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,aAAa,GAAG,IAAI;AACxB,IAAI,kBAAkB,GAAG;AACzB,GAAG,GAAG,OAAO;AACb,EAAE,iBAAiB,CAAC,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAC5D,EAAE,GAAG,EAAE;AACP;;;;", "x_google_ignoreList": [0]}