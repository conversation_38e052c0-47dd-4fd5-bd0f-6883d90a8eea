{"version": 3, "file": "documentNameUniqueness-BVI8kP6m.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/documentNameUniqueness.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nasync function ensureUniqueDocumentName(label, userId, documentType, excludeDocumentId) {\n  const whereClause = {\n    userId,\n    label,\n    type: documentType\n  };\n  if (excludeDocumentId) {\n    whereClause.id = { not: excludeDocumentId };\n  }\n  const existingDocuments = await prisma.document.findMany({\n    where: whereClause\n  });\n  if (existingDocuments.length === 0) {\n    return label;\n  }\n  const baseNamePattern = label.replace(/\\s*\\(\\d+\\)$/, \"\");\n  const similarDocumentsWhereClause = {\n    userId,\n    type: documentType,\n    label: {\n      startsWith: baseNamePattern\n    }\n  };\n  if (excludeDocumentId) {\n    similarDocumentsWhereClause.id = { not: excludeDocumentId };\n  }\n  const similarDocuments = await prisma.document.findMany({\n    where: similarDocumentsWhereClause\n  });\n  const suffixRegex = new RegExp(`^${baseNamePattern}\\\\s*\\\\((\\\\d+)\\\\)$`);\n  const existingSuffixes = similarDocuments.map((doc) => {\n    const match = doc.label.match(suffixRegex);\n    return match ? parseInt(match[1], 10) : 0;\n  }).filter((num) => !isNaN(num));\n  const nextSuffix = existingSuffixes.length > 0 ? Math.max(...existingSuffixes) + 1 : 1;\n  return `${baseNamePattern} (${nextSuffix})`;\n}\nexport {\n  ensureUniqueDocumentName as e\n};\n"], "names": [], "mappings": ";;AACA,eAAe,wBAAwB,CAAC,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAE;AACxF,EAAE,MAAM,WAAW,GAAG;AACtB,IAAI,MAAM;AACV,IAAI,KAAK;AACT,IAAI,IAAI,EAAE;AACV,GAAG;AACH,EAAE,IAAI,iBAAiB,EAAE;AACzB,IAAI,WAAW,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,iBAAiB,EAAE;AAC/C;AACA,EAAE,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC3D,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;AACtC,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;AAC1D,EAAE,MAAM,2BAA2B,GAAG;AACtC,IAAI,MAAM;AACV,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,KAAK,EAAE;AACX,MAAM,UAAU,EAAE;AAClB;AACA,GAAG;AACH,EAAE,IAAI,iBAAiB,EAAE;AACzB,IAAI,2BAA2B,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,iBAAiB,EAAE;AAC/D;AACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;AAC1D,IAAI,KAAK,EAAE;AACX,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,eAAe,CAAC,iBAAiB,CAAC,CAAC;AACxE,EAAE,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AACzD,IAAI,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;AAC9C,IAAI,OAAO,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AAC7C,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACjC,EAAE,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC;AACxF,EAAE,OAAO,CAAC,EAAE,eAAe,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,CAAC;AAC7C;;;;"}