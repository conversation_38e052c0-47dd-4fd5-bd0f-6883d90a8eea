{"version": 3, "file": "62-BxPq2pK1.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/settings/interview-coach/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/62.js"], "sourcesContent": ["import { p as prisma } from \"../../../../../chunks/prisma.js\";\nimport { e as error } from \"../../../../../chunks/index.js\";\nconst load = async ({ locals }) => {\n  if (!locals.user) {\n    throw error(401, \"Unauthorized\");\n  }\n  try {\n    const user = await prisma.user.findUnique({\n      where: { id: locals.user.id },\n      include: {\n        subscriptions: {\n          where: { status: \"active\" },\n          include: {\n            plan: {\n              include: {\n                features: {\n                  where: { featureId: \"ai_interview_coach\" },\n                  include: {\n                    limits: true\n                  }\n                }\n              }\n            }\n          }\n        },\n        featureUsage: {\n          where: {\n            featureId: \"ai_interview_coach\",\n            limitId: \"ai_interview_sessions_monthly\"\n          }\n        }\n      }\n    });\n    if (!user) {\n      return {\n        hasAccess: process.env.NODE_ENV !== \"production\",\n        usageLimit: null,\n        currentUsage: 0,\n        remainingSessions: null\n      };\n    }\n    const hasAccess = user.subscriptions?.some(\n      (sub) => sub.plan?.features?.some((feature) => feature?.featureId === \"ai_interview_coach\")\n    ) || false;\n    if (!hasAccess && process.env.NODE_ENV === \"production\") {\n      throw error(403, \"Feature not available in your plan\");\n    }\n    const usageLimit = user.subscriptions?.flatMap((sub) => sub.plan?.features ?? []).find((feature) => feature?.featureId === \"ai_interview_coach\")?.limits?.find((limit) => limit?.limitId === \"ai_interview_sessions_monthly\")?.value;\n    const currentUsage = user.featureUsage?.find(\n      (usage) => usage?.featureId === \"ai_interview_coach\" && usage?.limitId === \"ai_interview_sessions_monthly\"\n    )?.used ?? 0;\n    return {\n      hasAccess,\n      usageLimit: usageLimit ? parseInt(usageLimit) : null,\n      currentUsage,\n      remainingSessions: usageLimit ? Math.max(0, parseInt(usageLimit) - currentUsage) : null\n    };\n  } catch (e) {\n    console.error(\"Error loading interview coach data:\", e);\n    throw error(500, \"Failed to load data\");\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/dashboard/settings/interview-coach/_page.server.ts.js';\n\nexport const index = 62;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/dashboard/settings/interview-coach/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/dashboard/settings/interview-coach/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/62.CrCbZVh4.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/BvdI7LR8.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/DuGukytH.js\",\"_app/immutable/chunks/ncUU1dSD.js\",\"_app/immutable/chunks/5V1tIHTN.js\",\"_app/immutable/chunks/Cdn-N1RY.js\",\"_app/immutable/chunks/BkJY4La4.js\",\"_app/immutable/chunks/GwmmX_iF.js\",\"_app/immutable/chunks/D50jIuLr.js\",\"_app/immutable/chunks/I7hvcB12.js\",\"_app/immutable/chunks/BfX7a-t9.js\",\"_app/immutable/chunks/BosuxZz1.js\",\"_app/immutable/chunks/CnMg5bH0.js\",\"_app/immutable/chunks/BJIrNhIJ.js\",\"_app/immutable/chunks/DuoUhxYL.js\",\"_app/immutable/chunks/Bd3zs5C6.js\",\"_app/immutable/chunks/OXTnUuEm.js\",\"_app/immutable/chunks/CIOgxH3l.js\",\"_app/immutable/chunks/Bpi49Nrf.js\",\"_app/immutable/chunks/DX6rZLP_.js\",\"_app/immutable/chunks/tdzGgazS.js\",\"_app/immutable/chunks/BaVT73bJ.js\",\"_app/immutable/chunks/DT9WCdWY.js\",\"_app/immutable/chunks/OOsIR5sE.js\",\"_app/immutable/chunks/Cb-3cdbh.js\",\"_app/immutable/chunks/DMoa_yM9.js\",\"_app/immutable/chunks/XESq6qWN.js\",\"_app/immutable/chunks/CnpHcmx3.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/DaBofrVv.js\",\"_app/immutable/chunks/DM07Bv7T.js\",\"_app/immutable/chunks/DMTMHyMa.js\",\"_app/immutable/chunks/CzsE_FAw.js\",\"_app/immutable/chunks/0ykhD7u6.js\",\"_app/immutable/chunks/B1K98fMG.js\",\"_app/immutable/chunks/DjPYYl4Z.js\",\"_app/immutable/chunks/CPe_16wQ.js\",\"_app/immutable/chunks/QtAhPN2H.js\",\"_app/immutable/chunks/BBNNmnYR.js\",\"_app/immutable/chunks/FAbXdqfL.js\",\"_app/immutable/chunks/DkmCSZhC.js\",\"_app/immutable/chunks/D871oxnv.js\",\"_app/immutable/chunks/C88uNE8B.js\",\"_app/immutable/chunks/-SpbofVw.js\",\"_app/immutable/chunks/DR5zc253.js\",\"_app/immutable/chunks/DmZyh-PW.js\",\"_app/immutable/chunks/yW0TxTga.js\",\"_app/immutable/chunks/CKh8VGVX.js\",\"_app/immutable/chunks/BKLOCbjP.js\",\"_app/immutable/chunks/C2AK_5VT.js\"];\nexport const stylesheets = [\"_app/immutable/assets/Toaster.DKF17Rty.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;;AAEA,MAAM,IAAI,GAAG,OAAO,EAAE,MAAM,EAAE,KAAK;AACnC,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,cAAc,CAAC;AACpC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE;AACnC,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,KAAK,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE;AACrC,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB,cAAc,OAAO,EAAE;AACvB,gBAAgB,QAAQ,EAAE;AAC1B,kBAAkB,KAAK,EAAE,EAAE,SAAS,EAAE,oBAAoB,EAAE;AAC5D,kBAAkB,OAAO,EAAE;AAC3B,oBAAoB,MAAM,EAAE;AAC5B;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB,UAAU,KAAK,EAAE;AACjB,YAAY,SAAS,EAAE,oBAAoB;AAC3C,YAAY,OAAO,EAAE;AACrB;AACA;AACA;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,OAAO;AACb,QAAQ,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;AACxD,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,YAAY,EAAE,CAAC;AACvB,QAAQ,iBAAiB,EAAE;AAC3B,OAAO;AACP;AACA,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,IAAI;AAC9C,MAAM,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE,SAAS,KAAK,oBAAoB;AAChG,KAAK,IAAI,KAAK;AACd,IAAI,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE;AAC7D,MAAM,MAAM,KAAK,CAAC,GAAG,EAAE,oCAAoC,CAAC;AAC5D;AACA,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO,EAAE,SAAS,KAAK,oBAAoB,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE,OAAO,KAAK,+BAA+B,CAAC,EAAE,KAAK;AACxO,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,IAAI;AAChD,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE,SAAS,KAAK,oBAAoB,IAAI,KAAK,EAAE,OAAO,KAAK;AACjF,KAAK,EAAE,IAAI,IAAI,CAAC;AAChB,IAAI,OAAO;AACX,MAAM,SAAS;AACf,MAAM,UAAU,EAAE,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI;AAC1D,MAAM,YAAY;AAClB,MAAM,iBAAiB,EAAE,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,UAAU,CAAC,GAAG,YAAY,CAAC,GAAG;AACzF,KAAK;AACL,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,CAAC,CAAC;AAC3D,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE,qBAAqB,CAAC;AAC3C;AACA,CAAC;;;;;;;AC3DW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAqE,CAAC,EAAE;AAEnI,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AAC7wE,MAAC,WAAW,GAAG,CAAC,4CAA4C;AAC5D,MAAC,KAAK,GAAG;;;;"}