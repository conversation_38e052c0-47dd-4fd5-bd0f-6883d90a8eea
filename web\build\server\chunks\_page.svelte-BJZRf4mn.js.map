{"version": 3, "file": "_page.svelte-BJZRf4mn.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/dashboard/resumes/_id_/optimize/_page.svelte.js"], "sourcesContent": ["import { V as escape_html, U as ensure_array_like, N as bind_props, y as pop, w as push } from \"../../../../../../chunks/index3.js\";\nimport { C as Card } from \"../../../../../../chunks/card.js\";\nimport { C as Card_content } from \"../../../../../../chunks/card-content.js\";\nimport { C as Card_header } from \"../../../../../../chunks/card-header.js\";\nimport { C as Card_title } from \"../../../../../../chunks/card-title.js\";\nfunction _page($$payload, $$props) {\n  push();\n  let data = $$props[\"data\"];\n  $$payload.out += `<h1 class=\"mb-4 text-2xl font-semibold\">Optimize Resume</h1> <div class=\"text-muted-foreground mb-6 text-sm\">Dashboard > Resume > Optimize</div> `;\n  Card($$payload, {\n    class: \"mb-6\",\n    children: ($$payload2) => {\n      Card_header($$payload2, {\n        children: ($$payload3) => {\n          Card_title($$payload3, {\n            children: ($$payload4) => {\n              $$payload4.out += `<!---->${escape_html(data.resume.name)}`;\n            },\n            $$slots: { default: true }\n          });\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!----> `;\n      Card_content($$payload2, {\n        children: ($$payload3) => {\n          $$payload3.out += `<p class=\"text-muted-foreground mb-2 text-sm\">Last updated: ${escape_html(new Date(data.resume.updatedAt).toLocaleDateString())}</p> `;\n          if (data.optimization) {\n            $$payload3.out += \"<!--[-->\";\n            $$payload3.out += `<div class=\"space-y-4\">`;\n            if (data.optimization.score !== null) {\n              $$payload3.out += \"<!--[-->\";\n              $$payload3.out += `<p><strong>Optimization Score:</strong> ${escape_html(data.optimization.score)}%</p>`;\n            } else {\n              $$payload3.out += \"<!--[!-->\";\n            }\n            $$payload3.out += `<!--]--> `;\n            if (data.optimization.summary) {\n              $$payload3.out += \"<!--[-->\";\n              $$payload3.out += `<div><p class=\"font-semibold\">Summary:</p> <p class=\"text-muted-foreground text-sm\">${escape_html(data.optimization.summary)}</p></div>`;\n            } else {\n              $$payload3.out += \"<!--[!-->\";\n            }\n            $$payload3.out += `<!--]--> `;\n            if (data.optimization.suggestions?.length) {\n              $$payload3.out += \"<!--[-->\";\n              const each_array = ensure_array_like(data.optimization.suggestions);\n              $$payload3.out += `<div><p class=\"font-semibold\">Suggestions:</p> <ul class=\"text-muted-foreground list-inside list-disc text-sm\"><!--[-->`;\n              for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {\n                let item = each_array[$$index];\n                $$payload3.out += `<li><strong>${escape_html(item.section)}:</strong> ${escape_html(item.suggestion)}</li>`;\n              }\n              $$payload3.out += `<!--]--></ul></div>`;\n            } else {\n              $$payload3.out += \"<!--[!-->\";\n            }\n            $$payload3.out += `<!--]--></div>`;\n          } else {\n            $$payload3.out += \"<!--[!-->\";\n            $$payload3.out += `<p class=\"text-muted-foreground text-sm italic\">Optimization is pending. Please check back shortly.</p>`;\n          }\n          $$payload3.out += `<!--]-->`;\n        },\n        $$slots: { default: true }\n      });\n      $$payload2.out += `<!---->`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!---->`;\n  bind_props($$props, { data });\n  pop();\n}\nexport {\n  _page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAKA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;AAC5B,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iJAAiJ,CAAC;AACtK,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,KAAK,EAAE,MAAM;AACjB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,WAAW,CAAC,UAAU,EAAE;AAC9B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,UAAU,EAAE;AACjC,YAAY,QAAQ,EAAE,CAAC,UAAU,KAAK;AACtC,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;AACzE,aAAa;AACb,YAAY,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AACpC,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC,MAAM,YAAY,CAAC,UAAU,EAAE;AAC/B,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,4DAA4D,EAAE,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,KAAK,CAAC;AACnK,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE;AACjC,YAAY,UAAU,CAAC,GAAG,IAAI,UAAU;AACxC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC;AACvD,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,IAAI,EAAE;AAClD,cAAc,UAAU,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,wCAAwC,EAAE,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;AACtH,aAAa,MAAM;AACnB,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACzC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AAC3C,cAAc,UAAU,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,oFAAoF,EAAE,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC;AACzK,aAAa,MAAM;AACnB,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACzC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,EAAE;AACvD,cAAc,UAAU,CAAC,GAAG,IAAI,UAAU;AAC1C,cAAc,MAAM,UAAU,GAAG,iBAAiB,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;AACjF,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,uHAAuH,CAAC;AACzJ,cAAc,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,QAAQ,GAAG,UAAU,CAAC,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE,OAAO,EAAE,EAAE;AACjG,gBAAgB,IAAI,IAAI,GAAG,UAAU,CAAC,OAAO,CAAC;AAC9C,gBAAgB,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC;AAC3H;AACA,cAAc,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACrD,aAAa,MAAM;AACnB,cAAc,UAAU,CAAC,GAAG,IAAI,WAAW;AAC3C;AACA,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AAC9C,WAAW,MAAM;AACjB,YAAY,UAAU,CAAC,GAAG,IAAI,WAAW;AACzC,YAAY,UAAU,CAAC,GAAG,IAAI,CAAC,uGAAuG,CAAC;AACvI;AACA,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACtC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC,OAAO,CAAC;AACR,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;;;;"}