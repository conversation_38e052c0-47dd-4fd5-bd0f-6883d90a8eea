{"version": 3, "file": "_server.ts-DcB1sQJq.js", "sources": ["../../../.svelte-kit/adapter-node/entries/endpoints/api/companies/featured/_server.ts.js"], "sourcesContent": ["import { j as json } from \"../../../../../chunks/index.js\";\nimport { p as prisma } from \"../../../../../chunks/prisma.js\";\nconst GET = async () => {\n  try {\n    if (!prisma) {\n      console.error(\"Prisma client is not initialized\");\n      return json({ error: \"Database connection not available\" }, { status: 500 });\n    }\n    const totalCompanies = await prisma.company.count();\n    console.log(`📊 Total companies in database: ${totalCompanies}`);\n    const companiesWithJobs = await prisma.company.count({\n      where: { activeJobCount: { gt: 0 } }\n    });\n    console.log(`📊 Companies with active jobs: ${companiesWithJobs}`);\n    const companies = await prisma.company.findMany({\n      where: {\n        logoUrl: { not: null }\n        // Only companies with logos\n        // Remove the activeJobCount requirement for now to ensure we get companies\n      },\n      select: {\n        id: true,\n        name: true,\n        logoUrl: true,\n        companySize: true,\n        companyStage: true,\n        activeJobCount: true\n      },\n      orderBy: [{ activeJobCount: \"desc\" }, { name: \"asc\" }],\n      take: 60\n      // Get top 60 companies to distribute across categories\n    });\n    console.log(`📊 Retrieved ${companies.length} companies for carousel`);\n    const categorized = {\n      startups: [],\n      growth: [],\n      enterprise: []\n    };\n    companies.forEach((company) => {\n      const size = company.companySize;\n      if (size === \"SIZE_1_10\" || size === \"SIZE_11_50\") {\n        categorized.startups.push(company);\n      } else if (size === \"SIZE_51_200\" || size === \"SIZE_201_500\") {\n        categorized.growth.push(company);\n      } else if (size === \"SIZE_501_1000\" || size === \"SIZE_1001_5000\" || size === \"SIZE_5001_10000\" || size === \"SIZE_10000_PLUS\") {\n        categorized.enterprise.push(company);\n      } else {\n        if (company.activeJobCount && company.activeJobCount > 100) {\n          categorized.enterprise.push(company);\n        } else if (company.activeJobCount && company.activeJobCount > 20) {\n          categorized.growth.push(company);\n        } else {\n          categorized.startups.push(company);\n        }\n      }\n    });\n    const minPerCategory = 8;\n    if (categorized.startups.length < minPerCategory) {\n      const needed = minPerCategory - categorized.startups.length;\n      const fromGrowth = categorized.growth.splice(0, Math.min(needed, categorized.growth.length));\n      categorized.startups.push(...fromGrowth);\n    }\n    if (categorized.growth.length < minPerCategory) {\n      const needed = minPerCategory - categorized.growth.length;\n      const fromEnterprise = categorized.enterprise.splice(\n        0,\n        Math.min(needed, categorized.enterprise.length)\n      );\n      categorized.growth.push(...fromEnterprise);\n    }\n    if (categorized.enterprise.length < minPerCategory) {\n      const needed = minPerCategory - categorized.enterprise.length;\n      const fromStartups = categorized.startups.splice(\n        -Math.min(needed, categorized.startups.length)\n      );\n      categorized.enterprise.push(...fromStartups);\n    }\n    const maxPerCategory = 20;\n    categorized.startups = categorized.startups.slice(0, maxPerCategory);\n    categorized.growth = categorized.growth.slice(0, maxPerCategory);\n    categorized.enterprise = categorized.enterprise.slice(0, maxPerCategory);\n    return json(categorized);\n  } catch (error) {\n    console.error(\"Error fetching featured companies:\", error);\n    return json({ error: \"Failed to fetch featured companies\" }, { status: 500 });\n  }\n};\nexport {\n  GET\n};\n"], "names": [], "mappings": ";;;;AAEK,MAAC,GAAG,GAAG,YAAY;AACxB,EAAE,IAAI;AACN,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,OAAO,CAAC,KAAK,CAAC,kCAAkC,CAAC;AACvD,MAAM,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,mCAAmC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AAClF;AACA,IAAI,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;AACvD,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,gCAAgC,EAAE,cAAc,CAAC,CAAC,CAAC;AACpE,IAAI,MAAM,iBAAiB,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AACzD,MAAM,KAAK,EAAE,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE;AACxC,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,+BAA+B,EAAE,iBAAiB,CAAC,CAAC,CAAC;AACtE,IAAI,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACpD,MAAM,KAAK,EAAE;AACb,QAAQ,OAAO,EAAE,EAAE,GAAG,EAAE,IAAI;AAC5B;AACA;AACA,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,EAAE,EAAE,IAAI;AAChB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,OAAO,EAAE,IAAI;AACrB,QAAQ,WAAW,EAAE,IAAI;AACzB,QAAQ,YAAY,EAAE,IAAI;AAC1B,QAAQ,cAAc,EAAE;AACxB,OAAO;AACP,MAAM,OAAO,EAAE,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AAC5D,MAAM,IAAI,EAAE;AACZ;AACA,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAC1E,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM,QAAQ,EAAE,EAAE;AAClB,MAAM,MAAM,EAAE,EAAE;AAChB,MAAM,UAAU,EAAE;AAClB,KAAK;AACL,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AACnC,MAAM,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW;AACtC,MAAM,IAAI,IAAI,KAAK,WAAW,IAAI,IAAI,KAAK,YAAY,EAAE;AACzD,QAAQ,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAC1C,OAAO,MAAM,IAAI,IAAI,KAAK,aAAa,IAAI,IAAI,KAAK,cAAc,EAAE;AACpE,QAAQ,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AACxC,OAAO,MAAM,IAAI,IAAI,KAAK,eAAe,IAAI,IAAI,KAAK,gBAAgB,IAAI,IAAI,KAAK,iBAAiB,IAAI,IAAI,KAAK,iBAAiB,EAAE;AACpI,QAAQ,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,IAAI,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,GAAG,GAAG,EAAE;AACpE,UAAU,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;AAC9C,SAAS,MAAM,IAAI,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,cAAc,GAAG,EAAE,EAAE;AAC1E,UAAU,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC;AAC1C,SAAS,MAAM;AACf,UAAU,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;AAC5C;AACA;AACA,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,CAAC;AAC5B,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,cAAc,EAAE;AACtD,MAAM,MAAM,MAAM,GAAG,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM;AACjE,MAAM,MAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAClG,MAAM,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;AAC9C;AACA,IAAI,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,cAAc,EAAE;AACpD,MAAM,MAAM,MAAM,GAAG,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM;AAC/D,MAAM,MAAM,cAAc,GAAG,WAAW,CAAC,UAAU,CAAC,MAAM;AAC1D,QAAQ,CAAC;AACT,QAAQ,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,UAAU,CAAC,MAAM;AACtD,OAAO;AACP,MAAM,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;AAChD;AACA,IAAI,IAAI,WAAW,CAAC,UAAU,CAAC,MAAM,GAAG,cAAc,EAAE;AACxD,MAAM,MAAM,MAAM,GAAG,cAAc,GAAG,WAAW,CAAC,UAAU,CAAC,MAAM;AACnE,MAAM,MAAM,YAAY,GAAG,WAAW,CAAC,QAAQ,CAAC,MAAM;AACtD,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM;AACrD,OAAO;AACP,MAAM,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;AAClD;AACA,IAAI,MAAM,cAAc,GAAG,EAAE;AAC7B,IAAI,WAAW,CAAC,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC;AACxE,IAAI,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC;AACpE,IAAI,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC;AAC5E,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC;AAC9D,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACjF;AACA;;;;"}