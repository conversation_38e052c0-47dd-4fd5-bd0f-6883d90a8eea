{"version": 3, "file": "index14-C2WSwUih.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/index14.js"], "sourcesContent": ["import { J as derived, w as push, Q as spread_props, N as bind_props, y as pop, M as spread_attributes, O as copy_payload, P as assign_payload } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nimport { P as Portal } from \"./scroll-lock.js\";\nimport { b as box } from \"./watch.svelte.js\";\nimport \"style-to-object\";\nimport { u as useRefById, m as mergeProps } from \"./use-ref-by-id.svelte.js\";\nimport { P as Popper_layer_force_mount, a as Popper_layer, g as getFloatingContentCSSVars, b as Floating_layer_anchor, F as Floating_layer } from \"./popper-layer-force-mount.js\";\nimport { n as noop } from \"./noop.js\";\nimport { u as useId } from \"./use-id.js\";\nimport \"clsx\";\nimport { C as Context } from \"./context.js\";\nimport { i as ENTER, S as SPACE, d as getDataOpenClosed, c as getAriaExpanded } from \"./kbd-constants.js\";\nimport { i as isElement } from \"./is.js\";\nclass PopoverRootState {\n  opts;\n  contentNode = null;\n  triggerNode = null;\n  constructor(opts) {\n    this.opts = opts;\n  }\n  toggleOpen() {\n    this.opts.open.current = !this.opts.open.current;\n  }\n  handleClose() {\n    if (!this.opts.open.current) return;\n    this.opts.open.current = false;\n  }\n}\nclass PopoverTriggerState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById({\n      ...opts,\n      onRefChange: (node) => {\n        this.root.triggerNode = node;\n      }\n    });\n    this.onclick = this.onclick.bind(this);\n    this.onkeydown = this.onkeydown.bind(this);\n  }\n  onclick(e) {\n    if (this.opts.disabled.current) return;\n    if (e.button !== 0) return;\n    this.root.toggleOpen();\n  }\n  onkeydown(e) {\n    if (this.opts.disabled.current) return;\n    if (!(e.key === ENTER || e.key === SPACE)) return;\n    e.preventDefault();\n    this.root.toggleOpen();\n  }\n  #getAriaControls() {\n    if (this.root.opts.open.current && this.root.contentNode?.id) {\n      return this.root.contentNode?.id;\n    }\n    return void 0;\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    \"aria-haspopup\": \"dialog\",\n    \"aria-expanded\": getAriaExpanded(this.root.opts.open.current),\n    \"data-state\": getDataOpenClosed(this.root.opts.open.current),\n    \"aria-controls\": this.#getAriaControls(),\n    \"data-popover-trigger\": \"\",\n    disabled: this.opts.disabled.current,\n    //\n    onkeydown: this.onkeydown,\n    onclick: this.onclick\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n}\nclass PopoverContentState {\n  opts;\n  root;\n  constructor(opts, root) {\n    this.opts = opts;\n    this.root = root;\n    useRefById({\n      ...opts,\n      deps: () => this.root.opts.open.current,\n      onRefChange: (node) => {\n        this.root.contentNode = node;\n      }\n    });\n  }\n  onInteractOutside = (e) => {\n    this.opts.onInteractOutside.current(e);\n    if (e.defaultPrevented) return;\n    if (!isElement(e.target)) return;\n    const closestTrigger = e.target.closest(`[data-popover-trigger]`);\n    if (closestTrigger === this.root.triggerNode) return;\n    this.root.handleClose();\n  };\n  onEscapeKeydown = (e) => {\n    this.opts.onEscapeKeydown.current(e);\n    if (e.defaultPrevented) return;\n    this.root.handleClose();\n  };\n  onCloseAutoFocus = (e) => {\n    this.opts.onCloseAutoFocus.current(e);\n    if (e.defaultPrevented) return;\n    e.preventDefault();\n    this.root.triggerNode?.focus();\n  };\n  #snippetProps = derived(() => ({ open: this.root.opts.open.current }));\n  get snippetProps() {\n    return this.#snippetProps();\n  }\n  set snippetProps($$value) {\n    return this.#snippetProps($$value);\n  }\n  #props = derived(() => ({\n    id: this.opts.id.current,\n    tabindex: -1,\n    \"data-state\": getDataOpenClosed(this.root.opts.open.current),\n    \"data-popover-content\": \"\",\n    style: { pointerEvents: \"auto\" }\n  }));\n  get props() {\n    return this.#props();\n  }\n  set props($$value) {\n    return this.#props($$value);\n  }\n  popperProps = {\n    onInteractOutside: this.onInteractOutside,\n    onEscapeKeydown: this.onEscapeKeydown,\n    onCloseAutoFocus: this.onCloseAutoFocus\n  };\n}\nconst PopoverRootContext = new Context(\"Popover.Root\");\nfunction usePopoverRoot(props) {\n  return PopoverRootContext.set(new PopoverRootState(props));\n}\nfunction usePopoverTrigger(props) {\n  return new PopoverTriggerState(props, PopoverRootContext.get());\n}\nfunction usePopoverContent(props) {\n  return new PopoverContentState(props, PopoverRootContext.get());\n}\nfunction Popover_content$1($$payload, $$props) {\n  push();\n  let {\n    child,\n    children,\n    ref = null,\n    id = useId(),\n    forceMount = false,\n    onCloseAutoFocus = noop,\n    onEscapeKeydown = noop,\n    onInteractOutside = noop,\n    trapFocus = true,\n    preventScroll = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const contentState = usePopoverContent({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v),\n    onInteractOutside: box.with(() => onInteractOutside),\n    onEscapeKeydown: box.with(() => onEscapeKeydown),\n    onCloseAutoFocus: box.with(() => onCloseAutoFocus)\n  });\n  const mergedProps = mergeProps(restProps, contentState.props);\n  if (forceMount) {\n    $$payload.out += \"<!--[-->\";\n    {\n      let popper = function($$payload2, { props, wrapperProps }) {\n        const finalProps = mergeProps(props, {\n          style: getFloatingContentCSSVars(\"popover\")\n        });\n        if (child) {\n          $$payload2.out += \"<!--[-->\";\n          child($$payload2, {\n            props: finalProps,\n            wrapperProps,\n            ...contentState.snippetProps\n          });\n          $$payload2.out += `<!---->`;\n        } else {\n          $$payload2.out += \"<!--[!-->\";\n          $$payload2.out += `<div${spread_attributes({ ...wrapperProps }, null)}><div${spread_attributes({ ...finalProps }, null)}>`;\n          children?.($$payload2);\n          $$payload2.out += `<!----></div></div>`;\n        }\n        $$payload2.out += `<!--]-->`;\n      };\n      Popper_layer_force_mount($$payload, spread_props([\n        mergedProps,\n        contentState.popperProps,\n        {\n          enabled: contentState.root.opts.open.current,\n          id,\n          trapFocus,\n          preventScroll,\n          loop: true,\n          forceMount: true,\n          popper,\n          $$slots: { popper: true }\n        }\n      ]));\n    }\n  } else if (!forceMount) {\n    $$payload.out += \"<!--[1-->\";\n    {\n      let popper = function($$payload2, { props, wrapperProps }) {\n        const finalProps = mergeProps(props, {\n          style: getFloatingContentCSSVars(\"popover\")\n        });\n        if (child) {\n          $$payload2.out += \"<!--[-->\";\n          child($$payload2, {\n            props: finalProps,\n            wrapperProps,\n            ...contentState.snippetProps\n          });\n          $$payload2.out += `<!---->`;\n        } else {\n          $$payload2.out += \"<!--[!-->\";\n          $$payload2.out += `<div${spread_attributes({ ...wrapperProps }, null)}><div${spread_attributes({ ...finalProps }, null)}>`;\n          children?.($$payload2);\n          $$payload2.out += `<!----></div></div>`;\n        }\n        $$payload2.out += `<!--]-->`;\n      };\n      Popper_layer($$payload, spread_props([\n        mergedProps,\n        contentState.popperProps,\n        {\n          present: contentState.root.opts.open.current,\n          id,\n          trapFocus,\n          preventScroll,\n          loop: true,\n          forceMount: false,\n          popper,\n          $$slots: { popper: true }\n        }\n      ]));\n    }\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Popover_trigger$1($$payload, $$props) {\n  push();\n  let {\n    children,\n    child,\n    id = useId(),\n    ref = null,\n    type = \"button\",\n    disabled = false,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  const triggerState = usePopoverTrigger({\n    id: box.with(() => id),\n    ref: box.with(() => ref, (v) => ref = v),\n    disabled: box.with(() => Boolean(disabled))\n  });\n  const mergedProps = mergeProps(restProps, triggerState.props, { type });\n  Floating_layer_anchor($$payload, {\n    id,\n    children: ($$payload2) => {\n      if (child) {\n        $$payload2.out += \"<!--[-->\";\n        child($$payload2, { props: mergedProps });\n        $$payload2.out += `<!---->`;\n      } else {\n        $$payload2.out += \"<!--[!-->\";\n        $$payload2.out += `<button${spread_attributes({ ...mergedProps }, null)}>`;\n        children?.($$payload2);\n        $$payload2.out += `<!----></button>`;\n      }\n      $$payload2.out += `<!--]-->`;\n    }\n  });\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Popover($$payload, $$props) {\n  push();\n  let { open = false, onOpenChange = noop, children } = $$props;\n  usePopoverRoot({\n    open: box.with(() => open, (v) => {\n      open = v;\n      onOpenChange(v);\n    })\n  });\n  Floating_layer($$payload, {\n    children: ($$payload2) => {\n      children?.($$payload2);\n      $$payload2.out += `<!---->`;\n    }\n  });\n  bind_props($$props, { open });\n  pop();\n}\nfunction Popover_content($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    sideOffset = 4,\n    align = \"center\",\n    portalProps,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Portal($$payload2, spread_props([\n      portalProps,\n      {\n        children: ($$payload3) => {\n          $$payload3.out += `<!---->`;\n          Popover_content$1($$payload3, spread_props([\n            {\n              \"data-slot\": \"popover-content\",\n              sideOffset,\n              align,\n              class: cn(\"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-(--bits-popover-content-transform-origin) outline-hidden z-50 w-72 rounded-md border p-4 shadow-md\", className)\n            },\n            restProps,\n            {\n              get ref() {\n                return ref;\n              },\n              set ref($$value) {\n                ref = $$value;\n                $$settled = false;\n              }\n            }\n          ]));\n          $$payload3.out += `<!---->`;\n        },\n        $$slots: { default: true }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nfunction Popover_trigger($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  let $$settled = true;\n  let $$inner_payload;\n  function $$render_inner($$payload2) {\n    $$payload2.out += `<!---->`;\n    Popover_trigger$1($$payload2, spread_props([\n      {\n        \"data-slot\": \"popover-trigger\",\n        class: cn(\"\", className)\n      },\n      restProps,\n      {\n        get ref() {\n          return ref;\n        },\n        set ref($$value) {\n          ref = $$value;\n          $$settled = false;\n        }\n      }\n    ]));\n    $$payload2.out += `<!---->`;\n  }\n  do {\n    $$settled = true;\n    $$inner_payload = copy_payload($$payload);\n    $$render_inner($$inner_payload);\n  } while (!$$settled);\n  assign_payload($$payload, $$inner_payload);\n  bind_props($$props, { ref });\n  pop();\n}\nconst Root = Popover;\nexport {\n  Popover_trigger as P,\n  Root as R,\n  Popover_content as a\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA,MAAM,gBAAgB,CAAC;AACvB,EAAE,IAAI;AACN,EAAE,WAAW,GAAG,IAAI;AACpB,EAAE,WAAW,GAAG,IAAI;AACpB,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACpD;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;AACjC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,KAAK;AAClC;AACA;AACA,MAAM,mBAAmB,CAAC;AAC1B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI;AACpC;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1C,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AAC9C;AACA,EAAE,OAAO,CAAC,CAAC,EAAE;AACb,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC1B;AACA,EAAE,SAAS,CAAC,CAAC,EAAE;AACf,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;AACpC,IAAI,IAAI,EAAE,CAAC,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,CAAC,EAAE;AAC/C,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AAC1B;AACA,EAAE,gBAAgB,GAAG;AACrB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,EAAE;AAClE,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;AACtC;AACA,IAAI,OAAO,MAAM;AACjB;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,eAAe,EAAE,QAAQ;AAC7B,IAAI,eAAe,EAAE,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACjE,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAChE,IAAI,eAAe,EAAE,IAAI,CAAC,gBAAgB,EAAE;AAC5C,IAAI,sBAAsB,EAAE,EAAE;AAC9B,IAAI,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO;AACxC;AACA,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;AAC7B,IAAI,OAAO,EAAE,IAAI,CAAC;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA;AACA,MAAM,mBAAmB,CAAC;AAC1B,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI;AACpB,IAAI,UAAU,CAAC;AACf,MAAM,GAAG,IAAI;AACb,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AAC7C,MAAM,WAAW,EAAE,CAAC,IAAI,KAAK;AAC7B,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI;AACpC;AACA,KAAK,CAAC;AACN;AACA,EAAE,iBAAiB,GAAG,CAAC,CAAC,KAAK;AAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE;AAC9B,IAAI,MAAM,cAAc,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,CAAC;AACrE,IAAI,IAAI,cAAc,KAAK,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAClD,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC3B,GAAG;AACH,EAAE,eAAe,GAAG,CAAC,CAAC,KAAK;AAC3B,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;AACxC,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AAC3B,GAAG;AACH,EAAE,gBAAgB,GAAG,CAAC,CAAC,KAAK;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,CAAC,gBAAgB,EAAE;AAC5B,IAAI,CAAC,CAAC,cAAc,EAAE;AACtB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE;AAClC,GAAG;AACH,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;AACxE,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,EAAE;AAC/B;AACA,EAAE,IAAI,YAAY,CAAC,OAAO,EAAE;AAC5B,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AACtC;AACA,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO;AAC1B,IAAI,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO;AAC5B,IAAI,QAAQ,EAAE,EAAE;AAChB,IAAI,YAAY,EAAE,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AAChE,IAAI,sBAAsB,EAAE,EAAE;AAC9B,IAAI,KAAK,EAAE,EAAE,aAAa,EAAE,MAAM;AAClC,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE;AACxB;AACA,EAAE,IAAI,KAAK,CAAC,OAAO,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,WAAW,GAAG;AAChB,IAAI,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;AAC7C,IAAI,eAAe,EAAE,IAAI,CAAC,eAAe;AACzC,IAAI,gBAAgB,EAAE,IAAI,CAAC;AAC3B,GAAG;AACH;AACA,MAAM,kBAAkB,GAAG,IAAI,OAAO,CAAC,cAAc,CAAC;AACtD,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,OAAO,kBAAkB,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC;AAC5D;AACA,SAAS,iBAAiB,CAAC,KAAK,EAAE;AAClC,EAAE,OAAO,IAAI,mBAAmB,CAAC,KAAK,EAAE,kBAAkB,CAAC,GAAG,EAAE,CAAC;AACjE;AACA,SAAS,iBAAiB,CAAC,KAAK,EAAE;AAClC,EAAE,OAAO,IAAI,mBAAmB,CAAC,KAAK,EAAE,kBAAkB,CAAC,GAAG,EAAE,CAAC;AACjE;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,KAAK;AACT,IAAI,QAAQ;AACZ,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,UAAU,GAAG,KAAK;AACtB,IAAI,gBAAgB,GAAG,IAAI;AAC3B,IAAI,eAAe,GAAG,IAAI;AAC1B,IAAI,iBAAiB,GAAG,IAAI;AAC5B,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,aAAa,GAAG,KAAK;AACzB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC;AACzC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,iBAAiB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,iBAAiB,CAAC;AACxD,IAAI,eAAe,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,eAAe,CAAC;AACpD,IAAI,gBAAgB,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,gBAAgB;AACrD,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,CAAC;AAC/D,EAAE,IAAI,UAAU,EAAE;AAClB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI;AACJ,MAAM,IAAI,MAAM,GAAG,SAAS,UAAU,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;AACjE,QAAQ,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE;AAC7C,UAAU,KAAK,EAAE,yBAAyB,CAAC,SAAS;AACpD,SAAS,CAAC;AACV,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,UAAU,CAAC,GAAG,IAAI,UAAU;AACtC,UAAU,KAAK,CAAC,UAAU,EAAE;AAC5B,YAAY,KAAK,EAAE,UAAU;AAC7B,YAAY,YAAY;AACxB,YAAY,GAAG,YAAY,CAAC;AAC5B,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,IAAI,WAAW;AACvC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,YAAY,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACpI,UAAU,QAAQ,GAAG,UAAU,CAAC;AAChC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACjD;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,OAAO;AACP,MAAM,wBAAwB,CAAC,SAAS,EAAE,YAAY,CAAC;AACvD,QAAQ,WAAW;AACnB,QAAQ,YAAY,CAAC,WAAW;AAChC,QAAQ;AACR,UAAU,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACtD,UAAU,EAAE;AACZ,UAAU,SAAS;AACnB,UAAU,aAAa;AACvB,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,UAAU,EAAE,IAAI;AAC1B,UAAU,MAAM;AAChB,UAAU,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI;AACjC;AACA,OAAO,CAAC,CAAC;AACT;AACA,GAAG,MAAM,IAAI,CAAC,UAAU,EAAE;AAC1B,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI;AACJ,MAAM,IAAI,MAAM,GAAG,SAAS,UAAU,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,EAAE;AACjE,QAAQ,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,EAAE;AAC7C,UAAU,KAAK,EAAE,yBAAyB,CAAC,SAAS;AACpD,SAAS,CAAC;AACV,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,UAAU,CAAC,GAAG,IAAI,UAAU;AACtC,UAAU,KAAK,CAAC,UAAU,EAAE;AAC5B,YAAY,KAAK,EAAE,UAAU;AAC7B,YAAY,YAAY;AACxB,YAAY,GAAG,YAAY,CAAC;AAC5B,WAAW,CAAC;AACZ,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS,MAAM;AACf,UAAU,UAAU,CAAC,GAAG,IAAI,WAAW;AACvC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB,CAAC,EAAE,GAAG,YAAY,EAAE,EAAE,IAAI,CAAC,CAAC,KAAK,EAAE,iBAAiB,CAAC,EAAE,GAAG,UAAU,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AACpI,UAAU,QAAQ,GAAG,UAAU,CAAC;AAChC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACjD;AACA,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,OAAO;AACP,MAAM,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC;AAC3C,QAAQ,WAAW;AACnB,QAAQ,YAAY,CAAC,WAAW;AAChC,QAAQ;AACR,UAAU,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;AACtD,UAAU,EAAE;AACZ,UAAU,SAAS;AACnB,UAAU,aAAa;AACvB,UAAU,IAAI,EAAE,IAAI;AACpB,UAAU,UAAU,EAAE,KAAK;AAC3B,UAAU,MAAM;AAChB,UAAU,OAAO,EAAE,EAAE,MAAM,EAAE,IAAI;AACjC;AACA,OAAO,CAAC,CAAC;AACT;AACA,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,iBAAiB,CAAC,SAAS,EAAE,OAAO,EAAE;AAC/C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,EAAE,GAAG,KAAK,EAAE;AAChB,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,IAAI,GAAG,QAAQ;AACnB,IAAI,QAAQ,GAAG,KAAK;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,MAAM,YAAY,GAAG,iBAAiB,CAAC;AACzC,IAAI,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;AAC5C,IAAI,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,CAAC;AAC9C,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,EAAE,YAAY,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC;AACzE,EAAE,qBAAqB,CAAC,SAAS,EAAE;AACnC,IAAI,EAAE;AACN,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,IAAI,KAAK,EAAE;AACjB,QAAQ,UAAU,CAAC,GAAG,IAAI,UAAU;AACpC,QAAQ,KAAK,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;AACjD,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,OAAO,MAAM;AACb,QAAQ,UAAU,CAAC,GAAG,IAAI,WAAW;AACrC,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,iBAAiB,CAAC,EAAE,GAAG,WAAW,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAClF,QAAQ,QAAQ,GAAG,UAAU,CAAC;AAC9B,QAAQ,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC5C;AACA,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAClC;AACA,GAAG,CAAC;AACJ,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,OAAO,CAAC,SAAS,EAAE,OAAO,EAAE;AACrC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI,EAAE,IAAI,GAAG,KAAK,EAAE,YAAY,GAAG,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC/D,EAAE,cAAc,CAAC;AACjB,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,KAAK;AACtC,MAAM,IAAI,GAAG,CAAC;AACd,MAAM,YAAY,CAAC,CAAC,CAAC;AACrB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,cAAc,CAAC,SAAS,EAAE;AAC5B,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,QAAQ,GAAG,UAAU,CAAC;AAC5B,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACjC;AACA,GAAG,CAAC;AACJ,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC;AAC/B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,UAAU,GAAG,CAAC;AAClB,IAAI,KAAK,GAAG,QAAQ;AACpB,IAAI,WAAW;AACf,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC;AACpC,MAAM,WAAW;AACjB,MAAM;AACN,QAAQ,QAAQ,EAAE,CAAC,UAAU,KAAK;AAClC,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,UAAU,iBAAiB,CAAC,UAAU,EAAE,YAAY,CAAC;AACrD,YAAY;AACZ,cAAc,WAAW,EAAE,iBAAiB;AAC5C,cAAc,UAAU;AACxB,cAAc,KAAK;AACnB,cAAc,KAAK,EAAE,EAAE,CAAC,+dAA+d,EAAE,SAAS;AAClgB,aAAa;AACb,YAAY,SAAS;AACrB,YAAY;AACZ,cAAc,IAAI,GAAG,GAAG;AACxB,gBAAgB,OAAO,GAAG;AAC1B,eAAe;AACf,cAAc,IAAI,GAAG,CAAC,OAAO,EAAE;AAC/B,gBAAgB,GAAG,GAAG,OAAO;AAC7B,gBAAgB,SAAS,GAAG,KAAK;AACjC;AACA;AACA,WAAW,CAAC,CAAC;AACb,UAAU,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,SAAS;AACT,QAAQ,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAChC;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACA,SAAS,eAAe,CAAC,SAAS,EAAE,OAAO,EAAE;AAC7C,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,SAAS,GAAG,IAAI;AACtB,EAAE,IAAI,eAAe;AACrB,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AACtC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B,IAAI,iBAAiB,CAAC,UAAU,EAAE,YAAY,CAAC;AAC/C,MAAM;AACN,QAAQ,WAAW,EAAE,iBAAiB;AACtC,QAAQ,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,SAAS;AAC/B,OAAO;AACP,MAAM,SAAS;AACf,MAAM;AACN,QAAQ,IAAI,GAAG,GAAG;AAClB,UAAU,OAAO,GAAG;AACpB,SAAS;AACT,QAAQ,IAAI,GAAG,CAAC,OAAO,EAAE;AACzB,UAAU,GAAG,GAAG,OAAO;AACvB,UAAU,SAAS,GAAG,KAAK;AAC3B;AACA;AACA,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC/B;AACA,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI;AACpB,IAAI,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC;AAC7C,IAAI,cAAc,CAAC,eAAe,CAAC;AACnC,GAAG,QAAQ,CAAC,SAAS;AACrB,EAAE,cAAc,CAAC,SAAS,EAAE,eAAe,CAAC;AAC5C,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;AACK,MAAC,IAAI,GAAG;;;;"}