{"version": 3, "sources": ["../../bn.js/lib/bn.js", "../../inherits/inherits_browser.js", "../../inherits/inherits.js", "../../asn1.js/lib/asn1/base/reporter.js", "../../asn1.js/lib/asn1/base/buffer.js", "../../minimalistic-assert/index.js", "../../asn1.js/lib/asn1/base/node.js", "../../asn1.js/lib/asn1/constants/der.js", "../../asn1.js/lib/asn1/encoders/der.js", "../../asn1.js/lib/asn1/encoders/pem.js", "../../asn1.js/lib/asn1/encoders/index.js", "../../asn1.js/lib/asn1/decoders/der.js", "../../asn1.js/lib/asn1/decoders/pem.js", "../../asn1.js/lib/asn1/decoders/index.js", "../../asn1.js/lib/asn1/api.js", "../../asn1.js/lib/asn1/base/index.js", "../../asn1.js/lib/asn1/constants/index.js", "../../asn1.js/lib/asn1.js"], "sourcesContent": ["(function (module, exports) {\n  'use strict';\n\n  // Utils\n  function assert (val, msg) {\n    if (!val) throw new Error(msg || 'Assertion failed');\n  }\n\n  // Could use `inherits` module, but don't want to move from single file\n  // architecture yet.\n  function inherits (ctor, superCtor) {\n    ctor.super_ = superCtor;\n    var TempCtor = function () {};\n    TempCtor.prototype = superCtor.prototype;\n    ctor.prototype = new TempCtor();\n    ctor.prototype.constructor = ctor;\n  }\n\n  // BN\n\n  function BN (number, base, endian) {\n    if (BN.isBN(number)) {\n      return number;\n    }\n\n    this.negative = 0;\n    this.words = null;\n    this.length = 0;\n\n    // Reduction context\n    this.red = null;\n\n    if (number !== null) {\n      if (base === 'le' || base === 'be') {\n        endian = base;\n        base = 10;\n      }\n\n      this._init(number || 0, base || 10, endian || 'be');\n    }\n  }\n  if (typeof module === 'object') {\n    module.exports = BN;\n  } else {\n    exports.BN = BN;\n  }\n\n  BN.BN = BN;\n  BN.wordSize = 26;\n\n  var Buffer;\n  try {\n    if (typeof window !== 'undefined' && typeof window.Buffer !== 'undefined') {\n      Buffer = window.Buffer;\n    } else {\n      Buffer = require('buffer').Buffer;\n    }\n  } catch (e) {\n  }\n\n  BN.isBN = function isBN (num) {\n    if (num instanceof BN) {\n      return true;\n    }\n\n    return num !== null && typeof num === 'object' &&\n      num.constructor.wordSize === BN.wordSize && Array.isArray(num.words);\n  };\n\n  BN.max = function max (left, right) {\n    if (left.cmp(right) > 0) return left;\n    return right;\n  };\n\n  BN.min = function min (left, right) {\n    if (left.cmp(right) < 0) return left;\n    return right;\n  };\n\n  BN.prototype._init = function init (number, base, endian) {\n    if (typeof number === 'number') {\n      return this._initNumber(number, base, endian);\n    }\n\n    if (typeof number === 'object') {\n      return this._initArray(number, base, endian);\n    }\n\n    if (base === 'hex') {\n      base = 16;\n    }\n    assert(base === (base | 0) && base >= 2 && base <= 36);\n\n    number = number.toString().replace(/\\s+/g, '');\n    var start = 0;\n    if (number[0] === '-') {\n      start++;\n      this.negative = 1;\n    }\n\n    if (start < number.length) {\n      if (base === 16) {\n        this._parseHex(number, start, endian);\n      } else {\n        this._parseBase(number, base, start);\n        if (endian === 'le') {\n          this._initArray(this.toArray(), base, endian);\n        }\n      }\n    }\n  };\n\n  BN.prototype._initNumber = function _initNumber (number, base, endian) {\n    if (number < 0) {\n      this.negative = 1;\n      number = -number;\n    }\n    if (number < 0x4000000) {\n      this.words = [ number & 0x3ffffff ];\n      this.length = 1;\n    } else if (number < 0x10000000000000) {\n      this.words = [\n        number & 0x3ffffff,\n        (number / 0x4000000) & 0x3ffffff\n      ];\n      this.length = 2;\n    } else {\n      assert(number < 0x20000000000000); // 2 ^ 53 (unsafe)\n      this.words = [\n        number & 0x3ffffff,\n        (number / 0x4000000) & 0x3ffffff,\n        1\n      ];\n      this.length = 3;\n    }\n\n    if (endian !== 'le') return;\n\n    // Reverse the bytes\n    this._initArray(this.toArray(), base, endian);\n  };\n\n  BN.prototype._initArray = function _initArray (number, base, endian) {\n    // Perhaps a Uint8Array\n    assert(typeof number.length === 'number');\n    if (number.length <= 0) {\n      this.words = [ 0 ];\n      this.length = 1;\n      return this;\n    }\n\n    this.length = Math.ceil(number.length / 3);\n    this.words = new Array(this.length);\n    for (var i = 0; i < this.length; i++) {\n      this.words[i] = 0;\n    }\n\n    var j, w;\n    var off = 0;\n    if (endian === 'be') {\n      for (i = number.length - 1, j = 0; i >= 0; i -= 3) {\n        w = number[i] | (number[i - 1] << 8) | (number[i - 2] << 16);\n        this.words[j] |= (w << off) & 0x3ffffff;\n        this.words[j + 1] = (w >>> (26 - off)) & 0x3ffffff;\n        off += 24;\n        if (off >= 26) {\n          off -= 26;\n          j++;\n        }\n      }\n    } else if (endian === 'le') {\n      for (i = 0, j = 0; i < number.length; i += 3) {\n        w = number[i] | (number[i + 1] << 8) | (number[i + 2] << 16);\n        this.words[j] |= (w << off) & 0x3ffffff;\n        this.words[j + 1] = (w >>> (26 - off)) & 0x3ffffff;\n        off += 24;\n        if (off >= 26) {\n          off -= 26;\n          j++;\n        }\n      }\n    }\n    return this.strip();\n  };\n\n  function parseHex4Bits (string, index) {\n    var c = string.charCodeAt(index);\n    // 'A' - 'F'\n    if (c >= 65 && c <= 70) {\n      return c - 55;\n    // 'a' - 'f'\n    } else if (c >= 97 && c <= 102) {\n      return c - 87;\n    // '0' - '9'\n    } else {\n      return (c - 48) & 0xf;\n    }\n  }\n\n  function parseHexByte (string, lowerBound, index) {\n    var r = parseHex4Bits(string, index);\n    if (index - 1 >= lowerBound) {\n      r |= parseHex4Bits(string, index - 1) << 4;\n    }\n    return r;\n  }\n\n  BN.prototype._parseHex = function _parseHex (number, start, endian) {\n    // Create possibly bigger array to ensure that it fits the number\n    this.length = Math.ceil((number.length - start) / 6);\n    this.words = new Array(this.length);\n    for (var i = 0; i < this.length; i++) {\n      this.words[i] = 0;\n    }\n\n    // 24-bits chunks\n    var off = 0;\n    var j = 0;\n\n    var w;\n    if (endian === 'be') {\n      for (i = number.length - 1; i >= start; i -= 2) {\n        w = parseHexByte(number, start, i) << off;\n        this.words[j] |= w & 0x3ffffff;\n        if (off >= 18) {\n          off -= 18;\n          j += 1;\n          this.words[j] |= w >>> 26;\n        } else {\n          off += 8;\n        }\n      }\n    } else {\n      var parseLength = number.length - start;\n      for (i = parseLength % 2 === 0 ? start + 1 : start; i < number.length; i += 2) {\n        w = parseHexByte(number, start, i) << off;\n        this.words[j] |= w & 0x3ffffff;\n        if (off >= 18) {\n          off -= 18;\n          j += 1;\n          this.words[j] |= w >>> 26;\n        } else {\n          off += 8;\n        }\n      }\n    }\n\n    this.strip();\n  };\n\n  function parseBase (str, start, end, mul) {\n    var r = 0;\n    var len = Math.min(str.length, end);\n    for (var i = start; i < len; i++) {\n      var c = str.charCodeAt(i) - 48;\n\n      r *= mul;\n\n      // 'a'\n      if (c >= 49) {\n        r += c - 49 + 0xa;\n\n      // 'A'\n      } else if (c >= 17) {\n        r += c - 17 + 0xa;\n\n      // '0' - '9'\n      } else {\n        r += c;\n      }\n    }\n    return r;\n  }\n\n  BN.prototype._parseBase = function _parseBase (number, base, start) {\n    // Initialize as zero\n    this.words = [ 0 ];\n    this.length = 1;\n\n    // Find length of limb in base\n    for (var limbLen = 0, limbPow = 1; limbPow <= 0x3ffffff; limbPow *= base) {\n      limbLen++;\n    }\n    limbLen--;\n    limbPow = (limbPow / base) | 0;\n\n    var total = number.length - start;\n    var mod = total % limbLen;\n    var end = Math.min(total, total - mod) + start;\n\n    var word = 0;\n    for (var i = start; i < end; i += limbLen) {\n      word = parseBase(number, i, i + limbLen, base);\n\n      this.imuln(limbPow);\n      if (this.words[0] + word < 0x4000000) {\n        this.words[0] += word;\n      } else {\n        this._iaddn(word);\n      }\n    }\n\n    if (mod !== 0) {\n      var pow = 1;\n      word = parseBase(number, i, number.length, base);\n\n      for (i = 0; i < mod; i++) {\n        pow *= base;\n      }\n\n      this.imuln(pow);\n      if (this.words[0] + word < 0x4000000) {\n        this.words[0] += word;\n      } else {\n        this._iaddn(word);\n      }\n    }\n\n    this.strip();\n  };\n\n  BN.prototype.copy = function copy (dest) {\n    dest.words = new Array(this.length);\n    for (var i = 0; i < this.length; i++) {\n      dest.words[i] = this.words[i];\n    }\n    dest.length = this.length;\n    dest.negative = this.negative;\n    dest.red = this.red;\n  };\n\n  BN.prototype.clone = function clone () {\n    var r = new BN(null);\n    this.copy(r);\n    return r;\n  };\n\n  BN.prototype._expand = function _expand (size) {\n    while (this.length < size) {\n      this.words[this.length++] = 0;\n    }\n    return this;\n  };\n\n  // Remove leading `0` from `this`\n  BN.prototype.strip = function strip () {\n    while (this.length > 1 && this.words[this.length - 1] === 0) {\n      this.length--;\n    }\n    return this._normSign();\n  };\n\n  BN.prototype._normSign = function _normSign () {\n    // -0 = 0\n    if (this.length === 1 && this.words[0] === 0) {\n      this.negative = 0;\n    }\n    return this;\n  };\n\n  BN.prototype.inspect = function inspect () {\n    return (this.red ? '<BN-R: ' : '<BN: ') + this.toString(16) + '>';\n  };\n\n  /*\n\n  var zeros = [];\n  var groupSizes = [];\n  var groupBases = [];\n\n  var s = '';\n  var i = -1;\n  while (++i < BN.wordSize) {\n    zeros[i] = s;\n    s += '0';\n  }\n  groupSizes[0] = 0;\n  groupSizes[1] = 0;\n  groupBases[0] = 0;\n  groupBases[1] = 0;\n  var base = 2 - 1;\n  while (++base < 36 + 1) {\n    var groupSize = 0;\n    var groupBase = 1;\n    while (groupBase < (1 << BN.wordSize) / base) {\n      groupBase *= base;\n      groupSize += 1;\n    }\n    groupSizes[base] = groupSize;\n    groupBases[base] = groupBase;\n  }\n\n  */\n\n  var zeros = [\n    '',\n    '0',\n    '00',\n    '000',\n    '0000',\n    '00000',\n    '000000',\n    '0000000',\n    '00000000',\n    '000000000',\n    '0000000000',\n    '00000000000',\n    '000000000000',\n    '0000000000000',\n    '00000000000000',\n    '000000000000000',\n    '0000000000000000',\n    '00000000000000000',\n    '000000000000000000',\n    '0000000000000000000',\n    '00000000000000000000',\n    '000000000000000000000',\n    '0000000000000000000000',\n    '00000000000000000000000',\n    '000000000000000000000000',\n    '0000000000000000000000000'\n  ];\n\n  var groupSizes = [\n    0, 0,\n    25, 16, 12, 11, 10, 9, 8,\n    8, 7, 7, 7, 7, 6, 6,\n    6, 6, 6, 6, 6, 5, 5,\n    5, 5, 5, 5, 5, 5, 5,\n    5, 5, 5, 5, 5, 5, 5\n  ];\n\n  var groupBases = [\n    0, 0,\n    33554432, 43046721, 16777216, 48828125, 60466176, 40353607, 16777216,\n    43046721, 10000000, 19487171, 35831808, 62748517, 7529536, 11390625,\n    16777216, 24137569, 34012224, 47045881, 64000000, 4084101, 5153632,\n    6436343, 7962624, 9765625, 11881376, 14348907, 17210368, 20511149,\n    24300000, 28629151, 33554432, 39135393, 45435424, 52521875, 60466176\n  ];\n\n  BN.prototype.toString = function toString (base, padding) {\n    base = base || 10;\n    padding = padding | 0 || 1;\n\n    var out;\n    if (base === 16 || base === 'hex') {\n      out = '';\n      var off = 0;\n      var carry = 0;\n      for (var i = 0; i < this.length; i++) {\n        var w = this.words[i];\n        var word = (((w << off) | carry) & 0xffffff).toString(16);\n        carry = (w >>> (24 - off)) & 0xffffff;\n        off += 2;\n        if (off >= 26) {\n          off -= 26;\n          i--;\n        }\n        if (carry !== 0 || i !== this.length - 1) {\n          out = zeros[6 - word.length] + word + out;\n        } else {\n          out = word + out;\n        }\n      }\n      if (carry !== 0) {\n        out = carry.toString(16) + out;\n      }\n      while (out.length % padding !== 0) {\n        out = '0' + out;\n      }\n      if (this.negative !== 0) {\n        out = '-' + out;\n      }\n      return out;\n    }\n\n    if (base === (base | 0) && base >= 2 && base <= 36) {\n      // var groupSize = Math.floor(BN.wordSize * Math.LN2 / Math.log(base));\n      var groupSize = groupSizes[base];\n      // var groupBase = Math.pow(base, groupSize);\n      var groupBase = groupBases[base];\n      out = '';\n      var c = this.clone();\n      c.negative = 0;\n      while (!c.isZero()) {\n        var r = c.modn(groupBase).toString(base);\n        c = c.idivn(groupBase);\n\n        if (!c.isZero()) {\n          out = zeros[groupSize - r.length] + r + out;\n        } else {\n          out = r + out;\n        }\n      }\n      if (this.isZero()) {\n        out = '0' + out;\n      }\n      while (out.length % padding !== 0) {\n        out = '0' + out;\n      }\n      if (this.negative !== 0) {\n        out = '-' + out;\n      }\n      return out;\n    }\n\n    assert(false, 'Base should be between 2 and 36');\n  };\n\n  BN.prototype.toNumber = function toNumber () {\n    var ret = this.words[0];\n    if (this.length === 2) {\n      ret += this.words[1] * 0x4000000;\n    } else if (this.length === 3 && this.words[2] === 0x01) {\n      // NOTE: at this stage it is known that the top bit is set\n      ret += 0x10000000000000 + (this.words[1] * 0x4000000);\n    } else if (this.length > 2) {\n      assert(false, 'Number can only safely store up to 53 bits');\n    }\n    return (this.negative !== 0) ? -ret : ret;\n  };\n\n  BN.prototype.toJSON = function toJSON () {\n    return this.toString(16);\n  };\n\n  BN.prototype.toBuffer = function toBuffer (endian, length) {\n    assert(typeof Buffer !== 'undefined');\n    return this.toArrayLike(Buffer, endian, length);\n  };\n\n  BN.prototype.toArray = function toArray (endian, length) {\n    return this.toArrayLike(Array, endian, length);\n  };\n\n  BN.prototype.toArrayLike = function toArrayLike (ArrayType, endian, length) {\n    var byteLength = this.byteLength();\n    var reqLength = length || Math.max(1, byteLength);\n    assert(byteLength <= reqLength, 'byte array longer than desired length');\n    assert(reqLength > 0, 'Requested array length <= 0');\n\n    this.strip();\n    var littleEndian = endian === 'le';\n    var res = new ArrayType(reqLength);\n\n    var b, i;\n    var q = this.clone();\n    if (!littleEndian) {\n      // Assume big-endian\n      for (i = 0; i < reqLength - byteLength; i++) {\n        res[i] = 0;\n      }\n\n      for (i = 0; !q.isZero(); i++) {\n        b = q.andln(0xff);\n        q.iushrn(8);\n\n        res[reqLength - i - 1] = b;\n      }\n    } else {\n      for (i = 0; !q.isZero(); i++) {\n        b = q.andln(0xff);\n        q.iushrn(8);\n\n        res[i] = b;\n      }\n\n      for (; i < reqLength; i++) {\n        res[i] = 0;\n      }\n    }\n\n    return res;\n  };\n\n  if (Math.clz32) {\n    BN.prototype._countBits = function _countBits (w) {\n      return 32 - Math.clz32(w);\n    };\n  } else {\n    BN.prototype._countBits = function _countBits (w) {\n      var t = w;\n      var r = 0;\n      if (t >= 0x1000) {\n        r += 13;\n        t >>>= 13;\n      }\n      if (t >= 0x40) {\n        r += 7;\n        t >>>= 7;\n      }\n      if (t >= 0x8) {\n        r += 4;\n        t >>>= 4;\n      }\n      if (t >= 0x02) {\n        r += 2;\n        t >>>= 2;\n      }\n      return r + t;\n    };\n  }\n\n  BN.prototype._zeroBits = function _zeroBits (w) {\n    // Short-cut\n    if (w === 0) return 26;\n\n    var t = w;\n    var r = 0;\n    if ((t & 0x1fff) === 0) {\n      r += 13;\n      t >>>= 13;\n    }\n    if ((t & 0x7f) === 0) {\n      r += 7;\n      t >>>= 7;\n    }\n    if ((t & 0xf) === 0) {\n      r += 4;\n      t >>>= 4;\n    }\n    if ((t & 0x3) === 0) {\n      r += 2;\n      t >>>= 2;\n    }\n    if ((t & 0x1) === 0) {\n      r++;\n    }\n    return r;\n  };\n\n  // Return number of used bits in a BN\n  BN.prototype.bitLength = function bitLength () {\n    var w = this.words[this.length - 1];\n    var hi = this._countBits(w);\n    return (this.length - 1) * 26 + hi;\n  };\n\n  function toBitArray (num) {\n    var w = new Array(num.bitLength());\n\n    for (var bit = 0; bit < w.length; bit++) {\n      var off = (bit / 26) | 0;\n      var wbit = bit % 26;\n\n      w[bit] = (num.words[off] & (1 << wbit)) >>> wbit;\n    }\n\n    return w;\n  }\n\n  // Number of trailing zero bits\n  BN.prototype.zeroBits = function zeroBits () {\n    if (this.isZero()) return 0;\n\n    var r = 0;\n    for (var i = 0; i < this.length; i++) {\n      var b = this._zeroBits(this.words[i]);\n      r += b;\n      if (b !== 26) break;\n    }\n    return r;\n  };\n\n  BN.prototype.byteLength = function byteLength () {\n    return Math.ceil(this.bitLength() / 8);\n  };\n\n  BN.prototype.toTwos = function toTwos (width) {\n    if (this.negative !== 0) {\n      return this.abs().inotn(width).iaddn(1);\n    }\n    return this.clone();\n  };\n\n  BN.prototype.fromTwos = function fromTwos (width) {\n    if (this.testn(width - 1)) {\n      return this.notn(width).iaddn(1).ineg();\n    }\n    return this.clone();\n  };\n\n  BN.prototype.isNeg = function isNeg () {\n    return this.negative !== 0;\n  };\n\n  // Return negative clone of `this`\n  BN.prototype.neg = function neg () {\n    return this.clone().ineg();\n  };\n\n  BN.prototype.ineg = function ineg () {\n    if (!this.isZero()) {\n      this.negative ^= 1;\n    }\n\n    return this;\n  };\n\n  // Or `num` with `this` in-place\n  BN.prototype.iuor = function iuor (num) {\n    while (this.length < num.length) {\n      this.words[this.length++] = 0;\n    }\n\n    for (var i = 0; i < num.length; i++) {\n      this.words[i] = this.words[i] | num.words[i];\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.ior = function ior (num) {\n    assert((this.negative | num.negative) === 0);\n    return this.iuor(num);\n  };\n\n  // Or `num` with `this`\n  BN.prototype.or = function or (num) {\n    if (this.length > num.length) return this.clone().ior(num);\n    return num.clone().ior(this);\n  };\n\n  BN.prototype.uor = function uor (num) {\n    if (this.length > num.length) return this.clone().iuor(num);\n    return num.clone().iuor(this);\n  };\n\n  // And `num` with `this` in-place\n  BN.prototype.iuand = function iuand (num) {\n    // b = min-length(num, this)\n    var b;\n    if (this.length > num.length) {\n      b = num;\n    } else {\n      b = this;\n    }\n\n    for (var i = 0; i < b.length; i++) {\n      this.words[i] = this.words[i] & num.words[i];\n    }\n\n    this.length = b.length;\n\n    return this.strip();\n  };\n\n  BN.prototype.iand = function iand (num) {\n    assert((this.negative | num.negative) === 0);\n    return this.iuand(num);\n  };\n\n  // And `num` with `this`\n  BN.prototype.and = function and (num) {\n    if (this.length > num.length) return this.clone().iand(num);\n    return num.clone().iand(this);\n  };\n\n  BN.prototype.uand = function uand (num) {\n    if (this.length > num.length) return this.clone().iuand(num);\n    return num.clone().iuand(this);\n  };\n\n  // Xor `num` with `this` in-place\n  BN.prototype.iuxor = function iuxor (num) {\n    // a.length > b.length\n    var a;\n    var b;\n    if (this.length > num.length) {\n      a = this;\n      b = num;\n    } else {\n      a = num;\n      b = this;\n    }\n\n    for (var i = 0; i < b.length; i++) {\n      this.words[i] = a.words[i] ^ b.words[i];\n    }\n\n    if (this !== a) {\n      for (; i < a.length; i++) {\n        this.words[i] = a.words[i];\n      }\n    }\n\n    this.length = a.length;\n\n    return this.strip();\n  };\n\n  BN.prototype.ixor = function ixor (num) {\n    assert((this.negative | num.negative) === 0);\n    return this.iuxor(num);\n  };\n\n  // Xor `num` with `this`\n  BN.prototype.xor = function xor (num) {\n    if (this.length > num.length) return this.clone().ixor(num);\n    return num.clone().ixor(this);\n  };\n\n  BN.prototype.uxor = function uxor (num) {\n    if (this.length > num.length) return this.clone().iuxor(num);\n    return num.clone().iuxor(this);\n  };\n\n  // Not ``this`` with ``width`` bitwidth\n  BN.prototype.inotn = function inotn (width) {\n    assert(typeof width === 'number' && width >= 0);\n\n    var bytesNeeded = Math.ceil(width / 26) | 0;\n    var bitsLeft = width % 26;\n\n    // Extend the buffer with leading zeroes\n    this._expand(bytesNeeded);\n\n    if (bitsLeft > 0) {\n      bytesNeeded--;\n    }\n\n    // Handle complete words\n    for (var i = 0; i < bytesNeeded; i++) {\n      this.words[i] = ~this.words[i] & 0x3ffffff;\n    }\n\n    // Handle the residue\n    if (bitsLeft > 0) {\n      this.words[i] = ~this.words[i] & (0x3ffffff >> (26 - bitsLeft));\n    }\n\n    // And remove leading zeroes\n    return this.strip();\n  };\n\n  BN.prototype.notn = function notn (width) {\n    return this.clone().inotn(width);\n  };\n\n  // Set `bit` of `this`\n  BN.prototype.setn = function setn (bit, val) {\n    assert(typeof bit === 'number' && bit >= 0);\n\n    var off = (bit / 26) | 0;\n    var wbit = bit % 26;\n\n    this._expand(off + 1);\n\n    if (val) {\n      this.words[off] = this.words[off] | (1 << wbit);\n    } else {\n      this.words[off] = this.words[off] & ~(1 << wbit);\n    }\n\n    return this.strip();\n  };\n\n  // Add `num` to `this` in-place\n  BN.prototype.iadd = function iadd (num) {\n    var r;\n\n    // negative + positive\n    if (this.negative !== 0 && num.negative === 0) {\n      this.negative = 0;\n      r = this.isub(num);\n      this.negative ^= 1;\n      return this._normSign();\n\n    // positive + negative\n    } else if (this.negative === 0 && num.negative !== 0) {\n      num.negative = 0;\n      r = this.isub(num);\n      num.negative = 1;\n      return r._normSign();\n    }\n\n    // a.length > b.length\n    var a, b;\n    if (this.length > num.length) {\n      a = this;\n      b = num;\n    } else {\n      a = num;\n      b = this;\n    }\n\n    var carry = 0;\n    for (var i = 0; i < b.length; i++) {\n      r = (a.words[i] | 0) + (b.words[i] | 0) + carry;\n      this.words[i] = r & 0x3ffffff;\n      carry = r >>> 26;\n    }\n    for (; carry !== 0 && i < a.length; i++) {\n      r = (a.words[i] | 0) + carry;\n      this.words[i] = r & 0x3ffffff;\n      carry = r >>> 26;\n    }\n\n    this.length = a.length;\n    if (carry !== 0) {\n      this.words[this.length] = carry;\n      this.length++;\n    // Copy the rest of the words\n    } else if (a !== this) {\n      for (; i < a.length; i++) {\n        this.words[i] = a.words[i];\n      }\n    }\n\n    return this;\n  };\n\n  // Add `num` to `this`\n  BN.prototype.add = function add (num) {\n    var res;\n    if (num.negative !== 0 && this.negative === 0) {\n      num.negative = 0;\n      res = this.sub(num);\n      num.negative ^= 1;\n      return res;\n    } else if (num.negative === 0 && this.negative !== 0) {\n      this.negative = 0;\n      res = num.sub(this);\n      this.negative = 1;\n      return res;\n    }\n\n    if (this.length > num.length) return this.clone().iadd(num);\n\n    return num.clone().iadd(this);\n  };\n\n  // Subtract `num` from `this` in-place\n  BN.prototype.isub = function isub (num) {\n    // this - (-num) = this + num\n    if (num.negative !== 0) {\n      num.negative = 0;\n      var r = this.iadd(num);\n      num.negative = 1;\n      return r._normSign();\n\n    // -this - num = -(this + num)\n    } else if (this.negative !== 0) {\n      this.negative = 0;\n      this.iadd(num);\n      this.negative = 1;\n      return this._normSign();\n    }\n\n    // At this point both numbers are positive\n    var cmp = this.cmp(num);\n\n    // Optimization - zeroify\n    if (cmp === 0) {\n      this.negative = 0;\n      this.length = 1;\n      this.words[0] = 0;\n      return this;\n    }\n\n    // a > b\n    var a, b;\n    if (cmp > 0) {\n      a = this;\n      b = num;\n    } else {\n      a = num;\n      b = this;\n    }\n\n    var carry = 0;\n    for (var i = 0; i < b.length; i++) {\n      r = (a.words[i] | 0) - (b.words[i] | 0) + carry;\n      carry = r >> 26;\n      this.words[i] = r & 0x3ffffff;\n    }\n    for (; carry !== 0 && i < a.length; i++) {\n      r = (a.words[i] | 0) + carry;\n      carry = r >> 26;\n      this.words[i] = r & 0x3ffffff;\n    }\n\n    // Copy rest of the words\n    if (carry === 0 && i < a.length && a !== this) {\n      for (; i < a.length; i++) {\n        this.words[i] = a.words[i];\n      }\n    }\n\n    this.length = Math.max(this.length, i);\n\n    if (a !== this) {\n      this.negative = 1;\n    }\n\n    return this.strip();\n  };\n\n  // Subtract `num` from `this`\n  BN.prototype.sub = function sub (num) {\n    return this.clone().isub(num);\n  };\n\n  function smallMulTo (self, num, out) {\n    out.negative = num.negative ^ self.negative;\n    var len = (self.length + num.length) | 0;\n    out.length = len;\n    len = (len - 1) | 0;\n\n    // Peel one iteration (compiler can't do it, because of code complexity)\n    var a = self.words[0] | 0;\n    var b = num.words[0] | 0;\n    var r = a * b;\n\n    var lo = r & 0x3ffffff;\n    var carry = (r / 0x4000000) | 0;\n    out.words[0] = lo;\n\n    for (var k = 1; k < len; k++) {\n      // Sum all words with the same `i + j = k` and accumulate `ncarry`,\n      // note that ncarry could be >= 0x3ffffff\n      var ncarry = carry >>> 26;\n      var rword = carry & 0x3ffffff;\n      var maxJ = Math.min(k, num.length - 1);\n      for (var j = Math.max(0, k - self.length + 1); j <= maxJ; j++) {\n        var i = (k - j) | 0;\n        a = self.words[i] | 0;\n        b = num.words[j] | 0;\n        r = a * b + rword;\n        ncarry += (r / 0x4000000) | 0;\n        rword = r & 0x3ffffff;\n      }\n      out.words[k] = rword | 0;\n      carry = ncarry | 0;\n    }\n    if (carry !== 0) {\n      out.words[k] = carry | 0;\n    } else {\n      out.length--;\n    }\n\n    return out.strip();\n  }\n\n  // TODO(indutny): it may be reasonable to omit it for users who don't need\n  // to work with 256-bit numbers, otherwise it gives 20% improvement for 256-bit\n  // multiplication (like elliptic secp256k1).\n  var comb10MulTo = function comb10MulTo (self, num, out) {\n    var a = self.words;\n    var b = num.words;\n    var o = out.words;\n    var c = 0;\n    var lo;\n    var mid;\n    var hi;\n    var a0 = a[0] | 0;\n    var al0 = a0 & 0x1fff;\n    var ah0 = a0 >>> 13;\n    var a1 = a[1] | 0;\n    var al1 = a1 & 0x1fff;\n    var ah1 = a1 >>> 13;\n    var a2 = a[2] | 0;\n    var al2 = a2 & 0x1fff;\n    var ah2 = a2 >>> 13;\n    var a3 = a[3] | 0;\n    var al3 = a3 & 0x1fff;\n    var ah3 = a3 >>> 13;\n    var a4 = a[4] | 0;\n    var al4 = a4 & 0x1fff;\n    var ah4 = a4 >>> 13;\n    var a5 = a[5] | 0;\n    var al5 = a5 & 0x1fff;\n    var ah5 = a5 >>> 13;\n    var a6 = a[6] | 0;\n    var al6 = a6 & 0x1fff;\n    var ah6 = a6 >>> 13;\n    var a7 = a[7] | 0;\n    var al7 = a7 & 0x1fff;\n    var ah7 = a7 >>> 13;\n    var a8 = a[8] | 0;\n    var al8 = a8 & 0x1fff;\n    var ah8 = a8 >>> 13;\n    var a9 = a[9] | 0;\n    var al9 = a9 & 0x1fff;\n    var ah9 = a9 >>> 13;\n    var b0 = b[0] | 0;\n    var bl0 = b0 & 0x1fff;\n    var bh0 = b0 >>> 13;\n    var b1 = b[1] | 0;\n    var bl1 = b1 & 0x1fff;\n    var bh1 = b1 >>> 13;\n    var b2 = b[2] | 0;\n    var bl2 = b2 & 0x1fff;\n    var bh2 = b2 >>> 13;\n    var b3 = b[3] | 0;\n    var bl3 = b3 & 0x1fff;\n    var bh3 = b3 >>> 13;\n    var b4 = b[4] | 0;\n    var bl4 = b4 & 0x1fff;\n    var bh4 = b4 >>> 13;\n    var b5 = b[5] | 0;\n    var bl5 = b5 & 0x1fff;\n    var bh5 = b5 >>> 13;\n    var b6 = b[6] | 0;\n    var bl6 = b6 & 0x1fff;\n    var bh6 = b6 >>> 13;\n    var b7 = b[7] | 0;\n    var bl7 = b7 & 0x1fff;\n    var bh7 = b7 >>> 13;\n    var b8 = b[8] | 0;\n    var bl8 = b8 & 0x1fff;\n    var bh8 = b8 >>> 13;\n    var b9 = b[9] | 0;\n    var bl9 = b9 & 0x1fff;\n    var bh9 = b9 >>> 13;\n\n    out.negative = self.negative ^ num.negative;\n    out.length = 19;\n    /* k = 0 */\n    lo = Math.imul(al0, bl0);\n    mid = Math.imul(al0, bh0);\n    mid = (mid + Math.imul(ah0, bl0)) | 0;\n    hi = Math.imul(ah0, bh0);\n    var w0 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w0 >>> 26)) | 0;\n    w0 &= 0x3ffffff;\n    /* k = 1 */\n    lo = Math.imul(al1, bl0);\n    mid = Math.imul(al1, bh0);\n    mid = (mid + Math.imul(ah1, bl0)) | 0;\n    hi = Math.imul(ah1, bh0);\n    lo = (lo + Math.imul(al0, bl1)) | 0;\n    mid = (mid + Math.imul(al0, bh1)) | 0;\n    mid = (mid + Math.imul(ah0, bl1)) | 0;\n    hi = (hi + Math.imul(ah0, bh1)) | 0;\n    var w1 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w1 >>> 26)) | 0;\n    w1 &= 0x3ffffff;\n    /* k = 2 */\n    lo = Math.imul(al2, bl0);\n    mid = Math.imul(al2, bh0);\n    mid = (mid + Math.imul(ah2, bl0)) | 0;\n    hi = Math.imul(ah2, bh0);\n    lo = (lo + Math.imul(al1, bl1)) | 0;\n    mid = (mid + Math.imul(al1, bh1)) | 0;\n    mid = (mid + Math.imul(ah1, bl1)) | 0;\n    hi = (hi + Math.imul(ah1, bh1)) | 0;\n    lo = (lo + Math.imul(al0, bl2)) | 0;\n    mid = (mid + Math.imul(al0, bh2)) | 0;\n    mid = (mid + Math.imul(ah0, bl2)) | 0;\n    hi = (hi + Math.imul(ah0, bh2)) | 0;\n    var w2 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w2 >>> 26)) | 0;\n    w2 &= 0x3ffffff;\n    /* k = 3 */\n    lo = Math.imul(al3, bl0);\n    mid = Math.imul(al3, bh0);\n    mid = (mid + Math.imul(ah3, bl0)) | 0;\n    hi = Math.imul(ah3, bh0);\n    lo = (lo + Math.imul(al2, bl1)) | 0;\n    mid = (mid + Math.imul(al2, bh1)) | 0;\n    mid = (mid + Math.imul(ah2, bl1)) | 0;\n    hi = (hi + Math.imul(ah2, bh1)) | 0;\n    lo = (lo + Math.imul(al1, bl2)) | 0;\n    mid = (mid + Math.imul(al1, bh2)) | 0;\n    mid = (mid + Math.imul(ah1, bl2)) | 0;\n    hi = (hi + Math.imul(ah1, bh2)) | 0;\n    lo = (lo + Math.imul(al0, bl3)) | 0;\n    mid = (mid + Math.imul(al0, bh3)) | 0;\n    mid = (mid + Math.imul(ah0, bl3)) | 0;\n    hi = (hi + Math.imul(ah0, bh3)) | 0;\n    var w3 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w3 >>> 26)) | 0;\n    w3 &= 0x3ffffff;\n    /* k = 4 */\n    lo = Math.imul(al4, bl0);\n    mid = Math.imul(al4, bh0);\n    mid = (mid + Math.imul(ah4, bl0)) | 0;\n    hi = Math.imul(ah4, bh0);\n    lo = (lo + Math.imul(al3, bl1)) | 0;\n    mid = (mid + Math.imul(al3, bh1)) | 0;\n    mid = (mid + Math.imul(ah3, bl1)) | 0;\n    hi = (hi + Math.imul(ah3, bh1)) | 0;\n    lo = (lo + Math.imul(al2, bl2)) | 0;\n    mid = (mid + Math.imul(al2, bh2)) | 0;\n    mid = (mid + Math.imul(ah2, bl2)) | 0;\n    hi = (hi + Math.imul(ah2, bh2)) | 0;\n    lo = (lo + Math.imul(al1, bl3)) | 0;\n    mid = (mid + Math.imul(al1, bh3)) | 0;\n    mid = (mid + Math.imul(ah1, bl3)) | 0;\n    hi = (hi + Math.imul(ah1, bh3)) | 0;\n    lo = (lo + Math.imul(al0, bl4)) | 0;\n    mid = (mid + Math.imul(al0, bh4)) | 0;\n    mid = (mid + Math.imul(ah0, bl4)) | 0;\n    hi = (hi + Math.imul(ah0, bh4)) | 0;\n    var w4 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w4 >>> 26)) | 0;\n    w4 &= 0x3ffffff;\n    /* k = 5 */\n    lo = Math.imul(al5, bl0);\n    mid = Math.imul(al5, bh0);\n    mid = (mid + Math.imul(ah5, bl0)) | 0;\n    hi = Math.imul(ah5, bh0);\n    lo = (lo + Math.imul(al4, bl1)) | 0;\n    mid = (mid + Math.imul(al4, bh1)) | 0;\n    mid = (mid + Math.imul(ah4, bl1)) | 0;\n    hi = (hi + Math.imul(ah4, bh1)) | 0;\n    lo = (lo + Math.imul(al3, bl2)) | 0;\n    mid = (mid + Math.imul(al3, bh2)) | 0;\n    mid = (mid + Math.imul(ah3, bl2)) | 0;\n    hi = (hi + Math.imul(ah3, bh2)) | 0;\n    lo = (lo + Math.imul(al2, bl3)) | 0;\n    mid = (mid + Math.imul(al2, bh3)) | 0;\n    mid = (mid + Math.imul(ah2, bl3)) | 0;\n    hi = (hi + Math.imul(ah2, bh3)) | 0;\n    lo = (lo + Math.imul(al1, bl4)) | 0;\n    mid = (mid + Math.imul(al1, bh4)) | 0;\n    mid = (mid + Math.imul(ah1, bl4)) | 0;\n    hi = (hi + Math.imul(ah1, bh4)) | 0;\n    lo = (lo + Math.imul(al0, bl5)) | 0;\n    mid = (mid + Math.imul(al0, bh5)) | 0;\n    mid = (mid + Math.imul(ah0, bl5)) | 0;\n    hi = (hi + Math.imul(ah0, bh5)) | 0;\n    var w5 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w5 >>> 26)) | 0;\n    w5 &= 0x3ffffff;\n    /* k = 6 */\n    lo = Math.imul(al6, bl0);\n    mid = Math.imul(al6, bh0);\n    mid = (mid + Math.imul(ah6, bl0)) | 0;\n    hi = Math.imul(ah6, bh0);\n    lo = (lo + Math.imul(al5, bl1)) | 0;\n    mid = (mid + Math.imul(al5, bh1)) | 0;\n    mid = (mid + Math.imul(ah5, bl1)) | 0;\n    hi = (hi + Math.imul(ah5, bh1)) | 0;\n    lo = (lo + Math.imul(al4, bl2)) | 0;\n    mid = (mid + Math.imul(al4, bh2)) | 0;\n    mid = (mid + Math.imul(ah4, bl2)) | 0;\n    hi = (hi + Math.imul(ah4, bh2)) | 0;\n    lo = (lo + Math.imul(al3, bl3)) | 0;\n    mid = (mid + Math.imul(al3, bh3)) | 0;\n    mid = (mid + Math.imul(ah3, bl3)) | 0;\n    hi = (hi + Math.imul(ah3, bh3)) | 0;\n    lo = (lo + Math.imul(al2, bl4)) | 0;\n    mid = (mid + Math.imul(al2, bh4)) | 0;\n    mid = (mid + Math.imul(ah2, bl4)) | 0;\n    hi = (hi + Math.imul(ah2, bh4)) | 0;\n    lo = (lo + Math.imul(al1, bl5)) | 0;\n    mid = (mid + Math.imul(al1, bh5)) | 0;\n    mid = (mid + Math.imul(ah1, bl5)) | 0;\n    hi = (hi + Math.imul(ah1, bh5)) | 0;\n    lo = (lo + Math.imul(al0, bl6)) | 0;\n    mid = (mid + Math.imul(al0, bh6)) | 0;\n    mid = (mid + Math.imul(ah0, bl6)) | 0;\n    hi = (hi + Math.imul(ah0, bh6)) | 0;\n    var w6 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w6 >>> 26)) | 0;\n    w6 &= 0x3ffffff;\n    /* k = 7 */\n    lo = Math.imul(al7, bl0);\n    mid = Math.imul(al7, bh0);\n    mid = (mid + Math.imul(ah7, bl0)) | 0;\n    hi = Math.imul(ah7, bh0);\n    lo = (lo + Math.imul(al6, bl1)) | 0;\n    mid = (mid + Math.imul(al6, bh1)) | 0;\n    mid = (mid + Math.imul(ah6, bl1)) | 0;\n    hi = (hi + Math.imul(ah6, bh1)) | 0;\n    lo = (lo + Math.imul(al5, bl2)) | 0;\n    mid = (mid + Math.imul(al5, bh2)) | 0;\n    mid = (mid + Math.imul(ah5, bl2)) | 0;\n    hi = (hi + Math.imul(ah5, bh2)) | 0;\n    lo = (lo + Math.imul(al4, bl3)) | 0;\n    mid = (mid + Math.imul(al4, bh3)) | 0;\n    mid = (mid + Math.imul(ah4, bl3)) | 0;\n    hi = (hi + Math.imul(ah4, bh3)) | 0;\n    lo = (lo + Math.imul(al3, bl4)) | 0;\n    mid = (mid + Math.imul(al3, bh4)) | 0;\n    mid = (mid + Math.imul(ah3, bl4)) | 0;\n    hi = (hi + Math.imul(ah3, bh4)) | 0;\n    lo = (lo + Math.imul(al2, bl5)) | 0;\n    mid = (mid + Math.imul(al2, bh5)) | 0;\n    mid = (mid + Math.imul(ah2, bl5)) | 0;\n    hi = (hi + Math.imul(ah2, bh5)) | 0;\n    lo = (lo + Math.imul(al1, bl6)) | 0;\n    mid = (mid + Math.imul(al1, bh6)) | 0;\n    mid = (mid + Math.imul(ah1, bl6)) | 0;\n    hi = (hi + Math.imul(ah1, bh6)) | 0;\n    lo = (lo + Math.imul(al0, bl7)) | 0;\n    mid = (mid + Math.imul(al0, bh7)) | 0;\n    mid = (mid + Math.imul(ah0, bl7)) | 0;\n    hi = (hi + Math.imul(ah0, bh7)) | 0;\n    var w7 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w7 >>> 26)) | 0;\n    w7 &= 0x3ffffff;\n    /* k = 8 */\n    lo = Math.imul(al8, bl0);\n    mid = Math.imul(al8, bh0);\n    mid = (mid + Math.imul(ah8, bl0)) | 0;\n    hi = Math.imul(ah8, bh0);\n    lo = (lo + Math.imul(al7, bl1)) | 0;\n    mid = (mid + Math.imul(al7, bh1)) | 0;\n    mid = (mid + Math.imul(ah7, bl1)) | 0;\n    hi = (hi + Math.imul(ah7, bh1)) | 0;\n    lo = (lo + Math.imul(al6, bl2)) | 0;\n    mid = (mid + Math.imul(al6, bh2)) | 0;\n    mid = (mid + Math.imul(ah6, bl2)) | 0;\n    hi = (hi + Math.imul(ah6, bh2)) | 0;\n    lo = (lo + Math.imul(al5, bl3)) | 0;\n    mid = (mid + Math.imul(al5, bh3)) | 0;\n    mid = (mid + Math.imul(ah5, bl3)) | 0;\n    hi = (hi + Math.imul(ah5, bh3)) | 0;\n    lo = (lo + Math.imul(al4, bl4)) | 0;\n    mid = (mid + Math.imul(al4, bh4)) | 0;\n    mid = (mid + Math.imul(ah4, bl4)) | 0;\n    hi = (hi + Math.imul(ah4, bh4)) | 0;\n    lo = (lo + Math.imul(al3, bl5)) | 0;\n    mid = (mid + Math.imul(al3, bh5)) | 0;\n    mid = (mid + Math.imul(ah3, bl5)) | 0;\n    hi = (hi + Math.imul(ah3, bh5)) | 0;\n    lo = (lo + Math.imul(al2, bl6)) | 0;\n    mid = (mid + Math.imul(al2, bh6)) | 0;\n    mid = (mid + Math.imul(ah2, bl6)) | 0;\n    hi = (hi + Math.imul(ah2, bh6)) | 0;\n    lo = (lo + Math.imul(al1, bl7)) | 0;\n    mid = (mid + Math.imul(al1, bh7)) | 0;\n    mid = (mid + Math.imul(ah1, bl7)) | 0;\n    hi = (hi + Math.imul(ah1, bh7)) | 0;\n    lo = (lo + Math.imul(al0, bl8)) | 0;\n    mid = (mid + Math.imul(al0, bh8)) | 0;\n    mid = (mid + Math.imul(ah0, bl8)) | 0;\n    hi = (hi + Math.imul(ah0, bh8)) | 0;\n    var w8 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w8 >>> 26)) | 0;\n    w8 &= 0x3ffffff;\n    /* k = 9 */\n    lo = Math.imul(al9, bl0);\n    mid = Math.imul(al9, bh0);\n    mid = (mid + Math.imul(ah9, bl0)) | 0;\n    hi = Math.imul(ah9, bh0);\n    lo = (lo + Math.imul(al8, bl1)) | 0;\n    mid = (mid + Math.imul(al8, bh1)) | 0;\n    mid = (mid + Math.imul(ah8, bl1)) | 0;\n    hi = (hi + Math.imul(ah8, bh1)) | 0;\n    lo = (lo + Math.imul(al7, bl2)) | 0;\n    mid = (mid + Math.imul(al7, bh2)) | 0;\n    mid = (mid + Math.imul(ah7, bl2)) | 0;\n    hi = (hi + Math.imul(ah7, bh2)) | 0;\n    lo = (lo + Math.imul(al6, bl3)) | 0;\n    mid = (mid + Math.imul(al6, bh3)) | 0;\n    mid = (mid + Math.imul(ah6, bl3)) | 0;\n    hi = (hi + Math.imul(ah6, bh3)) | 0;\n    lo = (lo + Math.imul(al5, bl4)) | 0;\n    mid = (mid + Math.imul(al5, bh4)) | 0;\n    mid = (mid + Math.imul(ah5, bl4)) | 0;\n    hi = (hi + Math.imul(ah5, bh4)) | 0;\n    lo = (lo + Math.imul(al4, bl5)) | 0;\n    mid = (mid + Math.imul(al4, bh5)) | 0;\n    mid = (mid + Math.imul(ah4, bl5)) | 0;\n    hi = (hi + Math.imul(ah4, bh5)) | 0;\n    lo = (lo + Math.imul(al3, bl6)) | 0;\n    mid = (mid + Math.imul(al3, bh6)) | 0;\n    mid = (mid + Math.imul(ah3, bl6)) | 0;\n    hi = (hi + Math.imul(ah3, bh6)) | 0;\n    lo = (lo + Math.imul(al2, bl7)) | 0;\n    mid = (mid + Math.imul(al2, bh7)) | 0;\n    mid = (mid + Math.imul(ah2, bl7)) | 0;\n    hi = (hi + Math.imul(ah2, bh7)) | 0;\n    lo = (lo + Math.imul(al1, bl8)) | 0;\n    mid = (mid + Math.imul(al1, bh8)) | 0;\n    mid = (mid + Math.imul(ah1, bl8)) | 0;\n    hi = (hi + Math.imul(ah1, bh8)) | 0;\n    lo = (lo + Math.imul(al0, bl9)) | 0;\n    mid = (mid + Math.imul(al0, bh9)) | 0;\n    mid = (mid + Math.imul(ah0, bl9)) | 0;\n    hi = (hi + Math.imul(ah0, bh9)) | 0;\n    var w9 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w9 >>> 26)) | 0;\n    w9 &= 0x3ffffff;\n    /* k = 10 */\n    lo = Math.imul(al9, bl1);\n    mid = Math.imul(al9, bh1);\n    mid = (mid + Math.imul(ah9, bl1)) | 0;\n    hi = Math.imul(ah9, bh1);\n    lo = (lo + Math.imul(al8, bl2)) | 0;\n    mid = (mid + Math.imul(al8, bh2)) | 0;\n    mid = (mid + Math.imul(ah8, bl2)) | 0;\n    hi = (hi + Math.imul(ah8, bh2)) | 0;\n    lo = (lo + Math.imul(al7, bl3)) | 0;\n    mid = (mid + Math.imul(al7, bh3)) | 0;\n    mid = (mid + Math.imul(ah7, bl3)) | 0;\n    hi = (hi + Math.imul(ah7, bh3)) | 0;\n    lo = (lo + Math.imul(al6, bl4)) | 0;\n    mid = (mid + Math.imul(al6, bh4)) | 0;\n    mid = (mid + Math.imul(ah6, bl4)) | 0;\n    hi = (hi + Math.imul(ah6, bh4)) | 0;\n    lo = (lo + Math.imul(al5, bl5)) | 0;\n    mid = (mid + Math.imul(al5, bh5)) | 0;\n    mid = (mid + Math.imul(ah5, bl5)) | 0;\n    hi = (hi + Math.imul(ah5, bh5)) | 0;\n    lo = (lo + Math.imul(al4, bl6)) | 0;\n    mid = (mid + Math.imul(al4, bh6)) | 0;\n    mid = (mid + Math.imul(ah4, bl6)) | 0;\n    hi = (hi + Math.imul(ah4, bh6)) | 0;\n    lo = (lo + Math.imul(al3, bl7)) | 0;\n    mid = (mid + Math.imul(al3, bh7)) | 0;\n    mid = (mid + Math.imul(ah3, bl7)) | 0;\n    hi = (hi + Math.imul(ah3, bh7)) | 0;\n    lo = (lo + Math.imul(al2, bl8)) | 0;\n    mid = (mid + Math.imul(al2, bh8)) | 0;\n    mid = (mid + Math.imul(ah2, bl8)) | 0;\n    hi = (hi + Math.imul(ah2, bh8)) | 0;\n    lo = (lo + Math.imul(al1, bl9)) | 0;\n    mid = (mid + Math.imul(al1, bh9)) | 0;\n    mid = (mid + Math.imul(ah1, bl9)) | 0;\n    hi = (hi + Math.imul(ah1, bh9)) | 0;\n    var w10 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w10 >>> 26)) | 0;\n    w10 &= 0x3ffffff;\n    /* k = 11 */\n    lo = Math.imul(al9, bl2);\n    mid = Math.imul(al9, bh2);\n    mid = (mid + Math.imul(ah9, bl2)) | 0;\n    hi = Math.imul(ah9, bh2);\n    lo = (lo + Math.imul(al8, bl3)) | 0;\n    mid = (mid + Math.imul(al8, bh3)) | 0;\n    mid = (mid + Math.imul(ah8, bl3)) | 0;\n    hi = (hi + Math.imul(ah8, bh3)) | 0;\n    lo = (lo + Math.imul(al7, bl4)) | 0;\n    mid = (mid + Math.imul(al7, bh4)) | 0;\n    mid = (mid + Math.imul(ah7, bl4)) | 0;\n    hi = (hi + Math.imul(ah7, bh4)) | 0;\n    lo = (lo + Math.imul(al6, bl5)) | 0;\n    mid = (mid + Math.imul(al6, bh5)) | 0;\n    mid = (mid + Math.imul(ah6, bl5)) | 0;\n    hi = (hi + Math.imul(ah6, bh5)) | 0;\n    lo = (lo + Math.imul(al5, bl6)) | 0;\n    mid = (mid + Math.imul(al5, bh6)) | 0;\n    mid = (mid + Math.imul(ah5, bl6)) | 0;\n    hi = (hi + Math.imul(ah5, bh6)) | 0;\n    lo = (lo + Math.imul(al4, bl7)) | 0;\n    mid = (mid + Math.imul(al4, bh7)) | 0;\n    mid = (mid + Math.imul(ah4, bl7)) | 0;\n    hi = (hi + Math.imul(ah4, bh7)) | 0;\n    lo = (lo + Math.imul(al3, bl8)) | 0;\n    mid = (mid + Math.imul(al3, bh8)) | 0;\n    mid = (mid + Math.imul(ah3, bl8)) | 0;\n    hi = (hi + Math.imul(ah3, bh8)) | 0;\n    lo = (lo + Math.imul(al2, bl9)) | 0;\n    mid = (mid + Math.imul(al2, bh9)) | 0;\n    mid = (mid + Math.imul(ah2, bl9)) | 0;\n    hi = (hi + Math.imul(ah2, bh9)) | 0;\n    var w11 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w11 >>> 26)) | 0;\n    w11 &= 0x3ffffff;\n    /* k = 12 */\n    lo = Math.imul(al9, bl3);\n    mid = Math.imul(al9, bh3);\n    mid = (mid + Math.imul(ah9, bl3)) | 0;\n    hi = Math.imul(ah9, bh3);\n    lo = (lo + Math.imul(al8, bl4)) | 0;\n    mid = (mid + Math.imul(al8, bh4)) | 0;\n    mid = (mid + Math.imul(ah8, bl4)) | 0;\n    hi = (hi + Math.imul(ah8, bh4)) | 0;\n    lo = (lo + Math.imul(al7, bl5)) | 0;\n    mid = (mid + Math.imul(al7, bh5)) | 0;\n    mid = (mid + Math.imul(ah7, bl5)) | 0;\n    hi = (hi + Math.imul(ah7, bh5)) | 0;\n    lo = (lo + Math.imul(al6, bl6)) | 0;\n    mid = (mid + Math.imul(al6, bh6)) | 0;\n    mid = (mid + Math.imul(ah6, bl6)) | 0;\n    hi = (hi + Math.imul(ah6, bh6)) | 0;\n    lo = (lo + Math.imul(al5, bl7)) | 0;\n    mid = (mid + Math.imul(al5, bh7)) | 0;\n    mid = (mid + Math.imul(ah5, bl7)) | 0;\n    hi = (hi + Math.imul(ah5, bh7)) | 0;\n    lo = (lo + Math.imul(al4, bl8)) | 0;\n    mid = (mid + Math.imul(al4, bh8)) | 0;\n    mid = (mid + Math.imul(ah4, bl8)) | 0;\n    hi = (hi + Math.imul(ah4, bh8)) | 0;\n    lo = (lo + Math.imul(al3, bl9)) | 0;\n    mid = (mid + Math.imul(al3, bh9)) | 0;\n    mid = (mid + Math.imul(ah3, bl9)) | 0;\n    hi = (hi + Math.imul(ah3, bh9)) | 0;\n    var w12 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w12 >>> 26)) | 0;\n    w12 &= 0x3ffffff;\n    /* k = 13 */\n    lo = Math.imul(al9, bl4);\n    mid = Math.imul(al9, bh4);\n    mid = (mid + Math.imul(ah9, bl4)) | 0;\n    hi = Math.imul(ah9, bh4);\n    lo = (lo + Math.imul(al8, bl5)) | 0;\n    mid = (mid + Math.imul(al8, bh5)) | 0;\n    mid = (mid + Math.imul(ah8, bl5)) | 0;\n    hi = (hi + Math.imul(ah8, bh5)) | 0;\n    lo = (lo + Math.imul(al7, bl6)) | 0;\n    mid = (mid + Math.imul(al7, bh6)) | 0;\n    mid = (mid + Math.imul(ah7, bl6)) | 0;\n    hi = (hi + Math.imul(ah7, bh6)) | 0;\n    lo = (lo + Math.imul(al6, bl7)) | 0;\n    mid = (mid + Math.imul(al6, bh7)) | 0;\n    mid = (mid + Math.imul(ah6, bl7)) | 0;\n    hi = (hi + Math.imul(ah6, bh7)) | 0;\n    lo = (lo + Math.imul(al5, bl8)) | 0;\n    mid = (mid + Math.imul(al5, bh8)) | 0;\n    mid = (mid + Math.imul(ah5, bl8)) | 0;\n    hi = (hi + Math.imul(ah5, bh8)) | 0;\n    lo = (lo + Math.imul(al4, bl9)) | 0;\n    mid = (mid + Math.imul(al4, bh9)) | 0;\n    mid = (mid + Math.imul(ah4, bl9)) | 0;\n    hi = (hi + Math.imul(ah4, bh9)) | 0;\n    var w13 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w13 >>> 26)) | 0;\n    w13 &= 0x3ffffff;\n    /* k = 14 */\n    lo = Math.imul(al9, bl5);\n    mid = Math.imul(al9, bh5);\n    mid = (mid + Math.imul(ah9, bl5)) | 0;\n    hi = Math.imul(ah9, bh5);\n    lo = (lo + Math.imul(al8, bl6)) | 0;\n    mid = (mid + Math.imul(al8, bh6)) | 0;\n    mid = (mid + Math.imul(ah8, bl6)) | 0;\n    hi = (hi + Math.imul(ah8, bh6)) | 0;\n    lo = (lo + Math.imul(al7, bl7)) | 0;\n    mid = (mid + Math.imul(al7, bh7)) | 0;\n    mid = (mid + Math.imul(ah7, bl7)) | 0;\n    hi = (hi + Math.imul(ah7, bh7)) | 0;\n    lo = (lo + Math.imul(al6, bl8)) | 0;\n    mid = (mid + Math.imul(al6, bh8)) | 0;\n    mid = (mid + Math.imul(ah6, bl8)) | 0;\n    hi = (hi + Math.imul(ah6, bh8)) | 0;\n    lo = (lo + Math.imul(al5, bl9)) | 0;\n    mid = (mid + Math.imul(al5, bh9)) | 0;\n    mid = (mid + Math.imul(ah5, bl9)) | 0;\n    hi = (hi + Math.imul(ah5, bh9)) | 0;\n    var w14 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w14 >>> 26)) | 0;\n    w14 &= 0x3ffffff;\n    /* k = 15 */\n    lo = Math.imul(al9, bl6);\n    mid = Math.imul(al9, bh6);\n    mid = (mid + Math.imul(ah9, bl6)) | 0;\n    hi = Math.imul(ah9, bh6);\n    lo = (lo + Math.imul(al8, bl7)) | 0;\n    mid = (mid + Math.imul(al8, bh7)) | 0;\n    mid = (mid + Math.imul(ah8, bl7)) | 0;\n    hi = (hi + Math.imul(ah8, bh7)) | 0;\n    lo = (lo + Math.imul(al7, bl8)) | 0;\n    mid = (mid + Math.imul(al7, bh8)) | 0;\n    mid = (mid + Math.imul(ah7, bl8)) | 0;\n    hi = (hi + Math.imul(ah7, bh8)) | 0;\n    lo = (lo + Math.imul(al6, bl9)) | 0;\n    mid = (mid + Math.imul(al6, bh9)) | 0;\n    mid = (mid + Math.imul(ah6, bl9)) | 0;\n    hi = (hi + Math.imul(ah6, bh9)) | 0;\n    var w15 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w15 >>> 26)) | 0;\n    w15 &= 0x3ffffff;\n    /* k = 16 */\n    lo = Math.imul(al9, bl7);\n    mid = Math.imul(al9, bh7);\n    mid = (mid + Math.imul(ah9, bl7)) | 0;\n    hi = Math.imul(ah9, bh7);\n    lo = (lo + Math.imul(al8, bl8)) | 0;\n    mid = (mid + Math.imul(al8, bh8)) | 0;\n    mid = (mid + Math.imul(ah8, bl8)) | 0;\n    hi = (hi + Math.imul(ah8, bh8)) | 0;\n    lo = (lo + Math.imul(al7, bl9)) | 0;\n    mid = (mid + Math.imul(al7, bh9)) | 0;\n    mid = (mid + Math.imul(ah7, bl9)) | 0;\n    hi = (hi + Math.imul(ah7, bh9)) | 0;\n    var w16 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w16 >>> 26)) | 0;\n    w16 &= 0x3ffffff;\n    /* k = 17 */\n    lo = Math.imul(al9, bl8);\n    mid = Math.imul(al9, bh8);\n    mid = (mid + Math.imul(ah9, bl8)) | 0;\n    hi = Math.imul(ah9, bh8);\n    lo = (lo + Math.imul(al8, bl9)) | 0;\n    mid = (mid + Math.imul(al8, bh9)) | 0;\n    mid = (mid + Math.imul(ah8, bl9)) | 0;\n    hi = (hi + Math.imul(ah8, bh9)) | 0;\n    var w17 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w17 >>> 26)) | 0;\n    w17 &= 0x3ffffff;\n    /* k = 18 */\n    lo = Math.imul(al9, bl9);\n    mid = Math.imul(al9, bh9);\n    mid = (mid + Math.imul(ah9, bl9)) | 0;\n    hi = Math.imul(ah9, bh9);\n    var w18 = (((c + lo) | 0) + ((mid & 0x1fff) << 13)) | 0;\n    c = (((hi + (mid >>> 13)) | 0) + (w18 >>> 26)) | 0;\n    w18 &= 0x3ffffff;\n    o[0] = w0;\n    o[1] = w1;\n    o[2] = w2;\n    o[3] = w3;\n    o[4] = w4;\n    o[5] = w5;\n    o[6] = w6;\n    o[7] = w7;\n    o[8] = w8;\n    o[9] = w9;\n    o[10] = w10;\n    o[11] = w11;\n    o[12] = w12;\n    o[13] = w13;\n    o[14] = w14;\n    o[15] = w15;\n    o[16] = w16;\n    o[17] = w17;\n    o[18] = w18;\n    if (c !== 0) {\n      o[19] = c;\n      out.length++;\n    }\n    return out;\n  };\n\n  // Polyfill comb\n  if (!Math.imul) {\n    comb10MulTo = smallMulTo;\n  }\n\n  function bigMulTo (self, num, out) {\n    out.negative = num.negative ^ self.negative;\n    out.length = self.length + num.length;\n\n    var carry = 0;\n    var hncarry = 0;\n    for (var k = 0; k < out.length - 1; k++) {\n      // Sum all words with the same `i + j = k` and accumulate `ncarry`,\n      // note that ncarry could be >= 0x3ffffff\n      var ncarry = hncarry;\n      hncarry = 0;\n      var rword = carry & 0x3ffffff;\n      var maxJ = Math.min(k, num.length - 1);\n      for (var j = Math.max(0, k - self.length + 1); j <= maxJ; j++) {\n        var i = k - j;\n        var a = self.words[i] | 0;\n        var b = num.words[j] | 0;\n        var r = a * b;\n\n        var lo = r & 0x3ffffff;\n        ncarry = (ncarry + ((r / 0x4000000) | 0)) | 0;\n        lo = (lo + rword) | 0;\n        rword = lo & 0x3ffffff;\n        ncarry = (ncarry + (lo >>> 26)) | 0;\n\n        hncarry += ncarry >>> 26;\n        ncarry &= 0x3ffffff;\n      }\n      out.words[k] = rword;\n      carry = ncarry;\n      ncarry = hncarry;\n    }\n    if (carry !== 0) {\n      out.words[k] = carry;\n    } else {\n      out.length--;\n    }\n\n    return out.strip();\n  }\n\n  function jumboMulTo (self, num, out) {\n    var fftm = new FFTM();\n    return fftm.mulp(self, num, out);\n  }\n\n  BN.prototype.mulTo = function mulTo (num, out) {\n    var res;\n    var len = this.length + num.length;\n    if (this.length === 10 && num.length === 10) {\n      res = comb10MulTo(this, num, out);\n    } else if (len < 63) {\n      res = smallMulTo(this, num, out);\n    } else if (len < 1024) {\n      res = bigMulTo(this, num, out);\n    } else {\n      res = jumboMulTo(this, num, out);\n    }\n\n    return res;\n  };\n\n  // Cooley-Tukey algorithm for FFT\n  // slightly revisited to rely on looping instead of recursion\n\n  function FFTM (x, y) {\n    this.x = x;\n    this.y = y;\n  }\n\n  FFTM.prototype.makeRBT = function makeRBT (N) {\n    var t = new Array(N);\n    var l = BN.prototype._countBits(N) - 1;\n    for (var i = 0; i < N; i++) {\n      t[i] = this.revBin(i, l, N);\n    }\n\n    return t;\n  };\n\n  // Returns binary-reversed representation of `x`\n  FFTM.prototype.revBin = function revBin (x, l, N) {\n    if (x === 0 || x === N - 1) return x;\n\n    var rb = 0;\n    for (var i = 0; i < l; i++) {\n      rb |= (x & 1) << (l - i - 1);\n      x >>= 1;\n    }\n\n    return rb;\n  };\n\n  // Performs \"tweedling\" phase, therefore 'emulating'\n  // behaviour of the recursive algorithm\n  FFTM.prototype.permute = function permute (rbt, rws, iws, rtws, itws, N) {\n    for (var i = 0; i < N; i++) {\n      rtws[i] = rws[rbt[i]];\n      itws[i] = iws[rbt[i]];\n    }\n  };\n\n  FFTM.prototype.transform = function transform (rws, iws, rtws, itws, N, rbt) {\n    this.permute(rbt, rws, iws, rtws, itws, N);\n\n    for (var s = 1; s < N; s <<= 1) {\n      var l = s << 1;\n\n      var rtwdf = Math.cos(2 * Math.PI / l);\n      var itwdf = Math.sin(2 * Math.PI / l);\n\n      for (var p = 0; p < N; p += l) {\n        var rtwdf_ = rtwdf;\n        var itwdf_ = itwdf;\n\n        for (var j = 0; j < s; j++) {\n          var re = rtws[p + j];\n          var ie = itws[p + j];\n\n          var ro = rtws[p + j + s];\n          var io = itws[p + j + s];\n\n          var rx = rtwdf_ * ro - itwdf_ * io;\n\n          io = rtwdf_ * io + itwdf_ * ro;\n          ro = rx;\n\n          rtws[p + j] = re + ro;\n          itws[p + j] = ie + io;\n\n          rtws[p + j + s] = re - ro;\n          itws[p + j + s] = ie - io;\n\n          /* jshint maxdepth : false */\n          if (j !== l) {\n            rx = rtwdf * rtwdf_ - itwdf * itwdf_;\n\n            itwdf_ = rtwdf * itwdf_ + itwdf * rtwdf_;\n            rtwdf_ = rx;\n          }\n        }\n      }\n    }\n  };\n\n  FFTM.prototype.guessLen13b = function guessLen13b (n, m) {\n    var N = Math.max(m, n) | 1;\n    var odd = N & 1;\n    var i = 0;\n    for (N = N / 2 | 0; N; N = N >>> 1) {\n      i++;\n    }\n\n    return 1 << i + 1 + odd;\n  };\n\n  FFTM.prototype.conjugate = function conjugate (rws, iws, N) {\n    if (N <= 1) return;\n\n    for (var i = 0; i < N / 2; i++) {\n      var t = rws[i];\n\n      rws[i] = rws[N - i - 1];\n      rws[N - i - 1] = t;\n\n      t = iws[i];\n\n      iws[i] = -iws[N - i - 1];\n      iws[N - i - 1] = -t;\n    }\n  };\n\n  FFTM.prototype.normalize13b = function normalize13b (ws, N) {\n    var carry = 0;\n    for (var i = 0; i < N / 2; i++) {\n      var w = Math.round(ws[2 * i + 1] / N) * 0x2000 +\n        Math.round(ws[2 * i] / N) +\n        carry;\n\n      ws[i] = w & 0x3ffffff;\n\n      if (w < 0x4000000) {\n        carry = 0;\n      } else {\n        carry = w / 0x4000000 | 0;\n      }\n    }\n\n    return ws;\n  };\n\n  FFTM.prototype.convert13b = function convert13b (ws, len, rws, N) {\n    var carry = 0;\n    for (var i = 0; i < len; i++) {\n      carry = carry + (ws[i] | 0);\n\n      rws[2 * i] = carry & 0x1fff; carry = carry >>> 13;\n      rws[2 * i + 1] = carry & 0x1fff; carry = carry >>> 13;\n    }\n\n    // Pad with zeroes\n    for (i = 2 * len; i < N; ++i) {\n      rws[i] = 0;\n    }\n\n    assert(carry === 0);\n    assert((carry & ~0x1fff) === 0);\n  };\n\n  FFTM.prototype.stub = function stub (N) {\n    var ph = new Array(N);\n    for (var i = 0; i < N; i++) {\n      ph[i] = 0;\n    }\n\n    return ph;\n  };\n\n  FFTM.prototype.mulp = function mulp (x, y, out) {\n    var N = 2 * this.guessLen13b(x.length, y.length);\n\n    var rbt = this.makeRBT(N);\n\n    var _ = this.stub(N);\n\n    var rws = new Array(N);\n    var rwst = new Array(N);\n    var iwst = new Array(N);\n\n    var nrws = new Array(N);\n    var nrwst = new Array(N);\n    var niwst = new Array(N);\n\n    var rmws = out.words;\n    rmws.length = N;\n\n    this.convert13b(x.words, x.length, rws, N);\n    this.convert13b(y.words, y.length, nrws, N);\n\n    this.transform(rws, _, rwst, iwst, N, rbt);\n    this.transform(nrws, _, nrwst, niwst, N, rbt);\n\n    for (var i = 0; i < N; i++) {\n      var rx = rwst[i] * nrwst[i] - iwst[i] * niwst[i];\n      iwst[i] = rwst[i] * niwst[i] + iwst[i] * nrwst[i];\n      rwst[i] = rx;\n    }\n\n    this.conjugate(rwst, iwst, N);\n    this.transform(rwst, iwst, rmws, _, N, rbt);\n    this.conjugate(rmws, _, N);\n    this.normalize13b(rmws, N);\n\n    out.negative = x.negative ^ y.negative;\n    out.length = x.length + y.length;\n    return out.strip();\n  };\n\n  // Multiply `this` by `num`\n  BN.prototype.mul = function mul (num) {\n    var out = new BN(null);\n    out.words = new Array(this.length + num.length);\n    return this.mulTo(num, out);\n  };\n\n  // Multiply employing FFT\n  BN.prototype.mulf = function mulf (num) {\n    var out = new BN(null);\n    out.words = new Array(this.length + num.length);\n    return jumboMulTo(this, num, out);\n  };\n\n  // In-place Multiplication\n  BN.prototype.imul = function imul (num) {\n    return this.clone().mulTo(num, this);\n  };\n\n  BN.prototype.imuln = function imuln (num) {\n    assert(typeof num === 'number');\n    assert(num < 0x4000000);\n\n    // Carry\n    var carry = 0;\n    for (var i = 0; i < this.length; i++) {\n      var w = (this.words[i] | 0) * num;\n      var lo = (w & 0x3ffffff) + (carry & 0x3ffffff);\n      carry >>= 26;\n      carry += (w / 0x4000000) | 0;\n      // NOTE: lo is 27bit maximum\n      carry += lo >>> 26;\n      this.words[i] = lo & 0x3ffffff;\n    }\n\n    if (carry !== 0) {\n      this.words[i] = carry;\n      this.length++;\n    }\n    this.length = num === 0 ? 1 : this.length;\n\n    return this;\n  };\n\n  BN.prototype.muln = function muln (num) {\n    return this.clone().imuln(num);\n  };\n\n  // `this` * `this`\n  BN.prototype.sqr = function sqr () {\n    return this.mul(this);\n  };\n\n  // `this` * `this` in-place\n  BN.prototype.isqr = function isqr () {\n    return this.imul(this.clone());\n  };\n\n  // Math.pow(`this`, `num`)\n  BN.prototype.pow = function pow (num) {\n    var w = toBitArray(num);\n    if (w.length === 0) return new BN(1);\n\n    // Skip leading zeroes\n    var res = this;\n    for (var i = 0; i < w.length; i++, res = res.sqr()) {\n      if (w[i] !== 0) break;\n    }\n\n    if (++i < w.length) {\n      for (var q = res.sqr(); i < w.length; i++, q = q.sqr()) {\n        if (w[i] === 0) continue;\n\n        res = res.mul(q);\n      }\n    }\n\n    return res;\n  };\n\n  // Shift-left in-place\n  BN.prototype.iushln = function iushln (bits) {\n    assert(typeof bits === 'number' && bits >= 0);\n    var r = bits % 26;\n    var s = (bits - r) / 26;\n    var carryMask = (0x3ffffff >>> (26 - r)) << (26 - r);\n    var i;\n\n    if (r !== 0) {\n      var carry = 0;\n\n      for (i = 0; i < this.length; i++) {\n        var newCarry = this.words[i] & carryMask;\n        var c = ((this.words[i] | 0) - newCarry) << r;\n        this.words[i] = c | carry;\n        carry = newCarry >>> (26 - r);\n      }\n\n      if (carry) {\n        this.words[i] = carry;\n        this.length++;\n      }\n    }\n\n    if (s !== 0) {\n      for (i = this.length - 1; i >= 0; i--) {\n        this.words[i + s] = this.words[i];\n      }\n\n      for (i = 0; i < s; i++) {\n        this.words[i] = 0;\n      }\n\n      this.length += s;\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.ishln = function ishln (bits) {\n    // TODO(indutny): implement me\n    assert(this.negative === 0);\n    return this.iushln(bits);\n  };\n\n  // Shift-right in-place\n  // NOTE: `hint` is a lowest bit before trailing zeroes\n  // NOTE: if `extended` is present - it will be filled with destroyed bits\n  BN.prototype.iushrn = function iushrn (bits, hint, extended) {\n    assert(typeof bits === 'number' && bits >= 0);\n    var h;\n    if (hint) {\n      h = (hint - (hint % 26)) / 26;\n    } else {\n      h = 0;\n    }\n\n    var r = bits % 26;\n    var s = Math.min((bits - r) / 26, this.length);\n    var mask = 0x3ffffff ^ ((0x3ffffff >>> r) << r);\n    var maskedWords = extended;\n\n    h -= s;\n    h = Math.max(0, h);\n\n    // Extended mode, copy masked part\n    if (maskedWords) {\n      for (var i = 0; i < s; i++) {\n        maskedWords.words[i] = this.words[i];\n      }\n      maskedWords.length = s;\n    }\n\n    if (s === 0) {\n      // No-op, we should not move anything at all\n    } else if (this.length > s) {\n      this.length -= s;\n      for (i = 0; i < this.length; i++) {\n        this.words[i] = this.words[i + s];\n      }\n    } else {\n      this.words[0] = 0;\n      this.length = 1;\n    }\n\n    var carry = 0;\n    for (i = this.length - 1; i >= 0 && (carry !== 0 || i >= h); i--) {\n      var word = this.words[i] | 0;\n      this.words[i] = (carry << (26 - r)) | (word >>> r);\n      carry = word & mask;\n    }\n\n    // Push carried bits as a mask\n    if (maskedWords && carry !== 0) {\n      maskedWords.words[maskedWords.length++] = carry;\n    }\n\n    if (this.length === 0) {\n      this.words[0] = 0;\n      this.length = 1;\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.ishrn = function ishrn (bits, hint, extended) {\n    // TODO(indutny): implement me\n    assert(this.negative === 0);\n    return this.iushrn(bits, hint, extended);\n  };\n\n  // Shift-left\n  BN.prototype.shln = function shln (bits) {\n    return this.clone().ishln(bits);\n  };\n\n  BN.prototype.ushln = function ushln (bits) {\n    return this.clone().iushln(bits);\n  };\n\n  // Shift-right\n  BN.prototype.shrn = function shrn (bits) {\n    return this.clone().ishrn(bits);\n  };\n\n  BN.prototype.ushrn = function ushrn (bits) {\n    return this.clone().iushrn(bits);\n  };\n\n  // Test if n bit is set\n  BN.prototype.testn = function testn (bit) {\n    assert(typeof bit === 'number' && bit >= 0);\n    var r = bit % 26;\n    var s = (bit - r) / 26;\n    var q = 1 << r;\n\n    // Fast case: bit is much higher than all existing words\n    if (this.length <= s) return false;\n\n    // Check bit and return\n    var w = this.words[s];\n\n    return !!(w & q);\n  };\n\n  // Return only lowers bits of number (in-place)\n  BN.prototype.imaskn = function imaskn (bits) {\n    assert(typeof bits === 'number' && bits >= 0);\n    var r = bits % 26;\n    var s = (bits - r) / 26;\n\n    assert(this.negative === 0, 'imaskn works only with positive numbers');\n\n    if (this.length <= s) {\n      return this;\n    }\n\n    if (r !== 0) {\n      s++;\n    }\n    this.length = Math.min(s, this.length);\n\n    if (r !== 0) {\n      var mask = 0x3ffffff ^ ((0x3ffffff >>> r) << r);\n      this.words[this.length - 1] &= mask;\n    }\n\n    return this.strip();\n  };\n\n  // Return only lowers bits of number\n  BN.prototype.maskn = function maskn (bits) {\n    return this.clone().imaskn(bits);\n  };\n\n  // Add plain number `num` to `this`\n  BN.prototype.iaddn = function iaddn (num) {\n    assert(typeof num === 'number');\n    assert(num < 0x4000000);\n    if (num < 0) return this.isubn(-num);\n\n    // Possible sign change\n    if (this.negative !== 0) {\n      if (this.length === 1 && (this.words[0] | 0) < num) {\n        this.words[0] = num - (this.words[0] | 0);\n        this.negative = 0;\n        return this;\n      }\n\n      this.negative = 0;\n      this.isubn(num);\n      this.negative = 1;\n      return this;\n    }\n\n    // Add without checks\n    return this._iaddn(num);\n  };\n\n  BN.prototype._iaddn = function _iaddn (num) {\n    this.words[0] += num;\n\n    // Carry\n    for (var i = 0; i < this.length && this.words[i] >= 0x4000000; i++) {\n      this.words[i] -= 0x4000000;\n      if (i === this.length - 1) {\n        this.words[i + 1] = 1;\n      } else {\n        this.words[i + 1]++;\n      }\n    }\n    this.length = Math.max(this.length, i + 1);\n\n    return this;\n  };\n\n  // Subtract plain number `num` from `this`\n  BN.prototype.isubn = function isubn (num) {\n    assert(typeof num === 'number');\n    assert(num < 0x4000000);\n    if (num < 0) return this.iaddn(-num);\n\n    if (this.negative !== 0) {\n      this.negative = 0;\n      this.iaddn(num);\n      this.negative = 1;\n      return this;\n    }\n\n    this.words[0] -= num;\n\n    if (this.length === 1 && this.words[0] < 0) {\n      this.words[0] = -this.words[0];\n      this.negative = 1;\n    } else {\n      // Carry\n      for (var i = 0; i < this.length && this.words[i] < 0; i++) {\n        this.words[i] += 0x4000000;\n        this.words[i + 1] -= 1;\n      }\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.addn = function addn (num) {\n    return this.clone().iaddn(num);\n  };\n\n  BN.prototype.subn = function subn (num) {\n    return this.clone().isubn(num);\n  };\n\n  BN.prototype.iabs = function iabs () {\n    this.negative = 0;\n\n    return this;\n  };\n\n  BN.prototype.abs = function abs () {\n    return this.clone().iabs();\n  };\n\n  BN.prototype._ishlnsubmul = function _ishlnsubmul (num, mul, shift) {\n    var len = num.length + shift;\n    var i;\n\n    this._expand(len);\n\n    var w;\n    var carry = 0;\n    for (i = 0; i < num.length; i++) {\n      w = (this.words[i + shift] | 0) + carry;\n      var right = (num.words[i] | 0) * mul;\n      w -= right & 0x3ffffff;\n      carry = (w >> 26) - ((right / 0x4000000) | 0);\n      this.words[i + shift] = w & 0x3ffffff;\n    }\n    for (; i < this.length - shift; i++) {\n      w = (this.words[i + shift] | 0) + carry;\n      carry = w >> 26;\n      this.words[i + shift] = w & 0x3ffffff;\n    }\n\n    if (carry === 0) return this.strip();\n\n    // Subtraction overflow\n    assert(carry === -1);\n    carry = 0;\n    for (i = 0; i < this.length; i++) {\n      w = -(this.words[i] | 0) + carry;\n      carry = w >> 26;\n      this.words[i] = w & 0x3ffffff;\n    }\n    this.negative = 1;\n\n    return this.strip();\n  };\n\n  BN.prototype._wordDiv = function _wordDiv (num, mode) {\n    var shift = this.length - num.length;\n\n    var a = this.clone();\n    var b = num;\n\n    // Normalize\n    var bhi = b.words[b.length - 1] | 0;\n    var bhiBits = this._countBits(bhi);\n    shift = 26 - bhiBits;\n    if (shift !== 0) {\n      b = b.ushln(shift);\n      a.iushln(shift);\n      bhi = b.words[b.length - 1] | 0;\n    }\n\n    // Initialize quotient\n    var m = a.length - b.length;\n    var q;\n\n    if (mode !== 'mod') {\n      q = new BN(null);\n      q.length = m + 1;\n      q.words = new Array(q.length);\n      for (var i = 0; i < q.length; i++) {\n        q.words[i] = 0;\n      }\n    }\n\n    var diff = a.clone()._ishlnsubmul(b, 1, m);\n    if (diff.negative === 0) {\n      a = diff;\n      if (q) {\n        q.words[m] = 1;\n      }\n    }\n\n    for (var j = m - 1; j >= 0; j--) {\n      var qj = (a.words[b.length + j] | 0) * 0x4000000 +\n        (a.words[b.length + j - 1] | 0);\n\n      // NOTE: (qj / bhi) is (0x3ffffff * 0x4000000 + 0x3ffffff) / 0x2000000 max\n      // (0x7ffffff)\n      qj = Math.min((qj / bhi) | 0, 0x3ffffff);\n\n      a._ishlnsubmul(b, qj, j);\n      while (a.negative !== 0) {\n        qj--;\n        a.negative = 0;\n        a._ishlnsubmul(b, 1, j);\n        if (!a.isZero()) {\n          a.negative ^= 1;\n        }\n      }\n      if (q) {\n        q.words[j] = qj;\n      }\n    }\n    if (q) {\n      q.strip();\n    }\n    a.strip();\n\n    // Denormalize\n    if (mode !== 'div' && shift !== 0) {\n      a.iushrn(shift);\n    }\n\n    return {\n      div: q || null,\n      mod: a\n    };\n  };\n\n  // NOTE: 1) `mode` can be set to `mod` to request mod only,\n  //       to `div` to request div only, or be absent to\n  //       request both div & mod\n  //       2) `positive` is true if unsigned mod is requested\n  BN.prototype.divmod = function divmod (num, mode, positive) {\n    assert(!num.isZero());\n\n    if (this.isZero()) {\n      return {\n        div: new BN(0),\n        mod: new BN(0)\n      };\n    }\n\n    var div, mod, res;\n    if (this.negative !== 0 && num.negative === 0) {\n      res = this.neg().divmod(num, mode);\n\n      if (mode !== 'mod') {\n        div = res.div.neg();\n      }\n\n      if (mode !== 'div') {\n        mod = res.mod.neg();\n        if (positive && mod.negative !== 0) {\n          mod.iadd(num);\n        }\n      }\n\n      return {\n        div: div,\n        mod: mod\n      };\n    }\n\n    if (this.negative === 0 && num.negative !== 0) {\n      res = this.divmod(num.neg(), mode);\n\n      if (mode !== 'mod') {\n        div = res.div.neg();\n      }\n\n      return {\n        div: div,\n        mod: res.mod\n      };\n    }\n\n    if ((this.negative & num.negative) !== 0) {\n      res = this.neg().divmod(num.neg(), mode);\n\n      if (mode !== 'div') {\n        mod = res.mod.neg();\n        if (positive && mod.negative !== 0) {\n          mod.isub(num);\n        }\n      }\n\n      return {\n        div: res.div,\n        mod: mod\n      };\n    }\n\n    // Both numbers are positive at this point\n\n    // Strip both numbers to approximate shift value\n    if (num.length > this.length || this.cmp(num) < 0) {\n      return {\n        div: new BN(0),\n        mod: this\n      };\n    }\n\n    // Very short reduction\n    if (num.length === 1) {\n      if (mode === 'div') {\n        return {\n          div: this.divn(num.words[0]),\n          mod: null\n        };\n      }\n\n      if (mode === 'mod') {\n        return {\n          div: null,\n          mod: new BN(this.modn(num.words[0]))\n        };\n      }\n\n      return {\n        div: this.divn(num.words[0]),\n        mod: new BN(this.modn(num.words[0]))\n      };\n    }\n\n    return this._wordDiv(num, mode);\n  };\n\n  // Find `this` / `num`\n  BN.prototype.div = function div (num) {\n    return this.divmod(num, 'div', false).div;\n  };\n\n  // Find `this` % `num`\n  BN.prototype.mod = function mod (num) {\n    return this.divmod(num, 'mod', false).mod;\n  };\n\n  BN.prototype.umod = function umod (num) {\n    return this.divmod(num, 'mod', true).mod;\n  };\n\n  // Find Round(`this` / `num`)\n  BN.prototype.divRound = function divRound (num) {\n    var dm = this.divmod(num);\n\n    // Fast case - exact division\n    if (dm.mod.isZero()) return dm.div;\n\n    var mod = dm.div.negative !== 0 ? dm.mod.isub(num) : dm.mod;\n\n    var half = num.ushrn(1);\n    var r2 = num.andln(1);\n    var cmp = mod.cmp(half);\n\n    // Round down\n    if (cmp < 0 || r2 === 1 && cmp === 0) return dm.div;\n\n    // Round up\n    return dm.div.negative !== 0 ? dm.div.isubn(1) : dm.div.iaddn(1);\n  };\n\n  BN.prototype.modn = function modn (num) {\n    assert(num <= 0x3ffffff);\n    var p = (1 << 26) % num;\n\n    var acc = 0;\n    for (var i = this.length - 1; i >= 0; i--) {\n      acc = (p * acc + (this.words[i] | 0)) % num;\n    }\n\n    return acc;\n  };\n\n  // In-place division by number\n  BN.prototype.idivn = function idivn (num) {\n    assert(num <= 0x3ffffff);\n\n    var carry = 0;\n    for (var i = this.length - 1; i >= 0; i--) {\n      var w = (this.words[i] | 0) + carry * 0x4000000;\n      this.words[i] = (w / num) | 0;\n      carry = w % num;\n    }\n\n    return this.strip();\n  };\n\n  BN.prototype.divn = function divn (num) {\n    return this.clone().idivn(num);\n  };\n\n  BN.prototype.egcd = function egcd (p) {\n    assert(p.negative === 0);\n    assert(!p.isZero());\n\n    var x = this;\n    var y = p.clone();\n\n    if (x.negative !== 0) {\n      x = x.umod(p);\n    } else {\n      x = x.clone();\n    }\n\n    // A * x + B * y = x\n    var A = new BN(1);\n    var B = new BN(0);\n\n    // C * x + D * y = y\n    var C = new BN(0);\n    var D = new BN(1);\n\n    var g = 0;\n\n    while (x.isEven() && y.isEven()) {\n      x.iushrn(1);\n      y.iushrn(1);\n      ++g;\n    }\n\n    var yp = y.clone();\n    var xp = x.clone();\n\n    while (!x.isZero()) {\n      for (var i = 0, im = 1; (x.words[0] & im) === 0 && i < 26; ++i, im <<= 1);\n      if (i > 0) {\n        x.iushrn(i);\n        while (i-- > 0) {\n          if (A.isOdd() || B.isOdd()) {\n            A.iadd(yp);\n            B.isub(xp);\n          }\n\n          A.iushrn(1);\n          B.iushrn(1);\n        }\n      }\n\n      for (var j = 0, jm = 1; (y.words[0] & jm) === 0 && j < 26; ++j, jm <<= 1);\n      if (j > 0) {\n        y.iushrn(j);\n        while (j-- > 0) {\n          if (C.isOdd() || D.isOdd()) {\n            C.iadd(yp);\n            D.isub(xp);\n          }\n\n          C.iushrn(1);\n          D.iushrn(1);\n        }\n      }\n\n      if (x.cmp(y) >= 0) {\n        x.isub(y);\n        A.isub(C);\n        B.isub(D);\n      } else {\n        y.isub(x);\n        C.isub(A);\n        D.isub(B);\n      }\n    }\n\n    return {\n      a: C,\n      b: D,\n      gcd: y.iushln(g)\n    };\n  };\n\n  // This is reduced incarnation of the binary EEA\n  // above, designated to invert members of the\n  // _prime_ fields F(p) at a maximal speed\n  BN.prototype._invmp = function _invmp (p) {\n    assert(p.negative === 0);\n    assert(!p.isZero());\n\n    var a = this;\n    var b = p.clone();\n\n    if (a.negative !== 0) {\n      a = a.umod(p);\n    } else {\n      a = a.clone();\n    }\n\n    var x1 = new BN(1);\n    var x2 = new BN(0);\n\n    var delta = b.clone();\n\n    while (a.cmpn(1) > 0 && b.cmpn(1) > 0) {\n      for (var i = 0, im = 1; (a.words[0] & im) === 0 && i < 26; ++i, im <<= 1);\n      if (i > 0) {\n        a.iushrn(i);\n        while (i-- > 0) {\n          if (x1.isOdd()) {\n            x1.iadd(delta);\n          }\n\n          x1.iushrn(1);\n        }\n      }\n\n      for (var j = 0, jm = 1; (b.words[0] & jm) === 0 && j < 26; ++j, jm <<= 1);\n      if (j > 0) {\n        b.iushrn(j);\n        while (j-- > 0) {\n          if (x2.isOdd()) {\n            x2.iadd(delta);\n          }\n\n          x2.iushrn(1);\n        }\n      }\n\n      if (a.cmp(b) >= 0) {\n        a.isub(b);\n        x1.isub(x2);\n      } else {\n        b.isub(a);\n        x2.isub(x1);\n      }\n    }\n\n    var res;\n    if (a.cmpn(1) === 0) {\n      res = x1;\n    } else {\n      res = x2;\n    }\n\n    if (res.cmpn(0) < 0) {\n      res.iadd(p);\n    }\n\n    return res;\n  };\n\n  BN.prototype.gcd = function gcd (num) {\n    if (this.isZero()) return num.abs();\n    if (num.isZero()) return this.abs();\n\n    var a = this.clone();\n    var b = num.clone();\n    a.negative = 0;\n    b.negative = 0;\n\n    // Remove common factor of two\n    for (var shift = 0; a.isEven() && b.isEven(); shift++) {\n      a.iushrn(1);\n      b.iushrn(1);\n    }\n\n    do {\n      while (a.isEven()) {\n        a.iushrn(1);\n      }\n      while (b.isEven()) {\n        b.iushrn(1);\n      }\n\n      var r = a.cmp(b);\n      if (r < 0) {\n        // Swap `a` and `b` to make `a` always bigger than `b`\n        var t = a;\n        a = b;\n        b = t;\n      } else if (r === 0 || b.cmpn(1) === 0) {\n        break;\n      }\n\n      a.isub(b);\n    } while (true);\n\n    return b.iushln(shift);\n  };\n\n  // Invert number in the field F(num)\n  BN.prototype.invm = function invm (num) {\n    return this.egcd(num).a.umod(num);\n  };\n\n  BN.prototype.isEven = function isEven () {\n    return (this.words[0] & 1) === 0;\n  };\n\n  BN.prototype.isOdd = function isOdd () {\n    return (this.words[0] & 1) === 1;\n  };\n\n  // And first word and num\n  BN.prototype.andln = function andln (num) {\n    return this.words[0] & num;\n  };\n\n  // Increment at the bit position in-line\n  BN.prototype.bincn = function bincn (bit) {\n    assert(typeof bit === 'number');\n    var r = bit % 26;\n    var s = (bit - r) / 26;\n    var q = 1 << r;\n\n    // Fast case: bit is much higher than all existing words\n    if (this.length <= s) {\n      this._expand(s + 1);\n      this.words[s] |= q;\n      return this;\n    }\n\n    // Add bit and propagate, if needed\n    var carry = q;\n    for (var i = s; carry !== 0 && i < this.length; i++) {\n      var w = this.words[i] | 0;\n      w += carry;\n      carry = w >>> 26;\n      w &= 0x3ffffff;\n      this.words[i] = w;\n    }\n    if (carry !== 0) {\n      this.words[i] = carry;\n      this.length++;\n    }\n    return this;\n  };\n\n  BN.prototype.isZero = function isZero () {\n    return this.length === 1 && this.words[0] === 0;\n  };\n\n  BN.prototype.cmpn = function cmpn (num) {\n    var negative = num < 0;\n\n    if (this.negative !== 0 && !negative) return -1;\n    if (this.negative === 0 && negative) return 1;\n\n    this.strip();\n\n    var res;\n    if (this.length > 1) {\n      res = 1;\n    } else {\n      if (negative) {\n        num = -num;\n      }\n\n      assert(num <= 0x3ffffff, 'Number is too big');\n\n      var w = this.words[0] | 0;\n      res = w === num ? 0 : w < num ? -1 : 1;\n    }\n    if (this.negative !== 0) return -res | 0;\n    return res;\n  };\n\n  // Compare two numbers and return:\n  // 1 - if `this` > `num`\n  // 0 - if `this` == `num`\n  // -1 - if `this` < `num`\n  BN.prototype.cmp = function cmp (num) {\n    if (this.negative !== 0 && num.negative === 0) return -1;\n    if (this.negative === 0 && num.negative !== 0) return 1;\n\n    var res = this.ucmp(num);\n    if (this.negative !== 0) return -res | 0;\n    return res;\n  };\n\n  // Unsigned comparison\n  BN.prototype.ucmp = function ucmp (num) {\n    // At this point both numbers have the same sign\n    if (this.length > num.length) return 1;\n    if (this.length < num.length) return -1;\n\n    var res = 0;\n    for (var i = this.length - 1; i >= 0; i--) {\n      var a = this.words[i] | 0;\n      var b = num.words[i] | 0;\n\n      if (a === b) continue;\n      if (a < b) {\n        res = -1;\n      } else if (a > b) {\n        res = 1;\n      }\n      break;\n    }\n    return res;\n  };\n\n  BN.prototype.gtn = function gtn (num) {\n    return this.cmpn(num) === 1;\n  };\n\n  BN.prototype.gt = function gt (num) {\n    return this.cmp(num) === 1;\n  };\n\n  BN.prototype.gten = function gten (num) {\n    return this.cmpn(num) >= 0;\n  };\n\n  BN.prototype.gte = function gte (num) {\n    return this.cmp(num) >= 0;\n  };\n\n  BN.prototype.ltn = function ltn (num) {\n    return this.cmpn(num) === -1;\n  };\n\n  BN.prototype.lt = function lt (num) {\n    return this.cmp(num) === -1;\n  };\n\n  BN.prototype.lten = function lten (num) {\n    return this.cmpn(num) <= 0;\n  };\n\n  BN.prototype.lte = function lte (num) {\n    return this.cmp(num) <= 0;\n  };\n\n  BN.prototype.eqn = function eqn (num) {\n    return this.cmpn(num) === 0;\n  };\n\n  BN.prototype.eq = function eq (num) {\n    return this.cmp(num) === 0;\n  };\n\n  //\n  // A reduce context, could be using montgomery or something better, depending\n  // on the `m` itself.\n  //\n  BN.red = function red (num) {\n    return new Red(num);\n  };\n\n  BN.prototype.toRed = function toRed (ctx) {\n    assert(!this.red, 'Already a number in reduction context');\n    assert(this.negative === 0, 'red works only with positives');\n    return ctx.convertTo(this)._forceRed(ctx);\n  };\n\n  BN.prototype.fromRed = function fromRed () {\n    assert(this.red, 'fromRed works only with numbers in reduction context');\n    return this.red.convertFrom(this);\n  };\n\n  BN.prototype._forceRed = function _forceRed (ctx) {\n    this.red = ctx;\n    return this;\n  };\n\n  BN.prototype.forceRed = function forceRed (ctx) {\n    assert(!this.red, 'Already a number in reduction context');\n    return this._forceRed(ctx);\n  };\n\n  BN.prototype.redAdd = function redAdd (num) {\n    assert(this.red, 'redAdd works only with red numbers');\n    return this.red.add(this, num);\n  };\n\n  BN.prototype.redIAdd = function redIAdd (num) {\n    assert(this.red, 'redIAdd works only with red numbers');\n    return this.red.iadd(this, num);\n  };\n\n  BN.prototype.redSub = function redSub (num) {\n    assert(this.red, 'redSub works only with red numbers');\n    return this.red.sub(this, num);\n  };\n\n  BN.prototype.redISub = function redISub (num) {\n    assert(this.red, 'redISub works only with red numbers');\n    return this.red.isub(this, num);\n  };\n\n  BN.prototype.redShl = function redShl (num) {\n    assert(this.red, 'redShl works only with red numbers');\n    return this.red.shl(this, num);\n  };\n\n  BN.prototype.redMul = function redMul (num) {\n    assert(this.red, 'redMul works only with red numbers');\n    this.red._verify2(this, num);\n    return this.red.mul(this, num);\n  };\n\n  BN.prototype.redIMul = function redIMul (num) {\n    assert(this.red, 'redMul works only with red numbers');\n    this.red._verify2(this, num);\n    return this.red.imul(this, num);\n  };\n\n  BN.prototype.redSqr = function redSqr () {\n    assert(this.red, 'redSqr works only with red numbers');\n    this.red._verify1(this);\n    return this.red.sqr(this);\n  };\n\n  BN.prototype.redISqr = function redISqr () {\n    assert(this.red, 'redISqr works only with red numbers');\n    this.red._verify1(this);\n    return this.red.isqr(this);\n  };\n\n  // Square root over p\n  BN.prototype.redSqrt = function redSqrt () {\n    assert(this.red, 'redSqrt works only with red numbers');\n    this.red._verify1(this);\n    return this.red.sqrt(this);\n  };\n\n  BN.prototype.redInvm = function redInvm () {\n    assert(this.red, 'redInvm works only with red numbers');\n    this.red._verify1(this);\n    return this.red.invm(this);\n  };\n\n  // Return negative clone of `this` % `red modulo`\n  BN.prototype.redNeg = function redNeg () {\n    assert(this.red, 'redNeg works only with red numbers');\n    this.red._verify1(this);\n    return this.red.neg(this);\n  };\n\n  BN.prototype.redPow = function redPow (num) {\n    assert(this.red && !num.red, 'redPow(normalNum)');\n    this.red._verify1(this);\n    return this.red.pow(this, num);\n  };\n\n  // Prime numbers with efficient reduction\n  var primes = {\n    k256: null,\n    p224: null,\n    p192: null,\n    p25519: null\n  };\n\n  // Pseudo-Mersenne prime\n  function MPrime (name, p) {\n    // P = 2 ^ N - K\n    this.name = name;\n    this.p = new BN(p, 16);\n    this.n = this.p.bitLength();\n    this.k = new BN(1).iushln(this.n).isub(this.p);\n\n    this.tmp = this._tmp();\n  }\n\n  MPrime.prototype._tmp = function _tmp () {\n    var tmp = new BN(null);\n    tmp.words = new Array(Math.ceil(this.n / 13));\n    return tmp;\n  };\n\n  MPrime.prototype.ireduce = function ireduce (num) {\n    // Assumes that `num` is less than `P^2`\n    // num = HI * (2 ^ N - K) + HI * K + LO = HI * K + LO (mod P)\n    var r = num;\n    var rlen;\n\n    do {\n      this.split(r, this.tmp);\n      r = this.imulK(r);\n      r = r.iadd(this.tmp);\n      rlen = r.bitLength();\n    } while (rlen > this.n);\n\n    var cmp = rlen < this.n ? -1 : r.ucmp(this.p);\n    if (cmp === 0) {\n      r.words[0] = 0;\n      r.length = 1;\n    } else if (cmp > 0) {\n      r.isub(this.p);\n    } else {\n      if (r.strip !== undefined) {\n        // r is BN v4 instance\n        r.strip();\n      } else {\n        // r is BN v5 instance\n        r._strip();\n      }\n    }\n\n    return r;\n  };\n\n  MPrime.prototype.split = function split (input, out) {\n    input.iushrn(this.n, 0, out);\n  };\n\n  MPrime.prototype.imulK = function imulK (num) {\n    return num.imul(this.k);\n  };\n\n  function K256 () {\n    MPrime.call(\n      this,\n      'k256',\n      'ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f');\n  }\n  inherits(K256, MPrime);\n\n  K256.prototype.split = function split (input, output) {\n    // 256 = 9 * 26 + 22\n    var mask = 0x3fffff;\n\n    var outLen = Math.min(input.length, 9);\n    for (var i = 0; i < outLen; i++) {\n      output.words[i] = input.words[i];\n    }\n    output.length = outLen;\n\n    if (input.length <= 9) {\n      input.words[0] = 0;\n      input.length = 1;\n      return;\n    }\n\n    // Shift by 9 limbs\n    var prev = input.words[9];\n    output.words[output.length++] = prev & mask;\n\n    for (i = 10; i < input.length; i++) {\n      var next = input.words[i] | 0;\n      input.words[i - 10] = ((next & mask) << 4) | (prev >>> 22);\n      prev = next;\n    }\n    prev >>>= 22;\n    input.words[i - 10] = prev;\n    if (prev === 0 && input.length > 10) {\n      input.length -= 10;\n    } else {\n      input.length -= 9;\n    }\n  };\n\n  K256.prototype.imulK = function imulK (num) {\n    // K = 0x1000003d1 = [ 0x40, 0x3d1 ]\n    num.words[num.length] = 0;\n    num.words[num.length + 1] = 0;\n    num.length += 2;\n\n    // bounded at: 0x40 * 0x3ffffff + 0x3d0 = 0x100000390\n    var lo = 0;\n    for (var i = 0; i < num.length; i++) {\n      var w = num.words[i] | 0;\n      lo += w * 0x3d1;\n      num.words[i] = lo & 0x3ffffff;\n      lo = w * 0x40 + ((lo / 0x4000000) | 0);\n    }\n\n    // Fast length reduction\n    if (num.words[num.length - 1] === 0) {\n      num.length--;\n      if (num.words[num.length - 1] === 0) {\n        num.length--;\n      }\n    }\n    return num;\n  };\n\n  function P224 () {\n    MPrime.call(\n      this,\n      'p224',\n      'ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001');\n  }\n  inherits(P224, MPrime);\n\n  function P192 () {\n    MPrime.call(\n      this,\n      'p192',\n      'ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff');\n  }\n  inherits(P192, MPrime);\n\n  function P25519 () {\n    // 2 ^ 255 - 19\n    MPrime.call(\n      this,\n      '25519',\n      '7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed');\n  }\n  inherits(P25519, MPrime);\n\n  P25519.prototype.imulK = function imulK (num) {\n    // K = 0x13\n    var carry = 0;\n    for (var i = 0; i < num.length; i++) {\n      var hi = (num.words[i] | 0) * 0x13 + carry;\n      var lo = hi & 0x3ffffff;\n      hi >>>= 26;\n\n      num.words[i] = lo;\n      carry = hi;\n    }\n    if (carry !== 0) {\n      num.words[num.length++] = carry;\n    }\n    return num;\n  };\n\n  // Exported mostly for testing purposes, use plain name instead\n  BN._prime = function prime (name) {\n    // Cached version of prime\n    if (primes[name]) return primes[name];\n\n    var prime;\n    if (name === 'k256') {\n      prime = new K256();\n    } else if (name === 'p224') {\n      prime = new P224();\n    } else if (name === 'p192') {\n      prime = new P192();\n    } else if (name === 'p25519') {\n      prime = new P25519();\n    } else {\n      throw new Error('Unknown prime ' + name);\n    }\n    primes[name] = prime;\n\n    return prime;\n  };\n\n  //\n  // Base reduction engine\n  //\n  function Red (m) {\n    if (typeof m === 'string') {\n      var prime = BN._prime(m);\n      this.m = prime.p;\n      this.prime = prime;\n    } else {\n      assert(m.gtn(1), 'modulus must be greater than 1');\n      this.m = m;\n      this.prime = null;\n    }\n  }\n\n  Red.prototype._verify1 = function _verify1 (a) {\n    assert(a.negative === 0, 'red works only with positives');\n    assert(a.red, 'red works only with red numbers');\n  };\n\n  Red.prototype._verify2 = function _verify2 (a, b) {\n    assert((a.negative | b.negative) === 0, 'red works only with positives');\n    assert(a.red && a.red === b.red,\n      'red works only with red numbers');\n  };\n\n  Red.prototype.imod = function imod (a) {\n    if (this.prime) return this.prime.ireduce(a)._forceRed(this);\n    return a.umod(this.m)._forceRed(this);\n  };\n\n  Red.prototype.neg = function neg (a) {\n    if (a.isZero()) {\n      return a.clone();\n    }\n\n    return this.m.sub(a)._forceRed(this);\n  };\n\n  Red.prototype.add = function add (a, b) {\n    this._verify2(a, b);\n\n    var res = a.add(b);\n    if (res.cmp(this.m) >= 0) {\n      res.isub(this.m);\n    }\n    return res._forceRed(this);\n  };\n\n  Red.prototype.iadd = function iadd (a, b) {\n    this._verify2(a, b);\n\n    var res = a.iadd(b);\n    if (res.cmp(this.m) >= 0) {\n      res.isub(this.m);\n    }\n    return res;\n  };\n\n  Red.prototype.sub = function sub (a, b) {\n    this._verify2(a, b);\n\n    var res = a.sub(b);\n    if (res.cmpn(0) < 0) {\n      res.iadd(this.m);\n    }\n    return res._forceRed(this);\n  };\n\n  Red.prototype.isub = function isub (a, b) {\n    this._verify2(a, b);\n\n    var res = a.isub(b);\n    if (res.cmpn(0) < 0) {\n      res.iadd(this.m);\n    }\n    return res;\n  };\n\n  Red.prototype.shl = function shl (a, num) {\n    this._verify1(a);\n    return this.imod(a.ushln(num));\n  };\n\n  Red.prototype.imul = function imul (a, b) {\n    this._verify2(a, b);\n    return this.imod(a.imul(b));\n  };\n\n  Red.prototype.mul = function mul (a, b) {\n    this._verify2(a, b);\n    return this.imod(a.mul(b));\n  };\n\n  Red.prototype.isqr = function isqr (a) {\n    return this.imul(a, a.clone());\n  };\n\n  Red.prototype.sqr = function sqr (a) {\n    return this.mul(a, a);\n  };\n\n  Red.prototype.sqrt = function sqrt (a) {\n    if (a.isZero()) return a.clone();\n\n    var mod3 = this.m.andln(3);\n    assert(mod3 % 2 === 1);\n\n    // Fast case\n    if (mod3 === 3) {\n      var pow = this.m.add(new BN(1)).iushrn(2);\n      return this.pow(a, pow);\n    }\n\n    // Tonelli-Shanks algorithm (Totally unoptimized and slow)\n    //\n    // Find Q and S, that Q * 2 ^ S = (P - 1)\n    var q = this.m.subn(1);\n    var s = 0;\n    while (!q.isZero() && q.andln(1) === 0) {\n      s++;\n      q.iushrn(1);\n    }\n    assert(!q.isZero());\n\n    var one = new BN(1).toRed(this);\n    var nOne = one.redNeg();\n\n    // Find quadratic non-residue\n    // NOTE: Max is such because of generalized Riemann hypothesis.\n    var lpow = this.m.subn(1).iushrn(1);\n    var z = this.m.bitLength();\n    z = new BN(2 * z * z).toRed(this);\n\n    while (this.pow(z, lpow).cmp(nOne) !== 0) {\n      z.redIAdd(nOne);\n    }\n\n    var c = this.pow(z, q);\n    var r = this.pow(a, q.addn(1).iushrn(1));\n    var t = this.pow(a, q);\n    var m = s;\n    while (t.cmp(one) !== 0) {\n      var tmp = t;\n      for (var i = 0; tmp.cmp(one) !== 0; i++) {\n        tmp = tmp.redSqr();\n      }\n      assert(i < m);\n      var b = this.pow(c, new BN(1).iushln(m - i - 1));\n\n      r = r.redMul(b);\n      c = b.redSqr();\n      t = t.redMul(c);\n      m = i;\n    }\n\n    return r;\n  };\n\n  Red.prototype.invm = function invm (a) {\n    var inv = a._invmp(this.m);\n    if (inv.negative !== 0) {\n      inv.negative = 0;\n      return this.imod(inv).redNeg();\n    } else {\n      return this.imod(inv);\n    }\n  };\n\n  Red.prototype.pow = function pow (a, num) {\n    if (num.isZero()) return new BN(1).toRed(this);\n    if (num.cmpn(1) === 0) return a.clone();\n\n    var windowSize = 4;\n    var wnd = new Array(1 << windowSize);\n    wnd[0] = new BN(1).toRed(this);\n    wnd[1] = a;\n    for (var i = 2; i < wnd.length; i++) {\n      wnd[i] = this.mul(wnd[i - 1], a);\n    }\n\n    var res = wnd[0];\n    var current = 0;\n    var currentLen = 0;\n    var start = num.bitLength() % 26;\n    if (start === 0) {\n      start = 26;\n    }\n\n    for (i = num.length - 1; i >= 0; i--) {\n      var word = num.words[i];\n      for (var j = start - 1; j >= 0; j--) {\n        var bit = (word >> j) & 1;\n        if (res !== wnd[0]) {\n          res = this.sqr(res);\n        }\n\n        if (bit === 0 && current === 0) {\n          currentLen = 0;\n          continue;\n        }\n\n        current <<= 1;\n        current |= bit;\n        currentLen++;\n        if (currentLen !== windowSize && (i !== 0 || j !== 0)) continue;\n\n        res = this.mul(res, wnd[current]);\n        currentLen = 0;\n        current = 0;\n      }\n      start = 26;\n    }\n\n    return res;\n  };\n\n  Red.prototype.convertTo = function convertTo (num) {\n    var r = num.umod(this.m);\n\n    return r === num ? r.clone() : r;\n  };\n\n  Red.prototype.convertFrom = function convertFrom (num) {\n    var res = num.clone();\n    res.red = null;\n    return res;\n  };\n\n  //\n  // Montgomery method engine\n  //\n\n  BN.mont = function mont (num) {\n    return new Mont(num);\n  };\n\n  function Mont (m) {\n    Red.call(this, m);\n\n    this.shift = this.m.bitLength();\n    if (this.shift % 26 !== 0) {\n      this.shift += 26 - (this.shift % 26);\n    }\n\n    this.r = new BN(1).iushln(this.shift);\n    this.r2 = this.imod(this.r.sqr());\n    this.rinv = this.r._invmp(this.m);\n\n    this.minv = this.rinv.mul(this.r).isubn(1).div(this.m);\n    this.minv = this.minv.umod(this.r);\n    this.minv = this.r.sub(this.minv);\n  }\n  inherits(Mont, Red);\n\n  Mont.prototype.convertTo = function convertTo (num) {\n    return this.imod(num.ushln(this.shift));\n  };\n\n  Mont.prototype.convertFrom = function convertFrom (num) {\n    var r = this.imod(num.mul(this.rinv));\n    r.red = null;\n    return r;\n  };\n\n  Mont.prototype.imul = function imul (a, b) {\n    if (a.isZero() || b.isZero()) {\n      a.words[0] = 0;\n      a.length = 1;\n      return a;\n    }\n\n    var t = a.imul(b);\n    var c = t.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m);\n    var u = t.isub(c).iushrn(this.shift);\n    var res = u;\n\n    if (u.cmp(this.m) >= 0) {\n      res = u.isub(this.m);\n    } else if (u.cmpn(0) < 0) {\n      res = u.iadd(this.m);\n    }\n\n    return res._forceRed(this);\n  };\n\n  Mont.prototype.mul = function mul (a, b) {\n    if (a.isZero() || b.isZero()) return new BN(0)._forceRed(this);\n\n    var t = a.mul(b);\n    var c = t.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m);\n    var u = t.isub(c).iushrn(this.shift);\n    var res = u;\n    if (u.cmp(this.m) >= 0) {\n      res = u.isub(this.m);\n    } else if (u.cmpn(0) < 0) {\n      res = u.iadd(this.m);\n    }\n\n    return res._forceRed(this);\n  };\n\n  Mont.prototype.invm = function invm (a) {\n    // (AR)^-1 * R^2 = (A^-1 * R^-1) * R^2 = A^-1 * R\n    var res = this.imod(a._invmp(this.m).mul(this.r2));\n    return res._forceRed(this);\n  };\n})(typeof module === 'undefined' || module, this);\n", "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      ctor.prototype = Object.create(superCtor.prototype, {\n        constructor: {\n          value: ctor,\n          enumerable: false,\n          writable: true,\n          configurable: true\n        }\n      })\n    }\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    if (superCtor) {\n      ctor.super_ = superCtor\n      var TempCtor = function () {}\n      TempCtor.prototype = superCtor.prototype\n      ctor.prototype = new TempCtor()\n      ctor.prototype.constructor = ctor\n    }\n  }\n}\n", "try {\n  var util = require('util');\n  /* istanbul ignore next */\n  if (typeof util.inherits !== 'function') throw '';\n  module.exports = util.inherits;\n} catch (e) {\n  /* istanbul ignore next */\n  module.exports = require('./inherits_browser.js');\n}\n", "'use strict';\n\nconst inherits = require('inherits');\n\nfunction Reporter(options) {\n  this._reporterState = {\n    obj: null,\n    path: [],\n    options: options || {},\n    errors: []\n  };\n}\nexports.Reporter = Reporter;\n\nReporter.prototype.isError = function isError(obj) {\n  return obj instanceof ReporterError;\n};\n\nReporter.prototype.save = function save() {\n  const state = this._reporterState;\n\n  return { obj: state.obj, pathLen: state.path.length };\n};\n\nReporter.prototype.restore = function restore(data) {\n  const state = this._reporterState;\n\n  state.obj = data.obj;\n  state.path = state.path.slice(0, data.pathLen);\n};\n\nReporter.prototype.enterKey = function enterKey(key) {\n  return this._reporterState.path.push(key);\n};\n\nReporter.prototype.exitKey = function exitKey(index) {\n  const state = this._reporterState;\n\n  state.path = state.path.slice(0, index - 1);\n};\n\nReporter.prototype.leaveKey = function leaveKey(index, key, value) {\n  const state = this._reporterState;\n\n  this.exitKey(index);\n  if (state.obj !== null)\n    state.obj[key] = value;\n};\n\nReporter.prototype.path = function path() {\n  return this._reporterState.path.join('/');\n};\n\nReporter.prototype.enterObject = function enterObject() {\n  const state = this._reporterState;\n\n  const prev = state.obj;\n  state.obj = {};\n  return prev;\n};\n\nReporter.prototype.leaveObject = function leaveObject(prev) {\n  const state = this._reporterState;\n\n  const now = state.obj;\n  state.obj = prev;\n  return now;\n};\n\nReporter.prototype.error = function error(msg) {\n  let err;\n  const state = this._reporterState;\n\n  const inherited = msg instanceof ReporterError;\n  if (inherited) {\n    err = msg;\n  } else {\n    err = new ReporterError(state.path.map(function(elem) {\n      return '[' + JSON.stringify(elem) + ']';\n    }).join(''), msg.message || msg, msg.stack);\n  }\n\n  if (!state.options.partial)\n    throw err;\n\n  if (!inherited)\n    state.errors.push(err);\n\n  return err;\n};\n\nReporter.prototype.wrapResult = function wrapResult(result) {\n  const state = this._reporterState;\n  if (!state.options.partial)\n    return result;\n\n  return {\n    result: this.isError(result) ? null : result,\n    errors: state.errors\n  };\n};\n\nfunction ReporterError(path, msg) {\n  this.path = path;\n  this.rethrow(msg);\n}\ninherits(ReporterError, Error);\n\nReporterError.prototype.rethrow = function rethrow(msg) {\n  this.message = msg + ' at: ' + (this.path || '(shallow)');\n  if (Error.captureStackTrace)\n    Error.captureStackTrace(this, ReporterError);\n\n  if (!this.stack) {\n    try {\n      // IE only adds stack when thrown\n      throw new Error(this.message);\n    } catch (e) {\n      this.stack = e.stack;\n    }\n  }\n  return this;\n};\n", "'use strict';\n\nconst inherits = require('inherits');\nconst Reporter = require('../base/reporter').Reporter;\nconst Buffer = require('safer-buffer').Buffer;\n\nfunction DecoderBuffer(base, options) {\n  Reporter.call(this, options);\n  if (!Buffer.isBuffer(base)) {\n    this.error('Input not Buffer');\n    return;\n  }\n\n  this.base = base;\n  this.offset = 0;\n  this.length = base.length;\n}\ninherits(DecoderBuffer, Reporter);\nexports.DecoderBuffer = DecoderBuffer;\n\nDecoderBuffer.isDecoderBuffer = function isDecoderBuffer(data) {\n  if (data instanceof DecoderBuffer) {\n    return true;\n  }\n\n  // Or accept compatible API\n  const isCompatible = typeof data === 'object' &&\n    Buffer.isBuffer(data.base) &&\n    data.constructor.name === 'DecoderBuffer' &&\n    typeof data.offset === 'number' &&\n    typeof data.length === 'number' &&\n    typeof data.save === 'function' &&\n    typeof data.restore === 'function' &&\n    typeof data.isEmpty === 'function' &&\n    typeof data.readUInt8 === 'function' &&\n    typeof data.skip === 'function' &&\n    typeof data.raw === 'function';\n\n  return isCompatible;\n};\n\nDecoderBuffer.prototype.save = function save() {\n  return { offset: this.offset, reporter: Reporter.prototype.save.call(this) };\n};\n\nDecoderBuffer.prototype.restore = function restore(save) {\n  // Return skipped data\n  const res = new DecoderBuffer(this.base);\n  res.offset = save.offset;\n  res.length = this.offset;\n\n  this.offset = save.offset;\n  Reporter.prototype.restore.call(this, save.reporter);\n\n  return res;\n};\n\nDecoderBuffer.prototype.isEmpty = function isEmpty() {\n  return this.offset === this.length;\n};\n\nDecoderBuffer.prototype.readUInt8 = function readUInt8(fail) {\n  if (this.offset + 1 <= this.length)\n    return this.base.readUInt8(this.offset++, true);\n  else\n    return this.error(fail || 'DecoderBuffer overrun');\n};\n\nDecoderBuffer.prototype.skip = function skip(bytes, fail) {\n  if (!(this.offset + bytes <= this.length))\n    return this.error(fail || 'DecoderBuffer overrun');\n\n  const res = new DecoderBuffer(this.base);\n\n  // Share reporter state\n  res._reporterState = this._reporterState;\n\n  res.offset = this.offset;\n  res.length = this.offset + bytes;\n  this.offset += bytes;\n  return res;\n};\n\nDecoderBuffer.prototype.raw = function raw(save) {\n  return this.base.slice(save ? save.offset : this.offset, this.length);\n};\n\nfunction EncoderBuffer(value, reporter) {\n  if (Array.isArray(value)) {\n    this.length = 0;\n    this.value = value.map(function(item) {\n      if (!EncoderBuffer.isEncoderBuffer(item))\n        item = new EncoderBuffer(item, reporter);\n      this.length += item.length;\n      return item;\n    }, this);\n  } else if (typeof value === 'number') {\n    if (!(0 <= value && value <= 0xff))\n      return reporter.error('non-byte EncoderBuffer value');\n    this.value = value;\n    this.length = 1;\n  } else if (typeof value === 'string') {\n    this.value = value;\n    this.length = Buffer.byteLength(value);\n  } else if (Buffer.isBuffer(value)) {\n    this.value = value;\n    this.length = value.length;\n  } else {\n    return reporter.error('Unsupported type: ' + typeof value);\n  }\n}\nexports.EncoderBuffer = EncoderBuffer;\n\nEncoderBuffer.isEncoderBuffer = function isEncoderBuffer(data) {\n  if (data instanceof EncoderBuffer) {\n    return true;\n  }\n\n  // Or accept compatible API\n  const isCompatible = typeof data === 'object' &&\n    data.constructor.name === 'EncoderBuffer' &&\n    typeof data.length === 'number' &&\n    typeof data.join === 'function';\n\n  return isCompatible;\n};\n\nEncoderBuffer.prototype.join = function join(out, offset) {\n  if (!out)\n    out = Buffer.alloc(this.length);\n  if (!offset)\n    offset = 0;\n\n  if (this.length === 0)\n    return out;\n\n  if (Array.isArray(this.value)) {\n    this.value.forEach(function(item) {\n      item.join(out, offset);\n      offset += item.length;\n    });\n  } else {\n    if (typeof this.value === 'number')\n      out[offset] = this.value;\n    else if (typeof this.value === 'string')\n      out.write(this.value, offset);\n    else if (Buffer.isBuffer(this.value))\n      this.value.copy(out, offset);\n    offset += this.length;\n  }\n\n  return out;\n};\n", "module.exports = assert;\n\nfunction assert(val, msg) {\n  if (!val)\n    throw new Error(msg || 'Asser<PERSON> failed');\n}\n\nassert.equal = function assertEqual(l, r, msg) {\n  if (l != r)\n    throw new Error(msg || ('<PERSON><PERSON><PERSON> failed: ' + l + ' != ' + r));\n};\n", "'use strict';\n\nconst Reporter = require('../base/reporter').Reporter;\nconst EncoderBuffer = require('../base/buffer').EncoderBuffer;\nconst DecoderBuffer = require('../base/buffer').DecoderBuffer;\nconst assert = require('minimalistic-assert');\n\n// Supported tags\nconst tags = [\n  'seq', 'seqof', 'set', 'setof', 'objid', 'bool',\n  'gentime', 'utctime', 'null_', 'enum', 'int', 'objDesc',\n  'bitstr', 'bmpstr', 'charstr', 'genstr', 'graphstr', 'ia5str', 'iso646str',\n  'numstr', 'octstr', 'printstr', 't61str', 'unistr', 'utf8str', 'videostr'\n];\n\n// Public methods list\nconst methods = [\n  'key', 'obj', 'use', 'optional', 'explicit', 'implicit', 'def', 'choice',\n  'any', 'contains'\n].concat(tags);\n\n// Overrided methods list\nconst overrided = [\n  '_peekTag', '_decodeTag', '_use',\n  '_decodeStr', '_decodeObjid', '_decodeTime',\n  '_decodeNull', '_decodeInt', '_decodeBool', '_decodeList',\n\n  '_encodeComposite', '_encodeStr', '_encodeObjid', '_encodeTime',\n  '_encodeNull', '_encodeInt', '_encodeBool'\n];\n\nfunction Node(enc, parent, name) {\n  const state = {};\n  this._baseState = state;\n\n  state.name = name;\n  state.enc = enc;\n\n  state.parent = parent || null;\n  state.children = null;\n\n  // State\n  state.tag = null;\n  state.args = null;\n  state.reverseArgs = null;\n  state.choice = null;\n  state.optional = false;\n  state.any = false;\n  state.obj = false;\n  state.use = null;\n  state.useDecoder = null;\n  state.key = null;\n  state['default'] = null;\n  state.explicit = null;\n  state.implicit = null;\n  state.contains = null;\n\n  // Should create new instance on each method\n  if (!state.parent) {\n    state.children = [];\n    this._wrap();\n  }\n}\nmodule.exports = Node;\n\nconst stateProps = [\n  'enc', 'parent', 'children', 'tag', 'args', 'reverseArgs', 'choice',\n  'optional', 'any', 'obj', 'use', 'alteredUse', 'key', 'default', 'explicit',\n  'implicit', 'contains'\n];\n\nNode.prototype.clone = function clone() {\n  const state = this._baseState;\n  const cstate = {};\n  stateProps.forEach(function(prop) {\n    cstate[prop] = state[prop];\n  });\n  const res = new this.constructor(cstate.parent);\n  res._baseState = cstate;\n  return res;\n};\n\nNode.prototype._wrap = function wrap() {\n  const state = this._baseState;\n  methods.forEach(function(method) {\n    this[method] = function _wrappedMethod() {\n      const clone = new this.constructor(this);\n      state.children.push(clone);\n      return clone[method].apply(clone, arguments);\n    };\n  }, this);\n};\n\nNode.prototype._init = function init(body) {\n  const state = this._baseState;\n\n  assert(state.parent === null);\n  body.call(this);\n\n  // Filter children\n  state.children = state.children.filter(function(child) {\n    return child._baseState.parent === this;\n  }, this);\n  assert.equal(state.children.length, 1, 'Root node can have only one child');\n};\n\nNode.prototype._useArgs = function useArgs(args) {\n  const state = this._baseState;\n\n  // Filter children and args\n  const children = args.filter(function(arg) {\n    return arg instanceof this.constructor;\n  }, this);\n  args = args.filter(function(arg) {\n    return !(arg instanceof this.constructor);\n  }, this);\n\n  if (children.length !== 0) {\n    assert(state.children === null);\n    state.children = children;\n\n    // Replace parent to maintain backward link\n    children.forEach(function(child) {\n      child._baseState.parent = this;\n    }, this);\n  }\n  if (args.length !== 0) {\n    assert(state.args === null);\n    state.args = args;\n    state.reverseArgs = args.map(function(arg) {\n      if (typeof arg !== 'object' || arg.constructor !== Object)\n        return arg;\n\n      const res = {};\n      Object.keys(arg).forEach(function(key) {\n        if (key == (key | 0))\n          key |= 0;\n        const value = arg[key];\n        res[value] = key;\n      });\n      return res;\n    });\n  }\n};\n\n//\n// Overrided methods\n//\n\noverrided.forEach(function(method) {\n  Node.prototype[method] = function _overrided() {\n    const state = this._baseState;\n    throw new Error(method + ' not implemented for encoding: ' + state.enc);\n  };\n});\n\n//\n// Public methods\n//\n\ntags.forEach(function(tag) {\n  Node.prototype[tag] = function _tagMethod() {\n    const state = this._baseState;\n    const args = Array.prototype.slice.call(arguments);\n\n    assert(state.tag === null);\n    state.tag = tag;\n\n    this._useArgs(args);\n\n    return this;\n  };\n});\n\nNode.prototype.use = function use(item) {\n  assert(item);\n  const state = this._baseState;\n\n  assert(state.use === null);\n  state.use = item;\n\n  return this;\n};\n\nNode.prototype.optional = function optional() {\n  const state = this._baseState;\n\n  state.optional = true;\n\n  return this;\n};\n\nNode.prototype.def = function def(val) {\n  const state = this._baseState;\n\n  assert(state['default'] === null);\n  state['default'] = val;\n  state.optional = true;\n\n  return this;\n};\n\nNode.prototype.explicit = function explicit(num) {\n  const state = this._baseState;\n\n  assert(state.explicit === null && state.implicit === null);\n  state.explicit = num;\n\n  return this;\n};\n\nNode.prototype.implicit = function implicit(num) {\n  const state = this._baseState;\n\n  assert(state.explicit === null && state.implicit === null);\n  state.implicit = num;\n\n  return this;\n};\n\nNode.prototype.obj = function obj() {\n  const state = this._baseState;\n  const args = Array.prototype.slice.call(arguments);\n\n  state.obj = true;\n\n  if (args.length !== 0)\n    this._useArgs(args);\n\n  return this;\n};\n\nNode.prototype.key = function key(newKey) {\n  const state = this._baseState;\n\n  assert(state.key === null);\n  state.key = newKey;\n\n  return this;\n};\n\nNode.prototype.any = function any() {\n  const state = this._baseState;\n\n  state.any = true;\n\n  return this;\n};\n\nNode.prototype.choice = function choice(obj) {\n  const state = this._baseState;\n\n  assert(state.choice === null);\n  state.choice = obj;\n  this._useArgs(Object.keys(obj).map(function(key) {\n    return obj[key];\n  }));\n\n  return this;\n};\n\nNode.prototype.contains = function contains(item) {\n  const state = this._baseState;\n\n  assert(state.use === null);\n  state.contains = item;\n\n  return this;\n};\n\n//\n// Decoding\n//\n\nNode.prototype._decode = function decode(input, options) {\n  const state = this._baseState;\n\n  // Decode root node\n  if (state.parent === null)\n    return input.wrapResult(state.children[0]._decode(input, options));\n\n  let result = state['default'];\n  let present = true;\n\n  let prevKey = null;\n  if (state.key !== null)\n    prevKey = input.enterKey(state.key);\n\n  // Check if tag is there\n  if (state.optional) {\n    let tag = null;\n    if (state.explicit !== null)\n      tag = state.explicit;\n    else if (state.implicit !== null)\n      tag = state.implicit;\n    else if (state.tag !== null)\n      tag = state.tag;\n\n    if (tag === null && !state.any) {\n      // Trial and Error\n      const save = input.save();\n      try {\n        if (state.choice === null)\n          this._decodeGeneric(state.tag, input, options);\n        else\n          this._decodeChoice(input, options);\n        present = true;\n      } catch (e) {\n        present = false;\n      }\n      input.restore(save);\n    } else {\n      present = this._peekTag(input, tag, state.any);\n\n      if (input.isError(present))\n        return present;\n    }\n  }\n\n  // Push object on stack\n  let prevObj;\n  if (state.obj && present)\n    prevObj = input.enterObject();\n\n  if (present) {\n    // Unwrap explicit values\n    if (state.explicit !== null) {\n      const explicit = this._decodeTag(input, state.explicit);\n      if (input.isError(explicit))\n        return explicit;\n      input = explicit;\n    }\n\n    const start = input.offset;\n\n    // Unwrap implicit and normal values\n    if (state.use === null && state.choice === null) {\n      let save;\n      if (state.any)\n        save = input.save();\n      const body = this._decodeTag(\n        input,\n        state.implicit !== null ? state.implicit : state.tag,\n        state.any\n      );\n      if (input.isError(body))\n        return body;\n\n      if (state.any)\n        result = input.raw(save);\n      else\n        input = body;\n    }\n\n    if (options && options.track && state.tag !== null)\n      options.track(input.path(), start, input.length, 'tagged');\n\n    if (options && options.track && state.tag !== null)\n      options.track(input.path(), input.offset, input.length, 'content');\n\n    // Select proper method for tag\n    if (state.any) {\n      // no-op\n    } else if (state.choice === null) {\n      result = this._decodeGeneric(state.tag, input, options);\n    } else {\n      result = this._decodeChoice(input, options);\n    }\n\n    if (input.isError(result))\n      return result;\n\n    // Decode children\n    if (!state.any && state.choice === null && state.children !== null) {\n      state.children.forEach(function decodeChildren(child) {\n        // NOTE: We are ignoring errors here, to let parser continue with other\n        // parts of encoded data\n        child._decode(input, options);\n      });\n    }\n\n    // Decode contained/encoded by schema, only in bit or octet strings\n    if (state.contains && (state.tag === 'octstr' || state.tag === 'bitstr')) {\n      const data = new DecoderBuffer(result);\n      result = this._getUse(state.contains, input._reporterState.obj)\n        ._decode(data, options);\n    }\n  }\n\n  // Pop object\n  if (state.obj && present)\n    result = input.leaveObject(prevObj);\n\n  // Set key\n  if (state.key !== null && (result !== null || present === true))\n    input.leaveKey(prevKey, state.key, result);\n  else if (prevKey !== null)\n    input.exitKey(prevKey);\n\n  return result;\n};\n\nNode.prototype._decodeGeneric = function decodeGeneric(tag, input, options) {\n  const state = this._baseState;\n\n  if (tag === 'seq' || tag === 'set')\n    return null;\n  if (tag === 'seqof' || tag === 'setof')\n    return this._decodeList(input, tag, state.args[0], options);\n  else if (/str$/.test(tag))\n    return this._decodeStr(input, tag, options);\n  else if (tag === 'objid' && state.args)\n    return this._decodeObjid(input, state.args[0], state.args[1], options);\n  else if (tag === 'objid')\n    return this._decodeObjid(input, null, null, options);\n  else if (tag === 'gentime' || tag === 'utctime')\n    return this._decodeTime(input, tag, options);\n  else if (tag === 'null_')\n    return this._decodeNull(input, options);\n  else if (tag === 'bool')\n    return this._decodeBool(input, options);\n  else if (tag === 'objDesc')\n    return this._decodeStr(input, tag, options);\n  else if (tag === 'int' || tag === 'enum')\n    return this._decodeInt(input, state.args && state.args[0], options);\n\n  if (state.use !== null) {\n    return this._getUse(state.use, input._reporterState.obj)\n      ._decode(input, options);\n  } else {\n    return input.error('unknown tag: ' + tag);\n  }\n};\n\nNode.prototype._getUse = function _getUse(entity, obj) {\n\n  const state = this._baseState;\n  // Create altered use decoder if implicit is set\n  state.useDecoder = this._use(entity, obj);\n  assert(state.useDecoder._baseState.parent === null);\n  state.useDecoder = state.useDecoder._baseState.children[0];\n  if (state.implicit !== state.useDecoder._baseState.implicit) {\n    state.useDecoder = state.useDecoder.clone();\n    state.useDecoder._baseState.implicit = state.implicit;\n  }\n  return state.useDecoder;\n};\n\nNode.prototype._decodeChoice = function decodeChoice(input, options) {\n  const state = this._baseState;\n  let result = null;\n  let match = false;\n\n  Object.keys(state.choice).some(function(key) {\n    const save = input.save();\n    const node = state.choice[key];\n    try {\n      const value = node._decode(input, options);\n      if (input.isError(value))\n        return false;\n\n      result = { type: key, value: value };\n      match = true;\n    } catch (e) {\n      input.restore(save);\n      return false;\n    }\n    return true;\n  }, this);\n\n  if (!match)\n    return input.error('Choice not matched');\n\n  return result;\n};\n\n//\n// Encoding\n//\n\nNode.prototype._createEncoderBuffer = function createEncoderBuffer(data) {\n  return new EncoderBuffer(data, this.reporter);\n};\n\nNode.prototype._encode = function encode(data, reporter, parent) {\n  const state = this._baseState;\n  if (state['default'] !== null && state['default'] === data)\n    return;\n\n  const result = this._encodeValue(data, reporter, parent);\n  if (result === undefined)\n    return;\n\n  if (this._skipDefault(result, reporter, parent))\n    return;\n\n  return result;\n};\n\nNode.prototype._encodeValue = function encode(data, reporter, parent) {\n  const state = this._baseState;\n\n  // Decode root node\n  if (state.parent === null)\n    return state.children[0]._encode(data, reporter || new Reporter());\n\n  let result = null;\n\n  // Set reporter to share it with a child class\n  this.reporter = reporter;\n\n  // Check if data is there\n  if (state.optional && data === undefined) {\n    if (state['default'] !== null)\n      data = state['default'];\n    else\n      return;\n  }\n\n  // Encode children first\n  let content = null;\n  let primitive = false;\n  if (state.any) {\n    // Anything that was given is translated to buffer\n    result = this._createEncoderBuffer(data);\n  } else if (state.choice) {\n    result = this._encodeChoice(data, reporter);\n  } else if (state.contains) {\n    content = this._getUse(state.contains, parent)._encode(data, reporter);\n    primitive = true;\n  } else if (state.children) {\n    content = state.children.map(function(child) {\n      if (child._baseState.tag === 'null_')\n        return child._encode(null, reporter, data);\n\n      if (child._baseState.key === null)\n        return reporter.error('Child should have a key');\n      const prevKey = reporter.enterKey(child._baseState.key);\n\n      if (typeof data !== 'object')\n        return reporter.error('Child expected, but input is not object');\n\n      const res = child._encode(data[child._baseState.key], reporter, data);\n      reporter.leaveKey(prevKey);\n\n      return res;\n    }, this).filter(function(child) {\n      return child;\n    });\n    content = this._createEncoderBuffer(content);\n  } else {\n    if (state.tag === 'seqof' || state.tag === 'setof') {\n      // TODO(indutny): this should be thrown on DSL level\n      if (!(state.args && state.args.length === 1))\n        return reporter.error('Too many args for : ' + state.tag);\n\n      if (!Array.isArray(data))\n        return reporter.error('seqof/setof, but data is not Array');\n\n      const child = this.clone();\n      child._baseState.implicit = null;\n      content = this._createEncoderBuffer(data.map(function(item) {\n        const state = this._baseState;\n\n        return this._getUse(state.args[0], data)._encode(item, reporter);\n      }, child));\n    } else if (state.use !== null) {\n      result = this._getUse(state.use, parent)._encode(data, reporter);\n    } else {\n      content = this._encodePrimitive(state.tag, data);\n      primitive = true;\n    }\n  }\n\n  // Encode data itself\n  if (!state.any && state.choice === null) {\n    const tag = state.implicit !== null ? state.implicit : state.tag;\n    const cls = state.implicit === null ? 'universal' : 'context';\n\n    if (tag === null) {\n      if (state.use === null)\n        reporter.error('Tag could be omitted only for .use()');\n    } else {\n      if (state.use === null)\n        result = this._encodeComposite(tag, primitive, cls, content);\n    }\n  }\n\n  // Wrap in explicit\n  if (state.explicit !== null)\n    result = this._encodeComposite(state.explicit, false, 'context', result);\n\n  return result;\n};\n\nNode.prototype._encodeChoice = function encodeChoice(data, reporter) {\n  const state = this._baseState;\n\n  const node = state.choice[data.type];\n  if (!node) {\n    assert(\n      false,\n      data.type + ' not found in ' +\n            JSON.stringify(Object.keys(state.choice)));\n  }\n  return node._encode(data.value, reporter);\n};\n\nNode.prototype._encodePrimitive = function encodePrimitive(tag, data) {\n  const state = this._baseState;\n\n  if (/str$/.test(tag))\n    return this._encodeStr(data, tag);\n  else if (tag === 'objid' && state.args)\n    return this._encodeObjid(data, state.reverseArgs[0], state.args[1]);\n  else if (tag === 'objid')\n    return this._encodeObjid(data, null, null);\n  else if (tag === 'gentime' || tag === 'utctime')\n    return this._encodeTime(data, tag);\n  else if (tag === 'null_')\n    return this._encodeNull();\n  else if (tag === 'int' || tag === 'enum')\n    return this._encodeInt(data, state.args && state.reverseArgs[0]);\n  else if (tag === 'bool')\n    return this._encodeBool(data);\n  else if (tag === 'objDesc')\n    return this._encodeStr(data, tag);\n  else\n    throw new Error('Unsupported tag: ' + tag);\n};\n\nNode.prototype._isNumstr = function isNumstr(str) {\n  return /^[0-9 ]*$/.test(str);\n};\n\nNode.prototype._isPrintstr = function isPrintstr(str) {\n  return /^[A-Za-z0-9 '()+,-./:=?]*$/.test(str);\n};\n", "'use strict';\n\n// Helper\nfunction reverse(map) {\n  const res = {};\n\n  Object.keys(map).forEach(function(key) {\n    // Convert key to integer if it is stringified\n    if ((key | 0) == key)\n      key = key | 0;\n\n    const value = map[key];\n    res[value] = key;\n  });\n\n  return res;\n}\n\nexports.tagClass = {\n  0: 'universal',\n  1: 'application',\n  2: 'context',\n  3: 'private'\n};\nexports.tagClassByName = reverse(exports.tagClass);\n\nexports.tag = {\n  0x00: 'end',\n  0x01: 'bool',\n  0x02: 'int',\n  0x03: 'bitstr',\n  0x04: 'octstr',\n  0x05: 'null_',\n  0x06: 'objid',\n  0x07: 'objDesc',\n  0x08: 'external',\n  0x09: 'real',\n  0x0a: 'enum',\n  0x0b: 'embed',\n  0x0c: 'utf8str',\n  0x0d: 'relativeOid',\n  0x10: 'seq',\n  0x11: 'set',\n  0x12: 'numstr',\n  0x13: 'printstr',\n  0x14: 't61str',\n  0x15: 'videostr',\n  0x16: 'ia5str',\n  0x17: 'utctime',\n  0x18: 'gentime',\n  0x19: 'graphstr',\n  0x1a: 'iso646str',\n  0x1b: 'genstr',\n  0x1c: 'unistr',\n  0x1d: 'charstr',\n  0x1e: 'bmpstr'\n};\nexports.tagByName = reverse(exports.tag);\n", "'use strict';\n\nconst inherits = require('inherits');\nconst Buffer = require('safer-buffer').Buffer;\nconst Node = require('../base/node');\n\n// Import DER constants\nconst der = require('../constants/der');\n\nfunction DEREncoder(entity) {\n  this.enc = 'der';\n  this.name = entity.name;\n  this.entity = entity;\n\n  // Construct base tree\n  this.tree = new DERNode();\n  this.tree._init(entity.body);\n}\nmodule.exports = DEREncoder;\n\nDEREncoder.prototype.encode = function encode(data, reporter) {\n  return this.tree._encode(data, reporter).join();\n};\n\n// Tree methods\n\nfunction DERNode(parent) {\n  Node.call(this, 'der', parent);\n}\ninherits(DERNode, Node);\n\nDERNode.prototype._encodeComposite = function encodeComposite(tag,\n  primitive,\n  cls,\n  content) {\n  const encodedTag = encodeTag(tag, primitive, cls, this.reporter);\n\n  // Short form\n  if (content.length < 0x80) {\n    const header = Buffer.alloc(2);\n    header[0] = encodedTag;\n    header[1] = content.length;\n    return this._createEncoderBuffer([ header, content ]);\n  }\n\n  // Long form\n  // Count octets required to store length\n  let lenOctets = 1;\n  for (let i = content.length; i >= 0x100; i >>= 8)\n    lenOctets++;\n\n  const header = Buffer.alloc(1 + 1 + lenOctets);\n  header[0] = encodedTag;\n  header[1] = 0x80 | lenOctets;\n\n  for (let i = 1 + lenOctets, j = content.length; j > 0; i--, j >>= 8)\n    header[i] = j & 0xff;\n\n  return this._createEncoderBuffer([ header, content ]);\n};\n\nDERNode.prototype._encodeStr = function encodeStr(str, tag) {\n  if (tag === 'bitstr') {\n    return this._createEncoderBuffer([ str.unused | 0, str.data ]);\n  } else if (tag === 'bmpstr') {\n    const buf = Buffer.alloc(str.length * 2);\n    for (let i = 0; i < str.length; i++) {\n      buf.writeUInt16BE(str.charCodeAt(i), i * 2);\n    }\n    return this._createEncoderBuffer(buf);\n  } else if (tag === 'numstr') {\n    if (!this._isNumstr(str)) {\n      return this.reporter.error('Encoding of string type: numstr supports ' +\n                                 'only digits and space');\n    }\n    return this._createEncoderBuffer(str);\n  } else if (tag === 'printstr') {\n    if (!this._isPrintstr(str)) {\n      return this.reporter.error('Encoding of string type: printstr supports ' +\n                                 'only latin upper and lower case letters, ' +\n                                 'digits, space, apostrophe, left and rigth ' +\n                                 'parenthesis, plus sign, comma, hyphen, ' +\n                                 'dot, slash, colon, equal sign, ' +\n                                 'question mark');\n    }\n    return this._createEncoderBuffer(str);\n  } else if (/str$/.test(tag)) {\n    return this._createEncoderBuffer(str);\n  } else if (tag === 'objDesc') {\n    return this._createEncoderBuffer(str);\n  } else {\n    return this.reporter.error('Encoding of string type: ' + tag +\n                               ' unsupported');\n  }\n};\n\nDERNode.prototype._encodeObjid = function encodeObjid(id, values, relative) {\n  if (typeof id === 'string') {\n    if (!values)\n      return this.reporter.error('string objid given, but no values map found');\n    if (!values.hasOwnProperty(id))\n      return this.reporter.error('objid not found in values map');\n    id = values[id].split(/[\\s.]+/g);\n    for (let i = 0; i < id.length; i++)\n      id[i] |= 0;\n  } else if (Array.isArray(id)) {\n    id = id.slice();\n    for (let i = 0; i < id.length; i++)\n      id[i] |= 0;\n  }\n\n  if (!Array.isArray(id)) {\n    return this.reporter.error('objid() should be either array or string, ' +\n                               'got: ' + JSON.stringify(id));\n  }\n\n  if (!relative) {\n    if (id[1] >= 40)\n      return this.reporter.error('Second objid identifier OOB');\n    id.splice(0, 2, id[0] * 40 + id[1]);\n  }\n\n  // Count number of octets\n  let size = 0;\n  for (let i = 0; i < id.length; i++) {\n    let ident = id[i];\n    for (size++; ident >= 0x80; ident >>= 7)\n      size++;\n  }\n\n  const objid = Buffer.alloc(size);\n  let offset = objid.length - 1;\n  for (let i = id.length - 1; i >= 0; i--) {\n    let ident = id[i];\n    objid[offset--] = ident & 0x7f;\n    while ((ident >>= 7) > 0)\n      objid[offset--] = 0x80 | (ident & 0x7f);\n  }\n\n  return this._createEncoderBuffer(objid);\n};\n\nfunction two(num) {\n  if (num < 10)\n    return '0' + num;\n  else\n    return num;\n}\n\nDERNode.prototype._encodeTime = function encodeTime(time, tag) {\n  let str;\n  const date = new Date(time);\n\n  if (tag === 'gentime') {\n    str = [\n      two(date.getUTCFullYear()),\n      two(date.getUTCMonth() + 1),\n      two(date.getUTCDate()),\n      two(date.getUTCHours()),\n      two(date.getUTCMinutes()),\n      two(date.getUTCSeconds()),\n      'Z'\n    ].join('');\n  } else if (tag === 'utctime') {\n    str = [\n      two(date.getUTCFullYear() % 100),\n      two(date.getUTCMonth() + 1),\n      two(date.getUTCDate()),\n      two(date.getUTCHours()),\n      two(date.getUTCMinutes()),\n      two(date.getUTCSeconds()),\n      'Z'\n    ].join('');\n  } else {\n    this.reporter.error('Encoding ' + tag + ' time is not supported yet');\n  }\n\n  return this._encodeStr(str, 'octstr');\n};\n\nDERNode.prototype._encodeNull = function encodeNull() {\n  return this._createEncoderBuffer('');\n};\n\nDERNode.prototype._encodeInt = function encodeInt(num, values) {\n  if (typeof num === 'string') {\n    if (!values)\n      return this.reporter.error('String int or enum given, but no values map');\n    if (!values.hasOwnProperty(num)) {\n      return this.reporter.error('Values map doesn\\'t contain: ' +\n                                 JSON.stringify(num));\n    }\n    num = values[num];\n  }\n\n  // Bignum, assume big endian\n  if (typeof num !== 'number' && !Buffer.isBuffer(num)) {\n    const numArray = num.toArray();\n    if (!num.sign && numArray[0] & 0x80) {\n      numArray.unshift(0);\n    }\n    num = Buffer.from(numArray);\n  }\n\n  if (Buffer.isBuffer(num)) {\n    let size = num.length;\n    if (num.length === 0)\n      size++;\n\n    const out = Buffer.alloc(size);\n    num.copy(out);\n    if (num.length === 0)\n      out[0] = 0;\n    return this._createEncoderBuffer(out);\n  }\n\n  if (num < 0x80)\n    return this._createEncoderBuffer(num);\n\n  if (num < 0x100)\n    return this._createEncoderBuffer([0, num]);\n\n  let size = 1;\n  for (let i = num; i >= 0x100; i >>= 8)\n    size++;\n\n  const out = new Array(size);\n  for (let i = out.length - 1; i >= 0; i--) {\n    out[i] = num & 0xff;\n    num >>= 8;\n  }\n  if(out[0] & 0x80) {\n    out.unshift(0);\n  }\n\n  return this._createEncoderBuffer(Buffer.from(out));\n};\n\nDERNode.prototype._encodeBool = function encodeBool(value) {\n  return this._createEncoderBuffer(value ? 0xff : 0);\n};\n\nDERNode.prototype._use = function use(entity, obj) {\n  if (typeof entity === 'function')\n    entity = entity(obj);\n  return entity._getEncoder('der').tree;\n};\n\nDERNode.prototype._skipDefault = function skipDefault(dataBuffer, reporter, parent) {\n  const state = this._baseState;\n  let i;\n  if (state['default'] === null)\n    return false;\n\n  const data = dataBuffer.join();\n  if (state.defaultBuffer === undefined)\n    state.defaultBuffer = this._encodeValue(state['default'], reporter, parent).join();\n\n  if (data.length !== state.defaultBuffer.length)\n    return false;\n\n  for (i=0; i < data.length; i++)\n    if (data[i] !== state.defaultBuffer[i])\n      return false;\n\n  return true;\n};\n\n// Utility methods\n\nfunction encodeTag(tag, primitive, cls, reporter) {\n  let res;\n\n  if (tag === 'seqof')\n    tag = 'seq';\n  else if (tag === 'setof')\n    tag = 'set';\n\n  if (der.tagByName.hasOwnProperty(tag))\n    res = der.tagByName[tag];\n  else if (typeof tag === 'number' && (tag | 0) === tag)\n    res = tag;\n  else\n    return reporter.error('Unknown tag: ' + tag);\n\n  if (res >= 0x1f)\n    return reporter.error('Multi-octet tag encoding unsupported');\n\n  if (!primitive)\n    res |= 0x20;\n\n  res |= (der.tagClassByName[cls || 'universal'] << 6);\n\n  return res;\n}\n", "'use strict';\n\nconst inherits = require('inherits');\n\nconst DEREncoder = require('./der');\n\nfunction PEMEncoder(entity) {\n  DEREncoder.call(this, entity);\n  this.enc = 'pem';\n}\ninherits(PEMEncoder, DEREncoder);\nmodule.exports = PEMEncoder;\n\nPEMEncoder.prototype.encode = function encode(data, options) {\n  const buf = DEREncoder.prototype.encode.call(this, data);\n\n  const p = buf.toString('base64');\n  const out = [ '-----BEGIN ' + options.label + '-----' ];\n  for (let i = 0; i < p.length; i += 64)\n    out.push(p.slice(i, i + 64));\n  out.push('-----END ' + options.label + '-----');\n  return out.join('\\n');\n};\n", "'use strict';\n\nconst encoders = exports;\n\nencoders.der = require('./der');\nencoders.pem = require('./pem');\n", "'use strict';\n\nconst inherits = require('inherits');\n\nconst bignum = require('bn.js');\nconst DecoderBuffer = require('../base/buffer').DecoderBuffer;\nconst Node = require('../base/node');\n\n// Import DER constants\nconst der = require('../constants/der');\n\nfunction DERDecoder(entity) {\n  this.enc = 'der';\n  this.name = entity.name;\n  this.entity = entity;\n\n  // Construct base tree\n  this.tree = new DERNode();\n  this.tree._init(entity.body);\n}\nmodule.exports = DERDecoder;\n\nDERDecoder.prototype.decode = function decode(data, options) {\n  if (!DecoderBuffer.isDecoderBuffer(data)) {\n    data = new DecoderBuffer(data, options);\n  }\n\n  return this.tree._decode(data, options);\n};\n\n// Tree methods\n\nfunction DERNode(parent) {\n  Node.call(this, 'der', parent);\n}\ninherits(DERNode, Node);\n\nDERNode.prototype._peekTag = function peekTag(buffer, tag, any) {\n  if (buffer.isEmpty())\n    return false;\n\n  const state = buffer.save();\n  const decodedTag = derDecodeTag(buffer, 'Failed to peek tag: \"' + tag + '\"');\n  if (buffer.isError(decodedTag))\n    return decodedTag;\n\n  buffer.restore(state);\n\n  return decodedTag.tag === tag || decodedTag.tagStr === tag ||\n    (decodedTag.tagStr + 'of') === tag || any;\n};\n\nDERNode.prototype._decodeTag = function decodeTag(buffer, tag, any) {\n  const decodedTag = derDecodeTag(buffer,\n    'Failed to decode tag of \"' + tag + '\"');\n  if (buffer.isError(decodedTag))\n    return decodedTag;\n\n  let len = derDecodeLen(buffer,\n    decodedTag.primitive,\n    'Failed to get length of \"' + tag + '\"');\n\n  // Failure\n  if (buffer.isError(len))\n    return len;\n\n  if (!any &&\n      decodedTag.tag !== tag &&\n      decodedTag.tagStr !== tag &&\n      decodedTag.tagStr + 'of' !== tag) {\n    return buffer.error('Failed to match tag: \"' + tag + '\"');\n  }\n\n  if (decodedTag.primitive || len !== null)\n    return buffer.skip(len, 'Failed to match body of: \"' + tag + '\"');\n\n  // Indefinite length... find END tag\n  const state = buffer.save();\n  const res = this._skipUntilEnd(\n    buffer,\n    'Failed to skip indefinite length body: \"' + this.tag + '\"');\n  if (buffer.isError(res))\n    return res;\n\n  len = buffer.offset - state.offset;\n  buffer.restore(state);\n  return buffer.skip(len, 'Failed to match body of: \"' + tag + '\"');\n};\n\nDERNode.prototype._skipUntilEnd = function skipUntilEnd(buffer, fail) {\n  for (;;) {\n    const tag = derDecodeTag(buffer, fail);\n    if (buffer.isError(tag))\n      return tag;\n    const len = derDecodeLen(buffer, tag.primitive, fail);\n    if (buffer.isError(len))\n      return len;\n\n    let res;\n    if (tag.primitive || len !== null)\n      res = buffer.skip(len);\n    else\n      res = this._skipUntilEnd(buffer, fail);\n\n    // Failure\n    if (buffer.isError(res))\n      return res;\n\n    if (tag.tagStr === 'end')\n      break;\n  }\n};\n\nDERNode.prototype._decodeList = function decodeList(buffer, tag, decoder,\n  options) {\n  const result = [];\n  while (!buffer.isEmpty()) {\n    const possibleEnd = this._peekTag(buffer, 'end');\n    if (buffer.isError(possibleEnd))\n      return possibleEnd;\n\n    const res = decoder.decode(buffer, 'der', options);\n    if (buffer.isError(res) && possibleEnd)\n      break;\n    result.push(res);\n  }\n  return result;\n};\n\nDERNode.prototype._decodeStr = function decodeStr(buffer, tag) {\n  if (tag === 'bitstr') {\n    const unused = buffer.readUInt8();\n    if (buffer.isError(unused))\n      return unused;\n    return { unused: unused, data: buffer.raw() };\n  } else if (tag === 'bmpstr') {\n    const raw = buffer.raw();\n    if (raw.length % 2 === 1)\n      return buffer.error('Decoding of string type: bmpstr length mismatch');\n\n    let str = '';\n    for (let i = 0; i < raw.length / 2; i++) {\n      str += String.fromCharCode(raw.readUInt16BE(i * 2));\n    }\n    return str;\n  } else if (tag === 'numstr') {\n    const numstr = buffer.raw().toString('ascii');\n    if (!this._isNumstr(numstr)) {\n      return buffer.error('Decoding of string type: ' +\n                          'numstr unsupported characters');\n    }\n    return numstr;\n  } else if (tag === 'octstr') {\n    return buffer.raw();\n  } else if (tag === 'objDesc') {\n    return buffer.raw();\n  } else if (tag === 'printstr') {\n    const printstr = buffer.raw().toString('ascii');\n    if (!this._isPrintstr(printstr)) {\n      return buffer.error('Decoding of string type: ' +\n                          'printstr unsupported characters');\n    }\n    return printstr;\n  } else if (/str$/.test(tag)) {\n    return buffer.raw().toString();\n  } else {\n    return buffer.error('Decoding of string type: ' + tag + ' unsupported');\n  }\n};\n\nDERNode.prototype._decodeObjid = function decodeObjid(buffer, values, relative) {\n  let result;\n  const identifiers = [];\n  let ident = 0;\n  let subident = 0;\n  while (!buffer.isEmpty()) {\n    subident = buffer.readUInt8();\n    ident <<= 7;\n    ident |= subident & 0x7f;\n    if ((subident & 0x80) === 0) {\n      identifiers.push(ident);\n      ident = 0;\n    }\n  }\n  if (subident & 0x80)\n    identifiers.push(ident);\n\n  const first = (identifiers[0] / 40) | 0;\n  const second = identifiers[0] % 40;\n\n  if (relative)\n    result = identifiers;\n  else\n    result = [first, second].concat(identifiers.slice(1));\n\n  if (values) {\n    let tmp = values[result.join(' ')];\n    if (tmp === undefined)\n      tmp = values[result.join('.')];\n    if (tmp !== undefined)\n      result = tmp;\n  }\n\n  return result;\n};\n\nDERNode.prototype._decodeTime = function decodeTime(buffer, tag) {\n  const str = buffer.raw().toString();\n\n  let year;\n  let mon;\n  let day;\n  let hour;\n  let min;\n  let sec;\n  if (tag === 'gentime') {\n    year = str.slice(0, 4) | 0;\n    mon = str.slice(4, 6) | 0;\n    day = str.slice(6, 8) | 0;\n    hour = str.slice(8, 10) | 0;\n    min = str.slice(10, 12) | 0;\n    sec = str.slice(12, 14) | 0;\n  } else if (tag === 'utctime') {\n    year = str.slice(0, 2) | 0;\n    mon = str.slice(2, 4) | 0;\n    day = str.slice(4, 6) | 0;\n    hour = str.slice(6, 8) | 0;\n    min = str.slice(8, 10) | 0;\n    sec = str.slice(10, 12) | 0;\n    if (year < 70)\n      year = 2000 + year;\n    else\n      year = 1900 + year;\n  } else {\n    return buffer.error('Decoding ' + tag + ' time is not supported yet');\n  }\n\n  return Date.UTC(year, mon - 1, day, hour, min, sec, 0);\n};\n\nDERNode.prototype._decodeNull = function decodeNull() {\n  return null;\n};\n\nDERNode.prototype._decodeBool = function decodeBool(buffer) {\n  const res = buffer.readUInt8();\n  if (buffer.isError(res))\n    return res;\n  else\n    return res !== 0;\n};\n\nDERNode.prototype._decodeInt = function decodeInt(buffer, values) {\n  // Bigint, return as it is (assume big endian)\n  const raw = buffer.raw();\n  let res = new bignum(raw);\n\n  if (values)\n    res = values[res.toString(10)] || res;\n\n  return res;\n};\n\nDERNode.prototype._use = function use(entity, obj) {\n  if (typeof entity === 'function')\n    entity = entity(obj);\n  return entity._getDecoder('der').tree;\n};\n\n// Utility methods\n\nfunction derDecodeTag(buf, fail) {\n  let tag = buf.readUInt8(fail);\n  if (buf.isError(tag))\n    return tag;\n\n  const cls = der.tagClass[tag >> 6];\n  const primitive = (tag & 0x20) === 0;\n\n  // Multi-octet tag - load\n  if ((tag & 0x1f) === 0x1f) {\n    let oct = tag;\n    tag = 0;\n    while ((oct & 0x80) === 0x80) {\n      oct = buf.readUInt8(fail);\n      if (buf.isError(oct))\n        return oct;\n\n      tag <<= 7;\n      tag |= oct & 0x7f;\n    }\n  } else {\n    tag &= 0x1f;\n  }\n  const tagStr = der.tag[tag];\n\n  return {\n    cls: cls,\n    primitive: primitive,\n    tag: tag,\n    tagStr: tagStr\n  };\n}\n\nfunction derDecodeLen(buf, primitive, fail) {\n  let len = buf.readUInt8(fail);\n  if (buf.isError(len))\n    return len;\n\n  // Indefinite form\n  if (!primitive && len === 0x80)\n    return null;\n\n  // Definite form\n  if ((len & 0x80) === 0) {\n    // Short form\n    return len;\n  }\n\n  // Long form\n  const num = len & 0x7f;\n  if (num > 4)\n    return buf.error('length octect is too long');\n\n  len = 0;\n  for (let i = 0; i < num; i++) {\n    len <<= 8;\n    const j = buf.readUInt8(fail);\n    if (buf.isError(j))\n      return j;\n    len |= j;\n  }\n\n  return len;\n}\n", "'use strict';\n\nconst inherits = require('inherits');\nconst Buffer = require('safer-buffer').Buffer;\n\nconst DERDecoder = require('./der');\n\nfunction PEMDecoder(entity) {\n  DERDecoder.call(this, entity);\n  this.enc = 'pem';\n}\ninherits(PEMDecoder, DERDecoder);\nmodule.exports = PEMDecoder;\n\nPEMDecoder.prototype.decode = function decode(data, options) {\n  const lines = data.toString().split(/[\\r\\n]+/g);\n\n  const label = options.label.toUpperCase();\n\n  const re = /^-----(BEGIN|END) ([^-]+)-----$/;\n  let start = -1;\n  let end = -1;\n  for (let i = 0; i < lines.length; i++) {\n    const match = lines[i].match(re);\n    if (match === null)\n      continue;\n\n    if (match[2] !== label)\n      continue;\n\n    if (start === -1) {\n      if (match[1] !== 'BEGIN')\n        break;\n      start = i;\n    } else {\n      if (match[1] !== 'END')\n        break;\n      end = i;\n      break;\n    }\n  }\n  if (start === -1 || end === -1)\n    throw new Error('PEM section not found for: ' + label);\n\n  const base64 = lines.slice(start + 1, end).join('');\n  // Remove excessive symbols\n  base64.replace(/[^a-z0-9+/=]+/gi, '');\n\n  const input = Buffer.from(base64, 'base64');\n  return DERDecoder.prototype.decode.call(this, input, options);\n};\n", "'use strict';\n\nconst decoders = exports;\n\ndecoders.der = require('./der');\ndecoders.pem = require('./pem');\n", "'use strict';\n\nconst encoders = require('./encoders');\nconst decoders = require('./decoders');\nconst inherits = require('inherits');\n\nconst api = exports;\n\napi.define = function define(name, body) {\n  return new Entity(name, body);\n};\n\nfunction Entity(name, body) {\n  this.name = name;\n  this.body = body;\n\n  this.decoders = {};\n  this.encoders = {};\n}\n\nEntity.prototype._createNamed = function createNamed(Base) {\n  const name = this.name;\n\n  function Generated(entity) {\n    this._initNamed(entity, name);\n  }\n  inherits(Generated, Base);\n  Generated.prototype._initNamed = function _initNamed(entity, name) {\n    Base.call(this, entity, name);\n  };\n\n  return new Generated(this);\n};\n\nEntity.prototype._getDecoder = function _getDecoder(enc) {\n  enc = enc || 'der';\n  // Lazily create decoder\n  if (!this.decoders.hasOwnProperty(enc))\n    this.decoders[enc] = this._createNamed(decoders[enc]);\n  return this.decoders[enc];\n};\n\nEntity.prototype.decode = function decode(data, enc, options) {\n  return this._getDecoder(enc).decode(data, options);\n};\n\nEntity.prototype._getEncoder = function _getEncoder(enc) {\n  enc = enc || 'der';\n  // Lazily create encoder\n  if (!this.encoders.hasOwnProperty(enc))\n    this.encoders[enc] = this._createNamed(encoders[enc]);\n  return this.encoders[enc];\n};\n\nEntity.prototype.encode = function encode(data, enc, /* internal */ reporter) {\n  return this._getEncoder(enc).encode(data, reporter);\n};\n", "'use strict';\n\nconst base = exports;\n\nbase.Reporter = require('./reporter').Reporter;\nbase.DecoderBuffer = require('./buffer').DecoderBuffer;\nbase.EncoderBuffer = require('./buffer').EncoderBuffer;\nbase.Node = require('./node');\n", "'use strict';\n\nconst constants = exports;\n\n// Helper\nconstants._reverse = function reverse(map) {\n  const res = {};\n\n  Object.keys(map).forEach(function(key) {\n    // Convert key to integer if it is stringified\n    if ((key | 0) == key)\n      key = key | 0;\n\n    const value = map[key];\n    res[value] = key;\n  });\n\n  return res;\n};\n\nconstants.der = require('./der');\n", "'use strict';\n\nconst asn1 = exports;\n\nasn1.bignum = require('bn.js');\n\nasn1.define = require('./asn1/api').define;\nasn1.base = require('./asn1/base');\nasn1.constants = require('./asn1/constants');\nasn1.decoders = require('./asn1/decoders');\nasn1.encoders = require('./asn1/encoders');\n"], "mappings": ";;;;;;;;;;;;AAAA;AAAA;AAAA,KAAC,SAAUA,SAAQC,UAAS;AAC1B;AAGA,eAAS,OAAQ,KAAK,KAAK;AACzB,YAAI,CAAC,IAAK,OAAM,IAAI,MAAM,OAAO,kBAAkB;AAAA,MACrD;AAIA,eAAS,SAAU,MAAM,WAAW;AAClC,aAAK,SAAS;AACd,YAAI,WAAW,WAAY;AAAA,QAAC;AAC5B,iBAAS,YAAY,UAAU;AAC/B,aAAK,YAAY,IAAI,SAAS;AAC9B,aAAK,UAAU,cAAc;AAAA,MAC/B;AAIA,eAAS,GAAI,QAAQ,MAAM,QAAQ;AACjC,YAAI,GAAG,KAAK,MAAM,GAAG;AACnB,iBAAO;AAAA,QACT;AAEA,aAAK,WAAW;AAChB,aAAK,QAAQ;AACb,aAAK,SAAS;AAGd,aAAK,MAAM;AAEX,YAAI,WAAW,MAAM;AACnB,cAAI,SAAS,QAAQ,SAAS,MAAM;AAClC,qBAAS;AACT,mBAAO;AAAA,UACT;AAEA,eAAK,MAAM,UAAU,GAAG,QAAQ,IAAI,UAAU,IAAI;AAAA,QACpD;AAAA,MACF;AACA,UAAI,OAAOD,YAAW,UAAU;AAC9B,QAAAA,QAAO,UAAU;AAAA,MACnB,OAAO;AACL,QAAAC,SAAQ,KAAK;AAAA,MACf;AAEA,SAAG,KAAK;AACR,SAAG,WAAW;AAEd,UAAI;AACJ,UAAI;AACF,YAAI,OAAO,WAAW,eAAe,OAAO,OAAO,WAAW,aAAa;AACzE,mBAAS,OAAO;AAAA,QAClB,OAAO;AACL,mBAAS,iBAAkB;AAAA,QAC7B;AAAA,MACF,SAAS,GAAG;AAAA,MACZ;AAEA,SAAG,OAAO,SAAS,KAAM,KAAK;AAC5B,YAAI,eAAe,IAAI;AACrB,iBAAO;AAAA,QACT;AAEA,eAAO,QAAQ,QAAQ,OAAO,QAAQ,YACpC,IAAI,YAAY,aAAa,GAAG,YAAY,MAAM,QAAQ,IAAI,KAAK;AAAA,MACvE;AAEA,SAAG,MAAM,SAAS,IAAK,MAAM,OAAO;AAClC,YAAI,KAAK,IAAI,KAAK,IAAI,EAAG,QAAO;AAChC,eAAO;AAAA,MACT;AAEA,SAAG,MAAM,SAAS,IAAK,MAAM,OAAO;AAClC,YAAI,KAAK,IAAI,KAAK,IAAI,EAAG,QAAO;AAChC,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,QAAQ,SAAS,KAAM,QAAQ,MAAM,QAAQ;AACxD,YAAI,OAAO,WAAW,UAAU;AAC9B,iBAAO,KAAK,YAAY,QAAQ,MAAM,MAAM;AAAA,QAC9C;AAEA,YAAI,OAAO,WAAW,UAAU;AAC9B,iBAAO,KAAK,WAAW,QAAQ,MAAM,MAAM;AAAA,QAC7C;AAEA,YAAI,SAAS,OAAO;AAClB,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,OAAO,MAAM,QAAQ,KAAK,QAAQ,EAAE;AAErD,iBAAS,OAAO,SAAS,EAAE,QAAQ,QAAQ,EAAE;AAC7C,YAAI,QAAQ;AACZ,YAAI,OAAO,CAAC,MAAM,KAAK;AACrB;AACA,eAAK,WAAW;AAAA,QAClB;AAEA,YAAI,QAAQ,OAAO,QAAQ;AACzB,cAAI,SAAS,IAAI;AACf,iBAAK,UAAU,QAAQ,OAAO,MAAM;AAAA,UACtC,OAAO;AACL,iBAAK,WAAW,QAAQ,MAAM,KAAK;AACnC,gBAAI,WAAW,MAAM;AACnB,mBAAK,WAAW,KAAK,QAAQ,GAAG,MAAM,MAAM;AAAA,YAC9C;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,SAAG,UAAU,cAAc,SAAS,YAAa,QAAQ,MAAM,QAAQ;AACrE,YAAI,SAAS,GAAG;AACd,eAAK,WAAW;AAChB,mBAAS,CAAC;AAAA,QACZ;AACA,YAAI,SAAS,UAAW;AACtB,eAAK,QAAQ,CAAE,SAAS,QAAU;AAClC,eAAK,SAAS;AAAA,QAChB,WAAW,SAAS,kBAAkB;AACpC,eAAK,QAAQ;AAAA,YACX,SAAS;AAAA,YACR,SAAS,WAAa;AAAA,UACzB;AACA,eAAK,SAAS;AAAA,QAChB,OAAO;AACL,iBAAO,SAAS,gBAAgB;AAChC,eAAK,QAAQ;AAAA,YACX,SAAS;AAAA,YACR,SAAS,WAAa;AAAA,YACvB;AAAA,UACF;AACA,eAAK,SAAS;AAAA,QAChB;AAEA,YAAI,WAAW,KAAM;AAGrB,aAAK,WAAW,KAAK,QAAQ,GAAG,MAAM,MAAM;AAAA,MAC9C;AAEA,SAAG,UAAU,aAAa,SAAS,WAAY,QAAQ,MAAM,QAAQ;AAEnE,eAAO,OAAO,OAAO,WAAW,QAAQ;AACxC,YAAI,OAAO,UAAU,GAAG;AACtB,eAAK,QAAQ,CAAE,CAAE;AACjB,eAAK,SAAS;AACd,iBAAO;AAAA,QACT;AAEA,aAAK,SAAS,KAAK,KAAK,OAAO,SAAS,CAAC;AACzC,aAAK,QAAQ,IAAI,MAAM,KAAK,MAAM;AAClC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,MAAM,CAAC,IAAI;AAAA,QAClB;AAEA,YAAI,GAAG;AACP,YAAI,MAAM;AACV,YAAI,WAAW,MAAM;AACnB,eAAK,IAAI,OAAO,SAAS,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG;AACjD,gBAAI,OAAO,CAAC,IAAK,OAAO,IAAI,CAAC,KAAK,IAAM,OAAO,IAAI,CAAC,KAAK;AACzD,iBAAK,MAAM,CAAC,KAAM,KAAK,MAAO;AAC9B,iBAAK,MAAM,IAAI,CAAC,IAAK,MAAO,KAAK,MAAQ;AACzC,mBAAO;AACP,gBAAI,OAAO,IAAI;AACb,qBAAO;AACP;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,WAAW,MAAM;AAC1B,eAAK,IAAI,GAAG,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AAC5C,gBAAI,OAAO,CAAC,IAAK,OAAO,IAAI,CAAC,KAAK,IAAM,OAAO,IAAI,CAAC,KAAK;AACzD,iBAAK,MAAM,CAAC,KAAM,KAAK,MAAO;AAC9B,iBAAK,MAAM,IAAI,CAAC,IAAK,MAAO,KAAK,MAAQ;AACzC,mBAAO;AACP,gBAAI,OAAO,IAAI;AACb,qBAAO;AACP;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,eAAS,cAAe,QAAQ,OAAO;AACrC,YAAI,IAAI,OAAO,WAAW,KAAK;AAE/B,YAAI,KAAK,MAAM,KAAK,IAAI;AACtB,iBAAO,IAAI;AAAA,QAEb,WAAW,KAAK,MAAM,KAAK,KAAK;AAC9B,iBAAO,IAAI;AAAA,QAEb,OAAO;AACL,iBAAQ,IAAI,KAAM;AAAA,QACpB;AAAA,MACF;AAEA,eAAS,aAAc,QAAQ,YAAY,OAAO;AAChD,YAAI,IAAI,cAAc,QAAQ,KAAK;AACnC,YAAI,QAAQ,KAAK,YAAY;AAC3B,eAAK,cAAc,QAAQ,QAAQ,CAAC,KAAK;AAAA,QAC3C;AACA,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,YAAY,SAAS,UAAW,QAAQ,OAAO,QAAQ;AAElE,aAAK,SAAS,KAAK,MAAM,OAAO,SAAS,SAAS,CAAC;AACnD,aAAK,QAAQ,IAAI,MAAM,KAAK,MAAM;AAClC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,MAAM,CAAC,IAAI;AAAA,QAClB;AAGA,YAAI,MAAM;AACV,YAAI,IAAI;AAER,YAAI;AACJ,YAAI,WAAW,MAAM;AACnB,eAAK,IAAI,OAAO,SAAS,GAAG,KAAK,OAAO,KAAK,GAAG;AAC9C,gBAAI,aAAa,QAAQ,OAAO,CAAC,KAAK;AACtC,iBAAK,MAAM,CAAC,KAAK,IAAI;AACrB,gBAAI,OAAO,IAAI;AACb,qBAAO;AACP,mBAAK;AACL,mBAAK,MAAM,CAAC,KAAK,MAAM;AAAA,YACzB,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,cAAc,OAAO,SAAS;AAClC,eAAK,IAAI,cAAc,MAAM,IAAI,QAAQ,IAAI,OAAO,IAAI,OAAO,QAAQ,KAAK,GAAG;AAC7E,gBAAI,aAAa,QAAQ,OAAO,CAAC,KAAK;AACtC,iBAAK,MAAM,CAAC,KAAK,IAAI;AACrB,gBAAI,OAAO,IAAI;AACb,qBAAO;AACP,mBAAK;AACL,mBAAK,MAAM,CAAC,KAAK,MAAM;AAAA,YACzB,OAAO;AACL,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAEA,aAAK,MAAM;AAAA,MACb;AAEA,eAAS,UAAW,KAAK,OAAO,KAAK,KAAK;AACxC,YAAI,IAAI;AACR,YAAI,MAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAClC,iBAAS,IAAI,OAAO,IAAI,KAAK,KAAK;AAChC,cAAI,IAAI,IAAI,WAAW,CAAC,IAAI;AAE5B,eAAK;AAGL,cAAI,KAAK,IAAI;AACX,iBAAK,IAAI,KAAK;AAAA,UAGhB,WAAW,KAAK,IAAI;AAClB,iBAAK,IAAI,KAAK;AAAA,UAGhB,OAAO;AACL,iBAAK;AAAA,UACP;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,aAAa,SAAS,WAAY,QAAQ,MAAM,OAAO;AAElE,aAAK,QAAQ,CAAE,CAAE;AACjB,aAAK,SAAS;AAGd,iBAAS,UAAU,GAAG,UAAU,GAAG,WAAW,UAAW,WAAW,MAAM;AACxE;AAAA,QACF;AACA;AACA,kBAAW,UAAU,OAAQ;AAE7B,YAAI,QAAQ,OAAO,SAAS;AAC5B,YAAI,MAAM,QAAQ;AAClB,YAAI,MAAM,KAAK,IAAI,OAAO,QAAQ,GAAG,IAAI;AAEzC,YAAI,OAAO;AACX,iBAAS,IAAI,OAAO,IAAI,KAAK,KAAK,SAAS;AACzC,iBAAO,UAAU,QAAQ,GAAG,IAAI,SAAS,IAAI;AAE7C,eAAK,MAAM,OAAO;AAClB,cAAI,KAAK,MAAM,CAAC,IAAI,OAAO,UAAW;AACpC,iBAAK,MAAM,CAAC,KAAK;AAAA,UACnB,OAAO;AACL,iBAAK,OAAO,IAAI;AAAA,UAClB;AAAA,QACF;AAEA,YAAI,QAAQ,GAAG;AACb,cAAI,MAAM;AACV,iBAAO,UAAU,QAAQ,GAAG,OAAO,QAAQ,IAAI;AAE/C,eAAK,IAAI,GAAG,IAAI,KAAK,KAAK;AACxB,mBAAO;AAAA,UACT;AAEA,eAAK,MAAM,GAAG;AACd,cAAI,KAAK,MAAM,CAAC,IAAI,OAAO,UAAW;AACpC,iBAAK,MAAM,CAAC,KAAK;AAAA,UACnB,OAAO;AACL,iBAAK,OAAO,IAAI;AAAA,UAClB;AAAA,QACF;AAEA,aAAK,MAAM;AAAA,MACb;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,MAAM;AACvC,aAAK,QAAQ,IAAI,MAAM,KAAK,MAAM;AAClC,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,eAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,QAC9B;AACA,aAAK,SAAS,KAAK;AACnB,aAAK,WAAW,KAAK;AACrB,aAAK,MAAM,KAAK;AAAA,MAClB;AAEA,SAAG,UAAU,QAAQ,SAAS,QAAS;AACrC,YAAI,IAAI,IAAI,GAAG,IAAI;AACnB,aAAK,KAAK,CAAC;AACX,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,UAAU,SAAS,QAAS,MAAM;AAC7C,eAAO,KAAK,SAAS,MAAM;AACzB,eAAK,MAAM,KAAK,QAAQ,IAAI;AAAA,QAC9B;AACA,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,QAAQ,SAAS,QAAS;AACrC,eAAO,KAAK,SAAS,KAAK,KAAK,MAAM,KAAK,SAAS,CAAC,MAAM,GAAG;AAC3D,eAAK;AAAA,QACP;AACA,eAAO,KAAK,UAAU;AAAA,MACxB;AAEA,SAAG,UAAU,YAAY,SAAS,YAAa;AAE7C,YAAI,KAAK,WAAW,KAAK,KAAK,MAAM,CAAC,MAAM,GAAG;AAC5C,eAAK,WAAW;AAAA,QAClB;AACA,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,UAAU,SAAS,UAAW;AACzC,gBAAQ,KAAK,MAAM,YAAY,WAAW,KAAK,SAAS,EAAE,IAAI;AAAA,MAChE;AAgCA,UAAI,QAAQ;AAAA,QACV;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,aAAa;AAAA,QACf;AAAA,QAAG;AAAA,QACH;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAI;AAAA,QAAG;AAAA,QACvB;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAClB;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAClB;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAClB;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,QAAG;AAAA,MACpB;AAEA,UAAI,aAAa;AAAA,QACf;AAAA,QAAG;AAAA,QACH;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAC5D;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAS;AAAA,QAC3D;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAS;AAAA,QAC3D;AAAA,QAAS;AAAA,QAAS;AAAA,QAAS;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QACzD;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,QAAU;AAAA,MAC9D;AAEA,SAAG,UAAU,WAAW,SAAS,SAAU,MAAM,SAAS;AACxD,eAAO,QAAQ;AACf,kBAAU,UAAU,KAAK;AAEzB,YAAI;AACJ,YAAI,SAAS,MAAM,SAAS,OAAO;AACjC,gBAAM;AACN,cAAI,MAAM;AACV,cAAI,QAAQ;AACZ,mBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,gBAAI,IAAI,KAAK,MAAM,CAAC;AACpB,gBAAI,SAAU,KAAK,MAAO,SAAS,UAAU,SAAS,EAAE;AACxD,oBAAS,MAAO,KAAK,MAAQ;AAC7B,mBAAO;AACP,gBAAI,OAAO,IAAI;AACb,qBAAO;AACP;AAAA,YACF;AACA,gBAAI,UAAU,KAAK,MAAM,KAAK,SAAS,GAAG;AACxC,oBAAM,MAAM,IAAI,KAAK,MAAM,IAAI,OAAO;AAAA,YACxC,OAAO;AACL,oBAAM,OAAO;AAAA,YACf;AAAA,UACF;AACA,cAAI,UAAU,GAAG;AACf,kBAAM,MAAM,SAAS,EAAE,IAAI;AAAA,UAC7B;AACA,iBAAO,IAAI,SAAS,YAAY,GAAG;AACjC,kBAAM,MAAM;AAAA,UACd;AACA,cAAI,KAAK,aAAa,GAAG;AACvB,kBAAM,MAAM;AAAA,UACd;AACA,iBAAO;AAAA,QACT;AAEA,YAAI,UAAU,OAAO,MAAM,QAAQ,KAAK,QAAQ,IAAI;AAElD,cAAI,YAAY,WAAW,IAAI;AAE/B,cAAI,YAAY,WAAW,IAAI;AAC/B,gBAAM;AACN,cAAI,IAAI,KAAK,MAAM;AACnB,YAAE,WAAW;AACb,iBAAO,CAAC,EAAE,OAAO,GAAG;AAClB,gBAAI,IAAI,EAAE,KAAK,SAAS,EAAE,SAAS,IAAI;AACvC,gBAAI,EAAE,MAAM,SAAS;AAErB,gBAAI,CAAC,EAAE,OAAO,GAAG;AACf,oBAAM,MAAM,YAAY,EAAE,MAAM,IAAI,IAAI;AAAA,YAC1C,OAAO;AACL,oBAAM,IAAI;AAAA,YACZ;AAAA,UACF;AACA,cAAI,KAAK,OAAO,GAAG;AACjB,kBAAM,MAAM;AAAA,UACd;AACA,iBAAO,IAAI,SAAS,YAAY,GAAG;AACjC,kBAAM,MAAM;AAAA,UACd;AACA,cAAI,KAAK,aAAa,GAAG;AACvB,kBAAM,MAAM;AAAA,UACd;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,OAAO,iCAAiC;AAAA,MACjD;AAEA,SAAG,UAAU,WAAW,SAAS,WAAY;AAC3C,YAAI,MAAM,KAAK,MAAM,CAAC;AACtB,YAAI,KAAK,WAAW,GAAG;AACrB,iBAAO,KAAK,MAAM,CAAC,IAAI;AAAA,QACzB,WAAW,KAAK,WAAW,KAAK,KAAK,MAAM,CAAC,MAAM,GAAM;AAEtD,iBAAO,mBAAoB,KAAK,MAAM,CAAC,IAAI;AAAA,QAC7C,WAAW,KAAK,SAAS,GAAG;AAC1B,iBAAO,OAAO,4CAA4C;AAAA,QAC5D;AACA,eAAQ,KAAK,aAAa,IAAK,CAAC,MAAM;AAAA,MACxC;AAEA,SAAG,UAAU,SAAS,SAAS,SAAU;AACvC,eAAO,KAAK,SAAS,EAAE;AAAA,MACzB;AAEA,SAAG,UAAU,WAAW,SAAS,SAAU,QAAQ,QAAQ;AACzD,eAAO,OAAO,WAAW,WAAW;AACpC,eAAO,KAAK,YAAY,QAAQ,QAAQ,MAAM;AAAA,MAChD;AAEA,SAAG,UAAU,UAAU,SAAS,QAAS,QAAQ,QAAQ;AACvD,eAAO,KAAK,YAAY,OAAO,QAAQ,MAAM;AAAA,MAC/C;AAEA,SAAG,UAAU,cAAc,SAAS,YAAa,WAAW,QAAQ,QAAQ;AAC1E,YAAI,aAAa,KAAK,WAAW;AACjC,YAAI,YAAY,UAAU,KAAK,IAAI,GAAG,UAAU;AAChD,eAAO,cAAc,WAAW,uCAAuC;AACvE,eAAO,YAAY,GAAG,6BAA6B;AAEnD,aAAK,MAAM;AACX,YAAI,eAAe,WAAW;AAC9B,YAAI,MAAM,IAAI,UAAU,SAAS;AAEjC,YAAI,GAAG;AACP,YAAI,IAAI,KAAK,MAAM;AACnB,YAAI,CAAC,cAAc;AAEjB,eAAK,IAAI,GAAG,IAAI,YAAY,YAAY,KAAK;AAC3C,gBAAI,CAAC,IAAI;AAAA,UACX;AAEA,eAAK,IAAI,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK;AAC5B,gBAAI,EAAE,MAAM,GAAI;AAChB,cAAE,OAAO,CAAC;AAEV,gBAAI,YAAY,IAAI,CAAC,IAAI;AAAA,UAC3B;AAAA,QACF,OAAO;AACL,eAAK,IAAI,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK;AAC5B,gBAAI,EAAE,MAAM,GAAI;AAChB,cAAE,OAAO,CAAC;AAEV,gBAAI,CAAC,IAAI;AAAA,UACX;AAEA,iBAAO,IAAI,WAAW,KAAK;AACzB,gBAAI,CAAC,IAAI;AAAA,UACX;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,UAAI,KAAK,OAAO;AACd,WAAG,UAAU,aAAa,SAAS,WAAY,GAAG;AAChD,iBAAO,KAAK,KAAK,MAAM,CAAC;AAAA,QAC1B;AAAA,MACF,OAAO;AACL,WAAG,UAAU,aAAa,SAAS,WAAY,GAAG;AAChD,cAAI,IAAI;AACR,cAAI,IAAI;AACR,cAAI,KAAK,MAAQ;AACf,iBAAK;AACL,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,IAAM;AACb,iBAAK;AACL,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,GAAK;AACZ,iBAAK;AACL,mBAAO;AAAA,UACT;AACA,cAAI,KAAK,GAAM;AACb,iBAAK;AACL,mBAAO;AAAA,UACT;AACA,iBAAO,IAAI;AAAA,QACb;AAAA,MACF;AAEA,SAAG,UAAU,YAAY,SAAS,UAAW,GAAG;AAE9C,YAAI,MAAM,EAAG,QAAO;AAEpB,YAAI,IAAI;AACR,YAAI,IAAI;AACR,aAAK,IAAI,UAAY,GAAG;AACtB,eAAK;AACL,iBAAO;AAAA,QACT;AACA,aAAK,IAAI,SAAU,GAAG;AACpB,eAAK;AACL,iBAAO;AAAA,QACT;AACA,aAAK,IAAI,QAAS,GAAG;AACnB,eAAK;AACL,iBAAO;AAAA,QACT;AACA,aAAK,IAAI,OAAS,GAAG;AACnB,eAAK;AACL,iBAAO;AAAA,QACT;AACA,aAAK,IAAI,OAAS,GAAG;AACnB;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,YAAY,SAAS,YAAa;AAC7C,YAAI,IAAI,KAAK,MAAM,KAAK,SAAS,CAAC;AAClC,YAAI,KAAK,KAAK,WAAW,CAAC;AAC1B,gBAAQ,KAAK,SAAS,KAAK,KAAK;AAAA,MAClC;AAEA,eAAS,WAAY,KAAK;AACxB,YAAI,IAAI,IAAI,MAAM,IAAI,UAAU,CAAC;AAEjC,iBAAS,MAAM,GAAG,MAAM,EAAE,QAAQ,OAAO;AACvC,cAAI,MAAO,MAAM,KAAM;AACvB,cAAI,OAAO,MAAM;AAEjB,YAAE,GAAG,KAAK,IAAI,MAAM,GAAG,IAAK,KAAK,UAAW;AAAA,QAC9C;AAEA,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,WAAW,SAAS,WAAY;AAC3C,YAAI,KAAK,OAAO,EAAG,QAAO;AAE1B,YAAI,IAAI;AACR,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,IAAI,KAAK,UAAU,KAAK,MAAM,CAAC,CAAC;AACpC,eAAK;AACL,cAAI,MAAM,GAAI;AAAA,QAChB;AACA,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,aAAa,SAAS,aAAc;AAC/C,eAAO,KAAK,KAAK,KAAK,UAAU,IAAI,CAAC;AAAA,MACvC;AAEA,SAAG,UAAU,SAAS,SAAS,OAAQ,OAAO;AAC5C,YAAI,KAAK,aAAa,GAAG;AACvB,iBAAO,KAAK,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,CAAC;AAAA,QACxC;AACA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,WAAW,SAAS,SAAU,OAAO;AAChD,YAAI,KAAK,MAAM,QAAQ,CAAC,GAAG;AACzB,iBAAO,KAAK,KAAK,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK;AAAA,QACxC;AACA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,QAAQ,SAAS,QAAS;AACrC,eAAO,KAAK,aAAa;AAAA,MAC3B;AAGA,SAAG,UAAU,MAAM,SAAS,MAAO;AACjC,eAAO,KAAK,MAAM,EAAE,KAAK;AAAA,MAC3B;AAEA,SAAG,UAAU,OAAO,SAAS,OAAQ;AACnC,YAAI,CAAC,KAAK,OAAO,GAAG;AAClB,eAAK,YAAY;AAAA,QACnB;AAEA,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,SAAS,IAAI,QAAQ;AAC/B,eAAK,MAAM,KAAK,QAAQ,IAAI;AAAA,QAC9B;AAEA,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,eAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC;AAAA,QAC7C;AAEA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,gBAAQ,KAAK,WAAW,IAAI,cAAc,CAAC;AAC3C,eAAO,KAAK,KAAK,GAAG;AAAA,MACtB;AAGA,SAAG,UAAU,KAAK,SAAS,GAAI,KAAK;AAClC,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO,KAAK,MAAM,EAAE,IAAI,GAAG;AACzD,eAAO,IAAI,MAAM,EAAE,IAAI,IAAI;AAAA,MAC7B;AAEA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO,KAAK,MAAM,EAAE,KAAK,GAAG;AAC1D,eAAO,IAAI,MAAM,EAAE,KAAK,IAAI;AAAA,MAC9B;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AAExC,YAAI;AACJ,YAAI,KAAK,SAAS,IAAI,QAAQ;AAC5B,cAAI;AAAA,QACN,OAAO;AACL,cAAI;AAAA,QACN;AAEA,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,eAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC;AAAA,QAC7C;AAEA,aAAK,SAAS,EAAE;AAEhB,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,gBAAQ,KAAK,WAAW,IAAI,cAAc,CAAC;AAC3C,eAAO,KAAK,MAAM,GAAG;AAAA,MACvB;AAGA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO,KAAK,MAAM,EAAE,KAAK,GAAG;AAC1D,eAAO,IAAI,MAAM,EAAE,KAAK,IAAI;AAAA,MAC9B;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO,KAAK,MAAM,EAAE,MAAM,GAAG;AAC3D,eAAO,IAAI,MAAM,EAAE,MAAM,IAAI;AAAA,MAC/B;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AAExC,YAAI;AACJ,YAAI;AACJ,YAAI,KAAK,SAAS,IAAI,QAAQ;AAC5B,cAAI;AACJ,cAAI;AAAA,QACN,OAAO;AACL,cAAI;AACJ,cAAI;AAAA,QACN;AAEA,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,eAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC;AAAA,QACxC;AAEA,YAAI,SAAS,GAAG;AACd,iBAAO,IAAI,EAAE,QAAQ,KAAK;AACxB,iBAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC;AAAA,UAC3B;AAAA,QACF;AAEA,aAAK,SAAS,EAAE;AAEhB,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,gBAAQ,KAAK,WAAW,IAAI,cAAc,CAAC;AAC3C,eAAO,KAAK,MAAM,GAAG;AAAA,MACvB;AAGA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO,KAAK,MAAM,EAAE,KAAK,GAAG;AAC1D,eAAO,IAAI,MAAM,EAAE,KAAK,IAAI;AAAA,MAC9B;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO,KAAK,MAAM,EAAE,MAAM,GAAG;AAC3D,eAAO,IAAI,MAAM,EAAE,MAAM,IAAI;AAAA,MAC/B;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,OAAO;AAC1C,eAAO,OAAO,UAAU,YAAY,SAAS,CAAC;AAE9C,YAAI,cAAc,KAAK,KAAK,QAAQ,EAAE,IAAI;AAC1C,YAAI,WAAW,QAAQ;AAGvB,aAAK,QAAQ,WAAW;AAExB,YAAI,WAAW,GAAG;AAChB;AAAA,QACF;AAGA,iBAAS,IAAI,GAAG,IAAI,aAAa,KAAK;AACpC,eAAK,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,IAAI;AAAA,QACnC;AAGA,YAAI,WAAW,GAAG;AAChB,eAAK,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,IAAK,YAAc,KAAK;AAAA,QACvD;AAGA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,OAAO;AACxC,eAAO,KAAK,MAAM,EAAE,MAAM,KAAK;AAAA,MACjC;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK,KAAK;AAC3C,eAAO,OAAO,QAAQ,YAAY,OAAO,CAAC;AAE1C,YAAI,MAAO,MAAM,KAAM;AACvB,YAAI,OAAO,MAAM;AAEjB,aAAK,QAAQ,MAAM,CAAC;AAEpB,YAAI,KAAK;AACP,eAAK,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG,IAAK,KAAK;AAAA,QAC5C,OAAO;AACL,eAAK,MAAM,GAAG,IAAI,KAAK,MAAM,GAAG,IAAI,EAAE,KAAK;AAAA,QAC7C;AAEA,eAAO,KAAK,MAAM;AAAA,MACpB;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,YAAI;AAGJ,YAAI,KAAK,aAAa,KAAK,IAAI,aAAa,GAAG;AAC7C,eAAK,WAAW;AAChB,cAAI,KAAK,KAAK,GAAG;AACjB,eAAK,YAAY;AACjB,iBAAO,KAAK,UAAU;AAAA,QAGxB,WAAW,KAAK,aAAa,KAAK,IAAI,aAAa,GAAG;AACpD,cAAI,WAAW;AACf,cAAI,KAAK,KAAK,GAAG;AACjB,cAAI,WAAW;AACf,iBAAO,EAAE,UAAU;AAAA,QACrB;AAGA,YAAI,GAAG;AACP,YAAI,KAAK,SAAS,IAAI,QAAQ;AAC5B,cAAI;AACJ,cAAI;AAAA,QACN,OAAO;AACL,cAAI;AACJ,cAAI;AAAA,QACN;AAEA,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,eAAK,EAAE,MAAM,CAAC,IAAI,MAAM,EAAE,MAAM,CAAC,IAAI,KAAK;AAC1C,eAAK,MAAM,CAAC,IAAI,IAAI;AACpB,kBAAQ,MAAM;AAAA,QAChB;AACA,eAAO,UAAU,KAAK,IAAI,EAAE,QAAQ,KAAK;AACvC,eAAK,EAAE,MAAM,CAAC,IAAI,KAAK;AACvB,eAAK,MAAM,CAAC,IAAI,IAAI;AACpB,kBAAQ,MAAM;AAAA,QAChB;AAEA,aAAK,SAAS,EAAE;AAChB,YAAI,UAAU,GAAG;AACf,eAAK,MAAM,KAAK,MAAM,IAAI;AAC1B,eAAK;AAAA,QAEP,WAAW,MAAM,MAAM;AACrB,iBAAO,IAAI,EAAE,QAAQ,KAAK;AACxB,iBAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC;AAAA,UAC3B;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,YAAI;AACJ,YAAI,IAAI,aAAa,KAAK,KAAK,aAAa,GAAG;AAC7C,cAAI,WAAW;AACf,gBAAM,KAAK,IAAI,GAAG;AAClB,cAAI,YAAY;AAChB,iBAAO;AAAA,QACT,WAAW,IAAI,aAAa,KAAK,KAAK,aAAa,GAAG;AACpD,eAAK,WAAW;AAChB,gBAAM,IAAI,IAAI,IAAI;AAClB,eAAK,WAAW;AAChB,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO,KAAK,MAAM,EAAE,KAAK,GAAG;AAE1D,eAAO,IAAI,MAAM,EAAE,KAAK,IAAI;AAAA,MAC9B;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AAEtC,YAAI,IAAI,aAAa,GAAG;AACtB,cAAI,WAAW;AACf,cAAI,IAAI,KAAK,KAAK,GAAG;AACrB,cAAI,WAAW;AACf,iBAAO,EAAE,UAAU;AAAA,QAGrB,WAAW,KAAK,aAAa,GAAG;AAC9B,eAAK,WAAW;AAChB,eAAK,KAAK,GAAG;AACb,eAAK,WAAW;AAChB,iBAAO,KAAK,UAAU;AAAA,QACxB;AAGA,YAAI,MAAM,KAAK,IAAI,GAAG;AAGtB,YAAI,QAAQ,GAAG;AACb,eAAK,WAAW;AAChB,eAAK,SAAS;AACd,eAAK,MAAM,CAAC,IAAI;AAChB,iBAAO;AAAA,QACT;AAGA,YAAI,GAAG;AACP,YAAI,MAAM,GAAG;AACX,cAAI;AACJ,cAAI;AAAA,QACN,OAAO;AACL,cAAI;AACJ,cAAI;AAAA,QACN;AAEA,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,eAAK,EAAE,MAAM,CAAC,IAAI,MAAM,EAAE,MAAM,CAAC,IAAI,KAAK;AAC1C,kBAAQ,KAAK;AACb,eAAK,MAAM,CAAC,IAAI,IAAI;AAAA,QACtB;AACA,eAAO,UAAU,KAAK,IAAI,EAAE,QAAQ,KAAK;AACvC,eAAK,EAAE,MAAM,CAAC,IAAI,KAAK;AACvB,kBAAQ,KAAK;AACb,eAAK,MAAM,CAAC,IAAI,IAAI;AAAA,QACtB;AAGA,YAAI,UAAU,KAAK,IAAI,EAAE,UAAU,MAAM,MAAM;AAC7C,iBAAO,IAAI,EAAE,QAAQ,KAAK;AACxB,iBAAK,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC;AAAA,UAC3B;AAAA,QACF;AAEA,aAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,CAAC;AAErC,YAAI,MAAM,MAAM;AACd,eAAK,WAAW;AAAA,QAClB;AAEA,eAAO,KAAK,MAAM;AAAA,MACpB;AAGA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,eAAO,KAAK,MAAM,EAAE,KAAK,GAAG;AAAA,MAC9B;AAEA,eAAS,WAAY,MAAM,KAAK,KAAK;AACnC,YAAI,WAAW,IAAI,WAAW,KAAK;AACnC,YAAI,MAAO,KAAK,SAAS,IAAI,SAAU;AACvC,YAAI,SAAS;AACb,cAAO,MAAM,IAAK;AAGlB,YAAI,IAAI,KAAK,MAAM,CAAC,IAAI;AACxB,YAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AACvB,YAAI,IAAI,IAAI;AAEZ,YAAI,KAAK,IAAI;AACb,YAAI,QAAS,IAAI,WAAa;AAC9B,YAAI,MAAM,CAAC,IAAI;AAEf,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAG5B,cAAI,SAAS,UAAU;AACvB,cAAI,QAAQ,QAAQ;AACpB,cAAI,OAAO,KAAK,IAAI,GAAG,IAAI,SAAS,CAAC;AACrC,mBAAS,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,GAAG,KAAK,MAAM,KAAK;AAC7D,gBAAI,IAAK,IAAI,IAAK;AAClB,gBAAI,KAAK,MAAM,CAAC,IAAI;AACpB,gBAAI,IAAI,MAAM,CAAC,IAAI;AACnB,gBAAI,IAAI,IAAI;AACZ,sBAAW,IAAI,WAAa;AAC5B,oBAAQ,IAAI;AAAA,UACd;AACA,cAAI,MAAM,CAAC,IAAI,QAAQ;AACvB,kBAAQ,SAAS;AAAA,QACnB;AACA,YAAI,UAAU,GAAG;AACf,cAAI,MAAM,CAAC,IAAI,QAAQ;AAAA,QACzB,OAAO;AACL,cAAI;AAAA,QACN;AAEA,eAAO,IAAI,MAAM;AAAA,MACnB;AAKA,UAAI,cAAc,SAASC,aAAa,MAAM,KAAK,KAAK;AACtD,YAAI,IAAI,KAAK;AACb,YAAI,IAAI,IAAI;AACZ,YAAI,IAAI,IAAI;AACZ,YAAI,IAAI;AACR,YAAI;AACJ,YAAI;AACJ,YAAI;AACJ,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AACjB,YAAI,KAAK,EAAE,CAAC,IAAI;AAChB,YAAI,MAAM,KAAK;AACf,YAAI,MAAM,OAAO;AAEjB,YAAI,WAAW,KAAK,WAAW,IAAI;AACnC,YAAI,SAAS;AAEb,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,MAAQ,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACrD,aAAO,MAAM,QAAQ,MAAO,MAAM,OAAO,MAAO;AAChD,cAAM;AAEN,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AAEP,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AAEP,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AAEP,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AAEP,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AAEP,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AAEP,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AAEP,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAM,KAAK,KAAK,KAAK,KAAK,GAAG,IAAK;AAClC,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AAEP,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,cAAM,KAAK,KAAK,KAAK,GAAG;AACxB,cAAO,MAAM,KAAK,KAAK,KAAK,GAAG,IAAK;AACpC,aAAK,KAAK,KAAK,KAAK,GAAG;AACvB,YAAI,OAAS,IAAI,KAAM,OAAO,MAAM,SAAW,MAAO;AACtD,aAAO,MAAM,QAAQ,MAAO,MAAM,QAAQ,MAAO;AACjD,eAAO;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,CAAC,IAAI;AACP,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,UAAE,EAAE,IAAI;AACR,YAAI,MAAM,GAAG;AACX,YAAE,EAAE,IAAI;AACR,cAAI;AAAA,QACN;AACA,eAAO;AAAA,MACT;AAGA,UAAI,CAAC,KAAK,MAAM;AACd,sBAAc;AAAA,MAChB;AAEA,eAAS,SAAU,MAAM,KAAK,KAAK;AACjC,YAAI,WAAW,IAAI,WAAW,KAAK;AACnC,YAAI,SAAS,KAAK,SAAS,IAAI;AAE/B,YAAI,QAAQ;AACZ,YAAI,UAAU;AACd,iBAAS,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,KAAK;AAGvC,cAAI,SAAS;AACb,oBAAU;AACV,cAAI,QAAQ,QAAQ;AACpB,cAAI,OAAO,KAAK,IAAI,GAAG,IAAI,SAAS,CAAC;AACrC,mBAAS,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,SAAS,CAAC,GAAG,KAAK,MAAM,KAAK;AAC7D,gBAAI,IAAI,IAAI;AACZ,gBAAI,IAAI,KAAK,MAAM,CAAC,IAAI;AACxB,gBAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AACvB,gBAAI,IAAI,IAAI;AAEZ,gBAAI,KAAK,IAAI;AACb,qBAAU,UAAW,IAAI,WAAa,KAAM;AAC5C,iBAAM,KAAK,QAAS;AACpB,oBAAQ,KAAK;AACb,qBAAU,UAAU,OAAO,MAAO;AAElC,uBAAW,WAAW;AACtB,sBAAU;AAAA,UACZ;AACA,cAAI,MAAM,CAAC,IAAI;AACf,kBAAQ;AACR,mBAAS;AAAA,QACX;AACA,YAAI,UAAU,GAAG;AACf,cAAI,MAAM,CAAC,IAAI;AAAA,QACjB,OAAO;AACL,cAAI;AAAA,QACN;AAEA,eAAO,IAAI,MAAM;AAAA,MACnB;AAEA,eAAS,WAAY,MAAM,KAAK,KAAK;AACnC,YAAI,OAAO,IAAI,KAAK;AACpB,eAAO,KAAK,KAAK,MAAM,KAAK,GAAG;AAAA,MACjC;AAEA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK,KAAK;AAC7C,YAAI;AACJ,YAAI,MAAM,KAAK,SAAS,IAAI;AAC5B,YAAI,KAAK,WAAW,MAAM,IAAI,WAAW,IAAI;AAC3C,gBAAM,YAAY,MAAM,KAAK,GAAG;AAAA,QAClC,WAAW,MAAM,IAAI;AACnB,gBAAM,WAAW,MAAM,KAAK,GAAG;AAAA,QACjC,WAAW,MAAM,MAAM;AACrB,gBAAM,SAAS,MAAM,KAAK,GAAG;AAAA,QAC/B,OAAO;AACL,gBAAM,WAAW,MAAM,KAAK,GAAG;AAAA,QACjC;AAEA,eAAO;AAAA,MACT;AAKA,eAAS,KAAM,GAAG,GAAG;AACnB,aAAK,IAAI;AACT,aAAK,IAAI;AAAA,MACX;AAEA,WAAK,UAAU,UAAU,SAAS,QAAS,GAAG;AAC5C,YAAI,IAAI,IAAI,MAAM,CAAC;AACnB,YAAI,IAAI,GAAG,UAAU,WAAW,CAAC,IAAI;AACrC,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAE,CAAC,IAAI,KAAK,OAAO,GAAG,GAAG,CAAC;AAAA,QAC5B;AAEA,eAAO;AAAA,MACT;AAGA,WAAK,UAAU,SAAS,SAAS,OAAQ,GAAG,GAAG,GAAG;AAChD,YAAI,MAAM,KAAK,MAAM,IAAI,EAAG,QAAO;AAEnC,YAAI,KAAK;AACT,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,iBAAO,IAAI,MAAO,IAAI,IAAI;AAC1B,gBAAM;AAAA,QACR;AAEA,eAAO;AAAA,MACT;AAIA,WAAK,UAAU,UAAU,SAAS,QAAS,KAAK,KAAK,KAAK,MAAM,MAAM,GAAG;AACvE,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,eAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;AACpB,eAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC;AAAA,QACtB;AAAA,MACF;AAEA,WAAK,UAAU,YAAY,SAAS,UAAW,KAAK,KAAK,MAAM,MAAM,GAAG,KAAK;AAC3E,aAAK,QAAQ,KAAK,KAAK,KAAK,MAAM,MAAM,CAAC;AAEzC,iBAAS,IAAI,GAAG,IAAI,GAAG,MAAM,GAAG;AAC9B,cAAI,IAAI,KAAK;AAEb,cAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AACpC,cAAI,QAAQ,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC;AAEpC,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AAC7B,gBAAI,SAAS;AACb,gBAAI,SAAS;AAEb,qBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,kBAAI,KAAK,KAAK,IAAI,CAAC;AACnB,kBAAI,KAAK,KAAK,IAAI,CAAC;AAEnB,kBAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AACvB,kBAAI,KAAK,KAAK,IAAI,IAAI,CAAC;AAEvB,kBAAI,KAAK,SAAS,KAAK,SAAS;AAEhC,mBAAK,SAAS,KAAK,SAAS;AAC5B,mBAAK;AAEL,mBAAK,IAAI,CAAC,IAAI,KAAK;AACnB,mBAAK,IAAI,CAAC,IAAI,KAAK;AAEnB,mBAAK,IAAI,IAAI,CAAC,IAAI,KAAK;AACvB,mBAAK,IAAI,IAAI,CAAC,IAAI,KAAK;AAGvB,kBAAI,MAAM,GAAG;AACX,qBAAK,QAAQ,SAAS,QAAQ;AAE9B,yBAAS,QAAQ,SAAS,QAAQ;AAClC,yBAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,WAAK,UAAU,cAAc,SAAS,YAAa,GAAG,GAAG;AACvD,YAAI,IAAI,KAAK,IAAI,GAAG,CAAC,IAAI;AACzB,YAAI,MAAM,IAAI;AACd,YAAI,IAAI;AACR,aAAK,IAAI,IAAI,IAAI,GAAG,GAAG,IAAI,MAAM,GAAG;AAClC;AAAA,QACF;AAEA,eAAO,KAAK,IAAI,IAAI;AAAA,MACtB;AAEA,WAAK,UAAU,YAAY,SAAS,UAAW,KAAK,KAAK,GAAG;AAC1D,YAAI,KAAK,EAAG;AAEZ,iBAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC9B,cAAI,IAAI,IAAI,CAAC;AAEb,cAAI,CAAC,IAAI,IAAI,IAAI,IAAI,CAAC;AACtB,cAAI,IAAI,IAAI,CAAC,IAAI;AAEjB,cAAI,IAAI,CAAC;AAET,cAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;AACvB,cAAI,IAAI,IAAI,CAAC,IAAI,CAAC;AAAA,QACpB;AAAA,MACF;AAEA,WAAK,UAAU,eAAe,SAAS,aAAc,IAAI,GAAG;AAC1D,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK;AAC9B,cAAI,IAAI,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,OACtC,KAAK,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IACxB;AAEF,aAAG,CAAC,IAAI,IAAI;AAEZ,cAAI,IAAI,UAAW;AACjB,oBAAQ;AAAA,UACV,OAAO;AACL,oBAAQ,IAAI,WAAY;AAAA,UAC1B;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,WAAK,UAAU,aAAa,SAAS,WAAY,IAAI,KAAK,KAAK,GAAG;AAChE,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,kBAAQ,SAAS,GAAG,CAAC,IAAI;AAEzB,cAAI,IAAI,CAAC,IAAI,QAAQ;AAAQ,kBAAQ,UAAU;AAC/C,cAAI,IAAI,IAAI,CAAC,IAAI,QAAQ;AAAQ,kBAAQ,UAAU;AAAA,QACrD;AAGA,aAAK,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG;AAC5B,cAAI,CAAC,IAAI;AAAA,QACX;AAEA,eAAO,UAAU,CAAC;AAClB,gBAAQ,QAAQ,CAAC,UAAY,CAAC;AAAA,MAChC;AAEA,WAAK,UAAU,OAAO,SAAS,KAAM,GAAG;AACtC,YAAI,KAAK,IAAI,MAAM,CAAC;AACpB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,aAAG,CAAC,IAAI;AAAA,QACV;AAEA,eAAO;AAAA,MACT;AAEA,WAAK,UAAU,OAAO,SAAS,KAAM,GAAG,GAAG,KAAK;AAC9C,YAAI,IAAI,IAAI,KAAK,YAAY,EAAE,QAAQ,EAAE,MAAM;AAE/C,YAAI,MAAM,KAAK,QAAQ,CAAC;AAExB,YAAI,IAAI,KAAK,KAAK,CAAC;AAEnB,YAAI,MAAM,IAAI,MAAM,CAAC;AACrB,YAAI,OAAO,IAAI,MAAM,CAAC;AACtB,YAAI,OAAO,IAAI,MAAM,CAAC;AAEtB,YAAI,OAAO,IAAI,MAAM,CAAC;AACtB,YAAI,QAAQ,IAAI,MAAM,CAAC;AACvB,YAAI,QAAQ,IAAI,MAAM,CAAC;AAEvB,YAAI,OAAO,IAAI;AACf,aAAK,SAAS;AAEd,aAAK,WAAW,EAAE,OAAO,EAAE,QAAQ,KAAK,CAAC;AACzC,aAAK,WAAW,EAAE,OAAO,EAAE,QAAQ,MAAM,CAAC;AAE1C,aAAK,UAAU,KAAK,GAAG,MAAM,MAAM,GAAG,GAAG;AACzC,aAAK,UAAU,MAAM,GAAG,OAAO,OAAO,GAAG,GAAG;AAE5C,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,MAAM,CAAC;AAC/C,eAAK,CAAC,IAAI,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,MAAM,CAAC;AAChD,eAAK,CAAC,IAAI;AAAA,QACZ;AAEA,aAAK,UAAU,MAAM,MAAM,CAAC;AAC5B,aAAK,UAAU,MAAM,MAAM,MAAM,GAAG,GAAG,GAAG;AAC1C,aAAK,UAAU,MAAM,GAAG,CAAC;AACzB,aAAK,aAAa,MAAM,CAAC;AAEzB,YAAI,WAAW,EAAE,WAAW,EAAE;AAC9B,YAAI,SAAS,EAAE,SAAS,EAAE;AAC1B,eAAO,IAAI,MAAM;AAAA,MACnB;AAGA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,YAAI,MAAM,IAAI,GAAG,IAAI;AACrB,YAAI,QAAQ,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM;AAC9C,eAAO,KAAK,MAAM,KAAK,GAAG;AAAA,MAC5B;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,YAAI,MAAM,IAAI,GAAG,IAAI;AACrB,YAAI,QAAQ,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM;AAC9C,eAAO,WAAW,MAAM,KAAK,GAAG;AAAA,MAClC;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,MAAM,EAAE,MAAM,KAAK,IAAI;AAAA,MACrC;AAEA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AACxC,eAAO,OAAO,QAAQ,QAAQ;AAC9B,eAAO,MAAM,QAAS;AAGtB,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,cAAI,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK;AAC9B,cAAI,MAAM,IAAI,aAAc,QAAQ;AACpC,oBAAU;AACV,mBAAU,IAAI,WAAa;AAE3B,mBAAS,OAAO;AAChB,eAAK,MAAM,CAAC,IAAI,KAAK;AAAA,QACvB;AAEA,YAAI,UAAU,GAAG;AACf,eAAK,MAAM,CAAC,IAAI;AAChB,eAAK;AAAA,QACP;AACA,aAAK,SAAS,QAAQ,IAAI,IAAI,KAAK;AAEnC,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,MAAM,EAAE,MAAM,GAAG;AAAA,MAC/B;AAGA,SAAG,UAAU,MAAM,SAAS,MAAO;AACjC,eAAO,KAAK,IAAI,IAAI;AAAA,MACtB;AAGA,SAAG,UAAU,OAAO,SAAS,OAAQ;AACnC,eAAO,KAAK,KAAK,KAAK,MAAM,CAAC;AAAA,MAC/B;AAGA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,YAAI,IAAI,WAAW,GAAG;AACtB,YAAI,EAAE,WAAW,EAAG,QAAO,IAAI,GAAG,CAAC;AAGnC,YAAI,MAAM;AACV,iBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,MAAM,IAAI,IAAI,GAAG;AAClD,cAAI,EAAE,CAAC,MAAM,EAAG;AAAA,QAClB;AAEA,YAAI,EAAE,IAAI,EAAE,QAAQ;AAClB,mBAAS,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK,IAAI,EAAE,IAAI,GAAG;AACtD,gBAAI,EAAE,CAAC,MAAM,EAAG;AAEhB,kBAAM,IAAI,IAAI,CAAC;AAAA,UACjB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,SAAS,SAAS,OAAQ,MAAM;AAC3C,eAAO,OAAO,SAAS,YAAY,QAAQ,CAAC;AAC5C,YAAI,IAAI,OAAO;AACf,YAAI,KAAK,OAAO,KAAK;AACrB,YAAI,YAAa,aAAe,KAAK,KAAQ,KAAK;AAClD,YAAI;AAEJ,YAAI,MAAM,GAAG;AACX,cAAI,QAAQ;AAEZ,eAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,gBAAI,WAAW,KAAK,MAAM,CAAC,IAAI;AAC/B,gBAAI,KAAM,KAAK,MAAM,CAAC,IAAI,KAAK,YAAa;AAC5C,iBAAK,MAAM,CAAC,IAAI,IAAI;AACpB,oBAAQ,aAAc,KAAK;AAAA,UAC7B;AAEA,cAAI,OAAO;AACT,iBAAK,MAAM,CAAC,IAAI;AAChB,iBAAK;AAAA,UACP;AAAA,QACF;AAEA,YAAI,MAAM,GAAG;AACX,eAAK,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACrC,iBAAK,MAAM,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,UAClC;AAEA,eAAK,IAAI,GAAG,IAAI,GAAG,KAAK;AACtB,iBAAK,MAAM,CAAC,IAAI;AAAA,UAClB;AAEA,eAAK,UAAU;AAAA,QACjB;AAEA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,QAAQ,SAAS,MAAO,MAAM;AAEzC,eAAO,KAAK,aAAa,CAAC;AAC1B,eAAO,KAAK,OAAO,IAAI;AAAA,MACzB;AAKA,SAAG,UAAU,SAAS,SAAS,OAAQ,MAAM,MAAM,UAAU;AAC3D,eAAO,OAAO,SAAS,YAAY,QAAQ,CAAC;AAC5C,YAAI;AACJ,YAAI,MAAM;AACR,eAAK,OAAQ,OAAO,MAAO;AAAA,QAC7B,OAAO;AACL,cAAI;AAAA,QACN;AAEA,YAAI,IAAI,OAAO;AACf,YAAI,IAAI,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,MAAM;AAC7C,YAAI,OAAO,WAAc,aAAc,KAAM;AAC7C,YAAI,cAAc;AAElB,aAAK;AACL,YAAI,KAAK,IAAI,GAAG,CAAC;AAGjB,YAAI,aAAa;AACf,mBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,wBAAY,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,UACrC;AACA,sBAAY,SAAS;AAAA,QACvB;AAEA,YAAI,MAAM,GAAG;AAAA,QAEb,WAAW,KAAK,SAAS,GAAG;AAC1B,eAAK,UAAU;AACf,eAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,iBAAK,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC;AAAA,UAClC;AAAA,QACF,OAAO;AACL,eAAK,MAAM,CAAC,IAAI;AAChB,eAAK,SAAS;AAAA,QAChB;AAEA,YAAI,QAAQ;AACZ,aAAK,IAAI,KAAK,SAAS,GAAG,KAAK,MAAM,UAAU,KAAK,KAAK,IAAI,KAAK;AAChE,cAAI,OAAO,KAAK,MAAM,CAAC,IAAI;AAC3B,eAAK,MAAM,CAAC,IAAK,SAAU,KAAK,IAAO,SAAS;AAChD,kBAAQ,OAAO;AAAA,QACjB;AAGA,YAAI,eAAe,UAAU,GAAG;AAC9B,sBAAY,MAAM,YAAY,QAAQ,IAAI;AAAA,QAC5C;AAEA,YAAI,KAAK,WAAW,GAAG;AACrB,eAAK,MAAM,CAAC,IAAI;AAChB,eAAK,SAAS;AAAA,QAChB;AAEA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,QAAQ,SAAS,MAAO,MAAM,MAAM,UAAU;AAEzD,eAAO,KAAK,aAAa,CAAC;AAC1B,eAAO,KAAK,OAAO,MAAM,MAAM,QAAQ;AAAA,MACzC;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,MAAM;AACvC,eAAO,KAAK,MAAM,EAAE,MAAM,IAAI;AAAA,MAChC;AAEA,SAAG,UAAU,QAAQ,SAAS,MAAO,MAAM;AACzC,eAAO,KAAK,MAAM,EAAE,OAAO,IAAI;AAAA,MACjC;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,MAAM;AACvC,eAAO,KAAK,MAAM,EAAE,MAAM,IAAI;AAAA,MAChC;AAEA,SAAG,UAAU,QAAQ,SAAS,MAAO,MAAM;AACzC,eAAO,KAAK,MAAM,EAAE,OAAO,IAAI;AAAA,MACjC;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AACxC,eAAO,OAAO,QAAQ,YAAY,OAAO,CAAC;AAC1C,YAAI,IAAI,MAAM;AACd,YAAI,KAAK,MAAM,KAAK;AACpB,YAAI,IAAI,KAAK;AAGb,YAAI,KAAK,UAAU,EAAG,QAAO;AAG7B,YAAI,IAAI,KAAK,MAAM,CAAC;AAEpB,eAAO,CAAC,EAAE,IAAI;AAAA,MAChB;AAGA,SAAG,UAAU,SAAS,SAAS,OAAQ,MAAM;AAC3C,eAAO,OAAO,SAAS,YAAY,QAAQ,CAAC;AAC5C,YAAI,IAAI,OAAO;AACf,YAAI,KAAK,OAAO,KAAK;AAErB,eAAO,KAAK,aAAa,GAAG,yCAAyC;AAErE,YAAI,KAAK,UAAU,GAAG;AACpB,iBAAO;AAAA,QACT;AAEA,YAAI,MAAM,GAAG;AACX;AAAA,QACF;AACA,aAAK,SAAS,KAAK,IAAI,GAAG,KAAK,MAAM;AAErC,YAAI,MAAM,GAAG;AACX,cAAI,OAAO,WAAc,aAAc,KAAM;AAC7C,eAAK,MAAM,KAAK,SAAS,CAAC,KAAK;AAAA,QACjC;AAEA,eAAO,KAAK,MAAM;AAAA,MACpB;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,MAAM;AACzC,eAAO,KAAK,MAAM,EAAE,OAAO,IAAI;AAAA,MACjC;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AACxC,eAAO,OAAO,QAAQ,QAAQ;AAC9B,eAAO,MAAM,QAAS;AACtB,YAAI,MAAM,EAAG,QAAO,KAAK,MAAM,CAAC,GAAG;AAGnC,YAAI,KAAK,aAAa,GAAG;AACvB,cAAI,KAAK,WAAW,MAAM,KAAK,MAAM,CAAC,IAAI,KAAK,KAAK;AAClD,iBAAK,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI;AACvC,iBAAK,WAAW;AAChB,mBAAO;AAAA,UACT;AAEA,eAAK,WAAW;AAChB,eAAK,MAAM,GAAG;AACd,eAAK,WAAW;AAChB,iBAAO;AAAA,QACT;AAGA,eAAO,KAAK,OAAO,GAAG;AAAA,MACxB;AAEA,SAAG,UAAU,SAAS,SAAS,OAAQ,KAAK;AAC1C,aAAK,MAAM,CAAC,KAAK;AAGjB,iBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,KAAK,MAAM,CAAC,KAAK,UAAW,KAAK;AAClE,eAAK,MAAM,CAAC,KAAK;AACjB,cAAI,MAAM,KAAK,SAAS,GAAG;AACzB,iBAAK,MAAM,IAAI,CAAC,IAAI;AAAA,UACtB,OAAO;AACL,iBAAK,MAAM,IAAI,CAAC;AAAA,UAClB;AAAA,QACF;AACA,aAAK,SAAS,KAAK,IAAI,KAAK,QAAQ,IAAI,CAAC;AAEzC,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AACxC,eAAO,OAAO,QAAQ,QAAQ;AAC9B,eAAO,MAAM,QAAS;AACtB,YAAI,MAAM,EAAG,QAAO,KAAK,MAAM,CAAC,GAAG;AAEnC,YAAI,KAAK,aAAa,GAAG;AACvB,eAAK,WAAW;AAChB,eAAK,MAAM,GAAG;AACd,eAAK,WAAW;AAChB,iBAAO;AAAA,QACT;AAEA,aAAK,MAAM,CAAC,KAAK;AAEjB,YAAI,KAAK,WAAW,KAAK,KAAK,MAAM,CAAC,IAAI,GAAG;AAC1C,eAAK,MAAM,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC;AAC7B,eAAK,WAAW;AAAA,QAClB,OAAO;AAEL,mBAAS,IAAI,GAAG,IAAI,KAAK,UAAU,KAAK,MAAM,CAAC,IAAI,GAAG,KAAK;AACzD,iBAAK,MAAM,CAAC,KAAK;AACjB,iBAAK,MAAM,IAAI,CAAC,KAAK;AAAA,UACvB;AAAA,QACF;AAEA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,MAAM,EAAE,MAAM,GAAG;AAAA,MAC/B;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,MAAM,EAAE,MAAM,GAAG;AAAA,MAC/B;AAEA,SAAG,UAAU,OAAO,SAAS,OAAQ;AACnC,aAAK,WAAW;AAEhB,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,MAAM,SAAS,MAAO;AACjC,eAAO,KAAK,MAAM,EAAE,KAAK;AAAA,MAC3B;AAEA,SAAG,UAAU,eAAe,SAAS,aAAc,KAAK,KAAK,OAAO;AAClE,YAAI,MAAM,IAAI,SAAS;AACvB,YAAI;AAEJ,aAAK,QAAQ,GAAG;AAEhB,YAAI;AACJ,YAAI,QAAQ;AACZ,aAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAC/B,eAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAClC,cAAI,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK;AACjC,eAAK,QAAQ;AACb,mBAAS,KAAK,OAAQ,QAAQ,WAAa;AAC3C,eAAK,MAAM,IAAI,KAAK,IAAI,IAAI;AAAA,QAC9B;AACA,eAAO,IAAI,KAAK,SAAS,OAAO,KAAK;AACnC,eAAK,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AAClC,kBAAQ,KAAK;AACb,eAAK,MAAM,IAAI,KAAK,IAAI,IAAI;AAAA,QAC9B;AAEA,YAAI,UAAU,EAAG,QAAO,KAAK,MAAM;AAGnC,eAAO,UAAU,EAAE;AACnB,gBAAQ;AACR,aAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAChC,cAAI,EAAE,KAAK,MAAM,CAAC,IAAI,KAAK;AAC3B,kBAAQ,KAAK;AACb,eAAK,MAAM,CAAC,IAAI,IAAI;AAAA,QACtB;AACA,aAAK,WAAW;AAEhB,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,WAAW,SAAS,SAAU,KAAK,MAAM;AACpD,YAAI,QAAQ,KAAK,SAAS,IAAI;AAE9B,YAAI,IAAI,KAAK,MAAM;AACnB,YAAI,IAAI;AAGR,YAAI,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,IAAI;AAClC,YAAI,UAAU,KAAK,WAAW,GAAG;AACjC,gBAAQ,KAAK;AACb,YAAI,UAAU,GAAG;AACf,cAAI,EAAE,MAAM,KAAK;AACjB,YAAE,OAAO,KAAK;AACd,gBAAM,EAAE,MAAM,EAAE,SAAS,CAAC,IAAI;AAAA,QAChC;AAGA,YAAI,IAAI,EAAE,SAAS,EAAE;AACrB,YAAI;AAEJ,YAAI,SAAS,OAAO;AAClB,cAAI,IAAI,GAAG,IAAI;AACf,YAAE,SAAS,IAAI;AACf,YAAE,QAAQ,IAAI,MAAM,EAAE,MAAM;AAC5B,mBAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,cAAE,MAAM,CAAC,IAAI;AAAA,UACf;AAAA,QACF;AAEA,YAAI,OAAO,EAAE,MAAM,EAAE,aAAa,GAAG,GAAG,CAAC;AACzC,YAAI,KAAK,aAAa,GAAG;AACvB,cAAI;AACJ,cAAI,GAAG;AACL,cAAE,MAAM,CAAC,IAAI;AAAA,UACf;AAAA,QACF;AAEA,iBAAS,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC/B,cAAI,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,IAAI,KAAK,YACpC,EAAE,MAAM,EAAE,SAAS,IAAI,CAAC,IAAI;AAI/B,eAAK,KAAK,IAAK,KAAK,MAAO,GAAG,QAAS;AAEvC,YAAE,aAAa,GAAG,IAAI,CAAC;AACvB,iBAAO,EAAE,aAAa,GAAG;AACvB;AACA,cAAE,WAAW;AACb,cAAE,aAAa,GAAG,GAAG,CAAC;AACtB,gBAAI,CAAC,EAAE,OAAO,GAAG;AACf,gBAAE,YAAY;AAAA,YAChB;AAAA,UACF;AACA,cAAI,GAAG;AACL,cAAE,MAAM,CAAC,IAAI;AAAA,UACf;AAAA,QACF;AACA,YAAI,GAAG;AACL,YAAE,MAAM;AAAA,QACV;AACA,UAAE,MAAM;AAGR,YAAI,SAAS,SAAS,UAAU,GAAG;AACjC,YAAE,OAAO,KAAK;AAAA,QAChB;AAEA,eAAO;AAAA,UACL,KAAK,KAAK;AAAA,UACV,KAAK;AAAA,QACP;AAAA,MACF;AAMA,SAAG,UAAU,SAAS,SAAS,OAAQ,KAAK,MAAM,UAAU;AAC1D,eAAO,CAAC,IAAI,OAAO,CAAC;AAEpB,YAAI,KAAK,OAAO,GAAG;AACjB,iBAAO;AAAA,YACL,KAAK,IAAI,GAAG,CAAC;AAAA,YACb,KAAK,IAAI,GAAG,CAAC;AAAA,UACf;AAAA,QACF;AAEA,YAAI,KAAK,KAAK;AACd,YAAI,KAAK,aAAa,KAAK,IAAI,aAAa,GAAG;AAC7C,gBAAM,KAAK,IAAI,EAAE,OAAO,KAAK,IAAI;AAEjC,cAAI,SAAS,OAAO;AAClB,kBAAM,IAAI,IAAI,IAAI;AAAA,UACpB;AAEA,cAAI,SAAS,OAAO;AAClB,kBAAM,IAAI,IAAI,IAAI;AAClB,gBAAI,YAAY,IAAI,aAAa,GAAG;AAClC,kBAAI,KAAK,GAAG;AAAA,YACd;AAAA,UACF;AAEA,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAEA,YAAI,KAAK,aAAa,KAAK,IAAI,aAAa,GAAG;AAC7C,gBAAM,KAAK,OAAO,IAAI,IAAI,GAAG,IAAI;AAEjC,cAAI,SAAS,OAAO;AAClB,kBAAM,IAAI,IAAI,IAAI;AAAA,UACpB;AAEA,iBAAO;AAAA,YACL;AAAA,YACA,KAAK,IAAI;AAAA,UACX;AAAA,QACF;AAEA,aAAK,KAAK,WAAW,IAAI,cAAc,GAAG;AACxC,gBAAM,KAAK,IAAI,EAAE,OAAO,IAAI,IAAI,GAAG,IAAI;AAEvC,cAAI,SAAS,OAAO;AAClB,kBAAM,IAAI,IAAI,IAAI;AAClB,gBAAI,YAAY,IAAI,aAAa,GAAG;AAClC,kBAAI,KAAK,GAAG;AAAA,YACd;AAAA,UACF;AAEA,iBAAO;AAAA,YACL,KAAK,IAAI;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAKA,YAAI,IAAI,SAAS,KAAK,UAAU,KAAK,IAAI,GAAG,IAAI,GAAG;AACjD,iBAAO;AAAA,YACL,KAAK,IAAI,GAAG,CAAC;AAAA,YACb,KAAK;AAAA,UACP;AAAA,QACF;AAGA,YAAI,IAAI,WAAW,GAAG;AACpB,cAAI,SAAS,OAAO;AAClB,mBAAO;AAAA,cACL,KAAK,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC;AAAA,cAC3B,KAAK;AAAA,YACP;AAAA,UACF;AAEA,cAAI,SAAS,OAAO;AAClB,mBAAO;AAAA,cACL,KAAK;AAAA,cACL,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC;AAAA,YACrC;AAAA,UACF;AAEA,iBAAO;AAAA,YACL,KAAK,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC;AAAA,YAC3B,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,MAAM,CAAC,CAAC,CAAC;AAAA,UACrC;AAAA,QACF;AAEA,eAAO,KAAK,SAAS,KAAK,IAAI;AAAA,MAChC;AAGA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,eAAO,KAAK,OAAO,KAAK,OAAO,KAAK,EAAE;AAAA,MACxC;AAGA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,eAAO,KAAK,OAAO,KAAK,OAAO,KAAK,EAAE;AAAA,MACxC;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,OAAO,KAAK,OAAO,IAAI,EAAE;AAAA,MACvC;AAGA,SAAG,UAAU,WAAW,SAAS,SAAU,KAAK;AAC9C,YAAI,KAAK,KAAK,OAAO,GAAG;AAGxB,YAAI,GAAG,IAAI,OAAO,EAAG,QAAO,GAAG;AAE/B,YAAI,MAAM,GAAG,IAAI,aAAa,IAAI,GAAG,IAAI,KAAK,GAAG,IAAI,GAAG;AAExD,YAAI,OAAO,IAAI,MAAM,CAAC;AACtB,YAAI,KAAK,IAAI,MAAM,CAAC;AACpB,YAAI,MAAM,IAAI,IAAI,IAAI;AAGtB,YAAI,MAAM,KAAK,OAAO,KAAK,QAAQ,EAAG,QAAO,GAAG;AAGhD,eAAO,GAAG,IAAI,aAAa,IAAI,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,MAAM,CAAC;AAAA,MACjE;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,OAAO,QAAS;AACvB,YAAI,KAAK,KAAK,MAAM;AAEpB,YAAI,MAAM;AACV,iBAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,iBAAO,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,MAAM;AAAA,QAC1C;AAEA,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AACxC,eAAO,OAAO,QAAS;AAEvB,YAAI,QAAQ;AACZ,iBAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,cAAI,KAAK,KAAK,MAAM,CAAC,IAAI,KAAK,QAAQ;AACtC,eAAK,MAAM,CAAC,IAAK,IAAI,MAAO;AAC5B,kBAAQ,IAAI;AAAA,QACd;AAEA,eAAO,KAAK,MAAM;AAAA,MACpB;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,MAAM,EAAE,MAAM,GAAG;AAAA,MAC/B;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,GAAG;AACpC,eAAO,EAAE,aAAa,CAAC;AACvB,eAAO,CAAC,EAAE,OAAO,CAAC;AAElB,YAAI,IAAI;AACR,YAAI,IAAI,EAAE,MAAM;AAEhB,YAAI,EAAE,aAAa,GAAG;AACpB,cAAI,EAAE,KAAK,CAAC;AAAA,QACd,OAAO;AACL,cAAI,EAAE,MAAM;AAAA,QACd;AAGA,YAAI,IAAI,IAAI,GAAG,CAAC;AAChB,YAAI,IAAI,IAAI,GAAG,CAAC;AAGhB,YAAI,IAAI,IAAI,GAAG,CAAC;AAChB,YAAI,IAAI,IAAI,GAAG,CAAC;AAEhB,YAAI,IAAI;AAER,eAAO,EAAE,OAAO,KAAK,EAAE,OAAO,GAAG;AAC/B,YAAE,OAAO,CAAC;AACV,YAAE,OAAO,CAAC;AACV,YAAE;AAAA,QACJ;AAEA,YAAI,KAAK,EAAE,MAAM;AACjB,YAAI,KAAK,EAAE,MAAM;AAEjB,eAAO,CAAC,EAAE,OAAO,GAAG;AAClB,mBAAS,IAAI,GAAG,KAAK,IAAI,EAAE,MAAM,CAAC,IAAI,QAAQ,KAAK,IAAI,IAAI,EAAE,GAAG,OAAO,EAAE;AACzE,cAAI,IAAI,GAAG;AACT,cAAE,OAAO,CAAC;AACV,mBAAO,MAAM,GAAG;AACd,kBAAI,EAAE,MAAM,KAAK,EAAE,MAAM,GAAG;AAC1B,kBAAE,KAAK,EAAE;AACT,kBAAE,KAAK,EAAE;AAAA,cACX;AAEA,gBAAE,OAAO,CAAC;AACV,gBAAE,OAAO,CAAC;AAAA,YACZ;AAAA,UACF;AAEA,mBAAS,IAAI,GAAG,KAAK,IAAI,EAAE,MAAM,CAAC,IAAI,QAAQ,KAAK,IAAI,IAAI,EAAE,GAAG,OAAO,EAAE;AACzE,cAAI,IAAI,GAAG;AACT,cAAE,OAAO,CAAC;AACV,mBAAO,MAAM,GAAG;AACd,kBAAI,EAAE,MAAM,KAAK,EAAE,MAAM,GAAG;AAC1B,kBAAE,KAAK,EAAE;AACT,kBAAE,KAAK,EAAE;AAAA,cACX;AAEA,gBAAE,OAAO,CAAC;AACV,gBAAE,OAAO,CAAC;AAAA,YACZ;AAAA,UACF;AAEA,cAAI,EAAE,IAAI,CAAC,KAAK,GAAG;AACjB,cAAE,KAAK,CAAC;AACR,cAAE,KAAK,CAAC;AACR,cAAE,KAAK,CAAC;AAAA,UACV,OAAO;AACL,cAAE,KAAK,CAAC;AACR,cAAE,KAAK,CAAC;AACR,cAAE,KAAK,CAAC;AAAA,UACV;AAAA,QACF;AAEA,eAAO;AAAA,UACL,GAAG;AAAA,UACH,GAAG;AAAA,UACH,KAAK,EAAE,OAAO,CAAC;AAAA,QACjB;AAAA,MACF;AAKA,SAAG,UAAU,SAAS,SAAS,OAAQ,GAAG;AACxC,eAAO,EAAE,aAAa,CAAC;AACvB,eAAO,CAAC,EAAE,OAAO,CAAC;AAElB,YAAI,IAAI;AACR,YAAI,IAAI,EAAE,MAAM;AAEhB,YAAI,EAAE,aAAa,GAAG;AACpB,cAAI,EAAE,KAAK,CAAC;AAAA,QACd,OAAO;AACL,cAAI,EAAE,MAAM;AAAA,QACd;AAEA,YAAI,KAAK,IAAI,GAAG,CAAC;AACjB,YAAI,KAAK,IAAI,GAAG,CAAC;AAEjB,YAAI,QAAQ,EAAE,MAAM;AAEpB,eAAO,EAAE,KAAK,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC,IAAI,GAAG;AACrC,mBAAS,IAAI,GAAG,KAAK,IAAI,EAAE,MAAM,CAAC,IAAI,QAAQ,KAAK,IAAI,IAAI,EAAE,GAAG,OAAO,EAAE;AACzE,cAAI,IAAI,GAAG;AACT,cAAE,OAAO,CAAC;AACV,mBAAO,MAAM,GAAG;AACd,kBAAI,GAAG,MAAM,GAAG;AACd,mBAAG,KAAK,KAAK;AAAA,cACf;AAEA,iBAAG,OAAO,CAAC;AAAA,YACb;AAAA,UACF;AAEA,mBAAS,IAAI,GAAG,KAAK,IAAI,EAAE,MAAM,CAAC,IAAI,QAAQ,KAAK,IAAI,IAAI,EAAE,GAAG,OAAO,EAAE;AACzE,cAAI,IAAI,GAAG;AACT,cAAE,OAAO,CAAC;AACV,mBAAO,MAAM,GAAG;AACd,kBAAI,GAAG,MAAM,GAAG;AACd,mBAAG,KAAK,KAAK;AAAA,cACf;AAEA,iBAAG,OAAO,CAAC;AAAA,YACb;AAAA,UACF;AAEA,cAAI,EAAE,IAAI,CAAC,KAAK,GAAG;AACjB,cAAE,KAAK,CAAC;AACR,eAAG,KAAK,EAAE;AAAA,UACZ,OAAO;AACL,cAAE,KAAK,CAAC;AACR,eAAG,KAAK,EAAE;AAAA,UACZ;AAAA,QACF;AAEA,YAAI;AACJ,YAAI,EAAE,KAAK,CAAC,MAAM,GAAG;AACnB,gBAAM;AAAA,QACR,OAAO;AACL,gBAAM;AAAA,QACR;AAEA,YAAI,IAAI,KAAK,CAAC,IAAI,GAAG;AACnB,cAAI,KAAK,CAAC;AAAA,QACZ;AAEA,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,YAAI,KAAK,OAAO,EAAG,QAAO,IAAI,IAAI;AAClC,YAAI,IAAI,OAAO,EAAG,QAAO,KAAK,IAAI;AAElC,YAAI,IAAI,KAAK,MAAM;AACnB,YAAI,IAAI,IAAI,MAAM;AAClB,UAAE,WAAW;AACb,UAAE,WAAW;AAGb,iBAAS,QAAQ,GAAG,EAAE,OAAO,KAAK,EAAE,OAAO,GAAG,SAAS;AACrD,YAAE,OAAO,CAAC;AACV,YAAE,OAAO,CAAC;AAAA,QACZ;AAEA,WAAG;AACD,iBAAO,EAAE,OAAO,GAAG;AACjB,cAAE,OAAO,CAAC;AAAA,UACZ;AACA,iBAAO,EAAE,OAAO,GAAG;AACjB,cAAE,OAAO,CAAC;AAAA,UACZ;AAEA,cAAI,IAAI,EAAE,IAAI,CAAC;AACf,cAAI,IAAI,GAAG;AAET,gBAAI,IAAI;AACR,gBAAI;AACJ,gBAAI;AAAA,UACN,WAAW,MAAM,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG;AACrC;AAAA,UACF;AAEA,YAAE,KAAK,CAAC;AAAA,QACV,SAAS;AAET,eAAO,EAAE,OAAO,KAAK;AAAA,MACvB;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,KAAK,GAAG,EAAE,EAAE,KAAK,GAAG;AAAA,MAClC;AAEA,SAAG,UAAU,SAAS,SAAS,SAAU;AACvC,gBAAQ,KAAK,MAAM,CAAC,IAAI,OAAO;AAAA,MACjC;AAEA,SAAG,UAAU,QAAQ,SAAS,QAAS;AACrC,gBAAQ,KAAK,MAAM,CAAC,IAAI,OAAO;AAAA,MACjC;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AACxC,eAAO,KAAK,MAAM,CAAC,IAAI;AAAA,MACzB;AAGA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AACxC,eAAO,OAAO,QAAQ,QAAQ;AAC9B,YAAI,IAAI,MAAM;AACd,YAAI,KAAK,MAAM,KAAK;AACpB,YAAI,IAAI,KAAK;AAGb,YAAI,KAAK,UAAU,GAAG;AACpB,eAAK,QAAQ,IAAI,CAAC;AAClB,eAAK,MAAM,CAAC,KAAK;AACjB,iBAAO;AAAA,QACT;AAGA,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,UAAU,KAAK,IAAI,KAAK,QAAQ,KAAK;AACnD,cAAI,IAAI,KAAK,MAAM,CAAC,IAAI;AACxB,eAAK;AACL,kBAAQ,MAAM;AACd,eAAK;AACL,eAAK,MAAM,CAAC,IAAI;AAAA,QAClB;AACA,YAAI,UAAU,GAAG;AACf,eAAK,MAAM,CAAC,IAAI;AAChB,eAAK;AAAA,QACP;AACA,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,SAAS,SAAS,SAAU;AACvC,eAAO,KAAK,WAAW,KAAK,KAAK,MAAM,CAAC,MAAM;AAAA,MAChD;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,YAAI,WAAW,MAAM;AAErB,YAAI,KAAK,aAAa,KAAK,CAAC,SAAU,QAAO;AAC7C,YAAI,KAAK,aAAa,KAAK,SAAU,QAAO;AAE5C,aAAK,MAAM;AAEX,YAAI;AACJ,YAAI,KAAK,SAAS,GAAG;AACnB,gBAAM;AAAA,QACR,OAAO;AACL,cAAI,UAAU;AACZ,kBAAM,CAAC;AAAA,UACT;AAEA,iBAAO,OAAO,UAAW,mBAAmB;AAE5C,cAAI,IAAI,KAAK,MAAM,CAAC,IAAI;AACxB,gBAAM,MAAM,MAAM,IAAI,IAAI,MAAM,KAAK;AAAA,QACvC;AACA,YAAI,KAAK,aAAa,EAAG,QAAO,CAAC,MAAM;AACvC,eAAO;AAAA,MACT;AAMA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,YAAI,KAAK,aAAa,KAAK,IAAI,aAAa,EAAG,QAAO;AACtD,YAAI,KAAK,aAAa,KAAK,IAAI,aAAa,EAAG,QAAO;AAEtD,YAAI,MAAM,KAAK,KAAK,GAAG;AACvB,YAAI,KAAK,aAAa,EAAG,QAAO,CAAC,MAAM;AACvC,eAAO;AAAA,MACT;AAGA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AAEtC,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO;AACrC,YAAI,KAAK,SAAS,IAAI,OAAQ,QAAO;AAErC,YAAI,MAAM;AACV,iBAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG,KAAK;AACzC,cAAI,IAAI,KAAK,MAAM,CAAC,IAAI;AACxB,cAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAEvB,cAAI,MAAM,EAAG;AACb,cAAI,IAAI,GAAG;AACT,kBAAM;AAAA,UACR,WAAW,IAAI,GAAG;AAChB,kBAAM;AAAA,UACR;AACA;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,eAAO,KAAK,KAAK,GAAG,MAAM;AAAA,MAC5B;AAEA,SAAG,UAAU,KAAK,SAAS,GAAI,KAAK;AAClC,eAAO,KAAK,IAAI,GAAG,MAAM;AAAA,MAC3B;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,KAAK,GAAG,KAAK;AAAA,MAC3B;AAEA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,eAAO,KAAK,IAAI,GAAG,KAAK;AAAA,MAC1B;AAEA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,eAAO,KAAK,KAAK,GAAG,MAAM;AAAA,MAC5B;AAEA,SAAG,UAAU,KAAK,SAAS,GAAI,KAAK;AAClC,eAAO,KAAK,IAAI,GAAG,MAAM;AAAA,MAC3B;AAEA,SAAG,UAAU,OAAO,SAAS,KAAM,KAAK;AACtC,eAAO,KAAK,KAAK,GAAG,KAAK;AAAA,MAC3B;AAEA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,eAAO,KAAK,IAAI,GAAG,KAAK;AAAA,MAC1B;AAEA,SAAG,UAAU,MAAM,SAAS,IAAK,KAAK;AACpC,eAAO,KAAK,KAAK,GAAG,MAAM;AAAA,MAC5B;AAEA,SAAG,UAAU,KAAK,SAAS,GAAI,KAAK;AAClC,eAAO,KAAK,IAAI,GAAG,MAAM;AAAA,MAC3B;AAMA,SAAG,MAAM,SAAS,IAAK,KAAK;AAC1B,eAAO,IAAI,IAAI,GAAG;AAAA,MACpB;AAEA,SAAG,UAAU,QAAQ,SAAS,MAAO,KAAK;AACxC,eAAO,CAAC,KAAK,KAAK,uCAAuC;AACzD,eAAO,KAAK,aAAa,GAAG,+BAA+B;AAC3D,eAAO,IAAI,UAAU,IAAI,EAAE,UAAU,GAAG;AAAA,MAC1C;AAEA,SAAG,UAAU,UAAU,SAAS,UAAW;AACzC,eAAO,KAAK,KAAK,sDAAsD;AACvE,eAAO,KAAK,IAAI,YAAY,IAAI;AAAA,MAClC;AAEA,SAAG,UAAU,YAAY,SAAS,UAAW,KAAK;AAChD,aAAK,MAAM;AACX,eAAO;AAAA,MACT;AAEA,SAAG,UAAU,WAAW,SAAS,SAAU,KAAK;AAC9C,eAAO,CAAC,KAAK,KAAK,uCAAuC;AACzD,eAAO,KAAK,UAAU,GAAG;AAAA,MAC3B;AAEA,SAAG,UAAU,SAAS,SAAS,OAAQ,KAAK;AAC1C,eAAO,KAAK,KAAK,oCAAoC;AACrD,eAAO,KAAK,IAAI,IAAI,MAAM,GAAG;AAAA,MAC/B;AAEA,SAAG,UAAU,UAAU,SAAS,QAAS,KAAK;AAC5C,eAAO,KAAK,KAAK,qCAAqC;AACtD,eAAO,KAAK,IAAI,KAAK,MAAM,GAAG;AAAA,MAChC;AAEA,SAAG,UAAU,SAAS,SAAS,OAAQ,KAAK;AAC1C,eAAO,KAAK,KAAK,oCAAoC;AACrD,eAAO,KAAK,IAAI,IAAI,MAAM,GAAG;AAAA,MAC/B;AAEA,SAAG,UAAU,UAAU,SAAS,QAAS,KAAK;AAC5C,eAAO,KAAK,KAAK,qCAAqC;AACtD,eAAO,KAAK,IAAI,KAAK,MAAM,GAAG;AAAA,MAChC;AAEA,SAAG,UAAU,SAAS,SAAS,OAAQ,KAAK;AAC1C,eAAO,KAAK,KAAK,oCAAoC;AACrD,eAAO,KAAK,IAAI,IAAI,MAAM,GAAG;AAAA,MAC/B;AAEA,SAAG,UAAU,SAAS,SAAS,OAAQ,KAAK;AAC1C,eAAO,KAAK,KAAK,oCAAoC;AACrD,aAAK,IAAI,SAAS,MAAM,GAAG;AAC3B,eAAO,KAAK,IAAI,IAAI,MAAM,GAAG;AAAA,MAC/B;AAEA,SAAG,UAAU,UAAU,SAAS,QAAS,KAAK;AAC5C,eAAO,KAAK,KAAK,oCAAoC;AACrD,aAAK,IAAI,SAAS,MAAM,GAAG;AAC3B,eAAO,KAAK,IAAI,KAAK,MAAM,GAAG;AAAA,MAChC;AAEA,SAAG,UAAU,SAAS,SAAS,SAAU;AACvC,eAAO,KAAK,KAAK,oCAAoC;AACrD,aAAK,IAAI,SAAS,IAAI;AACtB,eAAO,KAAK,IAAI,IAAI,IAAI;AAAA,MAC1B;AAEA,SAAG,UAAU,UAAU,SAAS,UAAW;AACzC,eAAO,KAAK,KAAK,qCAAqC;AACtD,aAAK,IAAI,SAAS,IAAI;AACtB,eAAO,KAAK,IAAI,KAAK,IAAI;AAAA,MAC3B;AAGA,SAAG,UAAU,UAAU,SAAS,UAAW;AACzC,eAAO,KAAK,KAAK,qCAAqC;AACtD,aAAK,IAAI,SAAS,IAAI;AACtB,eAAO,KAAK,IAAI,KAAK,IAAI;AAAA,MAC3B;AAEA,SAAG,UAAU,UAAU,SAAS,UAAW;AACzC,eAAO,KAAK,KAAK,qCAAqC;AACtD,aAAK,IAAI,SAAS,IAAI;AACtB,eAAO,KAAK,IAAI,KAAK,IAAI;AAAA,MAC3B;AAGA,SAAG,UAAU,SAAS,SAAS,SAAU;AACvC,eAAO,KAAK,KAAK,oCAAoC;AACrD,aAAK,IAAI,SAAS,IAAI;AACtB,eAAO,KAAK,IAAI,IAAI,IAAI;AAAA,MAC1B;AAEA,SAAG,UAAU,SAAS,SAAS,OAAQ,KAAK;AAC1C,eAAO,KAAK,OAAO,CAAC,IAAI,KAAK,mBAAmB;AAChD,aAAK,IAAI,SAAS,IAAI;AACtB,eAAO,KAAK,IAAI,IAAI,MAAM,GAAG;AAAA,MAC/B;AAGA,UAAI,SAAS;AAAA,QACX,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,MACV;AAGA,eAAS,OAAQ,MAAM,GAAG;AAExB,aAAK,OAAO;AACZ,aAAK,IAAI,IAAI,GAAG,GAAG,EAAE;AACrB,aAAK,IAAI,KAAK,EAAE,UAAU;AAC1B,aAAK,IAAI,IAAI,GAAG,CAAC,EAAE,OAAO,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC;AAE7C,aAAK,MAAM,KAAK,KAAK;AAAA,MACvB;AAEA,aAAO,UAAU,OAAO,SAAS,OAAQ;AACvC,YAAI,MAAM,IAAI,GAAG,IAAI;AACrB,YAAI,QAAQ,IAAI,MAAM,KAAK,KAAK,KAAK,IAAI,EAAE,CAAC;AAC5C,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,UAAU,SAAS,QAAS,KAAK;AAGhD,YAAI,IAAI;AACR,YAAI;AAEJ,WAAG;AACD,eAAK,MAAM,GAAG,KAAK,GAAG;AACtB,cAAI,KAAK,MAAM,CAAC;AAChB,cAAI,EAAE,KAAK,KAAK,GAAG;AACnB,iBAAO,EAAE,UAAU;AAAA,QACrB,SAAS,OAAO,KAAK;AAErB,YAAI,MAAM,OAAO,KAAK,IAAI,KAAK,EAAE,KAAK,KAAK,CAAC;AAC5C,YAAI,QAAQ,GAAG;AACb,YAAE,MAAM,CAAC,IAAI;AACb,YAAE,SAAS;AAAA,QACb,WAAW,MAAM,GAAG;AAClB,YAAE,KAAK,KAAK,CAAC;AAAA,QACf,OAAO;AACL,cAAI,EAAE,UAAU,QAAW;AAEzB,cAAE,MAAM;AAAA,UACV,OAAO;AAEL,cAAE,OAAO;AAAA,UACX;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,aAAO,UAAU,QAAQ,SAAS,MAAO,OAAO,KAAK;AACnD,cAAM,OAAO,KAAK,GAAG,GAAG,GAAG;AAAA,MAC7B;AAEA,aAAO,UAAU,QAAQ,SAAS,MAAO,KAAK;AAC5C,eAAO,IAAI,KAAK,KAAK,CAAC;AAAA,MACxB;AAEA,eAAS,OAAQ;AACf,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QAAyE;AAAA,MAC7E;AACA,eAAS,MAAM,MAAM;AAErB,WAAK,UAAU,QAAQ,SAAS,MAAO,OAAO,QAAQ;AAEpD,YAAI,OAAO;AAEX,YAAI,SAAS,KAAK,IAAI,MAAM,QAAQ,CAAC;AACrC,iBAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,iBAAO,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC;AAAA,QACjC;AACA,eAAO,SAAS;AAEhB,YAAI,MAAM,UAAU,GAAG;AACrB,gBAAM,MAAM,CAAC,IAAI;AACjB,gBAAM,SAAS;AACf;AAAA,QACF;AAGA,YAAI,OAAO,MAAM,MAAM,CAAC;AACxB,eAAO,MAAM,OAAO,QAAQ,IAAI,OAAO;AAEvC,aAAK,IAAI,IAAI,IAAI,MAAM,QAAQ,KAAK;AAClC,cAAI,OAAO,MAAM,MAAM,CAAC,IAAI;AAC5B,gBAAM,MAAM,IAAI,EAAE,KAAM,OAAO,SAAS,IAAM,SAAS;AACvD,iBAAO;AAAA,QACT;AACA,kBAAU;AACV,cAAM,MAAM,IAAI,EAAE,IAAI;AACtB,YAAI,SAAS,KAAK,MAAM,SAAS,IAAI;AACnC,gBAAM,UAAU;AAAA,QAClB,OAAO;AACL,gBAAM,UAAU;AAAA,QAClB;AAAA,MACF;AAEA,WAAK,UAAU,QAAQ,SAAS,MAAO,KAAK;AAE1C,YAAI,MAAM,IAAI,MAAM,IAAI;AACxB,YAAI,MAAM,IAAI,SAAS,CAAC,IAAI;AAC5B,YAAI,UAAU;AAGd,YAAI,KAAK;AACT,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AACvB,gBAAM,IAAI;AACV,cAAI,MAAM,CAAC,IAAI,KAAK;AACpB,eAAK,IAAI,MAAS,KAAK,WAAa;AAAA,QACtC;AAGA,YAAI,IAAI,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG;AACnC,cAAI;AACJ,cAAI,IAAI,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG;AACnC,gBAAI;AAAA,UACN;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAEA,eAAS,OAAQ;AACf,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QAAgE;AAAA,MACpE;AACA,eAAS,MAAM,MAAM;AAErB,eAAS,OAAQ;AACf,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QAAuD;AAAA,MAC3D;AACA,eAAS,MAAM,MAAM;AAErB,eAAS,SAAU;AAEjB,eAAO;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,QAAqE;AAAA,MACzE;AACA,eAAS,QAAQ,MAAM;AAEvB,aAAO,UAAU,QAAQ,SAAS,MAAO,KAAK;AAE5C,YAAI,QAAQ;AACZ,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,KAAO;AACrC,cAAI,KAAK,KAAK;AACd,kBAAQ;AAER,cAAI,MAAM,CAAC,IAAI;AACf,kBAAQ;AAAA,QACV;AACA,YAAI,UAAU,GAAG;AACf,cAAI,MAAM,IAAI,QAAQ,IAAI;AAAA,QAC5B;AACA,eAAO;AAAA,MACT;AAGA,SAAG,SAAS,SAAS,MAAO,MAAM;AAEhC,YAAI,OAAO,IAAI,EAAG,QAAO,OAAO,IAAI;AAEpC,YAAIC;AACJ,YAAI,SAAS,QAAQ;AACnB,UAAAA,SAAQ,IAAI,KAAK;AAAA,QACnB,WAAW,SAAS,QAAQ;AAC1B,UAAAA,SAAQ,IAAI,KAAK;AAAA,QACnB,WAAW,SAAS,QAAQ;AAC1B,UAAAA,SAAQ,IAAI,KAAK;AAAA,QACnB,WAAW,SAAS,UAAU;AAC5B,UAAAA,SAAQ,IAAI,OAAO;AAAA,QACrB,OAAO;AACL,gBAAM,IAAI,MAAM,mBAAmB,IAAI;AAAA,QACzC;AACA,eAAO,IAAI,IAAIA;AAEf,eAAOA;AAAA,MACT;AAKA,eAAS,IAAK,GAAG;AACf,YAAI,OAAO,MAAM,UAAU;AACzB,cAAI,QAAQ,GAAG,OAAO,CAAC;AACvB,eAAK,IAAI,MAAM;AACf,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,iBAAO,EAAE,IAAI,CAAC,GAAG,gCAAgC;AACjD,eAAK,IAAI;AACT,eAAK,QAAQ;AAAA,QACf;AAAA,MACF;AAEA,UAAI,UAAU,WAAW,SAAS,SAAU,GAAG;AAC7C,eAAO,EAAE,aAAa,GAAG,+BAA+B;AACxD,eAAO,EAAE,KAAK,iCAAiC;AAAA,MACjD;AAEA,UAAI,UAAU,WAAW,SAAS,SAAU,GAAG,GAAG;AAChD,gBAAQ,EAAE,WAAW,EAAE,cAAc,GAAG,+BAA+B;AACvE;AAAA,UAAO,EAAE,OAAO,EAAE,QAAQ,EAAE;AAAA,UAC1B;AAAA,QAAiC;AAAA,MACrC;AAEA,UAAI,UAAU,OAAO,SAAS,KAAM,GAAG;AACrC,YAAI,KAAK,MAAO,QAAO,KAAK,MAAM,QAAQ,CAAC,EAAE,UAAU,IAAI;AAC3D,eAAO,EAAE,KAAK,KAAK,CAAC,EAAE,UAAU,IAAI;AAAA,MACtC;AAEA,UAAI,UAAU,MAAM,SAAS,IAAK,GAAG;AACnC,YAAI,EAAE,OAAO,GAAG;AACd,iBAAO,EAAE,MAAM;AAAA,QACjB;AAEA,eAAO,KAAK,EAAE,IAAI,CAAC,EAAE,UAAU,IAAI;AAAA,MACrC;AAEA,UAAI,UAAU,MAAM,SAAS,IAAK,GAAG,GAAG;AACtC,aAAK,SAAS,GAAG,CAAC;AAElB,YAAI,MAAM,EAAE,IAAI,CAAC;AACjB,YAAI,IAAI,IAAI,KAAK,CAAC,KAAK,GAAG;AACxB,cAAI,KAAK,KAAK,CAAC;AAAA,QACjB;AACA,eAAO,IAAI,UAAU,IAAI;AAAA,MAC3B;AAEA,UAAI,UAAU,OAAO,SAAS,KAAM,GAAG,GAAG;AACxC,aAAK,SAAS,GAAG,CAAC;AAElB,YAAI,MAAM,EAAE,KAAK,CAAC;AAClB,YAAI,IAAI,IAAI,KAAK,CAAC,KAAK,GAAG;AACxB,cAAI,KAAK,KAAK,CAAC;AAAA,QACjB;AACA,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,MAAM,SAAS,IAAK,GAAG,GAAG;AACtC,aAAK,SAAS,GAAG,CAAC;AAElB,YAAI,MAAM,EAAE,IAAI,CAAC;AACjB,YAAI,IAAI,KAAK,CAAC,IAAI,GAAG;AACnB,cAAI,KAAK,KAAK,CAAC;AAAA,QACjB;AACA,eAAO,IAAI,UAAU,IAAI;AAAA,MAC3B;AAEA,UAAI,UAAU,OAAO,SAAS,KAAM,GAAG,GAAG;AACxC,aAAK,SAAS,GAAG,CAAC;AAElB,YAAI,MAAM,EAAE,KAAK,CAAC;AAClB,YAAI,IAAI,KAAK,CAAC,IAAI,GAAG;AACnB,cAAI,KAAK,KAAK,CAAC;AAAA,QACjB;AACA,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,MAAM,SAAS,IAAK,GAAG,KAAK;AACxC,aAAK,SAAS,CAAC;AACf,eAAO,KAAK,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,MAC/B;AAEA,UAAI,UAAU,OAAO,SAAS,KAAM,GAAG,GAAG;AACxC,aAAK,SAAS,GAAG,CAAC;AAClB,eAAO,KAAK,KAAK,EAAE,KAAK,CAAC,CAAC;AAAA,MAC5B;AAEA,UAAI,UAAU,MAAM,SAAS,IAAK,GAAG,GAAG;AACtC,aAAK,SAAS,GAAG,CAAC;AAClB,eAAO,KAAK,KAAK,EAAE,IAAI,CAAC,CAAC;AAAA,MAC3B;AAEA,UAAI,UAAU,OAAO,SAAS,KAAM,GAAG;AACrC,eAAO,KAAK,KAAK,GAAG,EAAE,MAAM,CAAC;AAAA,MAC/B;AAEA,UAAI,UAAU,MAAM,SAAS,IAAK,GAAG;AACnC,eAAO,KAAK,IAAI,GAAG,CAAC;AAAA,MACtB;AAEA,UAAI,UAAU,OAAO,SAAS,KAAM,GAAG;AACrC,YAAI,EAAE,OAAO,EAAG,QAAO,EAAE,MAAM;AAE/B,YAAI,OAAO,KAAK,EAAE,MAAM,CAAC;AACzB,eAAO,OAAO,MAAM,CAAC;AAGrB,YAAI,SAAS,GAAG;AACd,cAAI,MAAM,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC;AACxC,iBAAO,KAAK,IAAI,GAAG,GAAG;AAAA,QACxB;AAKA,YAAI,IAAI,KAAK,EAAE,KAAK,CAAC;AACrB,YAAI,IAAI;AACR,eAAO,CAAC,EAAE,OAAO,KAAK,EAAE,MAAM,CAAC,MAAM,GAAG;AACtC;AACA,YAAE,OAAO,CAAC;AAAA,QACZ;AACA,eAAO,CAAC,EAAE,OAAO,CAAC;AAElB,YAAI,MAAM,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI;AAC9B,YAAI,OAAO,IAAI,OAAO;AAItB,YAAI,OAAO,KAAK,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC;AAClC,YAAI,IAAI,KAAK,EAAE,UAAU;AACzB,YAAI,IAAI,GAAG,IAAI,IAAI,CAAC,EAAE,MAAM,IAAI;AAEhC,eAAO,KAAK,IAAI,GAAG,IAAI,EAAE,IAAI,IAAI,MAAM,GAAG;AACxC,YAAE,QAAQ,IAAI;AAAA,QAChB;AAEA,YAAI,IAAI,KAAK,IAAI,GAAG,CAAC;AACrB,YAAI,IAAI,KAAK,IAAI,GAAG,EAAE,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;AACvC,YAAI,IAAI,KAAK,IAAI,GAAG,CAAC;AACrB,YAAI,IAAI;AACR,eAAO,EAAE,IAAI,GAAG,MAAM,GAAG;AACvB,cAAI,MAAM;AACV,mBAAS,IAAI,GAAG,IAAI,IAAI,GAAG,MAAM,GAAG,KAAK;AACvC,kBAAM,IAAI,OAAO;AAAA,UACnB;AACA,iBAAO,IAAI,CAAC;AACZ,cAAI,IAAI,KAAK,IAAI,GAAG,IAAI,GAAG,CAAC,EAAE,OAAO,IAAI,IAAI,CAAC,CAAC;AAE/C,cAAI,EAAE,OAAO,CAAC;AACd,cAAI,EAAE,OAAO;AACb,cAAI,EAAE,OAAO,CAAC;AACd,cAAI;AAAA,QACN;AAEA,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,OAAO,SAAS,KAAM,GAAG;AACrC,YAAI,MAAM,EAAE,OAAO,KAAK,CAAC;AACzB,YAAI,IAAI,aAAa,GAAG;AACtB,cAAI,WAAW;AACf,iBAAO,KAAK,KAAK,GAAG,EAAE,OAAO;AAAA,QAC/B,OAAO;AACL,iBAAO,KAAK,KAAK,GAAG;AAAA,QACtB;AAAA,MACF;AAEA,UAAI,UAAU,MAAM,SAAS,IAAK,GAAG,KAAK;AACxC,YAAI,IAAI,OAAO,EAAG,QAAO,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI;AAC7C,YAAI,IAAI,KAAK,CAAC,MAAM,EAAG,QAAO,EAAE,MAAM;AAEtC,YAAI,aAAa;AACjB,YAAI,MAAM,IAAI,MAAM,KAAK,UAAU;AACnC,YAAI,CAAC,IAAI,IAAI,GAAG,CAAC,EAAE,MAAM,IAAI;AAC7B,YAAI,CAAC,IAAI;AACT,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,CAAC,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC;AAAA,QACjC;AAEA,YAAI,MAAM,IAAI,CAAC;AACf,YAAI,UAAU;AACd,YAAI,aAAa;AACjB,YAAI,QAAQ,IAAI,UAAU,IAAI;AAC9B,YAAI,UAAU,GAAG;AACf,kBAAQ;AAAA,QACV;AAEA,aAAK,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACpC,cAAI,OAAO,IAAI,MAAM,CAAC;AACtB,mBAAS,IAAI,QAAQ,GAAG,KAAK,GAAG,KAAK;AACnC,gBAAI,MAAO,QAAQ,IAAK;AACxB,gBAAI,QAAQ,IAAI,CAAC,GAAG;AAClB,oBAAM,KAAK,IAAI,GAAG;AAAA,YACpB;AAEA,gBAAI,QAAQ,KAAK,YAAY,GAAG;AAC9B,2BAAa;AACb;AAAA,YACF;AAEA,wBAAY;AACZ,uBAAW;AACX;AACA,gBAAI,eAAe,eAAe,MAAM,KAAK,MAAM,GAAI;AAEvD,kBAAM,KAAK,IAAI,KAAK,IAAI,OAAO,CAAC;AAChC,yBAAa;AACb,sBAAU;AAAA,UACZ;AACA,kBAAQ;AAAA,QACV;AAEA,eAAO;AAAA,MACT;AAEA,UAAI,UAAU,YAAY,SAAS,UAAW,KAAK;AACjD,YAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AAEvB,eAAO,MAAM,MAAM,EAAE,MAAM,IAAI;AAAA,MACjC;AAEA,UAAI,UAAU,cAAc,SAAS,YAAa,KAAK;AACrD,YAAI,MAAM,IAAI,MAAM;AACpB,YAAI,MAAM;AACV,eAAO;AAAA,MACT;AAMA,SAAG,OAAO,SAAS,KAAM,KAAK;AAC5B,eAAO,IAAI,KAAK,GAAG;AAAA,MACrB;AAEA,eAAS,KAAM,GAAG;AAChB,YAAI,KAAK,MAAM,CAAC;AAEhB,aAAK,QAAQ,KAAK,EAAE,UAAU;AAC9B,YAAI,KAAK,QAAQ,OAAO,GAAG;AACzB,eAAK,SAAS,KAAM,KAAK,QAAQ;AAAA,QACnC;AAEA,aAAK,IAAI,IAAI,GAAG,CAAC,EAAE,OAAO,KAAK,KAAK;AACpC,aAAK,KAAK,KAAK,KAAK,KAAK,EAAE,IAAI,CAAC;AAChC,aAAK,OAAO,KAAK,EAAE,OAAO,KAAK,CAAC;AAEhC,aAAK,OAAO,KAAK,KAAK,IAAI,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,KAAK,CAAC;AACrD,aAAK,OAAO,KAAK,KAAK,KAAK,KAAK,CAAC;AACjC,aAAK,OAAO,KAAK,EAAE,IAAI,KAAK,IAAI;AAAA,MAClC;AACA,eAAS,MAAM,GAAG;AAElB,WAAK,UAAU,YAAY,SAAS,UAAW,KAAK;AAClD,eAAO,KAAK,KAAK,IAAI,MAAM,KAAK,KAAK,CAAC;AAAA,MACxC;AAEA,WAAK,UAAU,cAAc,SAAS,YAAa,KAAK;AACtD,YAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC;AACpC,UAAE,MAAM;AACR,eAAO;AAAA,MACT;AAEA,WAAK,UAAU,OAAO,SAAS,KAAM,GAAG,GAAG;AACzC,YAAI,EAAE,OAAO,KAAK,EAAE,OAAO,GAAG;AAC5B,YAAE,MAAM,CAAC,IAAI;AACb,YAAE,SAAS;AACX,iBAAO;AAAA,QACT;AAEA,YAAI,IAAI,EAAE,KAAK,CAAC;AAChB,YAAI,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,IAAI,KAAK,IAAI,EAAE,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,CAAC;AACxE,YAAI,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,KAAK,KAAK;AACnC,YAAI,MAAM;AAEV,YAAI,EAAE,IAAI,KAAK,CAAC,KAAK,GAAG;AACtB,gBAAM,EAAE,KAAK,KAAK,CAAC;AAAA,QACrB,WAAW,EAAE,KAAK,CAAC,IAAI,GAAG;AACxB,gBAAM,EAAE,KAAK,KAAK,CAAC;AAAA,QACrB;AAEA,eAAO,IAAI,UAAU,IAAI;AAAA,MAC3B;AAEA,WAAK,UAAU,MAAM,SAAS,IAAK,GAAG,GAAG;AACvC,YAAI,EAAE,OAAO,KAAK,EAAE,OAAO,EAAG,QAAO,IAAI,GAAG,CAAC,EAAE,UAAU,IAAI;AAE7D,YAAI,IAAI,EAAE,IAAI,CAAC;AACf,YAAI,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,IAAI,KAAK,IAAI,EAAE,OAAO,KAAK,KAAK,EAAE,IAAI,KAAK,CAAC;AACxE,YAAI,IAAI,EAAE,KAAK,CAAC,EAAE,OAAO,KAAK,KAAK;AACnC,YAAI,MAAM;AACV,YAAI,EAAE,IAAI,KAAK,CAAC,KAAK,GAAG;AACtB,gBAAM,EAAE,KAAK,KAAK,CAAC;AAAA,QACrB,WAAW,EAAE,KAAK,CAAC,IAAI,GAAG;AACxB,gBAAM,EAAE,KAAK,KAAK,CAAC;AAAA,QACrB;AAEA,eAAO,IAAI,UAAU,IAAI;AAAA,MAC3B;AAEA,WAAK,UAAU,OAAO,SAAS,KAAM,GAAG;AAEtC,YAAI,MAAM,KAAK,KAAK,EAAE,OAAO,KAAK,CAAC,EAAE,IAAI,KAAK,EAAE,CAAC;AACjD,eAAO,IAAI,UAAU,IAAI;AAAA,MAC3B;AAAA,IACF,GAAG,OAAO,WAAW,eAAe,QAAQ,OAAI;AAAA;AAAA;;;ACt3GhD;AAAA;AAAA,QAAI,OAAO,OAAO,WAAW,YAAY;AAEvC,aAAO,UAAU,SAAS,SAAS,MAAM,WAAW;AAClD,YAAI,WAAW;AACb,eAAK,SAAS;AACd,eAAK,YAAY,OAAO,OAAO,UAAU,WAAW;AAAA,YAClD,aAAa;AAAA,cACX,OAAO;AAAA,cACP,YAAY;AAAA,cACZ,UAAU;AAAA,cACV,cAAc;AAAA,YAChB;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,OAAO;AAEL,aAAO,UAAU,SAAS,SAAS,MAAM,WAAW;AAClD,YAAI,WAAW;AACb,eAAK,SAAS;AACd,cAAI,WAAW,WAAY;AAAA,UAAC;AAC5B,mBAAS,YAAY,UAAU;AAC/B,eAAK,YAAY,IAAI,SAAS;AAC9B,eAAK,UAAU,cAAc;AAAA,QAC/B;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;AC1BA;AAAA;AAAA,QAAI;AACE,aAAO;AAEX,UAAI,OAAO,KAAK,aAAa,WAAY,OAAM;AAC/C,aAAO,UAAU,KAAK;AAAA,IACxB,SAAS,GAAG;AAEV,aAAO,UAAU;AAAA,IACnB;AAPM;AAAA;AAAA;;;ACDN;AAAA;AAAA;AAEA,QAAM,WAAW;AAEjB,aAAS,SAAS,SAAS;AACzB,WAAK,iBAAiB;AAAA,QACpB,KAAK;AAAA,QACL,MAAM,CAAC;AAAA,QACP,SAAS,WAAW,CAAC;AAAA,QACrB,QAAQ,CAAC;AAAA,MACX;AAAA,IACF;AACA,YAAQ,WAAW;AAEnB,aAAS,UAAU,UAAU,SAAS,QAAQ,KAAK;AACjD,aAAO,eAAe;AAAA,IACxB;AAEA,aAAS,UAAU,OAAO,SAAS,OAAO;AACxC,YAAM,QAAQ,KAAK;AAEnB,aAAO,EAAE,KAAK,MAAM,KAAK,SAAS,MAAM,KAAK,OAAO;AAAA,IACtD;AAEA,aAAS,UAAU,UAAU,SAAS,QAAQ,MAAM;AAClD,YAAM,QAAQ,KAAK;AAEnB,YAAM,MAAM,KAAK;AACjB,YAAM,OAAO,MAAM,KAAK,MAAM,GAAG,KAAK,OAAO;AAAA,IAC/C;AAEA,aAAS,UAAU,WAAW,SAAS,SAAS,KAAK;AACnD,aAAO,KAAK,eAAe,KAAK,KAAK,GAAG;AAAA,IAC1C;AAEA,aAAS,UAAU,UAAU,SAAS,QAAQ,OAAO;AACnD,YAAM,QAAQ,KAAK;AAEnB,YAAM,OAAO,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC;AAAA,IAC5C;AAEA,aAAS,UAAU,WAAW,SAAS,SAAS,OAAO,KAAK,OAAO;AACjE,YAAM,QAAQ,KAAK;AAEnB,WAAK,QAAQ,KAAK;AAClB,UAAI,MAAM,QAAQ;AAChB,cAAM,IAAI,GAAG,IAAI;AAAA,IACrB;AAEA,aAAS,UAAU,OAAO,SAAS,OAAO;AACxC,aAAO,KAAK,eAAe,KAAK,KAAK,GAAG;AAAA,IAC1C;AAEA,aAAS,UAAU,cAAc,SAAS,cAAc;AACtD,YAAM,QAAQ,KAAK;AAEnB,YAAM,OAAO,MAAM;AACnB,YAAM,MAAM,CAAC;AACb,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,cAAc,SAAS,YAAY,MAAM;AAC1D,YAAM,QAAQ,KAAK;AAEnB,YAAM,MAAM,MAAM;AAClB,YAAM,MAAM;AACZ,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,QAAQ,SAAS,MAAM,KAAK;AAC7C,UAAI;AACJ,YAAM,QAAQ,KAAK;AAEnB,YAAM,YAAY,eAAe;AACjC,UAAI,WAAW;AACb,cAAM;AAAA,MACR,OAAO;AACL,cAAM,IAAI,cAAc,MAAM,KAAK,IAAI,SAAS,MAAM;AACpD,iBAAO,MAAM,KAAK,UAAU,IAAI,IAAI;AAAA,QACtC,CAAC,EAAE,KAAK,EAAE,GAAG,IAAI,WAAW,KAAK,IAAI,KAAK;AAAA,MAC5C;AAEA,UAAI,CAAC,MAAM,QAAQ;AACjB,cAAM;AAER,UAAI,CAAC;AACH,cAAM,OAAO,KAAK,GAAG;AAEvB,aAAO;AAAA,IACT;AAEA,aAAS,UAAU,aAAa,SAAS,WAAW,QAAQ;AAC1D,YAAM,QAAQ,KAAK;AACnB,UAAI,CAAC,MAAM,QAAQ;AACjB,eAAO;AAET,aAAO;AAAA,QACL,QAAQ,KAAK,QAAQ,MAAM,IAAI,OAAO;AAAA,QACtC,QAAQ,MAAM;AAAA,MAChB;AAAA,IACF;AAEA,aAAS,cAAc,MAAM,KAAK;AAChC,WAAK,OAAO;AACZ,WAAK,QAAQ,GAAG;AAAA,IAClB;AACA,aAAS,eAAe,KAAK;AAE7B,kBAAc,UAAU,UAAU,SAAS,QAAQ,KAAK;AACtD,WAAK,UAAU,MAAM,WAAW,KAAK,QAAQ;AAC7C,UAAI,MAAM;AACR,cAAM,kBAAkB,MAAM,aAAa;AAE7C,UAAI,CAAC,KAAK,OAAO;AACf,YAAI;AAEF,gBAAM,IAAI,MAAM,KAAK,OAAO;AAAA,QAC9B,SAAS,GAAG;AACV,eAAK,QAAQ,EAAE;AAAA,QACjB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC1HA,IAAAC,kBAAA;AAAA;AAAA;AAEA,QAAM,WAAW;AACjB,QAAM,WAAW,mBAA4B;AAC7C,QAAM,SAAS,gBAAwB;AAEvC,aAAS,cAAc,MAAM,SAAS;AACpC,eAAS,KAAK,MAAM,OAAO;AAC3B,UAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AAC1B,aAAK,MAAM,kBAAkB;AAC7B;AAAA,MACF;AAEA,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,SAAS,KAAK;AAAA,IACrB;AACA,aAAS,eAAe,QAAQ;AAChC,YAAQ,gBAAgB;AAExB,kBAAc,kBAAkB,SAAS,gBAAgB,MAAM;AAC7D,UAAI,gBAAgB,eAAe;AACjC,eAAO;AAAA,MACT;AAGA,YAAM,eAAe,OAAO,SAAS,YACnC,OAAO,SAAS,KAAK,IAAI,KACzB,KAAK,YAAY,SAAS,mBAC1B,OAAO,KAAK,WAAW,YACvB,OAAO,KAAK,WAAW,YACvB,OAAO,KAAK,SAAS,cACrB,OAAO,KAAK,YAAY,cACxB,OAAO,KAAK,YAAY,cACxB,OAAO,KAAK,cAAc,cAC1B,OAAO,KAAK,SAAS,cACrB,OAAO,KAAK,QAAQ;AAEtB,aAAO;AAAA,IACT;AAEA,kBAAc,UAAU,OAAO,SAAS,OAAO;AAC7C,aAAO,EAAE,QAAQ,KAAK,QAAQ,UAAU,SAAS,UAAU,KAAK,KAAK,IAAI,EAAE;AAAA,IAC7E;AAEA,kBAAc,UAAU,UAAU,SAAS,QAAQ,MAAM;AAEvD,YAAM,MAAM,IAAI,cAAc,KAAK,IAAI;AACvC,UAAI,SAAS,KAAK;AAClB,UAAI,SAAS,KAAK;AAElB,WAAK,SAAS,KAAK;AACnB,eAAS,UAAU,QAAQ,KAAK,MAAM,KAAK,QAAQ;AAEnD,aAAO;AAAA,IACT;AAEA,kBAAc,UAAU,UAAU,SAAS,UAAU;AACnD,aAAO,KAAK,WAAW,KAAK;AAAA,IAC9B;AAEA,kBAAc,UAAU,YAAY,SAAS,UAAU,MAAM;AAC3D,UAAI,KAAK,SAAS,KAAK,KAAK;AAC1B,eAAO,KAAK,KAAK,UAAU,KAAK,UAAU,IAAI;AAAA;AAE9C,eAAO,KAAK,MAAM,QAAQ,uBAAuB;AAAA,IACrD;AAEA,kBAAc,UAAU,OAAO,SAAS,KAAK,OAAO,MAAM;AACxD,UAAI,EAAE,KAAK,SAAS,SAAS,KAAK;AAChC,eAAO,KAAK,MAAM,QAAQ,uBAAuB;AAEnD,YAAM,MAAM,IAAI,cAAc,KAAK,IAAI;AAGvC,UAAI,iBAAiB,KAAK;AAE1B,UAAI,SAAS,KAAK;AAClB,UAAI,SAAS,KAAK,SAAS;AAC3B,WAAK,UAAU;AACf,aAAO;AAAA,IACT;AAEA,kBAAc,UAAU,MAAM,SAAS,IAAI,MAAM;AAC/C,aAAO,KAAK,KAAK,MAAM,OAAO,KAAK,SAAS,KAAK,QAAQ,KAAK,MAAM;AAAA,IACtE;AAEA,aAAS,cAAc,OAAO,UAAU;AACtC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAK,SAAS;AACd,aAAK,QAAQ,MAAM,IAAI,SAAS,MAAM;AACpC,cAAI,CAAC,cAAc,gBAAgB,IAAI;AACrC,mBAAO,IAAI,cAAc,MAAM,QAAQ;AACzC,eAAK,UAAU,KAAK;AACpB,iBAAO;AAAA,QACT,GAAG,IAAI;AAAA,MACT,WAAW,OAAO,UAAU,UAAU;AACpC,YAAI,EAAE,KAAK,SAAS,SAAS;AAC3B,iBAAO,SAAS,MAAM,8BAA8B;AACtD,aAAK,QAAQ;AACb,aAAK,SAAS;AAAA,MAChB,WAAW,OAAO,UAAU,UAAU;AACpC,aAAK,QAAQ;AACb,aAAK,SAAS,OAAO,WAAW,KAAK;AAAA,MACvC,WAAW,OAAO,SAAS,KAAK,GAAG;AACjC,aAAK,QAAQ;AACb,aAAK,SAAS,MAAM;AAAA,MACtB,OAAO;AACL,eAAO,SAAS,MAAM,uBAAuB,OAAO,KAAK;AAAA,MAC3D;AAAA,IACF;AACA,YAAQ,gBAAgB;AAExB,kBAAc,kBAAkB,SAAS,gBAAgB,MAAM;AAC7D,UAAI,gBAAgB,eAAe;AACjC,eAAO;AAAA,MACT;AAGA,YAAM,eAAe,OAAO,SAAS,YACnC,KAAK,YAAY,SAAS,mBAC1B,OAAO,KAAK,WAAW,YACvB,OAAO,KAAK,SAAS;AAEvB,aAAO;AAAA,IACT;AAEA,kBAAc,UAAU,OAAO,SAAS,KAAK,KAAK,QAAQ;AACxD,UAAI,CAAC;AACH,cAAM,OAAO,MAAM,KAAK,MAAM;AAChC,UAAI,CAAC;AACH,iBAAS;AAEX,UAAI,KAAK,WAAW;AAClB,eAAO;AAET,UAAI,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC7B,aAAK,MAAM,QAAQ,SAAS,MAAM;AAChC,eAAK,KAAK,KAAK,MAAM;AACrB,oBAAU,KAAK;AAAA,QACjB,CAAC;AAAA,MACH,OAAO;AACL,YAAI,OAAO,KAAK,UAAU;AACxB,cAAI,MAAM,IAAI,KAAK;AAAA,iBACZ,OAAO,KAAK,UAAU;AAC7B,cAAI,MAAM,KAAK,OAAO,MAAM;AAAA,iBACrB,OAAO,SAAS,KAAK,KAAK;AACjC,eAAK,MAAM,KAAK,KAAK,MAAM;AAC7B,kBAAU,KAAK;AAAA,MACjB;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;ACxJA;AAAA;AAAA,WAAO,UAAU;AAEjB,aAAS,OAAO,KAAK,KAAK;AACxB,UAAI,CAAC;AACH,cAAM,IAAI,MAAM,OAAO,kBAAkB;AAAA,IAC7C;AAEA,WAAO,QAAQ,SAAS,YAAY,GAAG,GAAG,KAAK;AAC7C,UAAI,KAAK;AACP,cAAM,IAAI,MAAM,OAAQ,uBAAuB,IAAI,SAAS,CAAE;AAAA,IAClE;AAAA;AAAA;;;ACVA;AAAA;AAAA;AAEA,QAAM,WAAW,mBAA4B;AAC7C,QAAM,gBAAgB,kBAA0B;AAChD,QAAM,gBAAgB,kBAA0B;AAChD,QAAM,SAAS;AAGf,QAAM,OAAO;AAAA,MACX;AAAA,MAAO;AAAA,MAAS;AAAA,MAAO;AAAA,MAAS;AAAA,MAAS;AAAA,MACzC;AAAA,MAAW;AAAA,MAAW;AAAA,MAAS;AAAA,MAAQ;AAAA,MAAO;AAAA,MAC9C;AAAA,MAAU;AAAA,MAAU;AAAA,MAAW;AAAA,MAAU;AAAA,MAAY;AAAA,MAAU;AAAA,MAC/D;AAAA,MAAU;AAAA,MAAU;AAAA,MAAY;AAAA,MAAU;AAAA,MAAU;AAAA,MAAW;AAAA,IACjE;AAGA,QAAM,UAAU;AAAA,MACd;AAAA,MAAO;AAAA,MAAO;AAAA,MAAO;AAAA,MAAY;AAAA,MAAY;AAAA,MAAY;AAAA,MAAO;AAAA,MAChE;AAAA,MAAO;AAAA,IACT,EAAE,OAAO,IAAI;AAGb,QAAM,YAAY;AAAA,MAChB;AAAA,MAAY;AAAA,MAAc;AAAA,MAC1B;AAAA,MAAc;AAAA,MAAgB;AAAA,MAC9B;AAAA,MAAe;AAAA,MAAc;AAAA,MAAe;AAAA,MAE5C;AAAA,MAAoB;AAAA,MAAc;AAAA,MAAgB;AAAA,MAClD;AAAA,MAAe;AAAA,MAAc;AAAA,IAC/B;AAEA,aAAS,KAAK,KAAK,QAAQ,MAAM;AAC/B,YAAM,QAAQ,CAAC;AACf,WAAK,aAAa;AAElB,YAAM,OAAO;AACb,YAAM,MAAM;AAEZ,YAAM,SAAS,UAAU;AACzB,YAAM,WAAW;AAGjB,YAAM,MAAM;AACZ,YAAM,OAAO;AACb,YAAM,cAAc;AACpB,YAAM,SAAS;AACf,YAAM,WAAW;AACjB,YAAM,MAAM;AACZ,YAAM,MAAM;AACZ,YAAM,MAAM;AACZ,YAAM,aAAa;AACnB,YAAM,MAAM;AACZ,YAAM,SAAS,IAAI;AACnB,YAAM,WAAW;AACjB,YAAM,WAAW;AACjB,YAAM,WAAW;AAGjB,UAAI,CAAC,MAAM,QAAQ;AACjB,cAAM,WAAW,CAAC;AAClB,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AACA,WAAO,UAAU;AAEjB,QAAM,aAAa;AAAA,MACjB;AAAA,MAAO;AAAA,MAAU;AAAA,MAAY;AAAA,MAAO;AAAA,MAAQ;AAAA,MAAe;AAAA,MAC3D;AAAA,MAAY;AAAA,MAAO;AAAA,MAAO;AAAA,MAAO;AAAA,MAAc;AAAA,MAAO;AAAA,MAAW;AAAA,MACjE;AAAA,MAAY;AAAA,IACd;AAEA,SAAK,UAAU,QAAQ,SAAS,QAAQ;AACtC,YAAM,QAAQ,KAAK;AACnB,YAAM,SAAS,CAAC;AAChB,iBAAW,QAAQ,SAAS,MAAM;AAChC,eAAO,IAAI,IAAI,MAAM,IAAI;AAAA,MAC3B,CAAC;AACD,YAAM,MAAM,IAAI,KAAK,YAAY,OAAO,MAAM;AAC9C,UAAI,aAAa;AACjB,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,QAAQ,SAAS,OAAO;AACrC,YAAM,QAAQ,KAAK;AACnB,cAAQ,QAAQ,SAAS,QAAQ;AAC/B,aAAK,MAAM,IAAI,SAAS,iBAAiB;AACvC,gBAAM,QAAQ,IAAI,KAAK,YAAY,IAAI;AACvC,gBAAM,SAAS,KAAK,KAAK;AACzB,iBAAO,MAAM,MAAM,EAAE,MAAM,OAAO,SAAS;AAAA,QAC7C;AAAA,MACF,GAAG,IAAI;AAAA,IACT;AAEA,SAAK,UAAU,QAAQ,SAAS,KAAK,MAAM;AACzC,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,WAAW,IAAI;AAC5B,WAAK,KAAK,IAAI;AAGd,YAAM,WAAW,MAAM,SAAS,OAAO,SAAS,OAAO;AACrD,eAAO,MAAM,WAAW,WAAW;AAAA,MACrC,GAAG,IAAI;AACP,aAAO,MAAM,MAAM,SAAS,QAAQ,GAAG,mCAAmC;AAAA,IAC5E;AAEA,SAAK,UAAU,WAAW,SAAS,QAAQ,MAAM;AAC/C,YAAM,QAAQ,KAAK;AAGnB,YAAM,WAAW,KAAK,OAAO,SAAS,KAAK;AACzC,eAAO,eAAe,KAAK;AAAA,MAC7B,GAAG,IAAI;AACP,aAAO,KAAK,OAAO,SAAS,KAAK;AAC/B,eAAO,EAAE,eAAe,KAAK;AAAA,MAC/B,GAAG,IAAI;AAEP,UAAI,SAAS,WAAW,GAAG;AACzB,eAAO,MAAM,aAAa,IAAI;AAC9B,cAAM,WAAW;AAGjB,iBAAS,QAAQ,SAAS,OAAO;AAC/B,gBAAM,WAAW,SAAS;AAAA,QAC5B,GAAG,IAAI;AAAA,MACT;AACA,UAAI,KAAK,WAAW,GAAG;AACrB,eAAO,MAAM,SAAS,IAAI;AAC1B,cAAM,OAAO;AACb,cAAM,cAAc,KAAK,IAAI,SAAS,KAAK;AACzC,cAAI,OAAO,QAAQ,YAAY,IAAI,gBAAgB;AACjD,mBAAO;AAET,gBAAM,MAAM,CAAC;AACb,iBAAO,KAAK,GAAG,EAAE,QAAQ,SAAS,KAAK;AACrC,gBAAI,QAAQ,MAAM;AAChB,qBAAO;AACT,kBAAM,QAAQ,IAAI,GAAG;AACrB,gBAAI,KAAK,IAAI;AAAA,UACf,CAAC;AACD,iBAAO;AAAA,QACT,CAAC;AAAA,MACH;AAAA,IACF;AAMA,cAAU,QAAQ,SAAS,QAAQ;AACjC,WAAK,UAAU,MAAM,IAAI,SAAS,aAAa;AAC7C,cAAM,QAAQ,KAAK;AACnB,cAAM,IAAI,MAAM,SAAS,oCAAoC,MAAM,GAAG;AAAA,MACxE;AAAA,IACF,CAAC;AAMD,SAAK,QAAQ,SAAS,KAAK;AACzB,WAAK,UAAU,GAAG,IAAI,SAAS,aAAa;AAC1C,cAAM,QAAQ,KAAK;AACnB,cAAM,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAEjD,eAAO,MAAM,QAAQ,IAAI;AACzB,cAAM,MAAM;AAEZ,aAAK,SAAS,IAAI;AAElB,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAED,SAAK,UAAU,MAAM,SAAS,IAAI,MAAM;AACtC,aAAO,IAAI;AACX,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,QAAQ,IAAI;AACzB,YAAM,MAAM;AAEZ,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,WAAW,SAAS,WAAW;AAC5C,YAAM,QAAQ,KAAK;AAEnB,YAAM,WAAW;AAEjB,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,MAAM,SAAS,IAAI,KAAK;AACrC,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,SAAS,MAAM,IAAI;AAChC,YAAM,SAAS,IAAI;AACnB,YAAM,WAAW;AAEjB,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,WAAW,SAAS,SAAS,KAAK;AAC/C,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,aAAa,QAAQ,MAAM,aAAa,IAAI;AACzD,YAAM,WAAW;AAEjB,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,WAAW,SAAS,SAAS,KAAK;AAC/C,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,aAAa,QAAQ,MAAM,aAAa,IAAI;AACzD,YAAM,WAAW;AAEjB,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,MAAM,SAAS,MAAM;AAClC,YAAM,QAAQ,KAAK;AACnB,YAAM,OAAO,MAAM,UAAU,MAAM,KAAK,SAAS;AAEjD,YAAM,MAAM;AAEZ,UAAI,KAAK,WAAW;AAClB,aAAK,SAAS,IAAI;AAEpB,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,MAAM,SAAS,IAAI,QAAQ;AACxC,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,QAAQ,IAAI;AACzB,YAAM,MAAM;AAEZ,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,MAAM,SAAS,MAAM;AAClC,YAAM,QAAQ,KAAK;AAEnB,YAAM,MAAM;AAEZ,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,SAAS,SAAS,OAAO,KAAK;AAC3C,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,WAAW,IAAI;AAC5B,YAAM,SAAS;AACf,WAAK,SAAS,OAAO,KAAK,GAAG,EAAE,IAAI,SAAS,KAAK;AAC/C,eAAO,IAAI,GAAG;AAAA,MAChB,CAAC,CAAC;AAEF,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,WAAW,SAAS,SAAS,MAAM;AAChD,YAAM,QAAQ,KAAK;AAEnB,aAAO,MAAM,QAAQ,IAAI;AACzB,YAAM,WAAW;AAEjB,aAAO;AAAA,IACT;AAMA,SAAK,UAAU,UAAU,SAAS,OAAO,OAAO,SAAS;AACvD,YAAM,QAAQ,KAAK;AAGnB,UAAI,MAAM,WAAW;AACnB,eAAO,MAAM,WAAW,MAAM,SAAS,CAAC,EAAE,QAAQ,OAAO,OAAO,CAAC;AAEnE,UAAI,SAAS,MAAM,SAAS;AAC5B,UAAI,UAAU;AAEd,UAAI,UAAU;AACd,UAAI,MAAM,QAAQ;AAChB,kBAAU,MAAM,SAAS,MAAM,GAAG;AAGpC,UAAI,MAAM,UAAU;AAClB,YAAI,MAAM;AACV,YAAI,MAAM,aAAa;AACrB,gBAAM,MAAM;AAAA,iBACL,MAAM,aAAa;AAC1B,gBAAM,MAAM;AAAA,iBACL,MAAM,QAAQ;AACrB,gBAAM,MAAM;AAEd,YAAI,QAAQ,QAAQ,CAAC,MAAM,KAAK;AAE9B,gBAAM,OAAO,MAAM,KAAK;AACxB,cAAI;AACF,gBAAI,MAAM,WAAW;AACnB,mBAAK,eAAe,MAAM,KAAK,OAAO,OAAO;AAAA;AAE7C,mBAAK,cAAc,OAAO,OAAO;AACnC,sBAAU;AAAA,UACZ,SAAS,GAAG;AACV,sBAAU;AAAA,UACZ;AACA,gBAAM,QAAQ,IAAI;AAAA,QACpB,OAAO;AACL,oBAAU,KAAK,SAAS,OAAO,KAAK,MAAM,GAAG;AAE7C,cAAI,MAAM,QAAQ,OAAO;AACvB,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,UAAI;AACJ,UAAI,MAAM,OAAO;AACf,kBAAU,MAAM,YAAY;AAE9B,UAAI,SAAS;AAEX,YAAI,MAAM,aAAa,MAAM;AAC3B,gBAAM,WAAW,KAAK,WAAW,OAAO,MAAM,QAAQ;AACtD,cAAI,MAAM,QAAQ,QAAQ;AACxB,mBAAO;AACT,kBAAQ;AAAA,QACV;AAEA,cAAM,QAAQ,MAAM;AAGpB,YAAI,MAAM,QAAQ,QAAQ,MAAM,WAAW,MAAM;AAC/C,cAAI;AACJ,cAAI,MAAM;AACR,mBAAO,MAAM,KAAK;AACpB,gBAAM,OAAO,KAAK;AAAA,YAChB;AAAA,YACA,MAAM,aAAa,OAAO,MAAM,WAAW,MAAM;AAAA,YACjD,MAAM;AAAA,UACR;AACA,cAAI,MAAM,QAAQ,IAAI;AACpB,mBAAO;AAET,cAAI,MAAM;AACR,qBAAS,MAAM,IAAI,IAAI;AAAA;AAEvB,oBAAQ;AAAA,QACZ;AAEA,YAAI,WAAW,QAAQ,SAAS,MAAM,QAAQ;AAC5C,kBAAQ,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM,QAAQ,QAAQ;AAE3D,YAAI,WAAW,QAAQ,SAAS,MAAM,QAAQ;AAC5C,kBAAQ,MAAM,MAAM,KAAK,GAAG,MAAM,QAAQ,MAAM,QAAQ,SAAS;AAGnE,YAAI,MAAM,KAAK;AAAA,QAEf,WAAW,MAAM,WAAW,MAAM;AAChC,mBAAS,KAAK,eAAe,MAAM,KAAK,OAAO,OAAO;AAAA,QACxD,OAAO;AACL,mBAAS,KAAK,cAAc,OAAO,OAAO;AAAA,QAC5C;AAEA,YAAI,MAAM,QAAQ,MAAM;AACtB,iBAAO;AAGT,YAAI,CAAC,MAAM,OAAO,MAAM,WAAW,QAAQ,MAAM,aAAa,MAAM;AAClE,gBAAM,SAAS,QAAQ,SAAS,eAAe,OAAO;AAGpD,kBAAM,QAAQ,OAAO,OAAO;AAAA,UAC9B,CAAC;AAAA,QACH;AAGA,YAAI,MAAM,aAAa,MAAM,QAAQ,YAAY,MAAM,QAAQ,WAAW;AACxE,gBAAM,OAAO,IAAI,cAAc,MAAM;AACrC,mBAAS,KAAK,QAAQ,MAAM,UAAU,MAAM,eAAe,GAAG,EAC3D,QAAQ,MAAM,OAAO;AAAA,QAC1B;AAAA,MACF;AAGA,UAAI,MAAM,OAAO;AACf,iBAAS,MAAM,YAAY,OAAO;AAGpC,UAAI,MAAM,QAAQ,SAAS,WAAW,QAAQ,YAAY;AACxD,cAAM,SAAS,SAAS,MAAM,KAAK,MAAM;AAAA,eAClC,YAAY;AACnB,cAAM,QAAQ,OAAO;AAEvB,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,iBAAiB,SAAS,cAAc,KAAK,OAAO,SAAS;AAC1E,YAAM,QAAQ,KAAK;AAEnB,UAAI,QAAQ,SAAS,QAAQ;AAC3B,eAAO;AACT,UAAI,QAAQ,WAAW,QAAQ;AAC7B,eAAO,KAAK,YAAY,OAAO,KAAK,MAAM,KAAK,CAAC,GAAG,OAAO;AAAA,eACnD,OAAO,KAAK,GAAG;AACtB,eAAO,KAAK,WAAW,OAAO,KAAK,OAAO;AAAA,eACnC,QAAQ,WAAW,MAAM;AAChC,eAAO,KAAK,aAAa,OAAO,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK,CAAC,GAAG,OAAO;AAAA,eAC9D,QAAQ;AACf,eAAO,KAAK,aAAa,OAAO,MAAM,MAAM,OAAO;AAAA,eAC5C,QAAQ,aAAa,QAAQ;AACpC,eAAO,KAAK,YAAY,OAAO,KAAK,OAAO;AAAA,eACpC,QAAQ;AACf,eAAO,KAAK,YAAY,OAAO,OAAO;AAAA,eAC/B,QAAQ;AACf,eAAO,KAAK,YAAY,OAAO,OAAO;AAAA,eAC/B,QAAQ;AACf,eAAO,KAAK,WAAW,OAAO,KAAK,OAAO;AAAA,eACnC,QAAQ,SAAS,QAAQ;AAChC,eAAO,KAAK,WAAW,OAAO,MAAM,QAAQ,MAAM,KAAK,CAAC,GAAG,OAAO;AAEpE,UAAI,MAAM,QAAQ,MAAM;AACtB,eAAO,KAAK,QAAQ,MAAM,KAAK,MAAM,eAAe,GAAG,EACpD,QAAQ,OAAO,OAAO;AAAA,MAC3B,OAAO;AACL,eAAO,MAAM,MAAM,kBAAkB,GAAG;AAAA,MAC1C;AAAA,IACF;AAEA,SAAK,UAAU,UAAU,SAAS,QAAQ,QAAQ,KAAK;AAErD,YAAM,QAAQ,KAAK;AAEnB,YAAM,aAAa,KAAK,KAAK,QAAQ,GAAG;AACxC,aAAO,MAAM,WAAW,WAAW,WAAW,IAAI;AAClD,YAAM,aAAa,MAAM,WAAW,WAAW,SAAS,CAAC;AACzD,UAAI,MAAM,aAAa,MAAM,WAAW,WAAW,UAAU;AAC3D,cAAM,aAAa,MAAM,WAAW,MAAM;AAC1C,cAAM,WAAW,WAAW,WAAW,MAAM;AAAA,MAC/C;AACA,aAAO,MAAM;AAAA,IACf;AAEA,SAAK,UAAU,gBAAgB,SAAS,aAAa,OAAO,SAAS;AACnE,YAAM,QAAQ,KAAK;AACnB,UAAI,SAAS;AACb,UAAI,QAAQ;AAEZ,aAAO,KAAK,MAAM,MAAM,EAAE,KAAK,SAAS,KAAK;AAC3C,cAAM,OAAO,MAAM,KAAK;AACxB,cAAM,OAAO,MAAM,OAAO,GAAG;AAC7B,YAAI;AACF,gBAAM,QAAQ,KAAK,QAAQ,OAAO,OAAO;AACzC,cAAI,MAAM,QAAQ,KAAK;AACrB,mBAAO;AAET,mBAAS,EAAE,MAAM,KAAK,MAAa;AACnC,kBAAQ;AAAA,QACV,SAAS,GAAG;AACV,gBAAM,QAAQ,IAAI;AAClB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,GAAG,IAAI;AAEP,UAAI,CAAC;AACH,eAAO,MAAM,MAAM,oBAAoB;AAEzC,aAAO;AAAA,IACT;AAMA,SAAK,UAAU,uBAAuB,SAAS,oBAAoB,MAAM;AACvE,aAAO,IAAI,cAAc,MAAM,KAAK,QAAQ;AAAA,IAC9C;AAEA,SAAK,UAAU,UAAU,SAAS,OAAO,MAAM,UAAU,QAAQ;AAC/D,YAAM,QAAQ,KAAK;AACnB,UAAI,MAAM,SAAS,MAAM,QAAQ,MAAM,SAAS,MAAM;AACpD;AAEF,YAAM,SAAS,KAAK,aAAa,MAAM,UAAU,MAAM;AACvD,UAAI,WAAW;AACb;AAEF,UAAI,KAAK,aAAa,QAAQ,UAAU,MAAM;AAC5C;AAEF,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,eAAe,SAAS,OAAO,MAAM,UAAU,QAAQ;AACpE,YAAM,QAAQ,KAAK;AAGnB,UAAI,MAAM,WAAW;AACnB,eAAO,MAAM,SAAS,CAAC,EAAE,QAAQ,MAAM,YAAY,IAAI,SAAS,CAAC;AAEnE,UAAI,SAAS;AAGb,WAAK,WAAW;AAGhB,UAAI,MAAM,YAAY,SAAS,QAAW;AACxC,YAAI,MAAM,SAAS,MAAM;AACvB,iBAAO,MAAM,SAAS;AAAA;AAEtB;AAAA,MACJ;AAGA,UAAI,UAAU;AACd,UAAI,YAAY;AAChB,UAAI,MAAM,KAAK;AAEb,iBAAS,KAAK,qBAAqB,IAAI;AAAA,MACzC,WAAW,MAAM,QAAQ;AACvB,iBAAS,KAAK,cAAc,MAAM,QAAQ;AAAA,MAC5C,WAAW,MAAM,UAAU;AACzB,kBAAU,KAAK,QAAQ,MAAM,UAAU,MAAM,EAAE,QAAQ,MAAM,QAAQ;AACrE,oBAAY;AAAA,MACd,WAAW,MAAM,UAAU;AACzB,kBAAU,MAAM,SAAS,IAAI,SAAS,OAAO;AAC3C,cAAI,MAAM,WAAW,QAAQ;AAC3B,mBAAO,MAAM,QAAQ,MAAM,UAAU,IAAI;AAE3C,cAAI,MAAM,WAAW,QAAQ;AAC3B,mBAAO,SAAS,MAAM,yBAAyB;AACjD,gBAAM,UAAU,SAAS,SAAS,MAAM,WAAW,GAAG;AAEtD,cAAI,OAAO,SAAS;AAClB,mBAAO,SAAS,MAAM,yCAAyC;AAEjE,gBAAM,MAAM,MAAM,QAAQ,KAAK,MAAM,WAAW,GAAG,GAAG,UAAU,IAAI;AACpE,mBAAS,SAAS,OAAO;AAEzB,iBAAO;AAAA,QACT,GAAG,IAAI,EAAE,OAAO,SAAS,OAAO;AAC9B,iBAAO;AAAA,QACT,CAAC;AACD,kBAAU,KAAK,qBAAqB,OAAO;AAAA,MAC7C,OAAO;AACL,YAAI,MAAM,QAAQ,WAAW,MAAM,QAAQ,SAAS;AAElD,cAAI,EAAE,MAAM,QAAQ,MAAM,KAAK,WAAW;AACxC,mBAAO,SAAS,MAAM,yBAAyB,MAAM,GAAG;AAE1D,cAAI,CAAC,MAAM,QAAQ,IAAI;AACrB,mBAAO,SAAS,MAAM,oCAAoC;AAE5D,gBAAM,QAAQ,KAAK,MAAM;AACzB,gBAAM,WAAW,WAAW;AAC5B,oBAAU,KAAK,qBAAqB,KAAK,IAAI,SAAS,MAAM;AAC1D,kBAAMC,SAAQ,KAAK;AAEnB,mBAAO,KAAK,QAAQA,OAAM,KAAK,CAAC,GAAG,IAAI,EAAE,QAAQ,MAAM,QAAQ;AAAA,UACjE,GAAG,KAAK,CAAC;AAAA,QACX,WAAW,MAAM,QAAQ,MAAM;AAC7B,mBAAS,KAAK,QAAQ,MAAM,KAAK,MAAM,EAAE,QAAQ,MAAM,QAAQ;AAAA,QACjE,OAAO;AACL,oBAAU,KAAK,iBAAiB,MAAM,KAAK,IAAI;AAC/C,sBAAY;AAAA,QACd;AAAA,MACF;AAGA,UAAI,CAAC,MAAM,OAAO,MAAM,WAAW,MAAM;AACvC,cAAM,MAAM,MAAM,aAAa,OAAO,MAAM,WAAW,MAAM;AAC7D,cAAM,MAAM,MAAM,aAAa,OAAO,cAAc;AAEpD,YAAI,QAAQ,MAAM;AAChB,cAAI,MAAM,QAAQ;AAChB,qBAAS,MAAM,sCAAsC;AAAA,QACzD,OAAO;AACL,cAAI,MAAM,QAAQ;AAChB,qBAAS,KAAK,iBAAiB,KAAK,WAAW,KAAK,OAAO;AAAA,QAC/D;AAAA,MACF;AAGA,UAAI,MAAM,aAAa;AACrB,iBAAS,KAAK,iBAAiB,MAAM,UAAU,OAAO,WAAW,MAAM;AAEzE,aAAO;AAAA,IACT;AAEA,SAAK,UAAU,gBAAgB,SAAS,aAAa,MAAM,UAAU;AACnE,YAAM,QAAQ,KAAK;AAEnB,YAAM,OAAO,MAAM,OAAO,KAAK,IAAI;AACnC,UAAI,CAAC,MAAM;AACT;AAAA,UACE;AAAA,UACA,KAAK,OAAO,mBACN,KAAK,UAAU,OAAO,KAAK,MAAM,MAAM,CAAC;AAAA,QAAC;AAAA,MACnD;AACA,aAAO,KAAK,QAAQ,KAAK,OAAO,QAAQ;AAAA,IAC1C;AAEA,SAAK,UAAU,mBAAmB,SAAS,gBAAgB,KAAK,MAAM;AACpE,YAAM,QAAQ,KAAK;AAEnB,UAAI,OAAO,KAAK,GAAG;AACjB,eAAO,KAAK,WAAW,MAAM,GAAG;AAAA,eACzB,QAAQ,WAAW,MAAM;AAChC,eAAO,KAAK,aAAa,MAAM,MAAM,YAAY,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC;AAAA,eAC3D,QAAQ;AACf,eAAO,KAAK,aAAa,MAAM,MAAM,IAAI;AAAA,eAClC,QAAQ,aAAa,QAAQ;AACpC,eAAO,KAAK,YAAY,MAAM,GAAG;AAAA,eAC1B,QAAQ;AACf,eAAO,KAAK,YAAY;AAAA,eACjB,QAAQ,SAAS,QAAQ;AAChC,eAAO,KAAK,WAAW,MAAM,MAAM,QAAQ,MAAM,YAAY,CAAC,CAAC;AAAA,eACxD,QAAQ;AACf,eAAO,KAAK,YAAY,IAAI;AAAA,eACrB,QAAQ;AACf,eAAO,KAAK,WAAW,MAAM,GAAG;AAAA;AAEhC,cAAM,IAAI,MAAM,sBAAsB,GAAG;AAAA,IAC7C;AAEA,SAAK,UAAU,YAAY,SAAS,SAAS,KAAK;AAChD,aAAO,YAAY,KAAK,GAAG;AAAA,IAC7B;AAEA,SAAK,UAAU,cAAc,SAAS,WAAW,KAAK;AACpD,aAAO,6BAA6B,KAAK,GAAG;AAAA,IAC9C;AAAA;AAAA;;;AC7nBA;AAAA;AAAA;AAGA,aAAS,QAAQ,KAAK;AACpB,YAAM,MAAM,CAAC;AAEb,aAAO,KAAK,GAAG,EAAE,QAAQ,SAAS,KAAK;AAErC,aAAK,MAAM,MAAM;AACf,gBAAM,MAAM;AAEd,cAAM,QAAQ,IAAI,GAAG;AACrB,YAAI,KAAK,IAAI;AAAA,MACf,CAAC;AAED,aAAO;AAAA,IACT;AAEA,YAAQ,WAAW;AAAA,MACjB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AACA,YAAQ,iBAAiB,QAAQ,QAAQ,QAAQ;AAEjD,YAAQ,MAAM;AAAA,MACZ,GAAM;AAAA,MACN,GAAM;AAAA,MACN,GAAM;AAAA,MACN,GAAM;AAAA,MACN,GAAM;AAAA,MACN,GAAM;AAAA,MACN,GAAM;AAAA,MACN,GAAM;AAAA,MACN,GAAM;AAAA,MACN,GAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,MACN,IAAM;AAAA,IACR;AACA,YAAQ,YAAY,QAAQ,QAAQ,GAAG;AAAA;AAAA;;;ACzDvC,IAAAC,eAAA;AAAA;AAAA;AAEA,QAAM,WAAW;AACjB,QAAM,SAAS,gBAAwB;AACvC,QAAM,OAAO;AAGb,QAAM,MAAM;AAEZ,aAAS,WAAW,QAAQ;AAC1B,WAAK,MAAM;AACX,WAAK,OAAO,OAAO;AACnB,WAAK,SAAS;AAGd,WAAK,OAAO,IAAI,QAAQ;AACxB,WAAK,KAAK,MAAM,OAAO,IAAI;AAAA,IAC7B;AACA,WAAO,UAAU;AAEjB,eAAW,UAAU,SAAS,SAAS,OAAO,MAAM,UAAU;AAC5D,aAAO,KAAK,KAAK,QAAQ,MAAM,QAAQ,EAAE,KAAK;AAAA,IAChD;AAIA,aAAS,QAAQ,QAAQ;AACvB,WAAK,KAAK,MAAM,OAAO,MAAM;AAAA,IAC/B;AACA,aAAS,SAAS,IAAI;AAEtB,YAAQ,UAAU,mBAAmB,SAAS,gBAAgB,KAC5D,WACA,KACA,SAAS;AACT,YAAM,aAAa,UAAU,KAAK,WAAW,KAAK,KAAK,QAAQ;AAG/D,UAAI,QAAQ,SAAS,KAAM;AACzB,cAAMC,UAAS,OAAO,MAAM,CAAC;AAC7B,QAAAA,QAAO,CAAC,IAAI;AACZ,QAAAA,QAAO,CAAC,IAAI,QAAQ;AACpB,eAAO,KAAK,qBAAqB,CAAEA,SAAQ,OAAQ,CAAC;AAAA,MACtD;AAIA,UAAI,YAAY;AAChB,eAAS,IAAI,QAAQ,QAAQ,KAAK,KAAO,MAAM;AAC7C;AAEF,YAAM,SAAS,OAAO,MAAM,IAAI,IAAI,SAAS;AAC7C,aAAO,CAAC,IAAI;AACZ,aAAO,CAAC,IAAI,MAAO;AAEnB,eAAS,IAAI,IAAI,WAAW,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK,MAAM;AAChE,eAAO,CAAC,IAAI,IAAI;AAElB,aAAO,KAAK,qBAAqB,CAAE,QAAQ,OAAQ,CAAC;AAAA,IACtD;AAEA,YAAQ,UAAU,aAAa,SAAS,UAAU,KAAK,KAAK;AAC1D,UAAI,QAAQ,UAAU;AACpB,eAAO,KAAK,qBAAqB,CAAE,IAAI,SAAS,GAAG,IAAI,IAAK,CAAC;AAAA,MAC/D,WAAW,QAAQ,UAAU;AAC3B,cAAM,MAAM,OAAO,MAAM,IAAI,SAAS,CAAC;AACvC,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,cAAI,cAAc,IAAI,WAAW,CAAC,GAAG,IAAI,CAAC;AAAA,QAC5C;AACA,eAAO,KAAK,qBAAqB,GAAG;AAAA,MACtC,WAAW,QAAQ,UAAU;AAC3B,YAAI,CAAC,KAAK,UAAU,GAAG,GAAG;AACxB,iBAAO,KAAK,SAAS,MAAM,gEACuB;AAAA,QACpD;AACA,eAAO,KAAK,qBAAqB,GAAG;AAAA,MACtC,WAAW,QAAQ,YAAY;AAC7B,YAAI,CAAC,KAAK,YAAY,GAAG,GAAG;AAC1B,iBAAO,KAAK,SAAS,MAAM,mNAKe;AAAA,QAC5C;AACA,eAAO,KAAK,qBAAqB,GAAG;AAAA,MACtC,WAAW,OAAO,KAAK,GAAG,GAAG;AAC3B,eAAO,KAAK,qBAAqB,GAAG;AAAA,MACtC,WAAW,QAAQ,WAAW;AAC5B,eAAO,KAAK,qBAAqB,GAAG;AAAA,MACtC,OAAO;AACL,eAAO,KAAK,SAAS,MAAM,8BAA8B,MAC9B,cAAc;AAAA,MAC3C;AAAA,IACF;AAEA,YAAQ,UAAU,eAAe,SAAS,YAAY,IAAI,QAAQ,UAAU;AAC1E,UAAI,OAAO,OAAO,UAAU;AAC1B,YAAI,CAAC;AACH,iBAAO,KAAK,SAAS,MAAM,6CAA6C;AAC1E,YAAI,CAAC,OAAO,eAAe,EAAE;AAC3B,iBAAO,KAAK,SAAS,MAAM,+BAA+B;AAC5D,aAAK,OAAO,EAAE,EAAE,MAAM,SAAS;AAC/B,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ;AAC7B,aAAG,CAAC,KAAK;AAAA,MACb,WAAW,MAAM,QAAQ,EAAE,GAAG;AAC5B,aAAK,GAAG,MAAM;AACd,iBAAS,IAAI,GAAG,IAAI,GAAG,QAAQ;AAC7B,aAAG,CAAC,KAAK;AAAA,MACb;AAEA,UAAI,CAAC,MAAM,QAAQ,EAAE,GAAG;AACtB,eAAO,KAAK,SAAS,MAAM,oDACU,KAAK,UAAU,EAAE,CAAC;AAAA,MACzD;AAEA,UAAI,CAAC,UAAU;AACb,YAAI,GAAG,CAAC,KAAK;AACX,iBAAO,KAAK,SAAS,MAAM,6BAA6B;AAC1D,WAAG,OAAO,GAAG,GAAG,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC;AAAA,MACpC;AAGA,UAAI,OAAO;AACX,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,KAAK;AAClC,YAAI,QAAQ,GAAG,CAAC;AAChB,aAAK,QAAQ,SAAS,KAAM,UAAU;AACpC;AAAA,MACJ;AAEA,YAAM,QAAQ,OAAO,MAAM,IAAI;AAC/B,UAAI,SAAS,MAAM,SAAS;AAC5B,eAAS,IAAI,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK;AACvC,YAAI,QAAQ,GAAG,CAAC;AAChB,cAAM,QAAQ,IAAI,QAAQ;AAC1B,gBAAQ,UAAU,KAAK;AACrB,gBAAM,QAAQ,IAAI,MAAQ,QAAQ;AAAA,MACtC;AAEA,aAAO,KAAK,qBAAqB,KAAK;AAAA,IACxC;AAEA,aAAS,IAAI,KAAK;AAChB,UAAI,MAAM;AACR,eAAO,MAAM;AAAA;AAEb,eAAO;AAAA,IACX;AAEA,YAAQ,UAAU,cAAc,SAAS,WAAW,MAAM,KAAK;AAC7D,UAAI;AACJ,YAAM,OAAO,IAAI,KAAK,IAAI;AAE1B,UAAI,QAAQ,WAAW;AACrB,cAAM;AAAA,UACJ,IAAI,KAAK,eAAe,CAAC;AAAA,UACzB,IAAI,KAAK,YAAY,IAAI,CAAC;AAAA,UAC1B,IAAI,KAAK,WAAW,CAAC;AAAA,UACrB,IAAI,KAAK,YAAY,CAAC;AAAA,UACtB,IAAI,KAAK,cAAc,CAAC;AAAA,UACxB,IAAI,KAAK,cAAc,CAAC;AAAA,UACxB;AAAA,QACF,EAAE,KAAK,EAAE;AAAA,MACX,WAAW,QAAQ,WAAW;AAC5B,cAAM;AAAA,UACJ,IAAI,KAAK,eAAe,IAAI,GAAG;AAAA,UAC/B,IAAI,KAAK,YAAY,IAAI,CAAC;AAAA,UAC1B,IAAI,KAAK,WAAW,CAAC;AAAA,UACrB,IAAI,KAAK,YAAY,CAAC;AAAA,UACtB,IAAI,KAAK,cAAc,CAAC;AAAA,UACxB,IAAI,KAAK,cAAc,CAAC;AAAA,UACxB;AAAA,QACF,EAAE,KAAK,EAAE;AAAA,MACX,OAAO;AACL,aAAK,SAAS,MAAM,cAAc,MAAM,4BAA4B;AAAA,MACtE;AAEA,aAAO,KAAK,WAAW,KAAK,QAAQ;AAAA,IACtC;AAEA,YAAQ,UAAU,cAAc,SAAS,aAAa;AACpD,aAAO,KAAK,qBAAqB,EAAE;AAAA,IACrC;AAEA,YAAQ,UAAU,aAAa,SAAS,UAAU,KAAK,QAAQ;AAC7D,UAAI,OAAO,QAAQ,UAAU;AAC3B,YAAI,CAAC;AACH,iBAAO,KAAK,SAAS,MAAM,6CAA6C;AAC1E,YAAI,CAAC,OAAO,eAAe,GAAG,GAAG;AAC/B,iBAAO,KAAK,SAAS,MAAM,iCACA,KAAK,UAAU,GAAG,CAAC;AAAA,QAChD;AACA,cAAM,OAAO,GAAG;AAAA,MAClB;AAGA,UAAI,OAAO,QAAQ,YAAY,CAAC,OAAO,SAAS,GAAG,GAAG;AACpD,cAAM,WAAW,IAAI,QAAQ;AAC7B,YAAI,CAAC,IAAI,QAAQ,SAAS,CAAC,IAAI,KAAM;AACnC,mBAAS,QAAQ,CAAC;AAAA,QACpB;AACA,cAAM,OAAO,KAAK,QAAQ;AAAA,MAC5B;AAEA,UAAI,OAAO,SAAS,GAAG,GAAG;AACxB,YAAIC,QAAO,IAAI;AACf,YAAI,IAAI,WAAW;AACjB,UAAAA;AAEF,cAAMC,OAAM,OAAO,MAAMD,KAAI;AAC7B,YAAI,KAAKC,IAAG;AACZ,YAAI,IAAI,WAAW;AACjB,UAAAA,KAAI,CAAC,IAAI;AACX,eAAO,KAAK,qBAAqBA,IAAG;AAAA,MACtC;AAEA,UAAI,MAAM;AACR,eAAO,KAAK,qBAAqB,GAAG;AAEtC,UAAI,MAAM;AACR,eAAO,KAAK,qBAAqB,CAAC,GAAG,GAAG,CAAC;AAE3C,UAAI,OAAO;AACX,eAAS,IAAI,KAAK,KAAK,KAAO,MAAM;AAClC;AAEF,YAAM,MAAM,IAAI,MAAM,IAAI;AAC1B,eAAS,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,YAAI,CAAC,IAAI,MAAM;AACf,gBAAQ;AAAA,MACV;AACA,UAAG,IAAI,CAAC,IAAI,KAAM;AAChB,YAAI,QAAQ,CAAC;AAAA,MACf;AAEA,aAAO,KAAK,qBAAqB,OAAO,KAAK,GAAG,CAAC;AAAA,IACnD;AAEA,YAAQ,UAAU,cAAc,SAAS,WAAW,OAAO;AACzD,aAAO,KAAK,qBAAqB,QAAQ,MAAO,CAAC;AAAA,IACnD;AAEA,YAAQ,UAAU,OAAO,SAAS,IAAI,QAAQ,KAAK;AACjD,UAAI,OAAO,WAAW;AACpB,iBAAS,OAAO,GAAG;AACrB,aAAO,OAAO,YAAY,KAAK,EAAE;AAAA,IACnC;AAEA,YAAQ,UAAU,eAAe,SAAS,YAAY,YAAY,UAAU,QAAQ;AAClF,YAAM,QAAQ,KAAK;AACnB,UAAI;AACJ,UAAI,MAAM,SAAS,MAAM;AACvB,eAAO;AAET,YAAM,OAAO,WAAW,KAAK;AAC7B,UAAI,MAAM,kBAAkB;AAC1B,cAAM,gBAAgB,KAAK,aAAa,MAAM,SAAS,GAAG,UAAU,MAAM,EAAE,KAAK;AAEnF,UAAI,KAAK,WAAW,MAAM,cAAc;AACtC,eAAO;AAET,WAAK,IAAE,GAAG,IAAI,KAAK,QAAQ;AACzB,YAAI,KAAK,CAAC,MAAM,MAAM,cAAc,CAAC;AACnC,iBAAO;AAEX,aAAO;AAAA,IACT;AAIA,aAAS,UAAU,KAAK,WAAW,KAAK,UAAU;AAChD,UAAI;AAEJ,UAAI,QAAQ;AACV,cAAM;AAAA,eACC,QAAQ;AACf,cAAM;AAER,UAAI,IAAI,UAAU,eAAe,GAAG;AAClC,cAAM,IAAI,UAAU,GAAG;AAAA,eAChB,OAAO,QAAQ,aAAa,MAAM,OAAO;AAChD,cAAM;AAAA;AAEN,eAAO,SAAS,MAAM,kBAAkB,GAAG;AAE7C,UAAI,OAAO;AACT,eAAO,SAAS,MAAM,sCAAsC;AAE9D,UAAI,CAAC;AACH,eAAO;AAET,aAAQ,IAAI,eAAe,OAAO,WAAW,KAAK;AAElD,aAAO;AAAA,IACT;AAAA;AAAA;;;ACtSA;AAAA;AAAA;AAEA,QAAM,WAAW;AAEjB,QAAM,aAAa;AAEnB,aAAS,WAAW,QAAQ;AAC1B,iBAAW,KAAK,MAAM,MAAM;AAC5B,WAAK,MAAM;AAAA,IACb;AACA,aAAS,YAAY,UAAU;AAC/B,WAAO,UAAU;AAEjB,eAAW,UAAU,SAAS,SAAS,OAAO,MAAM,SAAS;AAC3D,YAAM,MAAM,WAAW,UAAU,OAAO,KAAK,MAAM,IAAI;AAEvD,YAAM,IAAI,IAAI,SAAS,QAAQ;AAC/B,YAAM,MAAM,CAAE,gBAAgB,QAAQ,QAAQ,OAAQ;AACtD,eAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,YAAI,KAAK,EAAE,MAAM,GAAG,IAAI,EAAE,CAAC;AAC7B,UAAI,KAAK,cAAc,QAAQ,QAAQ,OAAO;AAC9C,aAAO,IAAI,KAAK,IAAI;AAAA,IACtB;AAAA;AAAA;;;ACtBA;AAAA;AAAA;AAEA,QAAM,WAAW;AAEjB,aAAS,MAAM;AACf,aAAS,MAAM;AAAA;AAAA;;;ACLf,IAAAC,eAAA;AAAA;AAAA;AAEA,QAAM,WAAW;AAEjB,QAAM,SAAS;AACf,QAAM,gBAAgB,kBAA0B;AAChD,QAAM,OAAO;AAGb,QAAM,MAAM;AAEZ,aAAS,WAAW,QAAQ;AAC1B,WAAK,MAAM;AACX,WAAK,OAAO,OAAO;AACnB,WAAK,SAAS;AAGd,WAAK,OAAO,IAAI,QAAQ;AACxB,WAAK,KAAK,MAAM,OAAO,IAAI;AAAA,IAC7B;AACA,WAAO,UAAU;AAEjB,eAAW,UAAU,SAAS,SAAS,OAAO,MAAM,SAAS;AAC3D,UAAI,CAAC,cAAc,gBAAgB,IAAI,GAAG;AACxC,eAAO,IAAI,cAAc,MAAM,OAAO;AAAA,MACxC;AAEA,aAAO,KAAK,KAAK,QAAQ,MAAM,OAAO;AAAA,IACxC;AAIA,aAAS,QAAQ,QAAQ;AACvB,WAAK,KAAK,MAAM,OAAO,MAAM;AAAA,IAC/B;AACA,aAAS,SAAS,IAAI;AAEtB,YAAQ,UAAU,WAAW,SAAS,QAAQ,QAAQ,KAAK,KAAK;AAC9D,UAAI,OAAO,QAAQ;AACjB,eAAO;AAET,YAAM,QAAQ,OAAO,KAAK;AAC1B,YAAM,aAAa,aAAa,QAAQ,0BAA0B,MAAM,GAAG;AAC3E,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO;AAET,aAAO,QAAQ,KAAK;AAEpB,aAAO,WAAW,QAAQ,OAAO,WAAW,WAAW,OACpD,WAAW,SAAS,SAAU,OAAO;AAAA,IAC1C;AAEA,YAAQ,UAAU,aAAa,SAAS,UAAU,QAAQ,KAAK,KAAK;AAClE,YAAM,aAAa;AAAA,QAAa;AAAA,QAC9B,8BAA8B,MAAM;AAAA,MAAG;AACzC,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO;AAET,UAAI,MAAM;AAAA,QAAa;AAAA,QACrB,WAAW;AAAA,QACX,8BAA8B,MAAM;AAAA,MAAG;AAGzC,UAAI,OAAO,QAAQ,GAAG;AACpB,eAAO;AAET,UAAI,CAAC,OACD,WAAW,QAAQ,OACnB,WAAW,WAAW,OACtB,WAAW,SAAS,SAAS,KAAK;AACpC,eAAO,OAAO,MAAM,2BAA2B,MAAM,GAAG;AAAA,MAC1D;AAEA,UAAI,WAAW,aAAa,QAAQ;AAClC,eAAO,OAAO,KAAK,KAAK,+BAA+B,MAAM,GAAG;AAGlE,YAAM,QAAQ,OAAO,KAAK;AAC1B,YAAM,MAAM,KAAK;AAAA,QACf;AAAA,QACA,6CAA6C,KAAK,MAAM;AAAA,MAAG;AAC7D,UAAI,OAAO,QAAQ,GAAG;AACpB,eAAO;AAET,YAAM,OAAO,SAAS,MAAM;AAC5B,aAAO,QAAQ,KAAK;AACpB,aAAO,OAAO,KAAK,KAAK,+BAA+B,MAAM,GAAG;AAAA,IAClE;AAEA,YAAQ,UAAU,gBAAgB,SAAS,aAAa,QAAQ,MAAM;AACpE,iBAAS;AACP,cAAM,MAAM,aAAa,QAAQ,IAAI;AACrC,YAAI,OAAO,QAAQ,GAAG;AACpB,iBAAO;AACT,cAAM,MAAM,aAAa,QAAQ,IAAI,WAAW,IAAI;AACpD,YAAI,OAAO,QAAQ,GAAG;AACpB,iBAAO;AAET,YAAI;AACJ,YAAI,IAAI,aAAa,QAAQ;AAC3B,gBAAM,OAAO,KAAK,GAAG;AAAA;AAErB,gBAAM,KAAK,cAAc,QAAQ,IAAI;AAGvC,YAAI,OAAO,QAAQ,GAAG;AACpB,iBAAO;AAET,YAAI,IAAI,WAAW;AACjB;AAAA,MACJ;AAAA,IACF;AAEA,YAAQ,UAAU,cAAc,SAAS,WAAW,QAAQ,KAAK,SAC/D,SAAS;AACT,YAAM,SAAS,CAAC;AAChB,aAAO,CAAC,OAAO,QAAQ,GAAG;AACxB,cAAM,cAAc,KAAK,SAAS,QAAQ,KAAK;AAC/C,YAAI,OAAO,QAAQ,WAAW;AAC5B,iBAAO;AAET,cAAM,MAAM,QAAQ,OAAO,QAAQ,OAAO,OAAO;AACjD,YAAI,OAAO,QAAQ,GAAG,KAAK;AACzB;AACF,eAAO,KAAK,GAAG;AAAA,MACjB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,aAAa,SAAS,UAAU,QAAQ,KAAK;AAC7D,UAAI,QAAQ,UAAU;AACpB,cAAM,SAAS,OAAO,UAAU;AAChC,YAAI,OAAO,QAAQ,MAAM;AACvB,iBAAO;AACT,eAAO,EAAE,QAAgB,MAAM,OAAO,IAAI,EAAE;AAAA,MAC9C,WAAW,QAAQ,UAAU;AAC3B,cAAM,MAAM,OAAO,IAAI;AACvB,YAAI,IAAI,SAAS,MAAM;AACrB,iBAAO,OAAO,MAAM,iDAAiD;AAEvE,YAAI,MAAM;AACV,iBAAS,IAAI,GAAG,IAAI,IAAI,SAAS,GAAG,KAAK;AACvC,iBAAO,OAAO,aAAa,IAAI,aAAa,IAAI,CAAC,CAAC;AAAA,QACpD;AACA,eAAO;AAAA,MACT,WAAW,QAAQ,UAAU;AAC3B,cAAM,SAAS,OAAO,IAAI,EAAE,SAAS,OAAO;AAC5C,YAAI,CAAC,KAAK,UAAU,MAAM,GAAG;AAC3B,iBAAO,OAAO,MAAM,wDAC+B;AAAA,QACrD;AACA,eAAO;AAAA,MACT,WAAW,QAAQ,UAAU;AAC3B,eAAO,OAAO,IAAI;AAAA,MACpB,WAAW,QAAQ,WAAW;AAC5B,eAAO,OAAO,IAAI;AAAA,MACpB,WAAW,QAAQ,YAAY;AAC7B,cAAM,WAAW,OAAO,IAAI,EAAE,SAAS,OAAO;AAC9C,YAAI,CAAC,KAAK,YAAY,QAAQ,GAAG;AAC/B,iBAAO,OAAO,MAAM,0DACiC;AAAA,QACvD;AACA,eAAO;AAAA,MACT,WAAW,OAAO,KAAK,GAAG,GAAG;AAC3B,eAAO,OAAO,IAAI,EAAE,SAAS;AAAA,MAC/B,OAAO;AACL,eAAO,OAAO,MAAM,8BAA8B,MAAM,cAAc;AAAA,MACxE;AAAA,IACF;AAEA,YAAQ,UAAU,eAAe,SAAS,YAAY,QAAQ,QAAQ,UAAU;AAC9E,UAAI;AACJ,YAAM,cAAc,CAAC;AACrB,UAAI,QAAQ;AACZ,UAAI,WAAW;AACf,aAAO,CAAC,OAAO,QAAQ,GAAG;AACxB,mBAAW,OAAO,UAAU;AAC5B,kBAAU;AACV,iBAAS,WAAW;AACpB,aAAK,WAAW,SAAU,GAAG;AAC3B,sBAAY,KAAK,KAAK;AACtB,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,WAAW;AACb,oBAAY,KAAK,KAAK;AAExB,YAAM,QAAS,YAAY,CAAC,IAAI,KAAM;AACtC,YAAM,SAAS,YAAY,CAAC,IAAI;AAEhC,UAAI;AACF,iBAAS;AAAA;AAET,iBAAS,CAAC,OAAO,MAAM,EAAE,OAAO,YAAY,MAAM,CAAC,CAAC;AAEtD,UAAI,QAAQ;AACV,YAAI,MAAM,OAAO,OAAO,KAAK,GAAG,CAAC;AACjC,YAAI,QAAQ;AACV,gBAAM,OAAO,OAAO,KAAK,GAAG,CAAC;AAC/B,YAAI,QAAQ;AACV,mBAAS;AAAA,MACb;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,cAAc,SAAS,WAAW,QAAQ,KAAK;AAC/D,YAAM,MAAM,OAAO,IAAI,EAAE,SAAS;AAElC,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI,QAAQ,WAAW;AACrB,eAAO,IAAI,MAAM,GAAG,CAAC,IAAI;AACzB,cAAM,IAAI,MAAM,GAAG,CAAC,IAAI;AACxB,cAAM,IAAI,MAAM,GAAG,CAAC,IAAI;AACxB,eAAO,IAAI,MAAM,GAAG,EAAE,IAAI;AAC1B,cAAM,IAAI,MAAM,IAAI,EAAE,IAAI;AAC1B,cAAM,IAAI,MAAM,IAAI,EAAE,IAAI;AAAA,MAC5B,WAAW,QAAQ,WAAW;AAC5B,eAAO,IAAI,MAAM,GAAG,CAAC,IAAI;AACzB,cAAM,IAAI,MAAM,GAAG,CAAC,IAAI;AACxB,cAAM,IAAI,MAAM,GAAG,CAAC,IAAI;AACxB,eAAO,IAAI,MAAM,GAAG,CAAC,IAAI;AACzB,cAAM,IAAI,MAAM,GAAG,EAAE,IAAI;AACzB,cAAM,IAAI,MAAM,IAAI,EAAE,IAAI;AAC1B,YAAI,OAAO;AACT,iBAAO,MAAO;AAAA;AAEd,iBAAO,OAAO;AAAA,MAClB,OAAO;AACL,eAAO,OAAO,MAAM,cAAc,MAAM,4BAA4B;AAAA,MACtE;AAEA,aAAO,KAAK,IAAI,MAAM,MAAM,GAAG,KAAK,MAAM,KAAK,KAAK,CAAC;AAAA,IACvD;AAEA,YAAQ,UAAU,cAAc,SAAS,aAAa;AACpD,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,cAAc,SAAS,WAAW,QAAQ;AAC1D,YAAM,MAAM,OAAO,UAAU;AAC7B,UAAI,OAAO,QAAQ,GAAG;AACpB,eAAO;AAAA;AAEP,eAAO,QAAQ;AAAA,IACnB;AAEA,YAAQ,UAAU,aAAa,SAAS,UAAU,QAAQ,QAAQ;AAEhE,YAAM,MAAM,OAAO,IAAI;AACvB,UAAI,MAAM,IAAI,OAAO,GAAG;AAExB,UAAI;AACF,cAAM,OAAO,IAAI,SAAS,EAAE,CAAC,KAAK;AAEpC,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,OAAO,SAAS,IAAI,QAAQ,KAAK;AACjD,UAAI,OAAO,WAAW;AACpB,iBAAS,OAAO,GAAG;AACrB,aAAO,OAAO,YAAY,KAAK,EAAE;AAAA,IACnC;AAIA,aAAS,aAAa,KAAK,MAAM;AAC/B,UAAI,MAAM,IAAI,UAAU,IAAI;AAC5B,UAAI,IAAI,QAAQ,GAAG;AACjB,eAAO;AAET,YAAM,MAAM,IAAI,SAAS,OAAO,CAAC;AACjC,YAAM,aAAa,MAAM,QAAU;AAGnC,WAAK,MAAM,QAAU,IAAM;AACzB,YAAI,MAAM;AACV,cAAM;AACN,gBAAQ,MAAM,SAAU,KAAM;AAC5B,gBAAM,IAAI,UAAU,IAAI;AACxB,cAAI,IAAI,QAAQ,GAAG;AACjB,mBAAO;AAET,kBAAQ;AACR,iBAAO,MAAM;AAAA,QACf;AAAA,MACF,OAAO;AACL,eAAO;AAAA,MACT;AACA,YAAM,SAAS,IAAI,IAAI,GAAG;AAE1B,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAEA,aAAS,aAAa,KAAK,WAAW,MAAM;AAC1C,UAAI,MAAM,IAAI,UAAU,IAAI;AAC5B,UAAI,IAAI,QAAQ,GAAG;AACjB,eAAO;AAGT,UAAI,CAAC,aAAa,QAAQ;AACxB,eAAO;AAGT,WAAK,MAAM,SAAU,GAAG;AAEtB,eAAO;AAAA,MACT;AAGA,YAAM,MAAM,MAAM;AAClB,UAAI,MAAM;AACR,eAAO,IAAI,MAAM,2BAA2B;AAE9C,YAAM;AACN,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,gBAAQ;AACR,cAAM,IAAI,IAAI,UAAU,IAAI;AAC5B,YAAI,IAAI,QAAQ,CAAC;AACf,iBAAO;AACT,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC9UA,IAAAC,eAAA;AAAA;AAAA;AAEA,QAAM,WAAW;AACjB,QAAM,SAAS,gBAAwB;AAEvC,QAAM,aAAa;AAEnB,aAAS,WAAW,QAAQ;AAC1B,iBAAW,KAAK,MAAM,MAAM;AAC5B,WAAK,MAAM;AAAA,IACb;AACA,aAAS,YAAY,UAAU;AAC/B,WAAO,UAAU;AAEjB,eAAW,UAAU,SAAS,SAAS,OAAO,MAAM,SAAS;AAC3D,YAAM,QAAQ,KAAK,SAAS,EAAE,MAAM,UAAU;AAE9C,YAAM,QAAQ,QAAQ,MAAM,YAAY;AAExC,YAAM,KAAK;AACX,UAAI,QAAQ;AACZ,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,cAAM,QAAQ,MAAM,CAAC,EAAE,MAAM,EAAE;AAC/B,YAAI,UAAU;AACZ;AAEF,YAAI,MAAM,CAAC,MAAM;AACf;AAEF,YAAI,UAAU,IAAI;AAChB,cAAI,MAAM,CAAC,MAAM;AACf;AACF,kBAAQ;AAAA,QACV,OAAO;AACL,cAAI,MAAM,CAAC,MAAM;AACf;AACF,gBAAM;AACN;AAAA,QACF;AAAA,MACF;AACA,UAAI,UAAU,MAAM,QAAQ;AAC1B,cAAM,IAAI,MAAM,gCAAgC,KAAK;AAEvD,YAAM,SAAS,MAAM,MAAM,QAAQ,GAAG,GAAG,EAAE,KAAK,EAAE;AAElD,aAAO,QAAQ,mBAAmB,EAAE;AAEpC,YAAM,QAAQ,OAAO,KAAK,QAAQ,QAAQ;AAC1C,aAAO,WAAW,UAAU,OAAO,KAAK,MAAM,OAAO,OAAO;AAAA,IAC9D;AAAA;AAAA;;;AClDA;AAAA;AAAA;AAEA,QAAM,WAAW;AAEjB,aAAS,MAAM;AACf,aAAS,MAAM;AAAA;AAAA;;;ACLf;AAAA;AAAA;AAEA,QAAM,WAAW;AACjB,QAAM,WAAW;AACjB,QAAM,WAAW;AAEjB,QAAM,MAAM;AAEZ,QAAI,SAAS,SAAS,OAAO,MAAM,MAAM;AACvC,aAAO,IAAI,OAAO,MAAM,IAAI;AAAA,IAC9B;AAEA,aAAS,OAAO,MAAM,MAAM;AAC1B,WAAK,OAAO;AACZ,WAAK,OAAO;AAEZ,WAAK,WAAW,CAAC;AACjB,WAAK,WAAW,CAAC;AAAA,IACnB;AAEA,WAAO,UAAU,eAAe,SAAS,YAAY,MAAM;AACzD,YAAM,OAAO,KAAK;AAElB,eAAS,UAAU,QAAQ;AACzB,aAAK,WAAW,QAAQ,IAAI;AAAA,MAC9B;AACA,eAAS,WAAW,IAAI;AACxB,gBAAU,UAAU,aAAa,SAAS,WAAW,QAAQC,OAAM;AACjE,aAAK,KAAK,MAAM,QAAQA,KAAI;AAAA,MAC9B;AAEA,aAAO,IAAI,UAAU,IAAI;AAAA,IAC3B;AAEA,WAAO,UAAU,cAAc,SAAS,YAAY,KAAK;AACvD,YAAM,OAAO;AAEb,UAAI,CAAC,KAAK,SAAS,eAAe,GAAG;AACnC,aAAK,SAAS,GAAG,IAAI,KAAK,aAAa,SAAS,GAAG,CAAC;AACtD,aAAO,KAAK,SAAS,GAAG;AAAA,IAC1B;AAEA,WAAO,UAAU,SAAS,SAAS,OAAO,MAAM,KAAK,SAAS;AAC5D,aAAO,KAAK,YAAY,GAAG,EAAE,OAAO,MAAM,OAAO;AAAA,IACnD;AAEA,WAAO,UAAU,cAAc,SAAS,YAAY,KAAK;AACvD,YAAM,OAAO;AAEb,UAAI,CAAC,KAAK,SAAS,eAAe,GAAG;AACnC,aAAK,SAAS,GAAG,IAAI,KAAK,aAAa,SAAS,GAAG,CAAC;AACtD,aAAO,KAAK,SAAS,GAAG;AAAA,IAC1B;AAEA,WAAO,UAAU,SAAS,SAAS,OAAO,MAAM,KAAoB,UAAU;AAC5E,aAAO,KAAK,YAAY,GAAG,EAAE,OAAO,MAAM,QAAQ;AAAA,IACpD;AAAA;AAAA;;;ACxDA;AAAA;AAAA;AAEA,QAAM,OAAO;AAEb,SAAK,WAAW,mBAAsB;AACtC,SAAK,gBAAgB,kBAAoB;AACzC,SAAK,gBAAgB,kBAAoB;AACzC,SAAK,OAAO;AAAA;AAAA;;;ACPZ;AAAA;AAAA;AAEA,QAAM,YAAY;AAGlB,cAAU,WAAW,SAAS,QAAQ,KAAK;AACzC,YAAM,MAAM,CAAC;AAEb,aAAO,KAAK,GAAG,EAAE,QAAQ,SAAS,KAAK;AAErC,aAAK,MAAM,MAAM;AACf,gBAAM,MAAM;AAEd,cAAM,QAAQ,IAAI,GAAG;AACrB,YAAI,KAAK,IAAI;AAAA,MACf,CAAC;AAED,aAAO;AAAA,IACT;AAEA,cAAU,MAAM;AAAA;AAAA;;;ACpBhB;AAAA;AAEA,QAAM,OAAO;AAEb,SAAK,SAAS;AAEd,SAAK,SAAS,cAAsB;AACpC,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;AAAA;AAAA;", "names": ["module", "exports", "comb10MulTo", "prime", "require_buffer", "state", "require_der", "header", "size", "out", "require_der", "require_pem", "name"]}