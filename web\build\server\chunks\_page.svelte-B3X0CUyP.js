import { p as push, P as stringify, O as escape_html, N as attr, M as ensure_array_like, Q as bind_props, q as pop } from './index3-CqUPEnZw.js';
import { S as SEO } from './SEO-UItXytUy.js';
import { H as HelpSidebar } from './HelpSidebar-DjmYKcdY.js';
import { H as HelpArticleCard } from './HelpArticleCard-CzuyIqVY.js';
import { P as PortableText } from './PortableText-BDnhGv-n.js';
import { formatDistanceToNow } from 'date-fns';
import { A as Arrow_left } from './arrow-left-DyZbJRhp.js';
import { C as Calendar } from './calendar-S3GMzTWi.js';
import { E as Eye } from './eye-B2tdw2__.js';
import 'clsx';
import './false-CRHihH2U.js';
import './accordion-trigger-DwieKZVA.js';
import './use-ref-by-id.svelte-BuOu7t9P.js';
import './index-DAbaXdpL.js';
import './_commonjsHelpers-BFTU3MAI.js';
import './use-id-CcFpwo20.js';
import './noop-n4I-x7yK.js';
import './utils-pWl1tgmi.js';
import 'tailwind-merge';
import './presence-layer-B0FVaAYL.js';
import './events-CUVXVLW9.js';
import './after-tick-BHyS0ZjN.js';
import './index-server-CezSOnuG.js';
import './context-oepKpCf5.js';
import './kbd-constants-Ch6RKbNZ.js';
import './use-roving-focus.svelte-BzQ2WziA.js';
import './is-mzPc4wSG.js';
import './chevron-down-xGjWLrZH.js';
import './Icon-A4vzmk-O.js';
import './scroll-area-Dn69zlyp.js';
import './use-debounce.svelte-gxToHznJ.js';
import './house-CyPv7nOm.js';
import './file-text-HttY5S5h.js';
import './credit-card-8KNeZIt3.js';
import './shield-BzJ8ZsQa.js';
import './circle-help-Bsq6Onfx.js';
import './card-D-TLkt4h.js';
import './card-description-CMuO6f9m.js';
import './card-footer-Bs6oLfVt.js';
import './card-header-BSbSWnCH.js';
import './card-title-DNJv4RN2.js';
import './badge-C9pSznab.js';
import './index-DjwFQdT_.js';
import './sanityClient-BQ6Z_2a-.js';
import '@sanity/client';
import './html-FW6Ia4bL.js';

function getCategoryName(slug) {
  const categoryMap = {
    "getting-started": "Getting Started",
    "auto-apply": "Using Auto Apply",
    "account-billing": "Account & Billing",
    troubleshooting: "Troubleshooting",
    "privacy-security": "Privacy & Security"
  };
  return categoryMap[slug] || slug;
}
function _page($$payload, $$props) {
  push();
  let data = $$props["data"];
  let updatedDate = "";
  updatedDate = data.article?.updatedAt ? formatDistanceToNow(new Date(data.article.updatedAt), { addSuffix: true }) : "Recently";
  SEO($$payload, {
    title: `${stringify(data.article.title)} | Help Center`,
    description: data.article.description,
    keywords: `help center, ${stringify(data.article.title.toLowerCase())}, ${stringify(data.article.category)}, support, guides`
  });
  $$payload.out += `<!----> <div class="container mx-auto px-4 py-12"><div class="grid gap-8 lg:grid-cols-4"><div class="lg:col-span-1">`;
  HelpSidebar($$payload, { categories: data.categories });
  $$payload.out += `<!----></div> <div class="lg:col-span-3"><div class="mb-6"><a href="/help" class="text-primary inline-flex items-center text-sm hover:underline">`;
  Arrow_left($$payload, { class: "mr-1 h-4 w-4" });
  $$payload.out += `<!----> Back to Help Center</a></div> <article><header class="mb-8"><h1 class="mb-4 text-3xl font-bold">${escape_html(data.article.title)}</h1> <div class="text-muted-foreground flex flex-wrap items-center gap-4 text-sm"><a${attr("href", `/help/category/${stringify(data.article.category)}`)} class="bg-primary/10 text-primary rounded-full px-3 py-1">${escape_html(data.article.category)}</a> <div class="flex items-center gap-1">`;
  Calendar($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> <span>Updated ${escape_html(updatedDate)}</span></div> <div class="flex items-center gap-1">`;
  Eye($$payload, { class: "h-4 w-4" });
  $$payload.out += `<!----> <span>${escape_html(data.article.viewCount)} view${escape_html(data.article.viewCount !== 1 ? "s" : "")}</span></div></div></header> <div class="prose prose-lg max-w-none">`;
  if (data.article.content) {
    $$payload.out += "<!--[-->";
    PortableText($$payload, { value: data.article.content });
  } else {
    $$payload.out += "<!--[!-->";
    $$payload.out += `<p class="bg-muted rounded-lg border p-4">This article doesn't have any content yet. Please check back later.</p>`;
  }
  $$payload.out += `<!--]--></div> `;
  if (data.article.tags && data.article.tags.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array = ensure_array_like(data.article.tags);
    $$payload.out += `<div class="mt-8"><h2 class="mb-2 text-lg font-semibold">Tags</h2> <div class="flex flex-wrap gap-2"><!--[-->`;
    for (let $$index = 0, $$length = each_array.length; $$index < $$length; $$index++) {
      let tag = each_array[$$index];
      $$payload.out += `<span class="bg-muted rounded-full px-3 py-1 text-sm">${escape_html(tag)}</span>`;
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (data.article.relatedArticles && data.article.relatedArticles.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_1 = ensure_array_like(data.article.relatedArticles);
    $$payload.out += `<div class="mt-12"><h2 class="mb-6 text-2xl font-semibold">Related Articles</h2> <div class="grid gap-6 md:grid-cols-2 xl:grid-cols-3"><!--[-->`;
    for (let $$index_1 = 0, $$length = each_array_1.length; $$index_1 < $$length; $$index_1++) {
      let article = each_array_1[$$index_1];
      HelpArticleCard($$payload, { article });
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--> `;
  if (data.categoryArticles && data.categoryArticles.length > 0) {
    $$payload.out += "<!--[-->";
    const each_array_2 = ensure_array_like(data.categoryArticles);
    $$payload.out += `<div class="mt-12"><h2 class="mb-6 text-2xl font-semibold">More in ${escape_html(getCategoryName(data.article.category))}</h2> <div class="grid gap-6 md:grid-cols-2 xl:grid-cols-3"><!--[-->`;
    for (let $$index_2 = 0, $$length = each_array_2.length; $$index_2 < $$length; $$index_2++) {
      let article = each_array_2[$$index_2];
      HelpArticleCard($$payload, { article });
    }
    $$payload.out += `<!--]--></div></div>`;
  } else {
    $$payload.out += "<!--[!-->";
  }
  $$payload.out += `<!--]--></article></div></div></div>`;
  bind_props($$props, { data });
  pop();
}

export { _page as default };
//# sourceMappingURL=_page.svelte-B3X0CUyP.js.map
