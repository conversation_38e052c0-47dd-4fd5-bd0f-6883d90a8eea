{"version": 3, "file": "UniversalDocumentViewer-Cv4LqmRL.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/UniversalDocumentViewer.js"], "sourcesContent": ["import { V as escape_html, y as pop, w as push } from \"./index3.js\";\nimport \"clsx\";\nimport { B as Button } from \"./button.js\";\nimport { S as Skeleton } from \"./skeleton.js\";\nimport { Z as Zoom_out, a as Zoom_in } from \"./zoom-out.js\";\nimport { R as Rotate_cw } from \"./rotate-cw.js\";\nimport { E as External_link } from \"./external-link.js\";\nimport { D as Download } from \"./download.js\";\nfunction UniversalDocumentViewer($$payload, $$props) {\n  push();\n  const { document } = $$props;\n  let loading = true;\n  let scale = 1;\n  let pdfDocument = null;\n  function getDocumentType() {\n    if (!document?.fileUrl) return \"unknown\";\n    const url = document.fileUrl.toLowerCase();\n    const fileName = document.fileName?.toLowerCase() || \"\";\n    if (url.match(/\\.(jpeg|jpg|gif|png|webp|svg|bmp|tiff)$/) || fileName.match(/\\.(jpeg|jpg|gif|png|webp|svg|bmp|tiff)$/) || document.type?.includes(\"image\")) {\n      return \"image\";\n    }\n    if (url.match(/\\.(pdf)$/) || fileName.match(/\\.(pdf)$/) || document.type?.includes(\"pdf\")) {\n      return \"pdf\";\n    }\n    if (url.match(/\\.(docx)$/) || fileName.match(/\\.(docx)$/) || document.type?.includes(\"docx\")) {\n      return \"docx\";\n    }\n    if (url.match(/\\.(doc)$/) || fileName.match(/\\.(doc)$/) || document.type?.includes(\"doc\")) {\n      return \"doc\";\n    }\n    if (url.match(/\\.(txt|md|rtf)$/) || fileName.match(/\\.(txt|md|rtf)$/) || document.type?.includes(\"text\")) {\n      return \"text\";\n    }\n    if (url.match(/\\.(xls|xlsx|ppt|pptx)$/) || fileName.match(/\\.(xls|xlsx|ppt|pptx)$/) || document.type?.includes(\"office\")) {\n      return \"office\";\n    }\n    if (url.match(/\\.(js|ts|html|css|json|xml|py|java|c|cpp|cs|go|rb|php)$/) || fileName.match(/\\.(js|ts|html|css|json|xml|py|java|c|cpp|cs|go|rb|php)$/) || document.type?.includes(\"code\")) {\n      return \"code\";\n    }\n    return \"generic\";\n  }\n  function downloadDocument() {\n    if (document?.fileUrl) {\n      window.open(document.fileUrl, \"_blank\");\n    }\n  }\n  function openInNewTab() {\n    if (document?.fileUrl) {\n      window.open(document.fileUrl, \"_blank\");\n    }\n  }\n  function zoomIn() {\n    scale += 0.2;\n    if (getDocumentType() === \"pdf\" && pdfDocument) ;\n  }\n  function zoomOut() {\n    if (scale <= 0.4) return;\n    scale -= 0.2;\n    if (getDocumentType() === \"pdf\" && pdfDocument) ;\n  }\n  function rotate() {\n  }\n  $$payload.out += `<div class=\"document-viewer flex h-full w-full flex-col overflow-hidden rounded-md border\"><div class=\"flex flex-1 items-center justify-center overflow-auto bg-gray-100\">`;\n  {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<div class=\"flex flex-col items-center justify-center p-4\">`;\n    Skeleton($$payload, { class: \"h-[400px] w-[300px] rounded-md\" });\n    $$payload.out += `<!----> <p class=\"mt-4 text-sm text-gray-500\">Loading document...</p></div>`;\n  }\n  $$payload.out += `<!--]--></div> <div class=\"flex items-center justify-between border-t bg-gray-50 p-2\"><div class=\"flex items-center space-x-2\"><span class=\"text-sm font-medium\">${escape_html(document?.label || \"Document\")}</span> <span class=\"text-xs text-gray-500\">${escape_html(document?.fileName || \"\")}</span> `;\n  {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--></div> <div class=\"flex items-center space-x-2\">`;\n  if (getDocumentType() === \"image\" || getDocumentType() === \"pdf\") {\n    $$payload.out += \"<!--[-->\";\n    Button($$payload, {\n      variant: \"outline\",\n      size: \"sm\",\n      onclick: zoomOut,\n      disabled: loading,\n      children: ($$payload2) => {\n        Zoom_out($$payload2, { class: \"h-4 w-4\" });\n      },\n      $$slots: { default: true }\n    });\n    $$payload.out += `<!----> `;\n    Button($$payload, {\n      variant: \"outline\",\n      size: \"sm\",\n      onclick: zoomIn,\n      disabled: loading,\n      children: ($$payload2) => {\n        Zoom_in($$payload2, { class: \"h-4 w-4\" });\n      },\n      $$slots: { default: true }\n    });\n    $$payload.out += `<!---->`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  if (getDocumentType() === \"image\") {\n    $$payload.out += \"<!--[-->\";\n    Button($$payload, {\n      variant: \"outline\",\n      size: \"sm\",\n      onclick: rotate,\n      disabled: loading,\n      children: ($$payload2) => {\n        Rotate_cw($$payload2, { class: \"h-4 w-4\" });\n      },\n      $$slots: { default: true }\n    });\n  } else {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  {\n    $$payload.out += \"<!--[!-->\";\n  }\n  $$payload.out += `<!--]--> `;\n  Button($$payload, {\n    variant: \"outline\",\n    size: \"sm\",\n    onclick: openInNewTab,\n    disabled: loading,\n    children: ($$payload2) => {\n      External_link($$payload2, { class: \"mr-1 h-4 w-4\" });\n      $$payload2.out += `<!----> Open`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----> `;\n  Button($$payload, {\n    variant: \"outline\",\n    size: \"sm\",\n    onclick: downloadDocument,\n    disabled: loading,\n    children: ($$payload2) => {\n      Download($$payload2, { class: \"mr-1 h-4 w-4\" });\n      $$payload2.out += `<!----> Download`;\n    },\n    $$slots: { default: true }\n  });\n  $$payload.out += `<!----></div></div></div>`;\n  pop();\n}\nexport {\n  UniversalDocumentViewer as U\n};\n"], "names": [], "mappings": ";;;;;;;;;AAQA,SAAS,uBAAuB,CAAC,SAAS,EAAE,OAAO,EAAE;AACrD,EAAE,IAAI,EAAE;AACR,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAO;AAC9B,EAAE,IAAI,OAAO,GAAG,IAAI;AACpB,EAAE,IAAI,KAAK,GAAG,CAAC;AACf,EAAE,IAAI,WAAW,GAAG,IAAI;AACxB,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,SAAS;AAC5C,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE;AAC9C,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE;AAC3D,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,yCAAyC,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,yCAAyC,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC/J,MAAM,OAAO,OAAO;AACpB;AACA,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC/F,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE;AAClG,MAAM,OAAO,MAAM;AACnB;AACA,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE;AAC/F,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC9G,MAAM,OAAO,MAAM;AACnB;AACA,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,wBAAwB,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,wBAAwB,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,EAAE;AAC9H,MAAM,OAAO,QAAQ;AACrB;AACA,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,yDAAyD,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,yDAAyD,CAAC,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC9L,MAAM,OAAO,MAAM;AACnB;AACA,IAAI,OAAO,SAAS;AACpB;AACA,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,IAAI,QAAQ,EAAE,OAAO,EAAE;AAC3B,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC7C;AACA;AACA,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,QAAQ,EAAE,OAAO,EAAE;AAC3B,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC;AAC7C;AACA;AACA,EAAE,SAAS,MAAM,GAAG;AACpB,IAAI,KAAK,IAAI,GAAG;AAChB,IAAI,IAAI,eAAe,EAAE,KAAK,KAAK,IAAI,WAAW,EAAE;AACpD;AACA,EAAE,SAAS,OAAO,GAAG;AACrB,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE;AACtB,IAAI,KAAK,IAAI,GAAG;AAChB,IAAI,IAAI,eAAe,EAAE,KAAK,KAAK,IAAI,WAAW,EAAE;AACpD;AACA,EAAE,SAAS,MAAM,GAAG;AACpB;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,0KAA0K,CAAC;AAC/L,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,2DAA2D,CAAC;AAClF,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,gCAAgC,EAAE,CAAC;AACpE,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,2EAA2E,CAAC;AAClG;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,iKAAiK,EAAE,WAAW,CAAC,QAAQ,EAAE,KAAK,IAAI,UAAU,CAAC,CAAC,4CAA4C,EAAE,WAAW,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC;AAC/T,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,wDAAwD,CAAC;AAC7E,EAAE,IAAI,eAAe,EAAE,KAAK,OAAO,IAAI,eAAe,EAAE,KAAK,KAAK,EAAE;AACpE,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,CAAC,SAAS,EAAE;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,QAAQ,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AAClD,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/B,IAAI,MAAM,CAAC,SAAS,EAAE;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACjD,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC9B,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,IAAI,eAAe,EAAE,KAAK,OAAO,EAAE;AACrC,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,MAAM,CAAC,SAAS,EAAE;AACtB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,QAAQ,EAAE,CAAC,UAAU,KAAK;AAChC,QAAQ,SAAS,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;AACnD,OAAO;AACP,MAAM,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC9B,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE;AACF,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AAC9B,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,OAAO,EAAE,YAAY;AACzB,IAAI,QAAQ,EAAE,OAAO;AACrB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,aAAa,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AAC1D,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;AACtC,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,MAAM,CAAC,SAAS,EAAE;AACpB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,IAAI,EAAE,IAAI;AACd,IAAI,OAAO,EAAE,gBAAgB;AAC7B,IAAI,QAAQ,EAAE,OAAO;AACrB,IAAI,QAAQ,EAAE,CAAC,UAAU,KAAK;AAC9B,MAAM,QAAQ,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC;AACrD,MAAM,UAAU,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI;AAC5B,GAAG,CAAC;AACJ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;AAC9C,EAAE,GAAG,EAAE;AACP;;;;"}