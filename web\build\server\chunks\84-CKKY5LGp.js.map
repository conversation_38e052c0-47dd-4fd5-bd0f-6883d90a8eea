{"version": 3, "file": "84-CKKY5LGp.js", "sources": ["../../../.svelte-kit/adapter-node/entries/pages/press/coverage/_page.server.ts.js", "../../../.svelte-kit/adapter-node/nodes/84.js"], "sourcesContent": ["import { c as client } from \"../../../../chunks/client2.js\";\nconst load = async () => {\n  try {\n    const newsCoveragePage = await client.fetch(`\n      *[_type == \"page\" && slug.current == \"press/coverage\"][0] {\n        title,\n        description,\n        content,\n        seo\n      }\n    `);\n    const newsCoverage = await client.fetch(`\n      *[_type == \"post\" && postType == \"news\"] | order(publishedAt desc) {\n        _id,\n        title,\n        publishedAt,\n        publicationName,\n        externalUrl,\n        excerpt\n      }\n    `);\n    return {\n      newsCoveragePage,\n      newsCoverage\n    };\n  } catch (error) {\n    console.error(\"Error loading news coverage data:\", error);\n    return {\n      newsCoveragePage: null,\n      newsCoverage: []\n    };\n  }\n};\nexport {\n  load\n};\n", "import * as server from '../entries/pages/press/coverage/_page.server.ts.js';\n\nexport const index = 84;\nlet component_cache;\nexport const component = async () => component_cache ??= (await import('../entries/pages/press/coverage/_page.svelte.js')).default;\nexport { server };\nexport const server_id = \"src/routes/press/coverage/+page.server.ts\";\nexport const imports = [\"_app/immutable/nodes/84.uj0bK2dL.js\",\"_app/immutable/chunks/BasJTneF.js\",\"_app/immutable/chunks/CGmarHxI.js\",\"_app/immutable/chunks/CgXBgsce.js\",\"_app/immutable/chunks/CIt1g2O9.js\",\"_app/immutable/chunks/CmxjS0TN.js\",\"_app/immutable/chunks/BwZiefMD.js\",\"_app/immutable/chunks/u21ee2wt.js\",\"_app/immutable/chunks/C3w0v0gR.js\",\"_app/immutable/chunks/B-Xjo-Yt.js\",\"_app/immutable/chunks/BIEMS98f.js\",\"_app/immutable/chunks/Btcx8l8F.js\",\"_app/immutable/chunks/C6g8ubaU.js\",\"_app/immutable/chunks/BMgaXnEE.js\",\"_app/immutable/chunks/DYwWIJ9y.js\",\"_app/immutable/chunks/w80wGXGd.js\",\"_app/immutable/chunks/DosGZj-c.js\",\"_app/immutable/chunks/CGtH72Kl.js\",\"_app/immutable/chunks/C1FmrZbK.js\",\"_app/immutable/chunks/zNKWipEG.js\",\"_app/immutable/chunks/BBa424ah.js\",\"_app/immutable/chunks/D4f2twK-.js\"];\nexport const stylesheets = [\"_app/immutable/assets/PortableText.DSHKgSkc.css\"];\nexport const fonts = [];\n"], "names": [], "mappings": ";;;AACA,MAAM,IAAI,GAAG,YAAY;AACzB,EAAE,IAAI;AACN,IAAI,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC;AACN,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC;AACN,IAAI,OAAO;AACX,MAAM,gBAAgB;AACtB,MAAM;AACN,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,IAAI,OAAO;AACX,MAAM,gBAAgB,EAAE,IAAI;AAC5B,MAAM,YAAY,EAAE;AACpB,KAAK;AACL;AACA,CAAC;;;;;;;AC9BW,MAAC,KAAK,GAAG;AACrB,IAAI,eAAe;AACP,MAAC,SAAS,GAAG,YAAY,eAAe,KAAK,CAAC,MAAM,OAAO,4BAAiD,CAAC,EAAE;AAE/G,MAAC,SAAS,GAAG;AACb,MAAC,OAAO,GAAG,CAAC,qCAAqC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC,CAAC,mCAAmC;AACryB,MAAC,WAAW,GAAG,CAAC,iDAAiD;AACjE,MAAC,KAAK,GAAG;;;;"}