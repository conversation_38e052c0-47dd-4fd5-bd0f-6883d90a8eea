{"version": 3, "file": "input-DF0gPqYN.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/input.js"], "sourcesContent": ["import { M as spread_attributes, T as clsx, N as bind_props, y as pop, w as push } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nfunction Input($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    value = void 0,\n    type,\n    files = void 0,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  if (type === \"file\") {\n    $$payload.out += \"<!--[-->\";\n    $$payload.out += `<input${spread_attributes(\n      {\n        \"data-slot\": \"input\",\n        class: clsx(cn(\"selection:bg-primary dark:bg-input/30 selection:text-primary-foreground border-input ring-offset-background placeholder:text-muted-foreground shadow-xs flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-2 text-sm font-medium outline-none transition-[color,box-shadow] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className)),\n        type: \"file\",\n        ...restProps\n      },\n      null\n    )}/>`;\n  } else {\n    $$payload.out += \"<!--[!-->\";\n    $$payload.out += `<input${spread_attributes(\n      {\n        \"data-slot\": \"input\",\n        class: clsx(cn(\"border-input bg-background selection:bg-primary dark:bg-input/30 selection:text-primary-foreground ring-offset-background placeholder:text-muted-foreground shadow-xs flex h-9 w-full min-w-0 rounded-md border px-3 py-1 text-base outline-none transition-[color,box-shadow] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\", \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", className)),\n        type,\n        value,\n        ...restProps\n      },\n      null\n    )}/>`;\n  }\n  $$payload.out += `<!--]-->`;\n  bind_props($$props, { ref, value, files });\n  pop();\n}\nexport {\n  Input as I\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,KAAK,CAAC,SAAS,EAAE,OAAO,EAAE;AACnC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,IAAI;AACR,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,IAAI,IAAI,KAAK,MAAM,EAAE;AACvB,IAAI,SAAS,CAAC,GAAG,IAAI,UAAU;AAC/B,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,iBAAiB;AAC/C,MAAM;AACN,QAAQ,WAAW,EAAE,OAAO;AAC5B,QAAQ,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,sVAAsV,EAAE,+EAA+E,EAAE,wGAAwG,EAAE,SAAS,CAAC,CAAC;AACrjB,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ,GAAG;AACX,OAAO;AACP,MAAM;AACN,KAAK,CAAC,EAAE,CAAC;AACT,GAAG,MAAM;AACT,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW;AAChC,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,iBAAiB;AAC/C,MAAM;AACN,QAAQ,WAAW,EAAE,OAAO;AAC5B,QAAQ,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,2UAA2U,EAAE,+EAA+E,EAAE,wGAAwG,EAAE,SAAS,CAAC,CAAC;AAC1iB,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,GAAG;AACX,OAAO;AACP,MAAM;AACN,KAAK,CAAC,EAAE,CAAC;AACT;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC7B,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AAC5C,EAAE,GAAG,EAAE;AACP;;;;"}