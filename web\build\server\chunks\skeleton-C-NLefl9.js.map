{"version": 3, "file": "skeleton-C-NLefl9.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/skeleton.js"], "sourcesContent": ["import { M as spread_attributes, T as clsx, N as bind_props, y as pop, w as push } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nfunction Skeleton($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  $$payload.out += `<div${spread_attributes(\n    {\n      \"data-slot\": \"skeleton\",\n      class: clsx(cn(\"bg-accent animate-pulse rounded-md\", className)),\n      ...restProps\n    },\n    null\n  )}></div>`;\n  bind_props($$props, { ref });\n  pop();\n}\nexport {\n  Skeleton as S\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,iBAAiB;AAC3C,IAAI;AACJ,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC;AACtE,MAAM,GAAG;AACT,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,OAAO,CAAC;AACZ,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,CAAC;AAC9B,EAAE,GAAG,EAAE;AACP;;;;"}