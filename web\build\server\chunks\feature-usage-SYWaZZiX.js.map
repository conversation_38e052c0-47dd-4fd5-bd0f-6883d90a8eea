{"version": 3, "file": "feature-usage-SYWaZZiX.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/feature-usage.js"], "sourcesContent": ["import { p as prisma } from \"./prisma.js\";\nimport { a as FEATURE_LIMITS, F as FEATURES } from \"./dynamic-registry.js\";\nimport { F as FeatureAccessLevel } from \"./features.js\";\nasync function featureTablesExist() {\n  try {\n    try {\n      await prisma.feature.findFirst({\n        select: { id: true },\n        take: 1\n      });\n    } catch {\n      console.log(\"Feature table does not exist\");\n      return false;\n    }\n    try {\n      await prisma.featureLimit.findFirst({\n        select: { id: true },\n        take: 1\n      });\n    } catch {\n      console.log(\"FeatureLimit table does not exist\");\n      return false;\n    }\n    try {\n      await prisma.featureUsage.findFirst({\n        select: { id: true },\n        take: 1\n      });\n      return true;\n    } catch (error) {\n      console.log(\"FeatureUsage table does not exist\", error);\n      return false;\n    }\n  } catch (error) {\n    console.error(\"Error checking if feature tables exist:\", error);\n    return false;\n  }\n}\nasync function incrementFeatureUsage(userId, featureId, limitId, amount = 1) {\n  return trackFeatureUsage(userId, featureId, limitId, amount);\n}\nasync function trackFeatureUsage(userId, featureId, limitId, amount = 1) {\n  const tablesExist = await featureTablesExist();\n  if (!tablesExist) {\n    console.warn(\"Feature usage tables do not exist yet\");\n    return {\n      id: \"temp\",\n      userId,\n      featureId,\n      limitId,\n      used: amount,\n      period: null,\n      updatedAt: /* @__PURE__ */ new Date()\n    };\n  }\n  const featureLimit = await prisma.featureLimit.findFirst({\n    where: {\n      id: limitId,\n      featureId\n    }\n  });\n  if (!featureLimit) {\n    const limit = FEATURE_LIMITS[limitId];\n    if (!limit) {\n      throw new Error(`Feature limit not found: ${limitId}`);\n    }\n    return handleFeatureUsageWithRegistry(userId, featureId, limitId, amount, limit);\n  }\n  let period = null;\n  if (featureLimit.type === \"monthly\") {\n    const now = /* @__PURE__ */ new Date();\n    period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, \"0\")}`;\n  } else if (featureLimit.type === \"yearly\") {\n    const now = /* @__PURE__ */ new Date();\n    period = `${now.getFullYear()}`;\n  }\n  let usage = await prisma.featureUsage.findFirst({\n    where: {\n      userId,\n      featureId,\n      limitId,\n      period\n    }\n  });\n  if (usage) {\n    usage = await prisma.featureUsage.update({\n      where: { id: usage.id },\n      data: {\n        used: usage.used + amount,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n  } else {\n    usage = await prisma.featureUsage.create({\n      data: {\n        userId,\n        featureId,\n        limitId,\n        used: amount,\n        period\n      }\n    });\n  }\n  return usage;\n}\nasync function handleFeatureUsageWithRegistry(userId, featureId, limitId, amount, limit) {\n  let period = null;\n  if (limit.type === \"monthly\") {\n    const now = /* @__PURE__ */ new Date();\n    period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, \"0\")}`;\n  } else if (limit.type === \"yearly\") {\n    const now = /* @__PURE__ */ new Date();\n    period = `${now.getFullYear()}`;\n  }\n  try {\n    const feature = await prisma.feature.findUnique({\n      where: { id: featureId }\n    });\n    if (!feature) {\n      const registryFeature = FEATURES.find((f) => f.id === featureId);\n      if (registryFeature) {\n        await prisma.feature.create({\n          data: {\n            id: registryFeature.id,\n            name: registryFeature.name,\n            description: registryFeature.description || \"\",\n            category: registryFeature.category || \"general\",\n            icon: registryFeature.icon || null,\n            beta: registryFeature.beta || false,\n            updatedAt: /* @__PURE__ */ new Date()\n          }\n        });\n      }\n    }\n    const featureLimit = await prisma.featureLimit.findUnique({\n      where: { id: limitId }\n    });\n    if (!featureLimit) {\n      await prisma.featureLimit.create({\n        data: {\n          id: limitId,\n          featureId,\n          name: limit.name,\n          description: limit.description ?? \"\",\n          defaultValue: limit.defaultValue.toString(),\n          type: limit.type,\n          unit: limit.unit ?? null,\n          resetDay: limit.resetDay ?? null\n        }\n      });\n    }\n  } catch (error) {\n    console.error(\"Error creating feature or limit:\", error);\n  }\n  let usage = await prisma.featureUsage.findFirst({\n    where: {\n      userId,\n      featureId,\n      limitId,\n      period\n    }\n  });\n  if (usage) {\n    usage = await prisma.featureUsage.update({\n      where: { id: usage.id },\n      data: {\n        used: usage.used + amount,\n        updatedAt: /* @__PURE__ */ new Date()\n      }\n    });\n  } else {\n    usage = await prisma.featureUsage.create({\n      data: {\n        userId,\n        featureId,\n        limitId,\n        used: amount,\n        period\n      }\n    });\n  }\n  return usage;\n}\nasync function hasReachedLimit(userId, featureId, limitId) {\n  if (process.env.NODE_ENV === \"development\" || process.env.VITE_DISABLE_FEATURE_LIMITS === \"true\") {\n    console.log(\"Development mode: Bypassing feature limit check\");\n    return false;\n  }\n  const tablesExist = await featureTablesExist();\n  if (!tablesExist) {\n    console.warn(\"Feature usage tables do not exist yet\");\n    return false;\n  }\n  const user = await prisma.user.findUnique({\n    where: { id: userId },\n    include: {\n      subscriptions: {\n        orderBy: { createdAt: \"desc\" },\n        take: 1,\n        include: {\n          plan: {\n            include: {\n              features: {\n                include: {\n                  limits: true\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  });\n  const currentPlan = user?.subscriptions[0]?.plan;\n  if (!currentPlan) {\n    return true;\n  }\n  const planFeature = currentPlan.features.find((pf) => pf.featureId === featureId);\n  if (!planFeature) {\n    return true;\n  }\n  const planLimit = planFeature.limits.find((pl) => pl.limitId === limitId);\n  if (!planLimit) {\n    return true;\n  }\n  const limit = parseInt(planLimit.value, 10);\n  if (limit === 0) {\n    return true;\n  }\n  const featureLimit = await prisma.featureLimit.findFirst({\n    where: {\n      id: limitId,\n      featureId\n    }\n  });\n  if (!featureLimit) {\n    throw new Error(`Feature limit not found: ${limitId}`);\n  }\n  let period = null;\n  if (featureLimit.type === \"monthly\") {\n    const now = /* @__PURE__ */ new Date();\n    period = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, \"0\")}`;\n  } else if (featureLimit.type === \"yearly\") {\n    const now = /* @__PURE__ */ new Date();\n    period = `${now.getFullYear()}`;\n  }\n  const usage = await prisma.featureUsage.findFirst({\n    where: {\n      userId,\n      featureId,\n      limitId,\n      period\n    }\n  });\n  if (!usage) {\n    return false;\n  }\n  return usage.used >= limit;\n}\nasync function getFeatureUsage(userId) {\n  try {\n    let getLimitValue = function(usage, currentPlan2) {\n      const planFeature = currentPlan2?.features?.find(\n        (pf) => pf.featureId === usage.featureId\n      );\n      const planLimit = planFeature?.limits?.find((pl) => pl.limitId === usage.limitId);\n      if (planLimit) {\n        return parseInt(planLimit.value, 10);\n      }\n      const feature = FEATURES.find((f) => f.id === usage.featureId);\n      if (feature?.limits) {\n        const limitId = usage.limitId;\n        const registryLimitId = feature.limits.find(\n          (l) => typeof l === \"string\" ? l === limitId : l.id === limitId\n        );\n        if (registryLimitId && typeof registryLimitId === \"string\") {\n          const featureLimit = FEATURE_LIMITS[registryLimitId];\n          if (featureLimit) {\n            if (typeof featureLimit.defaultValue === \"number\") {\n              return featureLimit.defaultValue;\n            } else if (featureLimit.defaultValue === \"unlimited\") {\n              return null;\n            } else {\n              return parseInt(String(featureLimit.defaultValue), 10);\n            }\n          }\n        }\n      }\n      return null;\n    }, getUsageMetrics = function(limit, used) {\n      const remaining = limit !== null ? Math.max(0, limit - used) : null;\n      const percentUsed = limit !== null ? Math.min(100, used / limit * 100) : null;\n      return { remaining, percentUsed };\n    };\n    const tablesExist = await featureTablesExist();\n    if (!tablesExist) {\n      console.warn(\"Feature usage tables do not exist yet\");\n      return [];\n    }\n    let featureUsage = [];\n    try {\n      const usageRecords = await prisma.featureUsage.findMany({\n        where: {\n          userId\n        }\n      });\n      console.log(`Found ${usageRecords.length} usage records`);\n      if (usageRecords.length === 0) {\n        console.log(\"No usage records found for user, but tables exist\");\n        try {\n          const features2 = await prisma.feature.findMany({\n            include: {\n              limits: true\n            }\n          });\n          if (features2.length > 0) {\n            console.log(`Found ${features2.length} features to show in UI`);\n            const placeholderUsage = features2.flatMap(\n              (feature) => feature.limits.map((limit) => ({\n                id: `placeholder-${feature.id}-${limit.id}`,\n                featureId: feature.id,\n                featureName: feature.name,\n                limitId: limit.id,\n                limitName: limit.name,\n                used: 0,\n                limit: parseInt(limit.defaultValue, 10) || null,\n                remaining: parseInt(limit.defaultValue, 10) || null,\n                percentUsed: 0,\n                period: (/* @__PURE__ */ new Date()).toISOString().substring(0, 7),\n                // YYYY-MM\n                updatedAt: /* @__PURE__ */ new Date(),\n                placeholder: true\n              }))\n            );\n            return placeholderUsage;\n          }\n        } catch (featuresError) {\n          console.error(\"Error getting features for placeholder usage:\", featuresError);\n        }\n        return [];\n      }\n      featureUsage = usageRecords;\n      const featureIds = [...new Set(featureUsage.map((u) => u.featureId))];\n      const limitIds = [...new Set(featureUsage.map((u) => u.limitId))];\n      let features = [];\n      let limits = [];\n      try {\n        features = await prisma.feature.findMany({\n          where: {\n            id: {\n              in: featureIds\n            }\n          }\n        });\n        console.log(`Found ${features.length} features`);\n      } catch (featureError) {\n        console.error(\"Error fetching features:\", featureError);\n      }\n      try {\n        limits = await prisma.featureLimit.findMany({\n          where: {\n            id: {\n              in: limitIds\n            }\n          }\n        });\n        console.log(`Found ${limits.length} limits`);\n      } catch (limitError) {\n        console.error(\"Error fetching limits:\", limitError);\n      }\n      featureUsage = featureUsage.map((usage) => {\n        const feature = features.find((f) => f.id === usage.featureId);\n        const limit = limits.find((l) => l.id === usage.limitId);\n        let featureName = \"Unknown Feature\";\n        let limitName = \"Unknown Limit\";\n        if (!feature) {\n          const registryFeature = FEATURES.find((f) => f.id === usage.featureId);\n          if (registryFeature) {\n            featureName = registryFeature.name;\n          }\n        }\n        if (!limit) {\n          const registryFeature = FEATURES.find((f) => f.id === usage.featureId);\n          if (registryFeature?.limits) {\n            const limitId = usage.limitId;\n            const registryLimitId = registryFeature.limits.find(\n              (l) => typeof l === \"string\" ? l === limitId : l.id === limitId\n            );\n            if (registryLimitId && typeof registryLimitId === \"string\" && FEATURE_LIMITS[registryLimitId]) {\n              limitName = FEATURE_LIMITS[registryLimitId].name;\n            }\n          }\n        }\n        return {\n          ...usage,\n          feature: feature ?? { name: featureName },\n          limit: limit ?? { name: limitName }\n        };\n      });\n    } catch (error) {\n      console.error(\"Error fetching feature usage:\", error);\n      return [];\n    }\n    let currentPlan = null;\n    try {\n      const userWithSubscriptions = await prisma.user.findUnique({\n        where: { id: userId },\n        include: {\n          subscriptions: {\n            orderBy: { createdAt: \"desc\" },\n            take: 1,\n            include: {\n              plan: true\n            }\n          }\n        }\n      });\n      if (userWithSubscriptions?.subscriptions && userWithSubscriptions.subscriptions.length > 0) {\n        const subscription = userWithSubscriptions.subscriptions[0];\n        if (subscription.plan) {\n          const plan = await prisma.plan.findUnique({\n            where: { id: subscription.planId },\n            include: {\n              features: {\n                include: {\n                  limits: true\n                }\n              }\n            }\n          });\n          currentPlan = plan;\n        }\n      }\n    } catch (error) {\n      console.error(\"Error fetching user subscription data:\", error);\n    }\n    return featureUsage.map((usage) => {\n      const limit = getLimitValue(usage, currentPlan);\n      const { remaining, percentUsed } = getUsageMetrics(limit, usage.used);\n      return {\n        id: usage.id,\n        featureId: usage.featureId,\n        featureName: usage.feature?.name ?? \"Unknown Feature\",\n        limitId: usage.limitId,\n        limitName: usage.limit?.name ?? \"Unknown Limit\",\n        used: usage.used,\n        limit,\n        remaining,\n        percentUsed,\n        period: usage.period,\n        updatedAt: usage.updatedAt\n      };\n    });\n  } catch (error) {\n    console.error(\"Error in getFeatureUsage:\", error);\n    return [];\n  }\n}\nasync function getUserFeatureUsageWithPlanLimits(userId) {\n  try {\n    const tablesExist = await featureTablesExist();\n    if (!tablesExist) {\n      console.warn(\"Feature usage tables do not exist yet\");\n      return {\n        features: [],\n        plan: null,\n        subscription: null\n      };\n    }\n    const user = await prisma.user.findUnique({\n      where: { id: userId },\n      include: {\n        subscriptions: {\n          orderBy: { createdAt: \"desc\" },\n          take: 1,\n          include: {\n            plan: true\n          }\n        },\n        featureUsage: true\n      }\n    });\n    if (!user) {\n      throw new Error(`User not found: ${userId}`);\n    }\n    const subscription = user.subscriptions[0] || null;\n    let plan = null;\n    if (subscription?.plan) {\n      const dbPlan = await prisma.plan.findUnique({\n        where: { id: subscription.planId },\n        include: {\n          features: {\n            include: {\n              limits: true\n            }\n          }\n        }\n      });\n      if (dbPlan) {\n        plan = {\n          id: dbPlan.id,\n          name: dbPlan.name,\n          description: dbPlan.description,\n          section: dbPlan.section,\n          monthlyPrice: dbPlan.monthlyPrice,\n          annualPrice: dbPlan.annualPrice,\n          stripePriceMonthlyId: dbPlan.stripePriceMonthlyId || void 0,\n          stripePriceYearlyId: dbPlan.stripePriceYearlyId || void 0,\n          popular: dbPlan.popular,\n          features: dbPlan.features.map((feature) => ({\n            featureId: feature.featureId,\n            accessLevel: feature.accessLevel,\n            limits: feature.limits.map((limit) => ({\n              limitId: limit.limitId,\n              value: limit.value === \"unlimited\" ? \"unlimited\" : parseInt(limit.value, 10)\n            }))\n          }))\n        };\n      }\n    }\n    const dbFeatures = await prisma.feature.findMany({\n      include: {\n        limits: true\n      }\n    });\n    const usageRecords = user.featureUsage;\n    const usageMap = /* @__PURE__ */ new Map();\n    usageRecords.forEach((usage) => {\n      const key = `${usage.featureId}:${usage.limitId}`;\n      usageMap.set(key, usage);\n    });\n    const featuresWithUsage = dbFeatures.map((feature) => {\n      const planFeature = plan?.features.find((pf) => pf.featureId === feature.id);\n      const accessLevel = planFeature?.accessLevel || FeatureAccessLevel.NotIncluded;\n      const limitsWithUsage = feature.limits.map((limit) => {\n        const planLimit = planFeature?.limits?.find((pl) => pl.limitId === limit.id);\n        const limitValue = planLimit?.value || (limit.defaultValue === \"unlimited\" ? \"unlimited\" : parseInt(limit.defaultValue, 10));\n        const usageKey = `${feature.id}:${limit.id}`;\n        const usage = usageMap.get(usageKey);\n        const used = usage?.used ?? 0;\n        const remaining = typeof limitValue === \"number\" ? Math.max(0, limitValue - used) : null;\n        const percentUsed = typeof limitValue === \"number\" ? Math.min(100, used / limitValue * 100) : null;\n        return {\n          id: limit.id,\n          name: limit.name,\n          description: limit.description,\n          type: limit.type,\n          unit: limit.unit,\n          resetDay: limit.resetDay,\n          value: limitValue,\n          used,\n          remaining,\n          percentUsed,\n          period: usage?.period ?? null,\n          lastUpdated: usage?.updatedAt ?? null\n        };\n      });\n      return {\n        id: feature.id,\n        name: feature.name,\n        description: feature.description,\n        category: feature.category,\n        icon: feature.icon,\n        beta: feature.beta,\n        accessLevel,\n        limits: limitsWithUsage\n      };\n    });\n    let userSubscription = null;\n    if (subscription) {\n      userSubscription = {\n        planId: subscription.planId,\n        startDate: subscription.currentPeriodStart || subscription.createdAt,\n        endDate: subscription.currentPeriodEnd || void 0,\n        cancelAtPeriodEnd: subscription.cancelAtPeriodEnd,\n        status: subscription.status,\n        trialEndDate: void 0\n        // Add this if available\n      };\n    }\n    return {\n      features: featuresWithUsage,\n      plan,\n      subscription: userSubscription\n    };\n  } catch (error) {\n    console.error(\"Error getting user feature usage with plan limits:\", error);\n    return {\n      features: [],\n      plan: null,\n      subscription: null,\n      error: error.message\n    };\n  }\n}\nexport {\n  featureTablesExist,\n  getFeatureUsage,\n  getUserFeatureUsageWithPlanLimits,\n  hasReachedLimit,\n  incrementFeatureUsage,\n  trackFeatureUsage\n};\n"], "names": [], "mappings": ";;;;;;;AAGA,eAAe,kBAAkB,GAAG;AACpC,EAAE,IAAI;AACN,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC;AACrC,QAAQ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;AAC5B,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,KAAK,CAAC,MAAM;AACZ,MAAM,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC;AACjD,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAC1C,QAAQ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;AAC5B,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,KAAK,CAAC,MAAM;AACZ,MAAM,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC;AACtD,MAAM,OAAO,KAAK;AAClB;AACA,IAAI,IAAI;AACR,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAC1C,QAAQ,MAAM,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE;AAC5B,QAAQ,IAAI,EAAE;AACd,OAAO,CAAC;AACR,MAAM,OAAO,IAAI;AACjB,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,KAAK,CAAC;AAC7D,MAAM,OAAO,KAAK;AAClB;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC;AACnE,IAAI,OAAO,KAAK;AAChB;AACA;AACA,eAAe,qBAAqB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE;AAC7E,EAAE,OAAO,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,CAAC;AAC9D;AACA,eAAe,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE;AACzE,EAAE,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAChD,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC;AACzD,IAAI,OAAO;AACX,MAAM,EAAE,EAAE,MAAM;AAChB,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,SAAS,kBAAkB,IAAI,IAAI;AACzC,KAAK;AACL;AACA,EAAE,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAC3D,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM;AACN;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,YAAY,EAAE;AACrB,IAAI,MAAM,KAAK,GAAG,cAAc,CAAC,OAAO,CAAC;AACzC,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,CAAC;AAC5D;AACA,IAAI,OAAO,8BAA8B,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC;AACpF;AACA,EAAE,IAAI,MAAM,GAAG,IAAI;AACnB,EAAE,IAAI,YAAY,CAAC,IAAI,KAAK,SAAS,EAAE;AACvC,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAClF,GAAG,MAAM,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC7C,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;AACnC;AACA,EAAE,IAAI,KAAK,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAClD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM;AACN;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,KAAK,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE;AAC7B,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI,GAAG,MAAM;AACjC,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM;AACd,QAAQ,SAAS;AACjB,QAAQ,OAAO;AACf,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ;AACR;AACA,KAAK,CAAC;AACN;AACA,EAAE,OAAO,KAAK;AACd;AACA,eAAe,8BAA8B,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE;AACzF,EAAE,IAAI,MAAM,GAAG,IAAI;AACnB,EAAE,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,EAAE;AAChC,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAClF,GAAG,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;AACtC,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;AACnC;AACA,EAAE,IAAI;AACN,IAAI,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;AACpD,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS;AAC5B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC;AACtE,MAAM,IAAI,eAAe,EAAE;AAC3B,QAAQ,MAAM,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AACpC,UAAU,IAAI,EAAE;AAChB,YAAY,EAAE,EAAE,eAAe,CAAC,EAAE;AAClC,YAAY,IAAI,EAAE,eAAe,CAAC,IAAI;AACtC,YAAY,WAAW,EAAE,eAAe,CAAC,WAAW,IAAI,EAAE;AAC1D,YAAY,QAAQ,EAAE,eAAe,CAAC,QAAQ,IAAI,SAAS;AAC3D,YAAY,IAAI,EAAE,eAAe,CAAC,IAAI,IAAI,IAAI;AAC9C,YAAY,IAAI,EAAE,eAAe,CAAC,IAAI,IAAI,KAAK;AAC/C,YAAY,SAAS,kBAAkB,IAAI,IAAI;AAC/C;AACA,SAAS,CAAC;AACV;AACA;AACA,IAAI,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;AAC9D,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO;AAC1B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AACvC,QAAQ,IAAI,EAAE;AACd,UAAU,EAAE,EAAE,OAAO;AACrB,UAAU,SAAS;AACnB,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI;AAC1B,UAAU,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;AAC9C,UAAU,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE;AACrD,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI;AAC1B,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI;AAClC,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI;AACtC;AACA,OAAO,CAAC;AACR;AACA,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC;AAC5D;AACA,EAAE,IAAI,KAAK,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAClD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM;AACN;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,KAAK,EAAE;AACb,IAAI,KAAK,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE;AAC7B,MAAM,IAAI,EAAE;AACZ,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI,GAAG,MAAM;AACjC,QAAQ,SAAS,kBAAkB,IAAI,IAAI;AAC3C;AACA,KAAK,CAAC;AACN,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC;AAC7C,MAAM,IAAI,EAAE;AACZ,QAAQ,MAAM;AACd,QAAQ,SAAS;AACjB,QAAQ,OAAO;AACf,QAAQ,IAAI,EAAE,MAAM;AACpB,QAAQ;AACR;AACA,KAAK,CAAC;AACN;AACA,EAAE,OAAO,KAAK;AACd;AACA,eAAe,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE;AAC3D,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B,KAAK,MAAM,EAAE;AACpG,IAAI,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC;AAClE,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAChD,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC;AACzD,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC5C,IAAI,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AACzB,IAAI,OAAO,EAAE;AACb,MAAM,aAAa,EAAE;AACrB,QAAQ,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACtC,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,OAAO,EAAE;AACjB,UAAU,IAAI,EAAE;AAChB,YAAY,OAAO,EAAE;AACrB,cAAc,QAAQ,EAAE;AACxB,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,MAAM,EAAE;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,CAAC;AACJ,EAAE,MAAM,WAAW,GAAG,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI;AAClD,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,OAAO,IAAI;AACf;AACA,EAAE,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,KAAK,SAAS,CAAC;AACnF,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,OAAO,IAAI;AACf;AACA,EAAE,MAAM,SAAS,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO,KAAK,OAAO,CAAC;AAC3E,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,OAAO,IAAI;AACf;AACA,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,CAAC;AAC7C,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE;AACnB,IAAI,OAAO,IAAI;AACf;AACA,EAAE,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AAC3D,IAAI,KAAK,EAAE;AACX,MAAM,EAAE,EAAE,OAAO;AACjB,MAAM;AACN;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,YAAY,EAAE;AACrB,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,CAAC;AAC1D;AACA,EAAE,IAAI,MAAM,GAAG,IAAI;AACnB,EAAE,IAAI,YAAY,CAAC,IAAI,KAAK,SAAS,EAAE;AACvC,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAClF,GAAG,MAAM,IAAI,YAAY,CAAC,IAAI,KAAK,QAAQ,EAAE;AAC7C,IAAI,MAAM,GAAG,mBAAmB,IAAI,IAAI,EAAE;AAC1C,IAAI,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;AACnC;AACA,EAAE,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC;AACpD,IAAI,KAAK,EAAE;AACX,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM;AACN;AACA,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO,KAAK;AAChB;AACA,EAAE,OAAO,KAAK,CAAC,IAAI,IAAI,KAAK;AAC5B;AACA,eAAe,eAAe,CAAC,MAAM,EAAE;AACvC,EAAE,IAAI;AACN,IAAI,IAAI,aAAa,GAAG,SAAS,KAAK,EAAE,YAAY,EAAE;AACtD,MAAM,MAAM,WAAW,GAAG,YAAY,EAAE,QAAQ,EAAE,IAAI;AACtD,QAAQ,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,KAAK,KAAK,CAAC;AACvC,OAAO;AACP,MAAM,MAAM,SAAS,GAAG,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC;AACvF,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,OAAO,QAAQ,CAAC,SAAS,CAAC,KAAK,EAAE,EAAE,CAAC;AAC5C;AACA,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;AACpE,MAAM,IAAI,OAAO,EAAE,MAAM,EAAE;AAC3B,QAAQ,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AACrC,QAAQ,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,IAAI;AACnD,UAAU,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,QAAQ,GAAG,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,EAAE,KAAK;AAClE,SAAS;AACT,QAAQ,IAAI,eAAe,IAAI,OAAO,eAAe,KAAK,QAAQ,EAAE;AACpE,UAAU,MAAM,YAAY,GAAG,cAAc,CAAC,eAAe,CAAC;AAC9D,UAAU,IAAI,YAAY,EAAE;AAC5B,YAAY,IAAI,OAAO,YAAY,CAAC,YAAY,KAAK,QAAQ,EAAE;AAC/D,cAAc,OAAO,YAAY,CAAC,YAAY;AAC9C,aAAa,MAAM,IAAI,YAAY,CAAC,YAAY,KAAK,WAAW,EAAE;AAClE,cAAc,OAAO,IAAI;AACzB,aAAa,MAAM;AACnB,cAAc,OAAO,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC;AACpE;AACA;AACA;AACA;AACA,MAAM,OAAO,IAAI;AACjB,KAAK,EAAE,eAAe,GAAG,SAAS,KAAK,EAAE,IAAI,EAAE;AAC/C,MAAM,MAAM,SAAS,GAAG,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI;AACzE,MAAM,MAAM,WAAW,GAAG,KAAK,KAAK,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,KAAK,GAAG,GAAG,CAAC,GAAG,IAAI;AACnF,MAAM,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE;AACvC,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAClD,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC;AAC3D,MAAM,OAAO,EAAE;AACf;AACA,IAAI,IAAI,YAAY,GAAG,EAAE;AACzB,IAAI,IAAI;AACR,MAAM,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AAC9D,QAAQ,KAAK,EAAE;AACf,UAAU;AACV;AACA,OAAO,CAAC;AACR,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;AAC/D,MAAM,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AACrC,QAAQ,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC;AACxE,QAAQ,IAAI;AACZ,UAAU,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AAC1D,YAAY,OAAO,EAAE;AACrB,cAAc,MAAM,EAAE;AACtB;AACA,WAAW,CAAC;AACZ,UAAU,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC;AAC3E,YAAY,MAAM,gBAAgB,GAAG,SAAS,CAAC,OAAO;AACtD,cAAc,CAAC,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM;AAC1D,gBAAgB,EAAE,EAAE,CAAC,YAAY,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AAC3D,gBAAgB,SAAS,EAAE,OAAO,CAAC,EAAE;AACrC,gBAAgB,WAAW,EAAE,OAAO,CAAC,IAAI;AACzC,gBAAgB,OAAO,EAAE,KAAK,CAAC,EAAE;AACjC,gBAAgB,SAAS,EAAE,KAAK,CAAC,IAAI;AACrC,gBAAgB,IAAI,EAAE,CAAC;AACvB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,IAAI,IAAI;AAC/D,gBAAgB,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,IAAI,IAAI;AACnE,gBAAgB,WAAW,EAAE,CAAC;AAC9B,gBAAgB,MAAM,EAAE,iBAAiB,IAAI,IAAI,EAAE,EAAE,WAAW,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;AAClF;AACA,gBAAgB,SAAS,kBAAkB,IAAI,IAAI,EAAE;AACrD,gBAAgB,WAAW,EAAE;AAC7B,eAAe,CAAC;AAChB,aAAa;AACb,YAAY,OAAO,gBAAgB;AACnC;AACA,SAAS,CAAC,OAAO,aAAa,EAAE;AAChC,UAAU,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,aAAa,CAAC;AACvF;AACA,QAAQ,OAAO,EAAE;AACjB;AACA,MAAM,YAAY,GAAG,YAAY;AACjC,MAAM,MAAM,UAAU,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;AAC3E,MAAM,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;AACvE,MAAM,IAAI,QAAQ,GAAG,EAAE;AACvB,MAAM,IAAI,MAAM,GAAG,EAAE;AACrB,MAAM,IAAI;AACV,QAAQ,QAAQ,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACjD,UAAU,KAAK,EAAE;AACjB,YAAY,EAAE,EAAE;AAChB,cAAc,EAAE,EAAE;AAClB;AACA;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACxD,OAAO,CAAC,OAAO,YAAY,EAAE;AAC7B,QAAQ,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,YAAY,CAAC;AAC/D;AACA,MAAM,IAAI;AACV,QAAQ,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC;AACpD,UAAU,KAAK,EAAE;AACjB,YAAY,EAAE,EAAE;AAChB,cAAc,EAAE,EAAE;AAClB;AACA;AACA,SAAS,CAAC;AACV,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACpD,OAAO,CAAC,OAAO,UAAU,EAAE;AAC3B,QAAQ,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,UAAU,CAAC;AAC3D;AACA,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AACjD,QAAQ,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;AACtE,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC;AAChE,QAAQ,IAAI,WAAW,GAAG,iBAAiB;AAC3C,QAAQ,IAAI,SAAS,GAAG,eAAe;AACvC,QAAQ,IAAI,CAAC,OAAO,EAAE;AACtB,UAAU,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;AAChF,UAAU,IAAI,eAAe,EAAE;AAC/B,YAAY,WAAW,GAAG,eAAe,CAAC,IAAI;AAC9C;AACA;AACA,QAAQ,IAAI,CAAC,KAAK,EAAE;AACpB,UAAU,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;AAChF,UAAU,IAAI,eAAe,EAAE,MAAM,EAAE;AACvC,YAAY,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;AACzC,YAAY,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,IAAI;AAC/D,cAAc,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,QAAQ,GAAG,CAAC,KAAK,OAAO,GAAG,CAAC,CAAC,EAAE,KAAK;AACtE,aAAa;AACb,YAAY,IAAI,eAAe,IAAI,OAAO,eAAe,KAAK,QAAQ,IAAI,cAAc,CAAC,eAAe,CAAC,EAAE;AAC3G,cAAc,SAAS,GAAG,cAAc,CAAC,eAAe,CAAC,CAAC,IAAI;AAC9D;AACA;AACA;AACA,QAAQ,OAAO;AACf,UAAU,GAAG,KAAK;AAClB,UAAU,OAAO,EAAE,OAAO,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE;AACnD,UAAU,KAAK,EAAE,KAAK,IAAI,EAAE,IAAI,EAAE,SAAS;AAC3C,SAAS;AACT,OAAO,CAAC;AACR,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC;AAC3D,MAAM,OAAO,EAAE;AACf;AACA,IAAI,IAAI,WAAW,GAAG,IAAI;AAC1B,IAAI,IAAI;AACR,MAAM,MAAM,qBAAqB,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACjE,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC7B,QAAQ,OAAO,EAAE;AACjB,UAAU,aAAa,EAAE;AACzB,YAAY,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AAC1C,YAAY,IAAI,EAAE,CAAC;AACnB,YAAY,OAAO,EAAE;AACrB,cAAc,IAAI,EAAE;AACpB;AACA;AACA;AACA,OAAO,CAAC;AACR,MAAM,IAAI,qBAAqB,EAAE,aAAa,IAAI,qBAAqB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAClG,QAAQ,MAAM,YAAY,GAAG,qBAAqB,CAAC,aAAa,CAAC,CAAC,CAAC;AACnE,QAAQ,IAAI,YAAY,CAAC,IAAI,EAAE;AAC/B,UAAU,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AACpD,YAAY,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,MAAM,EAAE;AAC9C,YAAY,OAAO,EAAE;AACrB,cAAc,QAAQ,EAAE;AACxB,gBAAgB,OAAO,EAAE;AACzB,kBAAkB,MAAM,EAAE;AAC1B;AACA;AACA;AACA,WAAW,CAAC;AACZ,UAAU,WAAW,GAAG,IAAI;AAC5B;AACA;AACA,KAAK,CAAC,OAAO,KAAK,EAAE;AACpB,MAAM,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC;AACpE;AACA,IAAI,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AACvC,MAAM,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC;AACrD,MAAM,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC;AAC3E,MAAM,OAAO;AACb,QAAQ,EAAE,EAAE,KAAK,CAAC,EAAE;AACpB,QAAQ,SAAS,EAAE,KAAK,CAAC,SAAS;AAClC,QAAQ,WAAW,EAAE,KAAK,CAAC,OAAO,EAAE,IAAI,IAAI,iBAAiB;AAC7D,QAAQ,OAAO,EAAE,KAAK,CAAC,OAAO;AAC9B,QAAQ,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,IAAI,eAAe;AACvD,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI;AACxB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,WAAW;AACnB,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,QAAQ,SAAS,EAAE,KAAK,CAAC;AACzB,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC;AACrD,IAAI,OAAO,EAAE;AACb;AACA;AACA,eAAe,iCAAiC,CAAC,MAAM,EAAE;AACzD,EAAE,IAAI;AACN,IAAI,MAAM,WAAW,GAAG,MAAM,kBAAkB,EAAE;AAClD,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC;AAC3D,MAAM,OAAO;AACb,QAAQ,QAAQ,EAAE,EAAE;AACpB,QAAQ,IAAI,EAAE,IAAI;AAClB,QAAQ,YAAY,EAAE;AACtB,OAAO;AACP;AACA,IAAI,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,MAAM,KAAK,EAAE,EAAE,EAAE,EAAE,MAAM,EAAE;AAC3B,MAAM,OAAO,EAAE;AACf,QAAQ,aAAa,EAAE;AACvB,UAAU,OAAO,EAAE,EAAE,SAAS,EAAE,MAAM,EAAE;AACxC,UAAU,IAAI,EAAE,CAAC;AACjB,UAAU,OAAO,EAAE;AACnB,YAAY,IAAI,EAAE;AAClB;AACA,SAAS;AACT,QAAQ,YAAY,EAAE;AACtB;AACA,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,MAAM,IAAI,KAAK,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC,CAAC;AAClD;AACA,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,IAAI;AACtD,IAAI,IAAI,IAAI,GAAG,IAAI;AACnB,IAAI,IAAI,YAAY,EAAE,IAAI,EAAE;AAC5B,MAAM,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;AAClD,QAAQ,KAAK,EAAE,EAAE,EAAE,EAAE,YAAY,CAAC,MAAM,EAAE;AAC1C,QAAQ,OAAO,EAAE;AACjB,UAAU,QAAQ,EAAE;AACpB,YAAY,OAAO,EAAE;AACrB,cAAc,MAAM,EAAE;AACtB;AACA;AACA;AACA,OAAO,CAAC;AACR,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,IAAI,GAAG;AACf,UAAU,EAAE,EAAE,MAAM,CAAC,EAAE;AACvB,UAAU,IAAI,EAAE,MAAM,CAAC,IAAI;AAC3B,UAAU,WAAW,EAAE,MAAM,CAAC,WAAW;AACzC,UAAU,OAAO,EAAE,MAAM,CAAC,OAAO;AACjC,UAAU,YAAY,EAAE,MAAM,CAAC,YAAY;AAC3C,UAAU,WAAW,EAAE,MAAM,CAAC,WAAW;AACzC,UAAU,oBAAoB,EAAE,MAAM,CAAC,oBAAoB,IAAI,KAAK,CAAC;AACrE,UAAU,mBAAmB,EAAE,MAAM,CAAC,mBAAmB,IAAI,KAAK,CAAC;AACnE,UAAU,OAAO,EAAE,MAAM,CAAC,OAAO;AACjC,UAAU,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM;AACtD,YAAY,SAAS,EAAE,OAAO,CAAC,SAAS;AACxC,YAAY,WAAW,EAAE,OAAO,CAAC,WAAW;AAC5C,YAAY,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,MAAM;AACnD,cAAc,OAAO,EAAE,KAAK,CAAC,OAAO;AACpC,cAAc,KAAK,EAAE,KAAK,CAAC,KAAK,KAAK,WAAW,GAAG,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE;AACzF,aAAa,CAAC;AACd,WAAW,CAAC;AACZ,SAAS;AACT;AACA;AACA,IAAI,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AACrD,MAAM,OAAO,EAAE;AACf,QAAQ,MAAM,EAAE;AAChB;AACA,KAAK,CAAC;AACN,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;AAC1C,IAAI,MAAM,QAAQ,mBAAmB,IAAI,GAAG,EAAE;AAC9C,IAAI,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACpC,MAAM,MAAM,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AACvD,MAAM,QAAQ,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC;AAC9B,KAAK,CAAC;AACN,IAAI,MAAM,iBAAiB,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;AAC1D,MAAM,MAAM,WAAW,GAAG,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,SAAS,KAAK,OAAO,CAAC,EAAE,CAAC;AAClF,MAAM,MAAM,WAAW,GAAG,WAAW,EAAE,WAAW,IAAI,kBAAkB,CAAC,WAAW;AACpF,MAAM,MAAM,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AAC5D,QAAQ,MAAM,SAAS,GAAG,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC;AACpF,QAAQ,MAAM,UAAU,GAAG,SAAS,EAAE,KAAK,KAAK,KAAK,CAAC,YAAY,KAAK,WAAW,GAAG,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AACpI,QAAQ,MAAM,QAAQ,GAAG,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AACpD,QAAQ,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;AAC5C,QAAQ,MAAM,IAAI,GAAG,KAAK,EAAE,IAAI,IAAI,CAAC;AACrC,QAAQ,MAAM,SAAS,GAAG,OAAO,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI;AAChG,QAAQ,MAAM,WAAW,GAAG,OAAO,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,UAAU,GAAG,GAAG,CAAC,GAAG,IAAI;AAC1G,QAAQ,OAAO;AACf,UAAU,EAAE,EAAE,KAAK,CAAC,EAAE;AACtB,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI;AAC1B,UAAU,WAAW,EAAE,KAAK,CAAC,WAAW;AACxC,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI;AAC1B,UAAU,IAAI,EAAE,KAAK,CAAC,IAAI;AAC1B,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,UAAU,KAAK,EAAE,UAAU;AAC3B,UAAU,IAAI;AACd,UAAU,SAAS;AACnB,UAAU,WAAW;AACrB,UAAU,MAAM,EAAE,KAAK,EAAE,MAAM,IAAI,IAAI;AACvC,UAAU,WAAW,EAAE,KAAK,EAAE,SAAS,IAAI;AAC3C,SAAS;AACT,OAAO,CAAC;AACR,MAAM,OAAO;AACb,QAAQ,EAAE,EAAE,OAAO,CAAC,EAAE;AACtB,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,WAAW,EAAE,OAAO,CAAC,WAAW;AACxC,QAAQ,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAClC,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,IAAI,EAAE,OAAO,CAAC,IAAI;AAC1B,QAAQ,WAAW;AACnB,QAAQ,MAAM,EAAE;AAChB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,gBAAgB,GAAG,IAAI;AAC/B,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,gBAAgB,GAAG;AACzB,QAAQ,MAAM,EAAE,YAAY,CAAC,MAAM;AACnC,QAAQ,SAAS,EAAE,YAAY,CAAC,kBAAkB,IAAI,YAAY,CAAC,SAAS;AAC5E,QAAQ,OAAO,EAAE,YAAY,CAAC,gBAAgB,IAAI,KAAK,CAAC;AACxD,QAAQ,iBAAiB,EAAE,YAAY,CAAC,iBAAiB;AACzD,QAAQ,MAAM,EAAE,YAAY,CAAC,MAAM;AACnC,QAAQ,YAAY,EAAE,KAAK;AAC3B;AACA,OAAO;AACP;AACA,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,IAAI;AACV,MAAM,YAAY,EAAE;AACpB,KAAK;AACL,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,CAAC,KAAK,CAAC,oDAAoD,EAAE,KAAK,CAAC;AAC9E,IAAI,OAAO;AACX,MAAM,QAAQ,EAAE,EAAE;AAClB,MAAM,IAAI,EAAE,IAAI;AAChB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,KAAK,EAAE,KAAK,CAAC;AACnB,KAAK;AACL;AACA;;;;"}