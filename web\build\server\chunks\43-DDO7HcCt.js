import { r as redirect } from './index-Ddp2AB5f.js';

const load = async ({ locals }) => {
  const user = locals.user;
  if (!user) {
    throw redirect(302, "/auth/sign-in");
  }
  return {
    user
  };
};

var _page_server_ts = /*#__PURE__*/Object.freeze({
  __proto__: null,
  load: load
});

const index = 43;
let component_cache;
const component = async () => component_cache ??= (await import('./_page.svelte-Dtzh8Z_2.js')).default;
const server_id = "src/routes/dashboard/settings/+page.server.ts";
const imports = ["_app/immutable/nodes/43.BsFAj_pd.js","_app/immutable/chunks/BasJTneF.js","_app/immutable/chunks/CGmarHxI.js","_app/immutable/chunks/CgXBgsce.js","_app/immutable/chunks/CIt1g2O9.js","_app/immutable/chunks/CmxjS0TN.js","_app/immutable/chunks/BwZiefMD.js","_app/immutable/chunks/C3w0v0gR.js","_app/immutable/chunks/BvdI7LR8.js","_app/immutable/chunks/BIEMS98f.js","_app/immutable/chunks/B1K98fMG.js","_app/immutable/chunks/ncUU1dSD.js","_app/immutable/chunks/B-Xjo-Yt.js","_app/immutable/chunks/u21ee2wt.js","_app/immutable/chunks/5V1tIHTN.js","_app/immutable/chunks/Btcx8l8F.js","_app/immutable/chunks/DM07Bv7T.js","_app/immutable/chunks/B3MJtjra.js","_app/immutable/chunks/nZgk9enP.js","_app/immutable/chunks/C6g8ubaU.js","_app/immutable/chunks/Z9Zpt0fH.js","_app/immutable/chunks/B_6ivTD3.js","_app/immutable/chunks/BBa424ah.js","_app/immutable/chunks/D4f2twK-.js","_app/immutable/chunks/w80wGXGd.js","_app/immutable/chunks/hA0h0kTo.js","_app/immutable/chunks/rNI1Perp.js","_app/immutable/chunks/FAbXdqfL.js","_app/immutable/chunks/CxmsTEaf.js","_app/immutable/chunks/B-l1ubNa.js","_app/immutable/chunks/1gTNXEeM.js","_app/immutable/chunks/BAawoUIy.js","_app/immutable/chunks/BSHZ37s_.js","_app/immutable/chunks/DkmCSZhC.js"];
const stylesheets = [];
const fonts = [];

export { component, fonts, imports, index, _page_server_ts as server, server_id, stylesheets };
//# sourceMappingURL=43-DDO7HcCt.js.map
