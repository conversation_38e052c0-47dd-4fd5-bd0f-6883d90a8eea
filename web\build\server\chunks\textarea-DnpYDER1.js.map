{"version": 3, "file": "textarea-DnpYDER1.js", "sources": ["../../../.svelte-kit/adapter-node/chunks/textarea.js"], "sourcesContent": ["import { M as spread_attributes, T as clsx, V as escape_html, N as bind_props, y as pop, w as push } from \"./index3.js\";\nimport { c as cn } from \"./utils.js\";\nfunction Textarea($$payload, $$props) {\n  push();\n  let {\n    ref = null,\n    value = void 0,\n    class: className,\n    $$slots,\n    $$events,\n    ...restProps\n  } = $$props;\n  $$payload.out += `<textarea${spread_attributes(\n    {\n      \"data-slot\": \"textarea\",\n      class: clsx(cn(\"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 field-sizing-content shadow-xs flex min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base outline-none transition-[color,box-shadow] focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className)),\n      ...restProps\n    },\n    null\n  )}>`;\n  const $$body = escape_html(value);\n  if ($$body) {\n    $$payload.out += `${$$body}`;\n  }\n  $$payload.out += `</textarea>`;\n  bind_props($$props, { ref, value });\n  pop();\n}\nexport {\n  Textarea as T\n};\n"], "names": [], "mappings": ";;;AAEA,SAAS,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE;AACtC,EAAE,IAAI,EAAE;AACR,EAAE,IAAI;AACN,IAAI,GAAG,GAAG,IAAI;AACd,IAAI,KAAK,GAAG,MAAM;AAClB,IAAI,KAAK,EAAE,SAAS;AACpB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,GAAG;AACP,GAAG,GAAG,OAAO;AACb,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,SAAS,EAAE,iBAAiB;AAChD,IAAI;AACJ,MAAM,WAAW,EAAE,UAAU;AAC7B,MAAM,KAAK,EAAE,IAAI,CAAC,EAAE,CAAC,qcAAqc,EAAE,SAAS,CAAC,CAAC;AACve,MAAM,GAAG;AACT,KAAK;AACL,IAAI;AACJ,GAAG,CAAC,CAAC,CAAC;AACN,EAAE,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,CAAC;AACnC,EAAE,IAAI,MAAM,EAAE;AACd,IAAI,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;AAChC;AACA,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AAChC,EAAE,UAAU,CAAC,OAAO,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;AACrC,EAAE,GAAG,EAAE;AACP;;;;"}